<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
http://www.springframework.org/schema/tx
http://www.springframework.org/schema/tx/spring-tx.xsd">

    <tx:annotation-driven/>

    <bean id="mediaDataSource" class="com.alibaba.druid.pool.DruidDataSource"
          init-method="init" destroy-method="close">
        <property name="driverClassName" value="${media.jdbc.driverClassName}"/>
        <property name="url" value="${media.jdbc.url}"/>
        <property name="username" value="${media.jdbc.username}"/>
        <property name="password" value="${media.jdbc.password}"/>
        <property name="filters" value="stat,slf4j"/>
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="${media.jdbc.maxActive}"/>
        <!-- 初始化大小 -->
        <property name="initialSize" value="${media.jdbc.initialSize}"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="${media.jdbc.maxWait}"/>
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="${media.jdbc.minIdle}"/>
        <!-- 逐出连接的检测时间间隔 -->
        <property name="timeBetweenEvictionRunsMillis" value="${media.jdbc.timeBetweenEvictionRunsMillis}"/>
        <!-- 最小逐出时间 -->
        <property name="minEvictableIdleTimeMillis" value="${media.jdbc.minEvictableIdleTimeMillis}"/>
        <!-- 测试有效用的SQL Query -->
        <property name="validationQuery" value="SELECT 'x'"/>
        <!-- 连接空闲时测试是否有效 -->
        <property name="testWhileIdle" value="${media.jdbc.testWhileIdle}"/>
        <!-- 获取连接时测试是否有效 -->
        <property name="testOnBorrow" value="${media.jdbc.testOnBorrow}"/>
        <!-- 归还连接时是否测试有效 -->
        <property name="testOnReturn" value="${media.jdbc.testOnReturn}"/>
    </bean>

    <bean id="mediaTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="mediaDataSource"/>
    </bean>

    <bean id="mediaSqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean" lazy-init="false">
        <property name="dataSource" ref="mediaDataSource"/>
        <property name="configLocation" value="classpath:mybatis.conf.xml"/>
        <property name="mapperLocations" value="classpath:mapping/media/*.xml"/>
<!--        <property name="plugins">-->
<!--            <array>-->
<!--                <ref bean="mybatisPlusInterceptor"/>-->
<!--            </array>-->
<!--        </property>-->
    </bean>

    <bean id="mediaSqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="mediaSqlSessionFactory"/>
    </bean>

    <!-- 配置扫描器 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.heytap.longvideo.search.mapper.media"/>
        <property name="sqlSessionFactoryBeanName" value="mediaSqlSessionFactory"/>
    </bean>


    <bean id="tvserviceDataSource" class="com.alibaba.druid.pool.DruidDataSource"
          init-method="init" destroy-method="close">
        <property name="driverClassName" value="${tvservice.jdbc.driverClassName}"/>
        <property name="url" value="${tvservice.jdbc.url}"/>
        <property name="username" value="${tvservice.jdbc.username}"/>
        <property name="password" value="${tvservice.jdbc.password}"/>
        <property name="filters" value="stat,slf4j"/>
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="${tvservice.jdbc.maxActive}"/>
        <!-- 初始化大小 -->
        <property name="initialSize" value="${tvservice.jdbc.initialSize}"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="${tvservice.jdbc.maxWait}"/>
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="${tvservice.jdbc.minIdle}"/>
        <!-- 逐出连接的检测时间间隔 -->
        <property name="timeBetweenEvictionRunsMillis" value="${tvservice.jdbc.timeBetweenEvictionRunsMillis}"/>
        <!-- 最小逐出时间 -->
        <property name="minEvictableIdleTimeMillis" value="${tvservice.jdbc.minEvictableIdleTimeMillis}"/>
        <!-- 测试有效用的SQL Query -->
        <property name="validationQuery" value="SELECT 'x'"/>
        <!-- 连接空闲时测试是否有效 -->
        <property name="testWhileIdle" value="${tvservice.jdbc.testWhileIdle}"/>
        <!-- 获取连接时测试是否有效 -->
        <property name="testOnBorrow" value="${tvservice.jdbc.testOnBorrow}"/>
        <!-- 归还连接时是否测试有效 -->
        <property name="testOnReturn" value="${tvservice.jdbc.testOnReturn}"/>
    </bean>

    <bean id="tvserviceTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="tvserviceDataSource"/>
    </bean>

    <bean id="tvserviceSqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean" lazy-init="false">
        <property name="dataSource" ref="tvserviceDataSource"/>
        <property name="configLocation" value="classpath:mybatis.conf.xml"/>
         <property name="mapperLocations" value="classpath:mapping/tvservice/*.xml"/>
<!--        <property name="plugins">-->
<!--            <array>-->
<!--                <ref bean="mybatisPlusInterceptor"/>-->
<!--            </array>-->
<!--        </property>-->
    </bean>

    <bean id="tvserviceSqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg index="0" ref="tvserviceSqlSessionFactory"/>
    </bean>

    <!-- 配置扫描器 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.heytap.longvideo.search.mapper.tvservice"/>
        <property name="sqlSessionFactoryBeanName" value="tvserviceSqlSessionFactory"/>
    </bean>


    <!--分页插件 -->
  <!--  <bean id="mybatisPlusInterceptor" class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
        <property name="interceptors">
            <list>
                <ref bean="paginationInnerInterceptor"/>
            </list>
        </property>
    </bean>
    <bean id="paginationInnerInterceptor" class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
        &lt;!&ndash; 对于单一数据库类型来说,都建议配置该值,避免每次分页都去抓取数据库类型 &ndash;&gt;
        <constructor-arg name="dbType" value="MYSQL"/>
        &lt;!&ndash; 设置最大单页限制数量，默认 500 条，-1 不受限制 &ndash;&gt;
        <property name="maxLimit" value="500"/>
    </bean>-->
</beans>
