#!/bin/sh

source /etc/profile
source $(dirname $0)/../../env.sh

#check JAVA_HOME & java
noJavaHome=false
if [ -z "$JAVA_HOME" ] ; then
    noJavaHome=true
fi
if [ ! -e "$JAVA_HOME/bin/java" ] ; then
    noJavaHome=true
fi
if $noJavaHome ; then
    echo
    echo "Error: JAVA_HOME environment variable is not set."
    echo
    exit 1
fi
#==============================================================================

#set JAVA_OPTS
JAVA_OPTS="-Xss256k"
#==============================================================================

#stop Server
APPLICATION_HOME=$(cd `dirname $0`/..; pwd)
echo ${APPLICATION_HOME}
ps -elf |grep java|grep -v 'grep'|grep ${APPLICATION_HOME}|awk -F ' ' '{print $4}'|while read line
do
    echo $line
    eval `kill ${line}`
done

sleep 3
PID=`ps auxf | grep java | grep $APPLICATION_HOME | grep -v bash | awk '{print $2}'`
if [ -n "$PID" ]; then
    echo $PID
    kill -9 $PID
fi
#==============================================================================