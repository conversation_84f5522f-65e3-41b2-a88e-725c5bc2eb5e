#!/bin/sh

source /etc/profile
source $(dirname $0)/../../env.sh

#check JAVA_HOME & java
noJavaHome=false
if [ -z "$JAVA_HOME" ] ; then
    noJavaHome=true
fi
if [ ! -e "$JAVA_HOME/bin/java" ] ; then
    noJavaHome=true
fi
if $noJavaHome ; then
    echo
    echo "Error: JAVA_HOME environment variable is not set."
    echo
    exit 1
fi
#==============================================================================

#set APPLICATION_HOME
CURR_DIR=`pwd`
cd `dirname "$0"`/..
APPLICATION_HOME=`pwd`
cd $CURR_DIR
if [ -z "$APPLICATION_HOME" ] ; then
    echo
    echo "Error: APPLICATION_HOME environment variable is not defined correctly."
    echo
    exit 1
fi
#==============================================================================

#stop Server
sh $APPLICATION_HOME/bin/shutdown.sh

#startup Server
sh $APPLICATION_HOME/bin/startup.sh
#==============================================================================