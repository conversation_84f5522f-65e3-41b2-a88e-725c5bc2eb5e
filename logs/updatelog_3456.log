 INFO [2025-07-31 14:24:09,293] [main] updatelog.thirdPartyMediaSyncConsumer(MediaJinsMqConsumerConfig.java:48) - start init thirdPartyMediaSyncConsumer
 INFO [2025-07-31 14:24:10,827] [main] updatelog.thirdPartyMediaSyncConsumer(MediaJinsMqConsumerConfig.java:66) - unofficial_media_topic consumer init success
 INFO [2025-07-31 14:24:10,828] [main] updatelog.thirdPartyMediaSyncDeadLetterConsumer(MediaJinsMqConsumerConfig.java:72) - start init thirdPartyMediaSyncDeadLetterConsumer
 INFO [2025-07-31 14:24:12,348] [main] updatelog.thirdPartyMediaSyncDeadLetterConsumer(MediaJinsMqConsumerConfig.java:108) - %DLQ%unofficial_media_topic consumer init success
 INFO [2025-07-31 14:24:12,350] [main] updatelog.jinsConsumer(MediaJinsMqConsumerConfig.java:115) - start init mediaJinsConsumer
 INFO [2025-07-31 14:24:13,784] [main] updatelog.jinsConsumer(MediaJinsMqConsumerConfig.java:129) - longvideo_media_change_20220321 consumer init success
 INFO [2025-07-31 14:25:12,727] [ConsumeMessageThread_3] updatelog.consumeEpisodeEvent(MediaJinsConsumerService.java:198) - update episode msg start,eid:1087862956810752000
 INFO [2025-07-31 14:25:12,727] [ConsumeMessageThread_4] updatelog.consumeEpisodeEvent(MediaJinsConsumerService.java:198) - update episode msg start,eid:1087864302410584064
 INFO [2025-07-31 14:25:12,853] [ConsumeMessageThread_8] updatelog.consumeEpisodeEvent(MediaJinsConsumerService.java:198) - update episode msg start,eid:1087861915402817536
 INFO [2025-07-31 14:25:13,097] [ConsumeMessageThread_12] updatelog.consumeEpisodeEvent(MediaJinsConsumerService.java:198) - update episode msg start,eid:1151408879532429312
 INFO [2025-07-31 14:25:13,300] [ConsumeMessageThread_16] updatelog.consumeEpisodeEvent(MediaJinsConsumerService.java:198) - update episode msg start,eid:822493172340039680
 INFO [2025-07-31 14:25:13,353] [ConsumeMessageThread_17] updatelog.consumeEpisodeEvent(MediaJinsConsumerService.java:198) - update episode msg start,eid:822493173334089728
