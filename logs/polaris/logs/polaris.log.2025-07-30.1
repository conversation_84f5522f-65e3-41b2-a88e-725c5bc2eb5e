2025-07-30 18:17:22.316 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-30 18:17:22.367 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-30 18:17:22.367 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-30 18:17:22.368 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-30 18:17:22.541 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-30 18:17:29.792 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-30 18:17:30.075 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-30 18:17:30.076 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-30 18:17:30.077 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-30 18:17:30.077 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-30 18:17:30.077 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-30 18:17:30.356 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-30 18:17:30.383 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-30 18:17:30.417 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-30 18:17:30.673 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-30 18:17:32.157 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-30 18:17:32.157 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-30 18:17:46.432 INFO [polaris-connection-manager-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-30 18:17:46.436 INFO [polaris-connection-manager-1:grpc.ConnectionManager] server polaris.builtin connection switched from euler-polaris.oppo.test:80 to euler-polaris.oppo.test:80
2025-07-30 18:17:47.275 WARN [polaris-prometheus-1:plugin.PrometheusReporter] Report prometheus http server info exception.
com.tencent.polaris.api.exception.RetriableException: ERR-1007(NETWORK_ERROR): fail to report client host **************, version 1.1.35,1.11.0-oppofix-u33-SNAPSHOT service ServiceKey{namespace='null', service='null'}, cause: Waited 10000 milliseconds (plus 38 milliseconds, 39000 nanoseconds delay) for shade.polaris.io.grpc.stub.ClientCalls$GrpcFuture@62f4e808[status=PENDING, info=[GrpcFuture{clientCall={delegate=PendingCall{realCall=ClientCallImpl{method=MethodDescriptor{fullMethodName=v1.PolarisGRPC/ReportClient, type=UNARY, idempotent=false, safe=false, sampledToLocalTracing=true, requestMarshaller=shade.polaris.io.grpc.protobuf.lite.ProtoLiteUtils$MessageMarshaller@500ef361, responseMarshaller=shade.polaris.io.grpc.protobuf.lite.ProtoLiteUtils$MessageMarshaller@69a6f48d, schemaDescriptor=com.tencent.polaris.specification.api.v1.service.manage.PolarisGRPCGrpc$PolarisGRPCMethodDescriptorSupplier@1ba63825}}}}}]]
	at com.tencent.polaris.plugins.connector.grpc.GrpcConnector.reportClient(GrpcConnector.java:609) ~[polaris-all-1.11.0-oppofix-u33-20250714.081653-20.jar:?]
	at com.tencent.polaris.plugins.stat.prometheus.plugin.PrometheusReporter.lambda$reportClient$0(PrometheusReporter.java:297) ~[polaris-all-1.11.0-oppofix-u33-20250714.081653-20.jar:?]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[?:1.8.0_441]
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308) ~[?:1.8.0_441]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java) ~[?:1.8.0_441]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) ~[?:1.8.0_441]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) ~[?:1.8.0_441]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_441]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_441]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_441]
Caused by: java.util.concurrent.TimeoutException: Waited 10000 milliseconds (plus 38 milliseconds, 39000 nanoseconds delay) for shade.polaris.io.grpc.stub.ClientCalls$GrpcFuture@62f4e808[status=PENDING, info=[GrpcFuture{clientCall={delegate=PendingCall{realCall=ClientCallImpl{method=MethodDescriptor{fullMethodName=v1.PolarisGRPC/ReportClient, type=UNARY, idempotent=false, safe=false, sampledToLocalTracing=true, requestMarshaller=shade.polaris.io.grpc.protobuf.lite.ProtoLiteUtils$MessageMarshaller@500ef361, responseMarshaller=shade.polaris.io.grpc.protobuf.lite.ProtoLiteUtils$MessageMarshaller@69a6f48d, schemaDescriptor=com.tencent.polaris.specification.api.v1.service.manage.PolarisGRPCGrpc$PolarisGRPCMethodDescriptorSupplier@1ba63825}}}}}]]
	at shade.polaris.com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:506) ~[polaris-all-1.11.0-oppofix-u33-20250714.081653-20.jar:?]
	at com.tencent.polaris.plugins.connector.grpc.GrpcConnector.reportClient(GrpcConnector.java:587) ~[polaris-all-1.11.0-oppofix-u33-20250714.081653-20.jar:?]
	... 9 more
2025-07-30 18:17:48.866 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 18199 ms
2025-07-30 18:17:48.866 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-30 18:17:48.870 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-30 18:17:48.942 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-30 18:17:49.290 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 347 ms
2025-07-30 18:17:49.290 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-30 18:17:49.290 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
