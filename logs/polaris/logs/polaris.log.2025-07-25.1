2025-07-25 12:06:15.025 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:06:15.041 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:06:15.041 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:06:15.041 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:06:15.089 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 12:06:15.162 INFO [main :memory.InMemoryRegistry] loaded 0 services from local cache
2025-07-25 12:06:15.581 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 12:06:15.582 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:06:15.584 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:06:15.584 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:06:15.584 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:06:15.880 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 12:06:15.901 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 12:06:15.936 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 12:06:16.326 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:06:16.752 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:06:16.752 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:06:20.506 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 4185 ms
2025-07-25 12:06:20.507 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 12:06:20.509 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:06:20.530 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:06:20.577 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 47 ms
2025-07-25 12:06:20.577 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 12:06:20.577 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:07:42.109 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:07:42.120 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:07:42.120 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:07:42.120 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:07:42.166 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 12:07:42.213 INFO [main :memory.InMemoryRegistry] loaded 0 services from local cache
2025-07-25 12:07:42.314 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 12:07:42.316 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:07:42.316 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:07:42.316 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:07:42.316 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:07:42.463 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 12:07:42.475 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 12:07:42.493 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 12:07:42.610 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:07:42.803 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:07:42.803 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:07:45.112 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 2505 ms
2025-07-25 12:07:45.112 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 12:07:45.114 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:07:45.123 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:07:45.171 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 48 ms
2025-07-25 12:07:45.171 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 12:07:45.171 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:10:01.611 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:10:01.619 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:10:01.619 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:10:01.619 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:10:01.649 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 12:10:01.691 INFO [main :memory.InMemoryRegistry] loaded 0 services from local cache
2025-07-25 12:10:01.807 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 12:10:01.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:10:01.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:10:01.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:10:01.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:10:01.966 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 12:10:01.980 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 12:10:01.999 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 12:10:02.121 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:10:02.318 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:10:02.318 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:10:04.838 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 2719 ms
2025-07-25 12:10:04.838 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 12:10:04.839 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:10:04.847 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:10:04.894 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 47 ms
2025-07-25 12:10:04.894 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 12:10:04.894 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:15:35.288 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:15:35.295 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:15:35.295 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:15:35.295 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:15:35.340 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 12:15:36.287 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-25 12:15:36.370 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 12:15:36.371 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 12:15:36.371 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 12:15:36.371 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 12:15:36.371 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 12:15:36.476 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 12:15:36.489 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 12:15:36.505 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 12:15:36.624 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:15:36.821 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:15:36.821 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 12:15:39.287 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 2666 ms
2025-07-25 12:15:39.287 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 12:15:39.289 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 12:15:39.301 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 12:15:39.347 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 46 ms
2025-07-25 12:15:39.347 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 12:15:39.347 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 15:23:38.927 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 15:23:38.939 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 15:23:38.939 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 15:23:38.939 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 15:23:38.980 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 15:23:41.317 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-25 15:23:41.472 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 15:23:41.473 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 15:23:41.473 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 15:23:41.473 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 15:23:41.473 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 15:23:41.633 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 15:23:41.655 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 15:23:41.689 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 15:23:41.908 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 15:23:42.304 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 15:23:42.304 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 15:23:45.792 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 3889 ms
2025-07-25 15:23:45.792 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 15:23:45.794 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 15:23:45.809 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 15:23:45.864 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 55 ms
2025-07-25 15:23:45.865 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 15:23:45.865 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 16:03:33.575 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 16:03:33.606 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 16:03:33.608 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 16:03:33.609 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 16:03:33.910 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 16:03:37.316 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-25 16:03:37.460 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 16:03:37.461 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 16:03:37.461 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 16:03:37.462 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 16:03:37.462 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 16:03:37.633 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 16:03:37.658 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 16:03:37.691 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 16:03:37.921 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 16:03:38.311 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 16:03:38.311 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 16:03:42.357 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 4439 ms
2025-07-25 16:03:42.357 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 16:03:42.359 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 16:03:42.377 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 16:03:42.432 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 55 ms
2025-07-25 16:03:42.432 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 16:03:42.432 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 16:18:42.605 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 16:18:42.614 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 16:18:42.615 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 16:18:42.615 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 16:18:42.673 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-25 16:18:44.367 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-25 16:18:44.519 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-25 16:18:44.520 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-25 16:18:44.520 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-25 16:18:44.520 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-25 16:18:44.520 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-25 16:18:44.704 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-25 16:18:44.734 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-25 16:18:44.768 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-25 16:18:45.004 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 16:18:45.409 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 16:18:45.409 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-25 16:18:49.792 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 4792 ms
2025-07-25 16:18:49.792 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-25 16:18:49.795 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-25 16:18:49.821 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-25 16:18:49.871 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 50 ms
2025-07-25 16:18:49.872 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-25 16:18:49.872 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
