2025-07-31 10:26:20.609 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 10:26:20.625 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 10:26:20.625 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 10:26:20.625 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 10:26:20.659 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-31 10:26:21.717 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-31 10:26:21.808 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-31 10:26:21.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 10:26:21.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 10:26:21.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 10:26:21.808 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 10:26:21.907 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-31 10:26:21.922 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-31 10:26:21.953 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-31 10:26:22.121 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 10:26:22.326 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 10:26:22.326 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 10:26:25.209 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 3092 ms
2025-07-31 10:26:25.209 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-31 10:26:25.211 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 10:26:25.222 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 10:26:25.273 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 51 ms
2025-07-31 10:26:25.273 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-31 10:26:25.273 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 10:41:19.779 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 10:41:19.787 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 10:41:19.787 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 10:41:19.787 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 10:41:19.817 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-31 10:41:21.045 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-31 10:41:21.147 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-31 10:41:21.148 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 10:41:21.148 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 10:41:21.148 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 10:41:21.149 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 10:41:21.267 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-31 10:41:21.284 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-31 10:41:21.305 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-31 10:41:21.467 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 10:41:21.778 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 10:41:21.778 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 10:41:27.816 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 6354 ms
2025-07-31 10:41:27.816 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-31 10:41:27.818 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 10:41:27.831 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 10:41:27.945 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 114 ms
2025-07-31 10:41:27.945 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-31 10:41:27.945 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 14:20:22.004 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 14:20:22.020 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 14:20:22.021 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 14:20:22.021 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 14:20:22.097 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-31 14:20:24.196 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-31 14:20:24.377 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-31 14:20:24.379 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 14:20:24.379 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 14:20:24.379 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 14:20:24.379 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 14:20:24.553 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-31 14:20:24.583 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-31 14:20:24.634 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-31 14:20:24.867 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 14:20:25.318 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 14:20:25.318 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 14:20:30.357 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 5494 ms
2025-07-31 14:20:30.357 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-31 14:20:30.360 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 14:20:30.380 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 14:20:30.502 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 122 ms
2025-07-31 14:20:30.502 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-31 14:20:30.502 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 14:22:54.691 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 14:22:54.703 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 14:22:54.703 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 14:22:54.703 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 14:22:54.743 INFO [main :memory.InMemoryRegistry] start to load local cache files from ./logs/polaris/backup
2025-07-31 14:22:55.906 INFO [main :memory.InMemoryRegistry] loaded 2 services from local cache
2025-07-31 14:22:56.007 INFO [main :localfile.LocalFileConfigFileConnector] init local file config connector,watch dir:[./logs/polaris/backup/config].
2025-07-31 14:22:56.007 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster BUILTIN_CLUSTER, service null has been made ready
2025-07-31 14:22:56.007 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_DISCOVER_CLUSTER, service null has been made ready
2025-07-31 14:22:56.007 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster SERVICE_CONFIG_CLUSTER, service null has been made ready
2025-07-31 14:22:56.007 INFO [main :grpc.ConnectionManager] [ServerConnector]cluster HEALTH_CHECK_CLUSTER, service null has been made ready
2025-07-31 14:22:56.129 INFO [main :compose.Extensions] locationProvider plugin local not found location
2025-07-31 14:22:56.148 INFO [main :handler.PrometheusHttpServer] [Metrics][Prometheus] http-server listen port : 38080
2025-07-31 14:22:56.175 INFO [main :plugin.PrometheusReporter] start schedule metric aggregation task, task interval 30000
2025-07-31 14:22:56.384 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 14:22:56.625 INFO [main :grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 14:22:56.625 INFO [polaris-prometheus-1:grpc.ConnectionManager] [ConnectionManager]in bound message size is 52428800
2025-07-31 14:22:59.727 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, code = 400202, version = 0, duration = 3348 ms
2025-07-31 14:22:59.727 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}
2025-07-31 14:22:59.729 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_route_rule.yaml'}, version = 0
2025-07-31 14:22:59.741 INFO [main :internal.AbstractConfigFileRepo] [Config] start pull config file. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
2025-07-31 14:22:59.789 INFO [main :internal.AbstractConfigFileRepo] [Config] pull config file finished. config file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, code = 400202, version = 0, duration = 48 ms
2025-07-31 14:22:59.789 WARN [main :internal.AbstractConfigFileRepo] [Config] config file not found, please check whether config file released. ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}
2025-07-31 14:22:59.789 INFO [main :internal.DefaultConfigFileLongPollingService] [Config] add long polling config file. file = ConfigFile{namespace='test.longvideo-search-rest', fileGroup='public', fileName='internal_lb_rule.yaml'}, version = 0
