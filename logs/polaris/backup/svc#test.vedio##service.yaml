---
code: 200000
info: "execute success"
type: "SERVICES"
service:
  name: ""
  namespace: "test.vedio"
  revision: ""
  clientId: ""
services:
- name: "com.heytap.longvideo.client.arrange.LvDetailPageRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.LvMediaSeriesRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvSelectionOperationRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-ad-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-relation-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.common.service.LvAdEsaRpcServiceV2"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.vip.sdk.rpc.VipEntranceSceneRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.MultiSubjectRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvMinusScreenRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.infrastructure.rpc.VideoAdEsaRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvAlbumListAndItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.CoinHubBudgetConfigRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.LvMediaSeriesItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.FlashSaleStrategyRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvThirdPartyCardParentRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.PageArrangeRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardEpisodeRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-counter-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.OriginalImageUrlMappingOcsUrlRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.LvAlbumConsumeHiveDataRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ImgUploadRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvSearchKeywordRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvSearchInterveneRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "oppomobile-user"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvSearchRecommendWordRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MediaAlbumTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.vip.sdk.rpc.VipProductRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-integrate-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-counter-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LayoutPositionItemRpc"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTopWechatDoubanMediaSaveRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.vip.sdk.rpc.VipRelatedStrategyRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ManualSpiderResultRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.infrastructure.rpc.LvAdEsaRpcServiceV2"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-list-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.PageDrawersResourceStrategyRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.relation.api.service.MiddleRelationService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UgcMisAuthorOrgRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MediaCommonServiceRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.infrastructure.rpc.LvAdEsaRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisPersonOrgRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.TopGroupCategoryRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.YunheRankRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvAlgorithmPoolRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.BusinessRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.infrastructure.rpc.AdEsaRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisPersonRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.common.service.AdConfigRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ContentTypeRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.common.lib.rpc.AdTaskActivityRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.vip.sdk.rpc.ProductQueueRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-user-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-toutiao-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UnofficialAlbumImageMappingRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardPersonRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubjectRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTelevisionScheduleItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ManualSpiderTaskRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-arrange-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UgcStandardAuthorRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardVidSidMappingRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.PageGroupItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvAlbumListItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.VipProductReplaceRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.oppo.browser.video.common.service.video.IVideoSourceAdapter"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-list-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MediaImageTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MediaSeriesRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.CouponRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubjectGroupRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.PageGroupRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvOperationResourceRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvThirdPartyCardRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-ucenter-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ImageTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.LvTopWechatDoubanMediaRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.LvSeriesAndSerisItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardAlbumRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-resource-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.oppo.cpc.video.framework.lib.vip.UserVipInfoRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubjectEsTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubscribeModuleRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubjectItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvDrawerRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.VirtualProgramRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvDetailSubscribeRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.TopGroupRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTopBlockRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.CoinHubDoubleCardConfigRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardRoleRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.VipBatchOpenRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.DrawersResourceStrategyRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.CoinHubBalanceAuditRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentTemplateItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTrailerOperationItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.YstRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "oneplustv-tvservice-vod"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisOrgTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvIconRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.common.lib.rpc.VipRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.common.service.AdEsaRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.PageRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-danmu-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.VipBatchRefundRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTrailerOperationRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvThirdPartyDetailFloatLayerRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MediaSeriesItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisAreaRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentPoolRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentMaterialBriefRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardVideoRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.CopyRightRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-youli-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.common.lib.rpc.OrderRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UgcMisVideoOrgRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisVideoOrgRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvQualityColumnRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.StandardTrailerRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisCopyrightRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentMaterialPosterRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvSearchFeedbackRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTelevisionChannelRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentMaterialRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-search-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-activity-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisOrgAreaRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-relation-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.infrastructure.rpc.AdConfigRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvSearchRecommendRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.BasicProductRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UgcStandardVideoRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-config-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ChannelGroupTypeRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.vip.sdk.rpc.ActivityProductRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTelevisionScheduleRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.common.service.LvAdEsaRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisProgramReviewTypeRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.MultiParamsRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvVersionUpdateModelConfigRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.CoinHubWithdrawLevelRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.oppo.browser.video.common.service.video.IVideoYouli"
  namespace: "test.vedio"
  product: "cec-video"
- name: "video-resource-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvTagRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisBulletOrgRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.common.lib.rpc.ActivityRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ChannelGroupRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubscribeModuleItemRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LayoutPositionRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.TopDetailRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.video.ad.common.service.VideoAdEsaRpcService"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvDetailPageSkinRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.SubjectPageRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvResourceStrategyRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UnofficialAlbumRoleRelRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.ImageTagVersionRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.PageLayoutRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvAlbumConsumeHiveDataRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.CoinHubTaskConfigRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LayoutPositionItemTimeArrangeRpc"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.common.lib.rpc.RetainDialogRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.VirtualProgramRelationRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "oppomobile-vip-rest"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvContentTemplateRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "longvideo-media-service"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.MisAlbumOrgRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.DwdKgAlignSpoDiRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.AlbumRankRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.media.UnofficialAlbumRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.interfaces.api.CoinHubCommonConfigRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
- name: "com.heytap.longvideo.client.arrange.LvAlbumListRpcApi"
  namespace: "test.vedio"
  product: "cec-video"
