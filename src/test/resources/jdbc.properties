media.jdbc.driverClassName=com.mysql.jdbc.Driver
media.jdbc.url=${GOBLIN_MYSQL_URL}
media.jdbc.username=${GOBLIN_MYSQL_USERNAME}
media.jdbc.password=${GOBLIN_MYSQL_PASSWORD}
media.jdbc.maxActive=20
media.jdbc.initialSize=5
media.jdbc.maxWait=60000
media.jdbc.minIdle=10
media.jdbc.timeBetweenEvictionRunsMillis=3000
media.jdbc.minEvictableIdleTimeMillis=300000
media.jdbc.testWhileIdle=true
media.jdbc.testOnBorrow=true
media.jdbc.testOnReturn=false

media.database.count=2
media.table.count=2

tvservice.jdbc.driverClassName=com.mysql.jdbc.Driver
tvservice.jdbc.url=****************************************************************************************************************************
tvservice.jdbc.username=jupiter
tvservice.jdbc.password=jupiter
tvservice.jdbc.maxActive=20
tvservice.jdbc.initialSize=5
tvservice.jdbc.maxWait=60000
tvservice.jdbc.minIdle=10
tvservice.jdbc.timeBetweenEvictionRunsMillis=3000
tvservice.jdbc.minEvictableIdleTimeMillis=300000
tvservice.jdbc.testWhileIdle=true
tvservice.jdbc.testOnBorrow=true
tvservice.jdbc.testOnReturn=false
