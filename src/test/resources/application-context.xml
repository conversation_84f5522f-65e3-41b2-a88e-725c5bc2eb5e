<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
    xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
    xmlns:heracles="http://www.oppo.com/schema/heracles"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
http://www.springframework.org/schema/context
http://www.springframework.org/schema/context/spring-context-3.0.xsd
http://www.springframework.org/schema/tx
http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
http://www.springframework.org/schema/aop
http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
http://www.oppo.com/schema/heracles http://www.oppo.com/schema/heracles.xsd">
    <context:annotation-config />
    <context:component-scan base-package="com.heytap.longvideo.**,com.oppo.cpc.**">
    </context:component-scan>

    <aop:aspectj-autoproxy proxy-target-class="true" />

    <bean id="log4jInitialization" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetClass" value="com.oppo.browser.app.framework.spring.MyLog4jConfigurer" />
        <property name="targetMethod" value="initLogging" />
        <property name="arguments">
            <list>
                <value>classpath:log4j2.xml</value>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor" />

    <heracles:config dynamic="*" />


    <import resource="database-conf.xml" />
    <import resource="setting-cache.xml" />

</beans>