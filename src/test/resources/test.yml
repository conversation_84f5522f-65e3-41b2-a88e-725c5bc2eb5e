dockerImages:
  - type: "MYSQL"
    imageVersion: "goblin-mysql-with-slave:5.7"

  - type: "REDISCLUSTER"
    imageVersion: "redis-cluster:5.0.12"

#  - type: "ROCKETMQ"
#    imageVersion: "rocketmq:4.7.2-alpine"

  - type: "MOCKSERVER"
    imageVersion: "mockserver:5.13.2"

  - type: "ELASTICSEARCH"
    imageVersion: "elasticsearch-oss:7.10.2"

  - type: "ZOOKEEPER"
    imageVersion: "zookeeper:3.5.9"


#rocketmq: #rocketmq相关配置
#  autoCreateTopics: #自动创建的topic列表，虽然不提供也会自动创建，但是这里会触发consumer重负载(一个心跳周期)导致首次开始消费延迟
#  - "add_credit_retry_topic_20190802"
mysql:
  database: "longvideo_media0"  #数据库名称
  initScript: "init1.sql"  #初始化脚本，放在和test.yml同级目录即可


env: #配置需要注入的OS Envrionments
  paas_port_0 : "8187"
  paas_port_1 : "8188"
  paas_prometheus_port : "9998"
  paas_dubbo_port : "8189"