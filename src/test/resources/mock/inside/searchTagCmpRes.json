{"aiSource": 0, "code": "-1", "contents": [{"aiSource": 0, "albumFeatureType": 1, "allowChaseAlbum": 0, "area": "内地剧", "brief": "女大学生穿越回古代", "buttonStatus": 1, "buttonText": "播放", "chaseAlbumStatus": 0, "contentType": "tv", "copyrightCode": "sohu", "deepLink": "yoli://yoli.com/yoli/longvideo/videodetail?linkValue=1104570270770974720", "directors": "", "featureType": 1, "functionScore": 1.16, "horizontalIcon": "http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20250212/vrsa_hor_1739333529025_9923525.jpg", "languages": "普通话", "linkType": 1, "linkValue": "1104570270770974720", "markCode": "", "markCodeUrl": "", "payStatus": 0, "programInfo": "全 24 集", "recommendInfo": "女大学生穿越回古代", "showScore": 0, "sid": "1104570270770974720", "sortDefine": 0.0, "source": "sohu", "sourceScore": 6.0, "stars": "温茉言|吴季峰|刘贾玺|袁梓铭|隋名旸|陈寰|张瀚方|王丽", "tags": "偶像,古装,爱情,剧情,奇幻,短剧", "thirdDate": 0, "title": "请君入梦", "verticalIcon": "http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20250212/vrsa_ver_1739333529060_9923525.jpg", "virtualSid": "1104570270770974720", "year": 2025}, {"aiSource": 0, "albumFeatureType": 1, "allowChaseAlbum": 0, "area": "内地剧", "brief": "老公脑子有问题", "buttonStatus": 1, "buttonText": "播放", "chaseAlbumStatus": 0, "contentType": "tv", "copyrightCode": "sohu", "deepLink": "yoli://yoli.com/yoli/longvideo/videodetail?linkValue=1098017064003751936", "directors": "", "featureType": 1, "functionScore": 1.16, "horizontalIcon": "http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20250114/vrsa_hor9923523.jpg", "languages": "普通话", "linkType": 1, "linkValue": "1098017064003751936", "markCode": "", "markCodeUrl": "", "payStatus": 0, "programInfo": "全 8 集", "recommendInfo": "老公脑子有问题", "showScore": 0, "sid": "1098017064003751936", "sortDefine": 0.0, "source": "sohu", "sourceScore": 6.0, "stars": "", "tags": "家庭,爱情,都市,短剧", "thirdDate": 0, "title": "霸婿", "verticalIcon": "http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20250114/vrsa_ver9923523.jpg", "virtualSid": "1098017064003751936", "year": 2025}], "deepLink": "yoli://yoli.com/YoliSearch/search/longTagDetail?code=-1&title=固定标签卡&sortType=1", "hasIntervene": false, "hasMore": true, "sortTypeList": [{"sortName": "最热排序", "sortType": 1}, {"sortName": "最新排序", "sortType": 2}], "title": "固定标签卡"}