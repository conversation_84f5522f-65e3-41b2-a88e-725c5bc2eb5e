[{"id": 3003, "interveneType": 2, "isShowMarkCode": 1, "linkType": 10030, "linkValue": "爱情:all", "matchType": 1, "name": "爱情", "queryKeyword": "爱情", "status": 1, "title": "爱情"}, {"id": 49, "interveneDetail": "[{\"title\":\"爱情公寓\",\"sid\":\"625490163333615616\",\"orderIndex\":2},{\"title\":\"爱情保卫战\",\"sid\":\"667337580165144576\",\"orderIndex\":3}]", "interveneType": 1, "isShowMarkCode": 0, "matchType": 2, "name": "测试-爱情-unit-test", "queryKeyword": "爱情,恋爱", "status": 1, "title": ""}, {"id": 48, "interveneDetail": "[{\"title\":\"爱情公寓\",\"sid\":\"625490163333615616\",\"orderIndex\":2},{\"title\":\"爱情保卫战\",\"sid\":\"667337580165144576\",\"orderIndex\":3}]", "interveneType": 1, "isShowMarkCode": 0, "matchType": 2, "name": "测试-爱情unit-test", "queryKeyword": "爱情,恋爱", "status": 1, "title": ""}, {"id": 50, "interveneDetail": "[{\"title\":\"爱情公寓\",\"sid\":\"625490163333615616\",\"orderIndex\":2},{\"title\":\"爱情保卫战\",\"sid\":\"667337580165144576\",\"orderIndex\":3}]", "interveneType": 1, "isShowMarkCode": 0, "matchType": 2, "name": "测试-爱情unit-test", "queryKeyword": "爱情,恋爱", "status": 1, "title": ""}]