[{"id": 49, "interveneDetail": "[{\"title\":\"一杯上路 普通话版\",\"sid\":\"936393417615912960\",\"orderIndex\":1},{\"title\":\"一杯上路\",\"sid\":\"936393415476817920\",\"orderIndex\":2}]", "interveneType": 1, "isShowMarkCode": 0, "matchType": 2, "name": "单元测试", "queryKeyword": "法", "status": 1, "title": ""}, {"id": 50, "interveneDetail": "[{\"title\":\"一杯上路 普通话版\",\"sid\":\"936393417615912960\",\"orderIndex\":1},{\"title\":\"一杯上路\",\"sid\":\"936393415476817920\",\"orderIndex\":2}]", "interveneType": 1, "isShowMarkCode": 0, "matchType": 2, "name": "单元测试", "queryKeyword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": 1, "title": ""}, {"id": 51, "interveneDetail": "[{\"title\":\"一杯上路 普通话版\",\"sid\":\"936393417615912960\",\"orderIndex\":1},{\"title\":\"一杯上路\",\"sid\":\"936393415476817920\",\"orderIndex\":2}]", "interveneType": 1, "isShowMarkCode": 0, "matchType": 2, "name": "单元测试", "queryKeyword": "流浪地", "status": 1, "title": ""}]