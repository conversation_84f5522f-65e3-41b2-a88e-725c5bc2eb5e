<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="searchByKeywordChain">
        THEN(
        getSearchIntentCmp, searchAlbumCmp, searchInterveneCmp, WHEN(allWebSearchCmp,searchTagCmp,searchRecommendCmp,searchSeriesCmp,searchActorCmp,searchBannerCmp,searchDuanjuCmp).ignoreError(true).maxWaitSeconds(2),resultAggregationCmp,defaultRecommendCmp
        );
    </chain>
    <chain name="outSideSearchByKeywordChain">
        THEN(
        thirdPartyStrategyMatchCmp, outSideGetSearchIntentCmp, searchAlbumCmp, WHEN(outSideSearchInterveneCmp).ignoreError(true).maxWaitSeconds(2), outSideResultAggregationCmp
        );
    </chain>
    <chain name="outSideSearchByKeywordChainV2">
        THEN(
        outSidePreHandleCmp,
        WHEN(outSideGetSearchIntentCmp, THEN(exactSearchCmp, searchAlbumCmp, allWebSearchCmp)),
        WHEN(outSideSearchInterveneCmp).ignoreError(true).maxWaitSeconds(2),
        outSideResultAggregationCmp,
        outSidePostHandleCmp
        );
    </chain>
</flow>