#生产环境
#authenticate.account.url=https://itoken.uc.oppomobile.com/token/auth
#authenticate.account.appKey=SmQtq2QCcA6zyxvcrXwH6B
#authenticate.account.secretKey=X2m8JeAn01FZVT46jg0l8Q==

#测试环境
authenticate.account.url=https://uc-tokengateway-test.wanyol.com/token/auth
# mock接口
# authenticate.account.url=http://10.177.102.21:48813/token/auth
authenticate.account.appKey=8Tr9F7CYQMyp8FPdYd4DSh
authenticate.account.secretKey=ma1/xKMEFruGq1fG3oRL2g==

authenticate.account.timeout=1000

# 分级token申请的<包名，appKey>map
account.pkgAppKey.map={"com.coloros.yoli":"ad991c15a8884cc482f57a3b87f59b7c","com.heytap.yoli":"e674e550d3b24cc18557fd650a532ec4"}
# 分级token申请的<包名，appSecret>map
account.pkgAppSecret.map={"com.coloros.yoli":"a184e2104bd742fa8613732e70084479","com.heytap.yoli":"46525dadc72c47a7b199304062c20aa6"}