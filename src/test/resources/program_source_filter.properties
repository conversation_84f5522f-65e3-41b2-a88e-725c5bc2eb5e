# 统一控制优酷节目是否下发开关 0：关闭，过滤优酷节目 1：打开，正常下发
vod.youkumobile.filter.switch=1
# 控制低版本过滤优酷还是下发兜底h5升级页开关， 0：过滤优酷节目 1：下发H5升级页
vod.youkumobile.lowerVersion.filter.switch=0
# 三方接口场景开关， 0：过滤优酷节目 1：下发H5升级页
third.party.youkumobile.filter.switch=1
upgrade.page.url=https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_versio_altgradigon/index.html?immersive=7&sid=%s&contentProvider=%s&sidName=%s
# 优酷引入版本，7.16.0格式
vod.youkumobile.appVersion=7.16.0
# 优酷引入版本，71600格式
vod.youkumobile.version=71600
# 支持跳转web快应用的最低快应用版本号
vod.min.webQuickVersion=90200
# cp方与接入版本的对照表
source.version.mapping={"huashi":41900,"senyu":41900,"sohu":52600,"mgmobile":50000,"letv":60300,"yst":60800,"youkumobile":71600,"weidiou": 80100, "funshion_lv": 80100}
# 兜底合作方列表
backup.source.list=["huashi", "senyu", "mgmobile"]

# 三方卡片appId-source映射配置
third.card.sourceApp.cpSource.codeMap={"video":["sohu","huashi","senyu","mgmobile","letv","yst","weidiou","funshion_lv"], "youkumobile":["youkumobile"]}
#新风行源支持的最低版本号
vod.newFunshion.version=8.1.0
special.source.list=["youkumobile", "ztv","miguolympic"]
source.quickEngine.version.mapping={"youkumobile":90200}