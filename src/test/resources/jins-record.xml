<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans.xsd">
  

    <bean id="CONSUME_FROM_LAST_OFFSET" class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">
        <property name="staticField" value="org.apache.rocketmq.common.consumer.ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET"/>
    </bean>


    <bean id="standardAlbum_update_snapshot" class="com.heytap.longvideo.search.mq.JinsRecordConsumerService">
        <property name="tableSet">
            <set>
               <value>standard_album</value>
                <value>standard_video</value>
                <value>mis_album_org</value>
                <value>mis_video_org</value>
            </set>
        </property>
        <property name="tablePrimaryKeyMap">
            <map>
                <entry key="standard_album" value="sid"/>
            </map>
        </property>
    </bean>


    <bean id="standardAlbumDefaultMQPushConsumer" class="org.apache.rocketmq.client.consumer.DefaultMQPushConsumer"
          init-method="start" destroy-method="shutdown">

        <property name="consumeFromWhere" ref="CONSUME_FROM_LAST_OFFSET"/>
        <property name="namesrvAddr" value="${rocketmq.name-server.mediaJins}"/>
        <property name="subscription">
            <map>
                <entry key="longvideo_media_change_20220321" value="*"/>
            </map>
        </property>
        <property name="consumerGroup" value="standardAlbum_update_snapshot_consumerGroup"/>
        <property name="messageListener" ref="standardAlbum_update_snapshot"></property>
    </bean>
  
  
  
  <bean id="longvideo_cms_update_snapshot" class="com.heytap.longvideo.search.mq.JinsRecordConsumerService">
        <property name="tableSet">
            <set>
            </set>
        </property>
         <property name="excludeTableSet">
            <set>
              	 <value>mtv_copyright</value>
                 <value>mtv_layout</value>
            </set>
        </property>
        <property name="tablePrimaryKeyMap">
            <map>
            </map>
        </property>
    </bean>


    <bean id="longvideoCmsDefaultMQPushConsumer" class="org.apache.rocketmq.client.consumer.DefaultMQPushConsumer"
          init-method="start" destroy-method="shutdown">

        <property name="consumeFromWhere" ref="CONSUME_FROM_LAST_OFFSET"/>
        <property name="namesrvAddr" value="${rocketmq.name-server.mediaJins}"/>
        <property name="subscription">
            <map>
                <entry key="longvideo_cms_change_jins" value="*"/>
            </map>
        </property>
        <property name="consumerGroup" value="longvideo_cms_update_snapshot_consumerGroup"/>
        <property name="messageListener" ref="longvideo_cms_update_snapshot"></property>
    </bean>


</beans>