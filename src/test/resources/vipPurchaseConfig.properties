#会员一期重构开始版本
purchasePage.default.startVersion=60300

# 购买页可用支付渠道
purchasePage.payChannels=wxpay,alipay,douyinpay,unionpay_uac
# 购买页默认支付渠道
purchasePage.default.payChannel=alipay
# 购买页默认支付按钮文案
purchasePage.default.buttonText=确认协议并支付
# 购买页默认选中的tab会员类型
purchasePage.default.vipTabType=mongo_video_vip

# 购买页默认头像
purchasePage.default.userLogo=https://longvideo.heytapimage.com/20230919150249023.png
# 购买页右上角菜单
purchasePage.menuList=[{"title":"兑换会员","needLogin":1,"linkType":"deeplink","linkValue":"ucvip://vip.yoli.com/vipMain?html=https%3A%2F%2Fuc-h5-test.wanyol.com%2Fvip%2Fvip_exchangeMember.html%3FisGradualToolbar%3Dfalse%26isTranslucentBar%3Dfalse%26isHideToolbar%3Dfalse"},{"title":"会员订单","needLogin":1,"linkType":"deeplink","linkValue":"yoli://yoli.com/yoli/h5?url=https%3A%2F%2Fdhfs-test-cpc.wanyol.com%2Fuserfiles%2Fcms%2Fvideo_member_page%2ForderList.html%3FhideCloseBtn%3D1"},{"title":"客服中心","needLogin":1,"linkType":"deeplink","linkValue":"ucvip://vip.yoli.com/vipMain?html=https%3A%2F%2Fuc-h5-test.wanyol.com%2Fvip%2Fvip_commonProblem.html%3FisGradualToolbar%3Dfalse%26isTranslucentBar%3Dfalse%26isHideToolbar%3Dfalse"}]
# 购买页会员权益更多页面
purchasePage.rights.moreMap={"video_vip":{"needLogin":1,"linkType":"deeplink","linkValue":"ucvip://vip.yoli.com/vipMain?html=https%3A%2F%2Fuc-h5-test.wanyol.com%2Fvip%2Fvideo_equityExplain.html%3FisGradualToolbar%3Dtrue%26isTranslucentBar%3Dtrue%26isHideToolbar%3Dfalse%26equityType%3DAdvertisementEquity"},"mongo_video_vip":{"needLogin":1,"linkType":"deeplink","linkValue":"ucvip://vip.yoli.com/vipMain?html=https%3A%2F%2Fuc-h5-test.wanyol.com%2Fvip%2Fvideo_equityExplain.html%3FisGradualToolbar%3Dtrue%26isTranslucentBar%3Dtrue%26isHideToolbar%3Dfalse%26vipType%3Dmongo_video_vip%26equityType%3DAdvertisementEquity"}}

# 订单列表页右上角菜单--新
orderList.menuList=[{"title":"会员续费管理","needLogin":1,"linkType":"deeplink","linkValue":"yoli://yoli.com/yoli/h5?url=https%3A%2F%2Fdhfs-test-cpc.wanyol.com%2Fuserfiles%2Fcms%2Fvideo_member_page%2Frenewal.html%3FhideCloseBtn%3D1%26hideTopbar%3D1%26immersive%3D2"}]

# 会员个人信息铭牌展示
# VIP未开通
purchasePage.vip.nameplate.offMap={"video_vip":"https://longvideo.heytapimage.com/20230113171006081.png","mongo_video_vip":"https://longvideo.heytapimage.com/20230113170926649.png"}
# VIP已开通
purchasePage.vip.nameplate.onMap={"video_vip":"https://longvideo.heytapimage.com/20230113171014852.png","mongo_video_vip":"https://longvideo.heytapimage.com/20230113170934995.png"}

# 会员个人vip信息展示文案
# 从未开通VIP
purchasePage.vip.neverOpenMap={"video_vip":"开通影视VIP，享海量大片&极致视听","mongo_video_vip":"开通芒果专区VIP，畅看芒果TV爆火综艺"}
# VIP未过期
purchasePage.vip.inEffectMap={"video_vip":"影视VIP会员%s到期","mongo_video_vip":"芒果专区VIP会员%s到期"}
# VIP已过期
purchasePage.vip.expiredMap={"video_vip":"影视VIP会员已过期%s天","mongo_video_vip":"芒果专区VIP会员已过期%s天"}

# W8.2 新UI会员到期提示文案 模板
purchasePage.vip.nameMap={"video_vip":"影视","mongo_video_vip":"芒果"}
#从未开通任一VIP，或过期1年。过期1年也算从未开通
purchasePage.vip.neverOpenMap.new={"video_vip":"开通 VIP 享海量影视资源","mongo_video_vip":"暂未开通 VIP"}
purchasePage.vip.unactivated.new=暂未开通%s VIP
purchasePage.vip.inEffect.new=%s专区 VIP %s 到期
purchasePage.vip.expired.new=%s专区 VIP 已过期 %s 天

# 购买页H5地址
purchasePage.urlMap={"video_vip":"https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_member_page/index.html?vipTabType=video_vip&hideCloseBtn=1&hideTopbar=1","mongo_video_vip":"https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_member_page/index.html?vipTabType=mongo_video_vip&hideCloseBtn=1&hideTopbar=1"}

# 非续费类商品的planCode(自定义天数的商品[非年卡、季卡、月卡]都是MONTHLY)
purchasePage.nonContinuousProduct.planCodeMap={"372":"YEAR","365":"YEAR","184":"MONTHLY","93":"QUARTER","92":"QUARTER","31":"MONTHLY","15":"MONTHLY","7":"MONTHLY","3":"MONTHLY","1":"MONTHLY"}

#需要替换的会员权益channelId
purchasePage.vip.rightsChannelMap={"57666184162576384":"video_vip","62458657966141440":"mongo_video_vip"}

# 下单接口兜底source
purchasePage.submitOrder.defaultSource=unknown

# 会员客服链接
vip.customerService=ucvip://vip.yoli.com/vipMain?html=https%3A%2F%2Fvip.heytap.com%2Fvip%2Fvip_commonProblem.html%3FisGradualToolbar%3Dfalse%26isTranslucentBar%3Dfalse%26isHideToolbar%3Dfalse

# 入场弹窗的条件之一：优惠价格大于等于x元
vip.dialog.entrance.price.difference=4.5

# 失效芒果商品id替换
vip.mongo.expiredProduct.replace={"97601":"104737","97602":"104738","97603":"104739","97604":"96194"}
# 失效芒果商品id替换,新旧替换方式开关 true:新方式--后台配置映射关系,false:旧方式--读取配置中心映射
vip.mongo.product.replace.modeSwitch=true

# 购买页链接更换域名的版本
purchasePage.newDomain.version=60700
# 购买页H5新地址
purchasePage.newUrlMap={"video_vip":"https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_member_page/index.html?vipTabType=video_vip&hideCloseBtn=1&hideTopbar=1&displayType=0","mongo_video_vip":"https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_member_page/index.html?vipTabType=mongo_video_vip&hideCloseBtn=1&hideTopbar=1&displayType=0"}

# 支付apk支持自动续费的开始版本（320），实际是213,但是213太久远，支付无法确定版本名，故使用3.2.0
purchasePage.auto.renew.pay.startVersion=30200

# 7.0版本开始 会员相关文案拆分配置
vip.newCopy.version=70000
# 媒资类型与会员类型的对应关系
mediaSource.vipType.map={"mgmobile":"mongo_video_vip","funshion":"video_vip","huashi":"video_vip","senyu":"video_vip","letv":"video_vip","yst":"video_vip","weidiou":"video_vip","funshion_lv":"video_vip"}

#source为特定字段则跳过验证直接展示匹配的内容
purchasePage.validateSkip.source=browserhomepage

#默认支付渠道前置功能开关，0后置, 1前置
purchasePage.default.forwardSwitch=1
# 权益页面按钮显示文案
vipRights.openText.template=开通{}VIP
vipRights.renewText.template=续费{}VIP
vipRights.videoVip.text=影视
vipRights.mongoVideoVip.text=芒果专区
#购买页最近购买轮播展示条数
purchasePage.recentOrder.size.map={"video_vip":10,"mongo_video_vip":15}
#购买页最近购买轮播默认开关，1为出现，0为隐藏
purchasePage.default.recentOrderSwitch=1

#商品类型支持的支付渠道（续费类型:0不续费,1连续包月,2连续包季,3连续包年）
product.payChannelRule=[{"vipType":"mongo_video_vip","channelRule":[{"renewalType":0,"supportedChannels":"douyinpay,wxpay,alipay,unionpay_uac"},{"renewalType":1,"supportedChannels":"alipay,wxpay"},{"renewalType":2,"supportedChannels":"douyinpay,alipay,wxpay"},{"renewalType":3,"supportedChannels":"unionpay_uac"}]},{"vipType":"video_vip","channelRule":[{"renewalType":0,"supportedChannels":"alipay,wxpay,douyinpay,unionpay_uac"},{"renewalType":1,"supportedChannels":"alipay,wxpay"},{"renewalType":2,"supportedChannels":"alipay,wxpay"},{"renewalType":3,"supportedChannels":"alipay,wxpay"}]}]
 #  会员购买页的模块样式：0：收起样式，1：全部展开，2：展开2个
vip.payment.page.moduleStyle=2

#个人中心--我的tab--体育会员配置
personCenter.miGuVip.vipName=体育会员
personCenter.miGuVip.personCenter=最全体育内容
personCenter.miGuVip.ipIcon=https://credit-jfimage-cn.heytapimage.com/member/1658978485038.png
personCenter.miGuVip.vipPurchasePageUrl=https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_member_page/index.html?vipTabType=mongo_video_vip&hideCloseBtn=1&hideTopbar=1

# S22指定活动策略id下的商品,本来是签约商品,但不走签约逻辑
activity.orderSource.noSign=VipActivity71,VipActivity72,VipActivity73,VipActivity74,VipActivity75

# S26黑产订单,退单退款理由
refund.order.reason=participated too many times

# S29 会员业务方下发批次管理相关配置
vip.BusinessBatchInfo.ttl.seconds=60
vip.BusinessBatchRemind.ttl.seconds=30
vip.BusinessBatchRemind.remind.part1={\"tag\":\"at\",\"userId\":\"all\",\"text\":\"@all\"}
vip.BusinessBatchRemind.remind.part2={\"tag\":\"text\",\"text\":\"\\n视频业务接口会员库存警告\\n批次名称：%s\\n有效期：%s\\n当前库存：%s \\n告警原因：库存不足1/10\\n\"}
vip.BusinessBatchRemind.remind.part3={\"tag\":\"link\",\"text\":\"立即查看\",\"url\":\"%s\",\"urlPc\":\"%s\",\"urlMb\":\"%s\"}
#业务方下发会员批次 库存不足1/10时,消息提醒群组人员，超时时间【毫秒】
vip.stock.remind.ttl=2000
vip.stock.remind.tt.seconds=2000
#注意：测试、生产环境地址不同
vip.stock.remind.url=http://admin-portal.browsertest-admin.wanyol.com/?businessType=longvideo&appFrom=90&ticket=ST-4590894-uemHkbuFGVYKWxAc5iaW-SIAM#/longvideo/member_management/redeemcode/manage