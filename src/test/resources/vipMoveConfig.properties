# ------ 开关切换 ------
#activity下单、vip-rest赠送接口：切换为调用视频自己接口开关. true:调视频,false:调主题
vip.open.entrance.switch=true
# 签约会员自动续费 获取订单信息数据--迁移后 视频的新url 对应旧配置：vipSignOrder.url
vipSignOrder.new.url=https://vod-mobile-test.wanyol.com/v1/vip/api/customize-order/submit
# 赠送会员接口-迁移后 视频的新url 对应旧配置：vip.innerAdd.url
vip.innerAdd.new.url=https://vod-mobile-test.wanyol.com/v1/vip/api/order/v1.0/inner-add
#会员退款接口-迁移后 视频的新url 对应旧配置：vip.refund.url
vip.refund.new.url=https://vod-mobile-test.wanyol.com/v1/vip/api/customize-order/revoke
#用户白名单 activity下单、vip-rest赠送接口：切换为调用视频自己接口 白名单用户id。注意：白名单不受开关影响
vip.open.Whitelist=["666666","888888"]
#续费定时任务开发,true：可执行（根据vip.open.entrance.switch=true执行全部，=false执行白名单用户），false：不执行
vip.auto.renew.switch=true

# ------ 下单相关配置 ------
#自定义签约订单消息队列通知tag
customize.sign.tag=customize.sign.content
#支付成功订单消息队列通知tag
customize.order.tag=customize.order.content
# 风控限制:单设备最大购买用户数
riskControlRestrictions=5
# imei风控限制缓存时间
riskControlExpireTime=2678400
# 配置白名单:配置购买方案的金额，方便测试时,减少支付金额
vip.buy.plan.imei.switch=true
vip.buy.plan.imei.config={"whiteImeiList":["201701149968554"],"buyPlanCodeList":[{"buyPlan":"MONTH","amount":1},{"buyPlan":"QUARTER","amount":2},{"buyPlan":"YEAR","amount":3},{"buyPlan": "XUNYOU_UNION_QUARTER","amount": 4}]}
#下单购买会员有效期时长限制
vip.expiration.date.limit=2038-01-01
#当前正在使用的自动续费编码和购买方案对应关系【旧】
renew.product.current.config=[{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0013","vipType":"video_vip","signingPay":true},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0014","vipType":"video_vip","signingPay":true},{"planCode":"CONTINUOUS_YEAR","renewProductCode":"**********0015","vipType":"video_vip","signingPay":true},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0019","vipType":"mongo_video_vip","signingPay":true},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0020","vipType":"mongo_video_vip","signingPay":true},{"planCode":"CONTINUOUS_YEAR","renewProductCode":"**********0018","vipType":"mongo_video_vip","signingPay":true}]
#订单未支付过期时间
order.expire.time=600
#支付成功通知url
pay.result.notify.url=https://vod-mobile-test.wanyol.com/v1/vip/api/order/pay-result-notify
#支付来源 填写应用名称即可 原来：vip-business
pay.source.app.name=oppomobile-vip-rest
#渠道id
pay.channel.id=445761021
#支付类型，固定为1
pay.mtype=2
#自动续费类型标识，固定为1；1：标识签约并支付，  2：标识只签约
pay.auto.renewal.mauto.renew=1
#签约结果回调通知url
pay.auto.renewal.sign.agreement.notify.url=https://vod-mobile-test.wanyol.com/v1/vip/api/order/signing-result-notify
# 请求下单RSA privateKey  下单发起支付 key
pay.confirm.order.private.key.video_vip=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMb1IWwHZly1EffMahFNAT/UA92DwbPobUWkZZRe2DBkI0dWCxRjMX+eQTwFHWbMROpi8/uZbAG1xe0HwP43WABSapnFYqFMrWnmaEsLlL1eueB2yj0MfDJouhrFb3ft0l1W/8Gnffj9C1DKoZqCbRTrA9hIyZLOwE+hVbEaQEbpAgMBAAECgYEAuj/p3L6Epc7gFjBnLvD+dWkdgTYBlFDGUoaWjTHIgmyL1hEHaDt/r+9PNEe5gW1CKoUdz++M+4DV25IBqjnXBzTEMWepXMlMja0jJtyIGMZOzLUCiM3/lBbAPUl9T9yAFQgBBED3Uc1Iqu95D2w+5/+ghfqKbhMdhnnzY/EtowECQQD9R/OtXtoXwbTgcC/Eox3+IOkuqA14pLCCs4YrW7XJCKTYuY8b83EF9K/jc+6XJMVwmrnvQdmuMfqPncbSjbJJAkEAyRfkDhSb51thqYTwkhx5m8KPOTcQDOplnEA0mOXh0t6aVjMEwgv/sLWg9w0pkydHPB5DAspSO/OVuB5MGCnvoQJAN+A5t9OnFXqcZq8ZOq4pnbiw+KQGZMkgT5U/Ui1nSzRm+ZDmK2pnUsMKUolu51UrYY4g2tGSLkXEWw6ZoDW2UQJAIGiLRoGnxpeBm9Ji+wk/yAyJDI2FNq+oBJCMaX/S7ysU8IN0ZtmMMaxz7pzecGrKHjQ6VH7K1gKhxHSNiu9nQQJAGsoRL7JIh/zNwpmgM4Z56c9hu1DXVGDxv3HPSY8iBCNcXcY7deXyxvCFle175dhjNHo88RqFWhHrsX7oLfAWew==
# 下单发起支付 key
pay.confirm.order.private.key.mongo_video_vip=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMb1IWwHZly1EffMahFNAT/UA92DwbPobUWkZZRe2DBkI0dWCxRjMX+eQTwFHWbMROpi8/uZbAG1xe0HwP43WABSapnFYqFMrWnmaEsLlL1eueB2yj0MfDJouhrFb3ft0l1W/8Gnffj9C1DKoZqCbRTrA9hIyZLOwE+hVbEaQEbpAgMBAAECgYEAuj/p3L6Epc7gFjBnLvD+dWkdgTYBlFDGUoaWjTHIgmyL1hEHaDt/r+9PNEe5gW1CKoUdz++M+4DV25IBqjnXBzTEMWepXMlMja0jJtyIGMZOzLUCiM3/lBbAPUl9T9yAFQgBBED3Uc1Iqu95D2w+5/+ghfqKbhMdhnnzY/EtowECQQD9R/OtXtoXwbTgcC/Eox3+IOkuqA14pLCCs4YrW7XJCKTYuY8b83EF9K/jc+6XJMVwmrnvQdmuMfqPncbSjbJJAkEAyRfkDhSb51thqYTwkhx5m8KPOTcQDOplnEA0mOXh0t6aVjMEwgv/sLWg9w0pkydHPB5DAspSO/OVuB5MGCnvoQJAN+A5t9OnFXqcZq8ZOq4pnbiw+KQGZMkgT5U/Ui1nSzRm+ZDmK2pnUsMKUolu51UrYY4g2tGSLkXEWw6ZoDW2UQJAIGiLRoGnxpeBm9Ji+wk/yAyJDI2FNq+oBJCMaX/S7ysU8IN0ZtmMMaxz7pzecGrKHjQ6VH7K1gKhxHSNiu9nQQJAGsoRL7JIh/zNwpmgM4Z56c9hu1DXVGDxv3HPSY8iBCNcXcY7deXyxvCFle175dhjNHo88RqFWhHrsX7oLfAWew==
pay.pre.submit.url=https://pay-gateway.pay-test.wanyol.com/api/place-order/v2/pre-pay
pay.pre.submit.appKey=72724314
pay.pre.submit.secret=D9zRJdxxE2g37/ARUM0WJQ==
#修改签(解)约回调地址,调用支付的接口地址
update.signinfo.url=https://pay-gateway.pay-test.wanyol.com/api/autorenew/update-signinfo
#修改签(解)约回调地址,视频的回调地址
update.signinfo.content=https://vod-mobile-test.wanyol.com/api/order/signing-result-notify
#请求支付接口 获得预下单token 的开关
pre.pay.submit.switch=true
#会员迁移mq
vip.move.producer.groupName=vip-move-producer-group
#会员迁移mq
vip.move.producer.instanceName=vip-move-producer-instance
#mq最大重试次数
mq.topic.vip.unsign.repeat.consume.times.limit=10
#
customize.sign.notify.status.switch=true
#芒果会员 天数->产品id 映射配置
mongo.video.vip.product.id.config={1:"97601",3:"95631",7:"97602",15:"97603",31:"98836",93:"98839",186:"96196",372:"96197",92:"96195",184:"96196",365:"96197"}
#
notify.mongo.switch=true
#
mq.topic.independent.maxRetry=2
mq.consumer.group.name=vip_base_data_consumer_group
mq.consumer.offset=CONSUME_FROM_FIRST_OFFSET
mq.message.model=CLUSTERING
mq.consumer.max.thread=16
mq.consumer.min.thread=16
mq.pull.batch.size=32
mq.pull.threshold.queue=1000
rocketmq.file.download.nx.force.consume=false
# notify.topic=vip_base_data_notify_topic
notify.tag=v1
notify.max.try=2

#kms订单信息加解密key
kms.order.aes.key=eyJ2ZXJzaW9uIjowLCJhbGciOiJBRVMtMjU2LUdDTSIsInBhZGRpbmciOiJOb1BhZGRpbmciLCJpdiI6InJqYnVRUUFpNGZLdDhDL3kiLCJhYWQiOiIiLCJ0YWciOiJXME1vTlhpaDQ0TCtwU1lHbWNZRHF3PT0iLCJlbmNyeXB0ZWRfZGF0YSI6InBOY1lkZGozWHNld3JqY0ZFN2JXUitSUHJhbEV3bmVZIn0=
#芒果回调
notify.mongo.video.open.url=https://vod-mobile-test.wanyol.com/v1/vip/mgmobile/purchaseCallback?f=json
notify.mongo.video.revoke.url=https://vod-mobile-test.wanyol.com/v1/vip/mgmobile/refundCallback?f=json
#影视回调
notify.video.open.url=https://vod-mobile-test.wanyol.com/v1/vip/purchaseCallback?f=json
notify.video.revoke.url=https://vod-mobile-test.wanyol.com/v1/vip/refundCallback?f=json
notify.mongo.video.key=e7a529a2c0b144af98837
notify.video.key=e7a529a2c0b144af98837

# ------ 支付回调相关配置 ------
#交易结果通知回调 签名的 publicKey
pay.result.notify.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJOiGsoifR0qAwpb72gbbDonYgJ973LBOzSa+SGccbl9Hyv/7Rnkoet015dieP5lTHbQiUcWrX3DVhLUM+9q8loTYETVvBjYi+fDtOIbUUdmaObCKmdHl1SSZlMHVGkbQ8yys8bqkw0DbBQuqN6WdYexcyFfrh1EvDol0c9o1l/wIDAQAB
#交易结果通知回调开关:true 拒绝请求,false 接收请求
pay.result.sign.check.switch=false
#增加会员时长开关,开关打开,即 add.vip.time.switch=true,才允许增加会员时长,默认为true
add.vip.time.switch=true
#刷新会员时,延时删除缓存时间
independent.vip.refresh.delay.time=500

#业务方自定义订单支付、退款相关通知
customize.order.biz.source=[{"appKey":"HWzvHQiPJStXLJbb5RFAye","bizSource":"VIDEO","signSuccessInvokeUrl":"https://vod-mobile-test.wanyol.com/activity-rest/v1/activity/signOrderCallback","secret":"e7a529a2c0b144af98837","invokeAppKey":"","paySuccessInvokeUrl":"https://vod-mobile-test.wanyol.com/activity-rest/v1/activity/payCallback","revokeSuccessInvokeUrl":"","unsignInvokeUrl":"https://vod-mobile-test.wanyol.com/activity-rest/v1/activity/removeOrderCallback"}]


# ------ 签(解)约回调相关配置 ------
# 签约结果通知回调开关:true 拒绝请求,false 接收请求
pay.sign.result.sign.check.switch=false
#检验是否为需要屏蔽的会员,避免迁移后展示别的会员数据
vip.shield.config.map={"heytap_game_vip_plus":"入口升级，请到“游戏中心-我的-畅玩卡”进行查看","heytap_game_vip_lite":"入口升级，请到“游戏中心-我的-畅玩卡”进行查看","heytap_book_vip":"会员升级，请到“书城APP-我的-VIP”进行查看","mini_game_vip":"会员已下架"}
#独立会员 自动发送短信提醒方式，在增加vip时长后，到期开始扣费之前发送短信提醒的天数
independent.vip.auto.renew.before.sendmessage.days.after.add.vip.time=5
#现金支付 查询订单状态HMAC key
pay.query.order.status.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQFKibfCTgQFgWduEqF6juXOSPixPijyOJ0cC7BVT7p59H1FeDSvaYg21y/wNWtcG/UnKpHYl6MmXQH+yFHPuFTqeobg9gTJkW78ZiUH4eJ9US9ebRmXcvszFVyLAuvFfQBsZt8Zwx80ouNBHd6KSIzDFW3K6jWE8ajqQQpSKTSwIDAQAB
#请求支付 续约地址
pay.autonew.pay.url=http://pay.pay-test.wanyol.com/plugin/autorenew/autorenewpay
#请求支付 解约url
pay.unsign.url=http://pay.pay-test.wanyol.com/plugin/autorenew/unsign
#pay.unsign.url=http://*************:48813/plugin/autorenew/unsign
#MO流程中会员签约解约配置
vip.biz.audit.mo.config.SIGNING_RECORD={"formId":"BO_EU_TERMINATIONOFCONTRACT","processDefinitionId":"obj_9915f3872c0f4ffdbfdbeb56d2681707","taskDefinitionId":"obj_c91acad68ad000017239e06a1cc012c1","titleFormat":"欢太会员解约申请-{0}-{1}-{2}","vipBizAuditorConfigs":[{"auditors":"W9010866","business":"VIDEO","vipTypes":["mongo_video_vip","video_vip"]}]}



# ------ 签约列表查询相关配置 ------
#测试token
vip.token.test={"TOKEN_eyJhbGciOiJFQ0RTQSIsInYiOiIxIn0.***************************************************************************************************************************************************************************************************************************************.MEQCIGnQrc_E4A3c19WPt1tbqjzIvL5-Jp6frMEkKUcwGy-5AiBtZBxRljJ6maXzGN6KiupFN5ynyn2cHJcQdRZsdPE5dg":"**********"}
#用户token接口域名,测试环境:无,生产：http://itoken.uc.oppo.local
account.token.host=https://uc-accountgateway-test.wanyol.com
#用户信息接口appKey
account.appKey=4m3nSMLCvSd4ppr5GyYtz2
#用户信息接口secret
account.secret=uirZN6qV9dO18W9R+kKtmA==
#用户接口连接超时时间
account.connection.timeout=1000
#用户接口读超时时间
account.read.timeout=2000


# ------ 退单相关配置 ------
#MO流程中会员订单退单配置
vip.biz.audit.mo.config.VIP_ORDER={"formId":"BO_EU_CHARGEBACK","processDefinitionId":"obj_d1736a9a73b742c48019e73e2b968344","taskDefinitionId":"obj_c91ac89230c0000147f01a8247a013ce","titleFormat":"欢太会员退单申请-{0}-{1}-{2}","vipBizAuditorConfigs":[{"auditors":"W9012042","business":"VIDEO","vipTypes":["mongo_video_vip","video_vip"]}]}
#现金支付 退款url（是否可删除）
pay.refund.order.url=http://pay.pay-test.wanyol.com/plugin/post/refund
#现金支付 新退款url
new.pay.refund.order.url=http://pay-gateway.pay-test.wanyol.com/api/refund/auto-refund
#退款结果通知回调 url
pay.refund.notify.url=http://vod-mobile-test.wanyol.com/v1/vip/api/order/refund-result-notify
#现金支付 商户code partnerCode 开发者id
pay.partner.code=**********
#现金支付 新退款签名 key
new.pay.refund.order.key=b4977e747c9370d0cbf2e900054b3a8t
#退款结果通知回调开关
refund.result.sign.check.switch=true
#现金支付 查询订单状态HMAC key
pay.query.order.status.key.video_vip=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQFKibfCTgQFgWduEqF6juXOSPixPijyOJ0cC7BVT7p59H1FeDSvaYg21y/wNWtcG/UnKpHYl6MmXQH+yFHPuFTqeobg9gTJkW78ZiUH4eJ9US9ebRmXcvszFVyLAuvFfQBsZt8Zwx80ouNBHd6KSIzDFW3K6jWE8ajqQQpSKTSwIDAQAB
pay.query.order.status.key.mongo_video_vip=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQFKibfCTgQFgWduEqF6juXOSPixPijyOJ0cC7BVT7p59H1FeDSvaYg21y/wNWtcG/UnKpHYl6MmXQH+yFHPuFTqeobg9gTJkW78ZiUH4eJ9US9ebRmXcvszFVyLAuvFfQBsZt8Zwx80ouNBHd6KSIzDFW3K6jWE8ajqQQpSKTSwIDAQAB
#现金支付 查询订单状态url 对接支付
pay.query.order.status.url=http://pay.pay-test.wanyol.com/plugin/post/orderquery

# -----------续约-----------
#独立会员自动续费 时间间隙
independent.vip.auto.renew.day.gap=1
#各个独立会员自动续费 时间间隙 , 生产 {"mongo_video_vip":0}
independent.vip.auto.renew.day.gap.map={"theme_vip":1}
#发送自动续费短信距离发起自动续费的天数
independent.vip.auto.renew.before.sendmessage.days=32
#自动续费，每天扣费的次数
auto.renew.timertask.day.limit.alipay=1000
auto.renew.timertask.day.limit.wxpay=1000
#自动续费配置
pay.renew.config=
[{"vipType":"mongo_video_vip","planCode":"CONTINUOUS_MONTHLY","switchStatus":"ENABLE","signEndTime":1641052800000,"settleId":"95012","renewAmount":"15","startTime":1641052800000,"endTime":1672156800000},{"vipType":"mongo_video_vip","planCode":"CONTINUOUS_QUARTER","switchStatus":"ENABLE","signEndTime":1638374400000,"settleId":"95015","renewAmount":"48","startTime":1640620800000,"endTime":1672156800000},{"vipType":"mongo_video_vip","planCode":"CONTINUOUS_YEAR","switchStatus":"ENABLE","signEndTime":1641052800000,"settleId":"95018","renewAmount":"178","startTime":1638374400000,"endTime":1640620800000},{"vipType":"mongo_video_vip","planCode":"CONTINUOUS_MONTHLY","switchStatus":"ENABLE","signStartTime":1641052800000,"signEndTime":1659542400000,"settleId":"96181","renewAmount":"19","startTime":1659542400000,"endTime":1659945000000},{"vipType":"mongo_video_vip","planCode":"CONTINUOUS_QUARTER","switchStatus":"ENABLE","signStartTime":1641052800000,"signEndTime":1659542400000,"settleId":"96183","renewAmount":"58","startTime":1659542400000,"endTime":1672156800000}]

# -----------短信-----------
#退款成功短信是否启用配置
sms.switch.config.REFUND_SUCCESS={"enable":true,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
#扣费前短信是否启用配置
sms.switch.config.BUCKLE_FEE_BEFORE={"enable":true,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
#扣费前提醒短信配置
sms.content.buckle_fee_before.mongo_video_vip=续期订阅提醒：您的视频-%s将于%s以%s元/%s的价格自动扣款，扣款成功后VIP自动续期至%s。如您于扣款日前取消订阅，上述扣款将不会发生。温馨提醒：您可通过“视频App>芒果专区VIP购买页>VIP订单>自动续费订单”进行解约，解约成功后将以短信形式通知您，敬请留意！
#影视会员扣款前短信
sms.content.buckle_fee_before.video_vip=续期订阅提醒：您的%s将于%s以%s元/%s的价格自动扣款，扣款成功后VIP自动续期至%s。如您于扣款日前取消订阅，上述扣款将不会发生。温馨提醒：您可通过“视频App>VIP购买页>VIP订单>自动续费订单”进行解约，解约成功后将以短信形式通知您，敬请留意！  
#退款成功短信配置
sms.content.refund_success.mongo_video_vip=您的视频-%s退费已完成，退款金额将通过您支付该笔订单的支付方式原路返回，请注意查收！与此同时，该笔订单对应的VIP时长和权益将被收回，期待您下次加入%s，祝您生活愉快！
sms.content.refund_success.video_vip=您的%s退费已完成，退款金额将通过您支付该笔订单的支付方式原路返回，请注意查收！与此同时，该笔订单对应的VIP时长和权益将被收回，期待您下次加入%s，祝您生活愉快！
#续费成功短信配置
sms.switch.config.RENEW={"enable":false,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
sms.content.renew.video_vip=%s续费成功，可继续享VIP免费看、抢先看、跳广告等专属特权，VIP有效期至%s年%s月%s日，进入“视频”App可查看和使用VIP权益。
sms.content.renew.mongo_video_vip=%s续费成功，可继续享王牌综艺免费看、更新剧集抢先看、跳广告、蓝光画质等特权，会员有效期至%s年%s月%s日，进入“视频”App可查看和使用会员权益。
#免费赠送（结算金额为0）短信是否启用配置
sms.switch.config.EXPERIENCE_OPEN={"enable":false,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
sms.content.experience_open.video_vip=恭喜你成为%s，可享VIP免费看、抢先看、跳广告等专属特权，VIP有效期至%s年%s月%s日，进入“视频”App可查看和使用VIP权益。
sms.content.experience_open.mongo_video_vip=恭喜你成为%s，可享王牌综艺免费看、更新剧集抢先看、跳广告、蓝光画质等特权，会员有效期至%s年%s月%s日，进入“视频”App可查看和使用会员权益
# 非会员开通（结算金额不为0，时长大于等于31天）
sms.switch.config.NOT_VIP_OPEN={"enable":false,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
sms.content.not_vip_open.video_vip=恭喜你成为%s，可享VIP免费看、抢先看、跳广告等专属特权，VIP有效期至%s年%s月%s日，进入“视频”App可查看和使用VIP权益。
sms.content.not_vip_open.mongo_video_vip=恭喜你成为视频-%s，可享王牌综艺免费看、更新剧集抢先看、跳广告、蓝光画质等特权，VIP有效期至%s年%s月%s日，进入“视频”App可查看和使用VIP权益。
# 签约成功短信
sms.switch.config.SIGN_SUCCESS={"enable":false,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
sms.content.sign_success.video_vip=您的%s自动扣费服务已开启，VIP到期前1天，将以%s元/%s进行扣费，续费金额以实际扣费金额为准。如需关闭服务，可前往“视频App>VIP购买页>VIP订单>自动续费订单”管理扣费项目。
sms.content.sign_success.mongo_video_vip=您的%s自动扣费服务已开启，会员到期当天，将以%s元/%s进行扣费，续费金额以实际扣费金额为准。如需关闭服务，可前往视“视频App>芒果会员购买页>会员订单>自动续费订单”管理扣费项目。
# 解约成功短信 
sms.switch.config.SIGN_REVOKED_SUCCESS={"enable":true,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
# 视频无解约通知
sms.content.sign_revoked_success.video_vip=您的%s已成功解约自动续费并取消订阅，下个扣款日起将不会发起相应扣款，VIP到期后，您的VIP权益将不可使用。期待您下次加入%s，祝您生活愉快！
sms.content.sign_revoked_success.mongo_video_vip=您的视频-%s已成功解约自动续费并取消订阅，下个扣款日起将不会发起相应扣款，VIP到期后，您的VIP权益将不可使用。期待您下次加入%s，祝您生活愉快！
#被动扣款成功
sms.switch.config.PASSIVE_BUCKLE_FEE_SUCCESS={"enable":true,"video_vip":["OPPO","realme","OnePlus"],"mongo_video_vip":["OPPO","realme","OnePlus"]}
sms.content.passive_buckle_fee_success.mongo_video_vip=您的视频-%s自动扣费服务已完成扣费%s元，并延长%s天的会员时长。如需关闭服务，可前往视“视频App>芒果专区VIP购买页>VIP订单>自动续费订单”管理扣费项目。
sms.content.passive_buckle_fee_success.video_vip=您的%s自动扣费服务已完成扣费%s元，并延长%s天的VIP时长。如需关闭服务，可前往“视频App>VIP购买页>VIP订单>自动续费订单”管理扣费项目。

#-------先知数据上报管理
#是否开启异步上报
report.switch.async=YES

# -----------其它-----------
#雪花算法
vip.dubbo.address=dgzx-theme-test-zk-cluster.yyy62a.zookeeper.oppo.test:2181,dgzx-theme-test-zk-cluster.yyye76.zookeeper.oppo.test:2181,dgzx-theme-test-zk-cluster.yyyez9.zookeeper.oppo.test:2181
datacenter.id=2
#雪花算法zk上的父节点名称，子节点数不超过15个，超过则更改父节点名称
vip.rights.snow.parentNode=vip-base-rights-snow-mirror

#通知mq消费超次数提醒配置
notify.tt.pattern.bo={"contentPattern":"notifyId:%s requestId:%s requestDetail:%s","level":"CRITICAL","module":"vip-base-date-service","receivers":["W9019714","W9025243"],"title":"【notify告警test1】","type":"ALARM"}
notify.tt.enable=true
#回调失败数据查询最大数量
notify.fail.list.max.size=10000

#签约商品配置
renew.product.all.config=[{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0001","vipType":"video_vip"},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0013","vipType":"video_vip"},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0002","vipType":"video_vip"},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0014","vipType":"video_vip"},{"planCode":"CONTINUOUS_YEAR","renewProductCode":"**********0003","vipType":"video_vip"},{"planCode":"CONTINUOUS_YEAR","renewProductCode":"**********0015","vipType":"video_vip"},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0010","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0011","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_YEAR","renewProductCode":"**********0012","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0016","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0017","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_YEAR","renewProductCode":"**********0018","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0019","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0021","vipType":"mongo_video_vip"},{"planCode":"CONTINUOUS_MONTHLY","renewProductCode":"**********0022","vipType":"video_vip"},{"planCode":"CONTINUOUS_QUARTER","renewProductCode":"**********0020","vipType":"mongo_video_vip"}]

#通知http超时时间
http.connect.timeout=2000
http.timeout=5000

#短信配置
send.by.mobile.url=http://oppo-sms-cn-test.wanyol.com/gate/api/single_sms
send.by.userId.url=http://oppo-sms-cn-test.wanyol.com/gate/api/ssoid_sms
send.message.api.id=f430a6bb7
send.message.api.key=722ec63deed6c4826b9cc407665031d3
send.message.agent.id=155
send.message.shop.id=177121
send.message.retry.times=2

#kms秘钥配置
kms.ak=AK068E192D03001356
kms.sk=cf4fbe47406365b9b74a272a2408d0b4eebceadfda989d72b237635c828f732d
kms.key.id=67bbd498-b49a-48bf-94fb-241c54467974
kms.url=http://kms-test.wanyol.com
kms.maximum.size=10
kms.expire.time=10000000
kms.user.aes.key=eyJ2ZXJzaW9uIjowLCJhbGciOiJBRVMtMjU2LUdDTSIsInBhZGRpbmciOiJOb1BhZGRpbmciLCJpdiI6IlNtaWIxSDcxTVcwbkkrT2siLCJhYWQiOiIiLCJ0YWciOiJqeXQ4OFNZTGhDNnBaT2dKOVc4NkhnPT0iLCJlbmNyeXB0ZWRfZGF0YSI6ImZCZnFZbkh0K3JJTG1yak1XSUt6STVaS3QrM2dDY1NQIn0=

#订单校验
check.order.connect.maxTotal=20
check.order.socket.timeout=6000
check.order.connect.timeout=1000

#前端自助退款最大金额限制，订单金额需<=30 才能退款，单位元，可小数
auto.robot.revoke.maxAmount=30
#90天内无会员订单退款记录，才能退单
auto.robot.revoke.days.limit=90
#自助机器人是否开启建议退款金额 建议：消耗率小于预设值 0.5,则全款退，大于等于0.5 则按残值退款
auto.robot.suggested.refund.switch=false
#自助机器人退款-订单消耗时长比例 0.5
suggested.refund.order.time.rate=0.5