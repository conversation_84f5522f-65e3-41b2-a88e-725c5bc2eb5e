scene.show.brand={"browserResultCard":1,"qsResultCard":1}

ocs.AccessKeyId=0_oTdIEqmquAWljZ1nJtUAIr1U3wsi0jfoI-lDpt
ocs.AccessKeySecret=LTdgWEW0dMUZu7-IkkV55XgMimaqrmTVeZAAQr9e
ocs.EndPoint=s3v2.dg-access-test.wanyol.com
ocs.Region=cn-south-2
ocs.BucketName=iot-long-video-original-hn2
ocs.OcsPath=upLoadTest
ocs.LocalPath=./

unofficial.album.program.detail.blackTags=doc_人物,doc_军事,doc_历史

# 快应用版本限制
taskUnlockEpisode.version=7.12.0

# 电视增量/全量爬虫相关配置
tv.media.spider.batch.size=100
tv.media.spider.url=http://**************:9667/video/douban/crawler/searchNameForDouBanDelayExecute?u-s303-name=QgghhJwsNHYJIYQQQsjJQmOXEEIIIYScLDR2CSGEEELIyUJjlxBCCCGEnCwiVBORw0KGgoAAAANSUhEUgAAArsAAAKkCAYAAAAA4fMAAAgAElEQVR4nOydPYgr2ZnptDJssA04WFhwsUgdNDwOeYJdS8MZSJ2H7bCzEhtJDjq7YWcdrBQtUnbD6cB7k&pw-2001=fepXPrrrwEAPrRjDLX5yaD8Y9xL8y7gz3MwDgn7pnDzn8LlW8KIxvfneDEEIIIeQ4qXUZQlzvn85x3XffbX99913W0NXDEsIIYQQQkgIajV2PvsMVv3bWjIJ2h3jP1ohDSFvVcibYVUSmK5mGAnnqqc1QrT2UvrqFmN8vKbWUoneXxR7NA9YkUs1Elz

vip.brand.priority={"video_vip":["huashi","letv","senyu","yst","mgmobile","funshion_lv","weidiou","sohu","youkumobile"],"mongo_video_vip":["mgmobile","huashi","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"],"video_vip,mongo_video_vip":["huashi","mgmobile","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"],"mongo_video_vip,video_vip": ["huashi","mgmobile","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"],"default":["huashi","mgmobile","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"]}

# 全网图片上传配置
unofficial.album.uploadPic.url=http://cpc-admin-api.test.wanyol.com/uploads
unofficial.album.uploadPic.serverId=browser_bos
# 图片服务器读超时时间
unofficial.album.uploadPic.readTimeOut=30000
# 图片服务器连接超时时间
unofficial.album.uploadPic.connectTimeOut=30000
# 连接池最大并发连接数
unofficial.album.uploadPic.connect.maxTotal=20
# 访问域外机器得到图片流
unofficial.album.uploadPic.outside.requestUrl=http://**************:9667/video/image/convert?imageUrl=%s&u-s303-name=QgghhJwsNHYJIYQQQsjJQmOXEEIIIYScLDR2CSGEEELIyUJjlxBCCCGEnCwiVBORw0KGgoAAAANSUhEUgAAArsAAAKkCAYAAAAA4fMAAAgAElEQVR4nOydPYgr2ZnptDJssA04WFhwsUgdNDwOeYJdS8MZSJ2H7bCzEhtJDjq7YWcdrBQtUnbD6cB7k&pw-2001=fepXPrrrwEAPrRjDLX5yaD8Y9xL8y7gz3MwDgn7pnDzn8LlW8KIxvfneDEEIIIeQ4qXUZQlzvn85x3XffbX99913W0NXDEsIIYQQQkgIajV2PvsMVv3bWjIJ2h3jP1ohDSFvVcibYVUSmK5mGAnnqqc1QrT2UvrqFmN8vKbWUoneXxR7NA9YkUs1Elz
# 是否域外机器得到图片流，是：域外机器，否：实例机器
unofficial.album.uploadPic.userOutSide.send=false
# 全网节目--标准化时，是否生存ocs图片
unofficial.album.mq.standard.generate.ocs.image=false
# 生成ocs图片的source白名单
unofficial.album.ocs.image.whitelist=tencent,iqiyi,youku,douban,keke
# 全网节目--生成ocs图片的并发量
unofficial.album.generate.ocs.image.concurrent.numbers=20
# 全网节目--查询es(未生成ocs图片),一次返回的数据量
unofficial.album.query.es.batch.size=20
# 全网节目--实例机器获取图片流的source
unofficial.album.uploadPic.inSide.machine.sourceList=iqiyi,tencent,youku,iqiyimobile
# 全网节目--代理机器获取图片流的source
unofficial.album.uploadPic.outSide.machine.sourceList=douban,keke
