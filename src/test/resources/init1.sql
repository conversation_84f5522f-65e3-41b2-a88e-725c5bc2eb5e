create database oppo_media;

use longvideo_media0;
CREATE TABLE `standard_album0` (
  `sid` varchar(64) NOT NULL COMMENT '节目唯一id',
    `high_light_vid` varchar(32) DEFAULT NULL COMMENT '最新高能视频ID',
    `title` varchar(128) NOT NULL DEFAULT '' COMMENT '节目标题',
    `sub_title` varchar(512) NOT NULL DEFAULT '' COMMENT '节目副标题',
    `manager_status` int(11) NOT NULL DEFAULT '0' COMMENT '媒资后台:0:自动更新,1:锁定视频matadata,2:锁定节目, >2 部分字段锁定',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '下发时以这个字段值为准判断状态:-2:黑名单,-1:删除,0:失效,1:生效,2:系统失效,3:合并失效,4:探测失效,5:源下线,6:注入失效',
    `source_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '节目源状态，0：不可用 1：可用',
    `verify_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '牌照方审核状态，0:审核不通过，1审核通过，与source_status整合',
    `source` varchar(16) DEFAULT NULL,
    `source_album_id` varchar(64) NOT NULL DEFAULT '' COMMENT '合作方专辑ID',
    `source_web_url` varchar(512) NOT NULL DEFAULT '' COMMENT '源站的URL地址',
    `source_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '入库原因:2：腾讯注入;3：人工新增; 5:人工复制;7：优酷注入',
    `source_score` varchar(16) NOT NULL DEFAULT '0' COMMENT '源站评分 如 优酷评分，腾讯评分',
    `cp_score` varchar(16) DEFAULT NULL COMMENT '合作方评分，后台应用使用，和source_score冗余使用',
    `program_type` varchar(16) NOT NULL DEFAULT '' COMMENT '节目类型',
    `sub_program_type` varchar(16) NOT NULL DEFAULT '' COMMENT '子节目类型',
    `unit` tinyint(4) NOT NULL DEFAULT '0' COMMENT '剧头单位 如，期 部 话等 0：默认 ，1：集，2：期，3:话，4：番',
    `category` varchar(512) NOT NULL DEFAULT '' COMMENT '专辑分类信息（用于分类标签）',
    `duration` int(11) NOT NULL DEFAULT '0' COMMENT '时长',
    `director` varchar(256) NOT NULL DEFAULT '' COMMENT '导演（或者主持人）',
    `actor` varchar(1024) NOT NULL DEFAULT '' COMMENT '演员（或者嘉宾）',
    `year` int(11) NOT NULL DEFAULT '0' COMMENT '年代',
    `area` varchar(32) NOT NULL DEFAULT '' COMMENT '地区',
    `tags` varchar(512) NOT NULL DEFAULT '' COMMENT '标签列表',
    `language` varchar(128) NOT NULL DEFAULT '' COMMENT '语言',
    `honor` varchar(1024) NOT NULL DEFAULT '' COMMENT '获奖信息',
    `information` text COMMENT '简介信息',
    `valid_episode` int(11) NOT NULL DEFAULT '0' COMMENT '有效剧集数',
    `total_episode` int(11) NOT NULL DEFAULT '0' COMMENT '总集数',
    `completed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已完结 0：未完结 1 已完结',
    `brief` varchar(128) NOT NULL DEFAULT '' COMMENT '简短推荐语',
    `period` varchar(128) NOT NULL DEFAULT '' COMMENT '更新周期',
    `vertical_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '海报小竖图',
    `vertical_icon_ocs` varchar(512) DEFAULT NULL COMMENT '海报小竖图OCS地址',
    `horizontal_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '海报小横图',
    `horizontal_icon_ocs` varchar(512) DEFAULT NULL COMMENT '海报小横图OCS地址',
    `vertical_image` varchar(512) NOT NULL DEFAULT '' COMMENT '海报大竖图',
    `vertical_image_ocs` varchar(512) DEFAULT NULL COMMENT '海报大竖图OCS地址',
    `horizontal_image` varchar(512) NOT NULL DEFAULT '' COMMENT '海报大横图',
    `horizontal_image_ocs` varchar(512) DEFAULT NULL COMMENT '海报大横图OCS地址',
    `back_ground_color` varchar(32) DEFAULT NULL COMMENT '竖图图片背景色值',
    `back_ground_color_two` varchar(32) DEFAULT NULL COMMENT '竖图图片背景色值 H S(55%) B(33%)',
    `back_ground_color_json` varchar(512) DEFAULT NULL COMMENT '图片背景色值',
    `feature_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1：正片，          \r\n	2：预告片，3：微电影，4，精彩看点',
    `risk_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '高危标识,0-否，1-是',
    `copyright` tinyint(1) NOT NULL DEFAULT '1' COMMENT '版权标识,0-否，1-是',
    `copyright_code` varchar(32) NOT NULL DEFAULT '' COMMENT '版权方标识',
    `program_info` varchar(64) NOT NULL DEFAULT '' COMMENT '剧集更新至期数信息',
    `medium` int(11) NOT NULL DEFAULT '1' COMMENT '1:PC,2:mobile',
    `pay_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:免费，1：会员免费，2：单片付费',
    `vip_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-所有人能看         \r\n	1-会员能看',
    `charge_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '单片付费类型: 0-非单片付费，1-单片付费',
    `price` int(11) NOT NULL DEFAULT '0' COMMENT '价格,可能有多个价格，需要扩展字段',
    `keyword` varchar(1024) NOT NULL DEFAULT '' COMMENT '关键词列表(一般用于搜索),搜索用,其他字段组合而来,看是否删掉',
    `extra_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NOT NULL DEFAULT '' COMMENT '扩展信息',
    `source_play_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '原始播放量',
    `source_hot` double(10,0) NOT NULL DEFAULT '0' COMMENT '剧头热度（目前仅TV端发送热度数据到搜索时使用）',
    `supply_type` varchar(32) NOT NULL DEFAULT 'normal' COMMENT '供应类型:special-特供，sole独播,normal-普通',
    `definition` varchar(1028) NOT NULL DEFAULT '' COMMENT '清晰度',
    `download_markcode` varchar(50) NOT NULL DEFAULT '' COMMENT '下载图标',
    `download_able` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1=不可下载,0=无限制,1=会员下载,2=单点下载',
    `mark_code` varchar(50) NOT NULL DEFAULT '' COMMENT '会员角标',
    `free_start_time` datetime DEFAULT NULL COMMENT '限免开始时间',
    `free_end_time` datetime DEFAULT NULL COMMENT '限免结束时间',
    `end_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '直播开始时间',
    `start_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '直播开始时间',
    `pay_effect_days` int(11) NOT NULL DEFAULT '0' COMMENT '付费观看有效天数',
    `vip_price` decimal(6,2) NOT NULL DEFAULT '0.00' COMMENT '单买节目VIP价格',
    `now_price` decimal(6,2) NOT NULL DEFAULT '0.00' COMMENT '单买节目现价格',
    `former_price` decimal(6,2) NOT NULL DEFAULT '0.00' COMMENT '单买节目原始价格',
    `show_time` varchar(32) NOT NULL DEFAULT '' COMMENT '上映时间',
    `process_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '标准化处理状态',
    `publish_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间/入库时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近一次更新时间',
    `source_series_id` varchar(512) NOT NULL DEFAULT '',
    `prePush` int(11) NOT NULL DEFAULT '0' COMMENT '是否预推 0：否  1：是',
    `preOnlineTime` datetime DEFAULT NULL COMMENT '预推上线时间',
    `online_time` datetime DEFAULT NULL COMMENT '节目上线时间',
    `previewInfo` text COMMENT '预告片关系json',
    `mapping_tags` varchar(512) NOT NULL DEFAULT '' COMMENT '映射标签',
    `tuputag` varchar(200) DEFAULT '' COMMENT '图谱tag',
    `competition_id` varchar(64) DEFAULT NULL COMMENT '赛事id',
    `competition_name` varchar(128) DEFAULT NULL COMMENT '赛事名称',
    `season_id` varchar(64) DEFAULT NULL COMMENT '赛季id',
    `season_name` varchar(128) DEFAULT NULL COMMENT '赛季名称',
    `participant_type` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '参赛方 0-人物，1-队伍，3-ufc，4-团体 5-双打',
    `competition_type` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '赛事类型 0:非对抗赛 1:对抗赛',
    `phase` varchar(32) DEFAULT NULL COMMENT '阶段',
    `match_group` varchar(32) DEFAULT NULL COMMENT '小组',
    `match_start_time` datetime DEFAULT NULL COMMENT '节目开始时间（不同于赛事开始时间）',
    `round` varchar(32) DEFAULT NULL COMMENT '轮次',
    `participant_countrys` varchar(256) DEFAULT NULL COMMENT '参赛国家列表，逗号隔开',
    `sport_name` varchar(128) DEFAULT NULL COMMENT '大项名称（奥运赛事用）',
    `event_name` varchar(128) DEFAULT NULL COMMENT '小项名称（奥运赛事用）',
    `presenter` mediumtext COMMENT '多解说类型节目解说员信息, json',
    `fit_age_min` tinyint(3) DEFAULT NULL COMMENT '最小适龄',
    `fit_age_max` tinyint(3) DEFAULT NULL COMMENT '最大适龄',
    `fit_age` varchar(255) DEFAULT NULL COMMENT 'cp方适龄原始内容',
    `douban_score` varchar(16) DEFAULT '-1.0' COMMENT '豆瓣评分',
    `oppo_score` varchar(16) DEFAULT '-1.0' COMMENT 'oppo评分，通过豆瓣评分计算',
    PRIMARY KEY (`sid`) USING BTREE,
    UNIQUE KEY `index_source_album_id` (`source_album_id`) USING BTREE,
    KEY `index_title` (`title`) USING BTREE,
    KEY `index_update_time` (`update_time`) USING BTREE,
    KEY `index_process_status` (`process_status`) USING BTREE,
    KEY `idx_prePush` (`prePush`),
    KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='标准化剧头表';

INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000132162693017600', NULL, '七次机会', '七个机会|七个偶然', 0, 6, 0, 0, 'mgmobile', 'l6qYNN.7.f4dd828ee5055f9d', 'https://m.mgtv.com/h/456988.html', 2, '9.5', '8.0', 'movie', 'movie', 1, '电影', 3402, '巴斯特·基顿', '巴斯特·基顿|T.罗伊·巴尔内斯|辛兹·爱德华|露丝·德怀尔|琪恩·亚瑟', 1925, '美国', '喜剧|爱情', '英语', '', '1895年出生的巴斯特·基顿，从小受到身为戏剧演员的父母的影响，从4岁开始就登台演出。基顿21岁进入电影界，很快就以冷峻的表情以及敏捷夸张的肢体语言形成了他独特的表演特点。1920年，基顿首次主演了剧情长片《苯人》，此后，他在好莱坞的发展一帆风顺，上世纪20年代，基顿总共出演了34部影片，1927年由他执导并主演的《将军号》奠定了他在好莱坞一代巨星的地位，而他也被后人视为动作喜剧片的开山鼻祖。', 0, 1, 0, '真人索尼克', '', 'http://3img.hitv.com/preview/internettv/sp_images/ott/2020/4/12/dianying/337777/20200412164216333-new.jpg', NULL, 'http://2img.hitv.com/preview/internettv/sp_images/ott/2020/4/12/dianying/337777/20200412164212886-new.jpg', NULL, 'http://3img.hitv.com/preview/internettv/sp_images/ott/2020/4/12/dianying/337777/20200412164216333-new.jpg', NULL, 'http://2img.hitv.com/preview/internettv/sp_images/ott/2020/4/12/dianying/337777/20200412164212886-new.jpg', NULL, NULL, NULL, '{\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000132162693017600_media_msn_0411vImg.png\",\"msnMaskLayerColor\":\"#85772EA5\",\"msnButtonColor\":\"#383214CC\"}', 0, 0, 1, 'mgmobile', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '', '', 0, '', NULL, NULL, '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '192503', 0, '2025-02-11 21:20:36', '2024-04-22 07:20:22', '2025-04-11 20:05:43', '[]', 0, NULL, NULL, NULL, '喜剧|爱情', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '8.9', '9.5');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000162440681533440', NULL, '大明王朝 第七季', '大明王朝(第七季）', 0, 6, 0, 0, 'mgmobile', 'qlOOE7.7.e809c178169e0aac', 'https://m.mgtv.com/h/642210.html', 2, '8.0', '8.0', 'comic', 'comic', 1, '动漫', 42431, '未知', '', 2009, '内地', '剧情', '普通话', '', '通过生动的动画学习大明王朝历史。', 0, 60, 1, '一起学习大明王朝的历史故事', '', 'http://3img.hitv.com/preview/sp_images/2024/04/18/202404182005563863148.jpg', NULL, 'http://0img.hitv.com/preview/sp_images/2024/04/18/202404182005445342034.jpg', NULL, 'http://3img.hitv.com/preview/sp_images/2024/04/18/202404182005563863148.jpg', NULL, 'http://0img.hitv.com/preview/sp_images/2024/04/18/202404182005445342034.jpg', NULL, NULL, NULL, '{\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000162440681533440_media_msn_0411vImg.png\",\"msnMaskLayerColor\":\"#854A2EA5\",\"msnButtonColor\":\"#382014CC\"}', 0, 0, 1, 'mgmobile', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '', '', 0, '', NULL, NULL, '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '200901', 0, '2025-02-14 15:35:46', '2024-04-22 09:20:42', '2025-04-11 20:05:43', '[]', 0, NULL, NULL, NULL, '其它', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 15, 18, '15-18岁', '-1.0', '-1.0');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000164956173414400', NULL, '船来船往', '', 0, 6, 0, 0, 'mgmobile', 'qlEyEO.7.ccfc72dc8b7c3b7e', 'https://m.mgtv.com/h/641312.html', 2, '7.0', '7.0', 'tv', 'tv', 1, '电视剧', 126021, '连春利', '吴孟达|张立威|王子子|刘威', 2011, '内地', '喜剧|都市', '普通话', '', ' 《船来船往》是一部以金厦等地的“小三通”为时代背景，以直航码头边的私家旅馆“老榕树客栈”为主题情景空间，以客栈主人和形形色色不同身份的房客为主要人物关系，讲述了两岸人民同根同源，血肉相亲的动人故事。', 0, 46, 1, '航码头私家旅馆“老榕树客栈”', '', 'http://2img.hitv.com/preview/sp_images/2021/04/13/20210413223935098.jpg', NULL, 'http://1img.hitv.com/preview/sp_images/2022/07/14/202207141025421065397.jpg', NULL, 'http://2img.hitv.com/preview/sp_images/2021/04/13/20210413223935098.jpg', NULL, 'http://1img.hitv.com/preview/sp_images/2022/07/14/202207141025421065397.jpg', NULL, NULL, NULL, '{\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000164956173414400_media_msn_0411vImg.png\",\"msnMaskLayerColor\":\"#852E2EA5\",\"msnButtonColor\":\"#381414CC\"}', 0, 0, 1, 'mgmobile', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '', '', 0, '', NULL, NULL, '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '201101', 0, '2025-04-04 20:05:05', '2024-04-22 09:30:42', '2025-04-11 20:05:42', '[]', 0, NULL, NULL, NULL, '喜剧|都市', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '-1.0', '-1.0');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000164956328603648', NULL, '蕾女心经', '', 0, 6, 0, 0, 'mgmobile', 'qlEla6.7.a88509847d44b8c7', 'https://m.mgtv.com/h/641475.html', 2, '7.0', '7.0', 'tv', 'tv', 1, '电视剧', 39702, '叶静|黄小蕾', '黄小蕾|叶静|王子子|刘一莹|刘亭作|高健', 2014, '内地', '喜剧|都市', '普通话', '', '该剧大牌云集，笑料不断，正所谓“蕾女不坏，强男不爱”，这部群星爆笑女性喜剧定会让你整个春日阳光明媚、心情美丽。', 0, 30, 0, '群星爆笑女性喜剧', '', 'http://1img.hitv.com/preview/internettv/sp_images/ott/2016/dianshiju/100895/20160531164743250-new.jpg', NULL, 'http://1img.hitv.com/preview/internettv/sp_images/ott/2016/dianshiju/100895/20161007161930395-new.jpg', NULL, 'http://1img.hitv.com/preview/internettv/sp_images/ott/2016/dianshiju/100895/20160531164743250-new.jpg', NULL, 'http://1img.hitv.com/preview/internettv/sp_images/ott/2016/dianshiju/100895/20161007161930395-new.jpg', NULL, NULL, NULL, '{\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000164956328603648_media_msn_0411vImg.png\",\"msnMaskLayerColor\":\"#852E60A5\",\"msnButtonColor\":\"#381429CC\"}', 0, 0, 1, 'mgmobile', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '', '', 0, '', NULL, NULL, '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '201403', 0, '2025-03-04 12:05:08', '2024-04-22 09:30:42', '2025-04-11 20:05:43', '[]', 0, NULL, NULL, NULL, '喜剧|都市', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '-1.0', '-1.0');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000176254852444160', NULL, '裴总每天都想父凭子贵', '', 0, 6, 0, 0, 'mgmobile', 'qllyya.7.e773fd1cf7f28b73', 'https://m.mgtv.com/h/644337.html', 2, '8.3', '8.0', 'tv', 'tv', 1, '电视剧', 0, '范博洋', '许梦圆', 2024, '内地', '网剧|都市|爱情', '普通话', '', '被鞋店店长逼婚，林家家闪婚嫁给了一个二婚带娃的单亲爸爸。本以为婚后的日子平淡如水，没想到二婚奶爸居然是全城首富！林家家：你还有什么事瞒着我！老公抱着孩子：这对龙凤胎，也是你亲生的……', 0, 99, 0, '闪婚单亲奶爸竟是全城首富', '5月6日11:30一次性上线全99集，非会员可看至11集，会员可看至33集，单点用户可看全集。', 'http://2img.hitv.com/preview/sp_images/2024/04/22/202404221008143375992.jpg', 'https://dhfs-test-cpc.wanyol.com/1000176254852444160_0506vIcon.jpeg', 'http://2img.hitv.com/preview/sp_images/2024/04/22/202404221008246981422.jpg', 'https://dhfs-test-cpc.wanyol.com/1000176254852444160_0506hIcon.jpeg', 'http://2img.hitv.com/preview/sp_images/2024/04/22/202404221008143375992.jpg', 'https://dhfs-test-cpc.wanyol.com/1000176254852444160_0506vImg.jpeg', 'http://2img.hitv.com/preview/sp_images/2024/04/22/202404221008246981422.jpg', 'https://dhfs-test-cpc.wanyol.com/1000176254852444160_0506hImg.jpeg', '#D6B4B0', '#804640', '{\"v_color_s66_b60\":\"#993E34\",\"fanZaiColor\":\"\",\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000176254852444160_media_msn_0415vImg.png\",\"msnMaskLayerColor\":\"#853E2EA5\",\"msnButtonColor\":\"#381B14CC\"}', 1, 0, 1, 'mgmobile', '更新至 33 集', 2, 1, 1, 0, 0, '', '', 0, 0, 'normal', '[]', '', 0, 'mgmobile_vip_1', NULL, NULL, '1970-01-01 00:00:00', '2024-05-06 11:25:42', 0, 0.00, 0.00, 0.00, '202405', 0, '2025-03-07 00:11:34', '2024-04-22 10:15:45', '2025-04-15 20:17:49', '[]', 0, NULL, NULL, NULL, '都市|爱情', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '6.7', '8.3');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000248093431738368', NULL, '再见不归路', '', 0, 6, 0, 0, 'mgmobile', 'qyqlay.7.64aaf91c97249eb5', 'https://m.mgtv.com/h/636473.html', 2, '7.0', '7.0', 'movie', 'movie', 1, '电影', 5117, '邓增辉', '潘思文|梁虹', 2021, '内地', '惊悚|悬疑|恐怖', '普通话', '', '影片主人公归可馨遭遇车祸受伤昏迷入院治疗，弥留之际经历了各种离奇事件后发现自己可能已经“身亡”，为了寻找真相跟随神秘护士再次踏入医院，最后重获新生。', 0, 1, 0, '昏迷后各种离奇事件', '', 'http://2img.hitv.com/preview/sp_images/2022/08/23/202208230920480853730.jpg', NULL, 'http://3img.hitv.com/preview/sp_images/2022/08/23/202208230920517769643.jpg', NULL, 'http://2img.hitv.com/preview/sp_images/2022/08/23/202208230920480853730.jpg', NULL, 'http://3img.hitv.com/preview/sp_images/2022/08/23/202208230920517769643.jpg', NULL, NULL, NULL, '{\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000248093431738368_media_msn_0411vImg.png\",\"msnMaskLayerColor\":\"#852E42A5\",\"msnButtonColor\":\"#38141CCC\"}', 0, 0, 1, 'mgmobile', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '', '', 0, '', NULL, NULL, '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '202105', 0, '2025-02-11 20:13:05', '2024-04-22 15:01:05', '2025-04-11 20:05:45', '[]', 0, NULL, NULL, NULL, '恐怖', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '-1.0', '-1.0');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1000248094161547264', NULL, '狼灵传说', '', 0, 6, 0, 0, 'mgmobile', 'qyqlY6.7.6106f3d9a5f73d80', 'https://m.mgtv.com/h/636495.html', 2, '7.0', '7.0', 'movie', 'movie', 1, '电影', 4560, '徐书勤', '徐书勤|唐文婷', 2021, '内地', '剧情|悬疑|惊悚', '普通话', '', '孤儿阿洛在一次砍柴途中，救了兔仙一家。十年之后，大旱，庄稼颗粒无收，发小阿勇看见阿洛家门口蹲着一只，阿洛忆起救兔子时杀兔子让他把狼骨埋在家里，便能心想事成。从此阿洛便依靠此咒语帮助乡邻度过难关。阿洛有法术的事，引起了财主妒忌，绑架了阿勇娘，逼迫阿洛说出法术的秘密。但贪心的财主因贪婪作恶，变成了狗。', 0, 1, 0, '民间神话故事改编', '', 'http://4img.hitv.com/preview/sp_images/2024/03/21/202403211438088258362.jpg', NULL, 'http://1img.hitv.com/preview/sp_images/2024/03/21/202403211438283563226.jpg', NULL, 'http://4img.hitv.com/preview/sp_images/2024/03/21/202403211438088258362.jpg', NULL, 'http://1img.hitv.com/preview/sp_images/2024/03/21/202403211438283563226.jpg', NULL, NULL, NULL, '{\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1000248094161547264_media_msn_0411vImg.png\",\"msnMaskLayerColor\":\"#3D2E85A5\",\"msnButtonColor\":\"#1A1438CC\"}', 0, 0, 1, 'mgmobile', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '', '', 0, '', NULL, NULL, '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '202103', 0, '2025-02-11 22:39:38', '2024-04-22 15:01:05', '2025-04-11 20:05:45', '[]', 0, NULL, NULL, NULL, '剧情|恐怖', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '-1.0', '-1.0');


INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('506311924435210240', NULL, '法医狂妃', '', '0', '1', '1', '1', 'sohu', '9649121', 'http://tv.sohu.com/s2020/dhpfykf/', '2', '6.1', 'comic', 'comic', '1', '悬疑|恋爱|美少女', '0', '无', '', '2020', '内地', '悬疑|恋爱|美少女', '普通话', '', '讲述了21世纪女法医柳蔚一朝穿越，成京都丞相家的庶出大小姐，与王爷一夜邂逅逃走，在运用现代法医学解决一件件案件过程中，与王爷逐渐发展出一段甜蜜爱情。', '10', '10', '1', '穿越女法医邂逅俊王爷', '10集全', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20200525/vrsa_ver9649121.jpg', 'https://dhfs-test-cpc.wanyol.com/506311924435210240_vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20200525/vrsa_hor9649121.jpg', 'https://dhfs-test-cpc.wanyol.com/506311924435210240_hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20200525/vrsa_ver9649121.jpg', 'https://dhfs-test-cpc.wanyol.com/506311924435210240_vImg.jpeg', 'http://photocdn.tv.sohu.com/img/20200525/vrsa_hor9649121.jpg', 'https://dhfs-test-cpc.wanyol.com/506311924435210240_hImg.jpeg', '#D6C3B0', '#806040', '1', '0', '1', 'sohu', '全 10 集', '2', '0', '0', '0', '0', '', '', '664588', '0', 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', '1', '', '1970-01-01 08:00:00', '2023-01-13 15:02:56', '0', '0.00', '0.00', '0.00', '202001', '0', '2023-04-14 00:32:31', '2022-04-28 12:15:14', '2023-04-14 00:32:31', '[]', '0', NULL, NULL, '悬疑|恋爱|青春', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('506311950884491264', NULL, '启蒙积木玩具定格动画：雷霆使命联合行动', '', '0', '1', '1', '1', 'sohu', '9657408', 'http://tv.sohu.com/s2020/dhpqmjmwjdgdhltsmlhxd/', '2', '6.3', 'comic', 'comic', '1', '励志|益智|轻松|早教', '0', '陈宇飞', '', '2020', '内地', '励志|益智|轻松|早教', '普通话', '', '为了成为守护缤纷城的精英小队，金鹰特战队与白虎特战队展开了激烈的军事竞赛。在比赛的同时，缤纷城遭到一群坏人的破坏，于是两个特战队立即终止比赛。参与到抓捕坏人的行动中，经过努力，最终将其一网打尽。', '15', '15', '1', '特战队化敌为友抓捕坏人', '15集全', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20200703/vrsa_ver9657408.jpg', 'https://dhfs-test-cpc.wanyol.com/506311950884491264_vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20200703/vrsa_hor9657408.jpg', 'https://dhfs-test-cpc.wanyol.com/506311950884491264_hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20200703/vrsa_ver9657408.jpg', 'https://dhfs-test-cpc.wanyol.com/506311950884491264_vImg.jpeg', 'http://photocdn.tv.sohu.com/img/20200703/vrsa_hor9657408.jpg', 'https://dhfs-test-cpc.wanyol.com/506311950884491264_hImg.jpeg', '#CAB0D6', '#6C4080', '1', '0', '1', 'sohu', '全 15 集', '2', '0', '0', '0', '0', '', '', '1201724', '0', 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', '1', '', '1970-01-01 08:00:00', '2023-01-13 14:57:57', '0', '0.00', '0.00', '0.00', '202001', '0', '2023-04-14 00:32:28', '2022-04-28 12:15:47', '2023-04-14 00:32:28', '[]', '0', NULL, NULL, '励志|益智|搞笑|早教', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('506315702588755968', NULL, '可凡倾听', '可凡倾听', '0', '1', '1', '1', 'sohu', '1449', 'http://tv.sohu.com/xylkfqt/', '2', '6.3', 'show', 'show', '2', '访谈', '0', '', '曹可凡', '2022', '内地', '访谈', '', '1', '《可凡倾听》栏目是一档人物访谈类专题节目。以倾听智者声音为宗旨，立足抓取高端文化名人做嘉宾，通过口述实录，为已经蹚过历史长河的标志性人物留存系列档案，同时该栏目打的是主持人曹可凡的品牌。', '815', '0', '0', '倾听智者的声音', '每周六24:00更新', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20150309/vrsa_ver1449.jpg', 'https://dhfs-test-cpc.wanyol.com/506315702588755968_vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/tvmobile/20140414/13974396788831138.jpg', 'https://dhfs-test-cpc.wanyol.com/506315702588755968_hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20150309/vrsa_ver1449.jpg', 'https://dhfs-test-cpc.wanyol.com/506315702588755968_vImg.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/tvmobile/20140414/13974396788831138.jpg', 'https://dhfs-test-cpc.wanyol.com/506315702588755968_hIcon.jpeg', '#D6B6B0', '#804A40', '1', '0', '1', 'sohu', '12-31 期', '2', '0', '0', '0', '0', '', '', '218952', '0', 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', '1', '', '1970-01-01 08:00:00', '2023-01-13 14:53:35', '0', '0.00', '0.00', '0.00', '202201', '0', '2023-04-14 00:19:11', '2022-03-19 22:24:00', '2023-04-14 00:19:11', '[]', '0', NULL, NULL, '访谈', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('506319464187383808', NULL, '苍生大医', '', '0', '1', '1', '1', 'sohu', '9633004', 'http://tv.sohu.com/s2020/dsjcsdy/', '2', '6.0', 'tv', 'tv', '1', '古装|剧情', '0', '梁辛全', '何晟铭|李依晓|朱晓渔|李宗翰|李子雄|王建新|刘冠麟|王子', '2015', '内地', '古装|剧情', '普通话', '', '三国时期，战火纷起。年轻的华佗在家乡亳州给百姓看病，过着自由洒脱的日子。华佗妙手让太守女儿明心起死回生，获得了神医美名，也从此与明心结下不解之缘。为了研制麻沸散给明心治疗眼睛，华佗开始游历寻药。在都城，华佗与英雄关羽、奸雄曹操和师兄吴思德屡有交集，见识了官场险恶，世态炎凉。最让华佗痛心的是，心爱的姑娘明心惨遭吴思德霸占。在东吴山中，绝望的华佗受隐者指点，幡然醒悟，立志成为医人医心医国的苍生大医。赤壁之战，刮骨疗毒，秘境历险。任凭风吹雨打，从不曾改变拯救苍生的赤子之心。麻沸散的研制成功，也让华佗和明心的爱情修成正果，可就在幸福来临时，曹操因病、因疑、因恨抓走了华佗，这位行医济世、妙手回春的苍生大医逝于囚牢。', '42', '42', '1', '四阿哥何晟铭穿越变华佗', '42集全', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20200717/vrsa_ver_1594973384642_9633004.jpg', 'https://dhfs-test-cpc.wanyol.com/506319464187383808_vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20200717/vrsa_hor_1594973384579_9633004.jpg', 'https://dhfs-test-cpc.wanyol.com/506319464187383808_hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20200717/vrsa_ver_1594973384642_9633004.jpg', 'https://dhfs-test-cpc.wanyol.com/506319464187383808_vImg.jpeg', 'http://photocdn.tv.sohu.com/img/20200717/vrsa_hor_1594973384579_9633004.jpg', 'https://dhfs-test-cpc.wanyol.com/506319464187383808_hImg.jpeg', '#B0D3D6', '#407B80', '1', '0', '1', 'sohu', '全 42 集', '2', '0', '0', '0', '0', '', '', '12490487', '0', 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', '1', '', '1970-01-01 00:00:00', '2023-01-13 15:02:32', '0', '0.00', '0.00', '0.00', '201501', '0', '2023-04-14 01:53:20', '2022-06-07 18:32:05', '2023-04-14 01:53:20', '[\"8141\"]', '0', NULL, NULL, '古装|剧情', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('506333307999440896', NULL, '金牌调解', '金牌调解', '14040', '1', '1', '1', 'sohu', '1012085', 'http://tv.sohu.com/jptj/', '2', '7.4', 'show', 'show', '2', '情感交友', '0', '', '章亭', '2019', '内地', '情感交友', '普通话', '1', '邀请一对（或多个）有矛盾的当事人进入演播室，主持人和人民调解员现场为当事人排忧解难，通过节目告诉观众面对纠纷的智慧和解决矛盾的艺术，将真实事件和综艺手段完美交融，塑造全新节目模式。节目中将大力体现人文关怀和心理疏导，倡导文明积极、健康向上的社会风尚。', '2975', '0', '0', '平息家庭纷争建和谐社会', '每天22:00更新', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20160607/vrsa_ver1465287814691_1012085.jpg', 'https://dhfs-test-cpc.wanyol.com/506333307999440896_vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20160607/vrsa_hor1465287814105_1012085.jpg', 'https://dhfs-test-cpc.wanyol.com/506333307999440896_hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20160607/vrsa_ver1465287814691_1012085.jpg', 'https://dhfs-test-cpc.wanyol.com/506333307999440896_vImg.jpeg', 'http://photocdn.tv.sohu.com/img/20160607/vrsa_hor1465287814105_1012085.jpg', 'https://dhfs-test-cpc.wanyol.com/506333307999440896_hImg.jpeg', '#D6BCB0', '#805440', '1', '0', '1', 'sohu', '04-10 期', '2', '0', '0', '0', '0', '', '', '65081680', '0', 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', '1', '', '1970-01-01 08:00:00', '2023-04-13 23:02:58', '0', '0.00', '0.00', '0.00', '201901', '0', '2023-04-14 11:03:42', '2022-03-16 02:39:32', '2023-04-14 11:03:42', '[]', '0', NULL, NULL, '交友', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('506333325988810752', NULL, '选择', '选择', '9216', '1', '1', '1', 'sohu', '998075', 'http://tv.sohu.com/s2019/xz2019/', '2', '6.8', 'show', 'show', '2', '情感交友|生活', '0', '', '王芳|羽彤|韩晓燕', '2020', '内地', '情感交友|生活', '', '1', '一档带有娱乐元素的大型演播室节目，节目为需求者解决选择的困惑，为被选择者提供应选策略，展现不同人群选择或被选择的过程，带给观众人生的启迪。同时，节目将给需要的人创造更多的选择机会，而这种选择机会将给选择者和被选择者带来命运的转折。', '1035', '0', '0', '中老年人的婚恋观', '', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20190217/vrsa_ver998075.jpg', 'https://dhfs-test-cpc.wanyol.com/506333325988810752_vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20190217/vrsa_hor998075.jpg', 'https://dhfs-test-cpc.wanyol.com/506333325988810752_hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20190217/vrsa_ver998075.jpg', 'https://dhfs-test-cpc.wanyol.com/506333325988810752_vImg.jpeg', 'http://photocdn.tv.sohu.com/img/20190217/vrsa_hor998075.jpg', 'https://dhfs-test-cpc.wanyol.com/506333325988810752_hImg.jpeg', '#D6B0B0', '#804041', '1', '0', '1', 'sohu', '04-10 期', '2', '0', '0', '0', '0', '', '', '5241752', '0', 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', '1', '', '1970-01-01 08:00:00', '2023-04-13 22:25:41', '0', '0.00', '0.00', '0.00', '202001', '0', '2023-04-14 10:46:22', '2022-03-16 10:06:00', '2023-04-14 10:46:22', '[\"6528\"]', '0', NULL, NULL, '交友|生活', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('625229331555176448', NULL, '流浪地球：飞跃2020特别版', '20分钟加长版流浪地球', '0', '1', '1', '1', 'huashi', 'M05CW3480F', '', '2', '8.6', 'movie', 'movie', '1', '冒险|动作|灾难|科幻', '8072', '郭帆', '吴京|李光洁|吴孟达', '2020', '中国内地', '科幻|冒险|动作|灾难', '普通话', '', '《流浪地球：飞跃2020特别版》：近未来，科学家们发现太阳急速衰老膨胀，短时间内包括地球在内的整个太阳系都将被太阳所吞没。为了自救，人类提出一个名为“流浪地球”的大胆计划，即倾全球之力在地球表面建造上万座发动机和转向发动机，推动地球离开太阳系，用2500年的时间奔往另外一个栖息之地。中国航天员刘培强（吴京 饰）在儿子刘启四岁那年前往国际空间站，和国际同侪肩负起领航者的重任。转眼刘启（屈楚萧 饰）长大，他带着妹妹朵朵（赵今麦 饰）偷偷跑到地表，偷开外公韩子昂（吴孟达 饰）的运输车，结果不仅遭到逮捕，还遭遇了全球发动机停摆的事件。为了修好发动机，阻止地球坠入木星，全球开始展开饱和式营救，连刘启他们的车也被强征加入。在与时间赛跑的过程中，无数的人前仆后继，奋不顾身，只为延续百代子孙生存的希望', '1', '1', '1', '20分钟加长版流浪地球', '', 'http://img2.funshion.com/sdw?oid=2bc47897005a07ed8b931102438e0dc1&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625229331555176448_vImg.jpeg', 'http://img.funshion.com/sdw?oid=c902d19d0a6aaf4e24ff9c824d82f380&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625229331555176448_hImg.jpeg', 'http://img2.funshion.com/sdw?oid=2bc47897005a07ed8b931102438e0dc1&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625229331555176448_vImg.jpeg', 'http://img.funshion.com/sdw?oid=c902d19d0a6aaf4e24ff9c824d82f380&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625229331555176448_hImg.jpeg', '#B0C1D6', '#405D80', '1', '0', '1', 'huashi', '', '2', '1', '1', '0', '0', '', '123456', '0', '0', 'normal', '[{\"level\":2,\"payStatus\":1},{\"level\":3,\"payStatus\":1},{\"level\":4,\"payStatus\":1}]', '', '1', 'funshion_vip_1', '1970-01-01 08:00:00', '2022-07-21 10:48:27', '3', '2.00', '4.00', '8.00', '202011', '0', '2023-04-20 01:10:18', '2022-07-21 10:47:59', '2023-04-20 01:10:19', '[\"326\"]', '0', NULL, '[]', '科幻|动作|灾难', 15, 18, '15-18岁');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('654220125049921536', NULL, '流浪地球：飞跃2020特别版', '', '0', '1', '1', '1', 'mgmobile', 'y6OllN.7.e5412c0bf658c639', 'https://m.mgtv.com/h/352448.html', '2', '8.6', 'movie', 'movie', '1', '电影', '8188', '郭帆', '屈楚萧|吴京|李光洁|吴孟达|赵今麦', '2020', '内地', '科幻|灾难|冒险', '普通话', '', '该片根据刘慈欣同名小说改编，讲述了在不久后的未来，太阳即将毁灭，依靠太阳为生的地球也即将面临灭亡。面对绝境，人类拒绝坐以待毙，在地球表面建造出了巨大的推进器，将地球推出太阳系、为人类寻找新的家园，这一宏大的计划被称为“流浪地球”计划。《流浪地球：飞跃2020特别版》相较于原版增加全新12分钟内容，故事内容更多、人物角色更加丰满。', '1', '1', '1', '流浪地球特别加长版', '', 'http://0img.hitv.com/preview/sp_images/2020/11/24/20201124104017697.jpg', 'https://dhfs-test-cpc.wanyol.com/654220125049921536_vImg.png', 'http://3img.hitv.com/preview/sp_images/2020/11/24/20201124104304043.jpg', 'https://dhfs-test-cpc.wanyol.com/654220125049921536_hImg.png', 'http://0img.hitv.com/preview/sp_images/2020/11/24/20201124104017697.jpg', 'https://dhfs-test-cpc.wanyol.com/654220125049921536_vImg.png', 'http://3img.hitv.com/preview/sp_images/2020/11/24/20201124104304043.jpg', 'https://dhfs-test-cpc.wanyol.com/654220125049921536_hImg.png', '#BAB0D6', '#514080', '1', '0', '1', 'mgmobile', '', '2', '1', '1', '0', '0', '', '', '0', '0', 'normal', '[{\"level\":1,\"payStatus\":1},{\"level\":2,\"payStatus\":1},{\"level\":3,\"payStatus\":1},{\"level\":4,\"payStatus\":1}]', '', '1', 'mgmobile_vip_1', '1970-01-01 08:00:00', '1970-01-01 08:00:00', '0', '0.00', '0.00', '0.00', '202011', '0', '2023-04-12 19:26:12', '2022-04-13 15:11:09', '2023-04-20 01:10:19', '[]', '0', NULL, NULL, '科幻|灾难|动作', 15, 18, '15-18岁');
--for search tag
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `tuputag`) VALUES ('936393415476817920', '936393416261152768', '一杯上路', '意想不到的反转结局', 0, 1, 1, 1, 'huashi', 'M05CM92VVA', '', 2, '7.9', 'movie', 'movie', 1, '剧情|爱情', 7647, '纳塔吾·彭皮里亚', '塔纳波·里拉塔纳卡邹|纳塔拉·诺帕卢塔亚朋|茱蒂蒙·琼查容苏因|瓦尔蕾特·瓦帖儿|普洛伊·霍旺| 普洛伊·霍旺', 2022, '泰国,香港', '剧情|爱情', '泰语', '', '《一杯上路 原声版》：影片以亲情、友情、爱情为题，讲述一对好兄弟跨越14万公里，进行一场笑泪齐飞的另类告别之旅。身患绝症的阿德（纳塔拉·诺帕卢塔亚朋 饰）在人生尽头决定与旧时好友博（朋抔·里拉塔纳卡邹 饰）开展一场告别之旅，两人在倒计时的追逐下，横跨整个泰国，与阿德青春里错过的爱情和留有愧疚的女孩一一和解。在旅途尾声，阿德却道出了一个深藏多年的秘密，也唤起了博耿耿于怀的心事……', 1, 1, 1, '意想不到的反转结局', '', 'http://img.funshion.com/sdw?oid=774e171ff352029907178a87ce718677&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393415476817920_1029vIcon.png', 'http://img1.funshion.com/sdw?oid=9e207845768c037384349353ee8e51c6&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393415476817920_1029hIcon.png', 'http://img.funshion.com/sdw?oid=774e171ff352029907178a87ce718677&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393415476817920_1029vImg.png', 'http://img1.funshion.com/sdw?oid=9e207845768c037384349353ee8e51c6&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393415476817920_1029hImg.png', '#B0C5D6', '#406480', '{\"v_color_s66_b60\":\"#346D99\"}', 1, 0, 1, 'huashi', '', 2, 1, 1, 0, 0, '', '1054751', 0, 0, 'normal', '[{\"level\":2,\"payStatus\":1},{\"level\":3,\"payStatus\":1},{\"level\":4,\"payStatus\":1}]', '', 1, 'funshion_vip_1', '1970-01-01 00:00:00', '1970-01-01 00:00:00', 10000, 0.00, 6.00, 6.00, '202202', 0, '2023-10-30 14:18:00', '2023-10-29 10:05:21', '2023-10-30 14:18:00', '[\"952\"]', 0, NULL, '[]', '剧情|爱情', '');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `tuputag`) VALUES ('936015996626579456', NULL, '电影梦', '', 0, 1, 1, 1, 'sohu', '9671008', '', 2, '6.3', 'movie', 'movie', 1, '爱情片|喜剧片', 5612, '芦伟', '芦伟|傅晓莉|吴泳茵|刘俊杰|沈保平|童冬军|陈升卫', 2020, '内地', '爱情片|喜剧片', '普通话', '', '这是一部爱情喜剧励志类的一个题材，影片是以追梦拉开整个篇幅，本片介绍一位新时代年轻人 为追求自己人生梦，经历了不少乌龙事件的喜剧题材，最终实现自己梦想。电影里面的小人物从残酷的逆境中艰难地成长，从外到内地抵抗着命运的不公，不论最后有没有 成功逆袭，但这种对逆境的不顺服的精神却始终激励着一群群人，为他们点燃困境中的一盏明灯。 \r\n激发当代年轻人的奋斗精神，本片简洁明述，片政论观点，务实鲜明、深刻全面，在立场方面， 宣传正义、正气、正能量。\r\n', 1, 1, 1, '小伙追星误入香港黑帮', '1集全', 'http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20201020/vrsa_ver9671008.jpg', 'https://dhfs-test-cpc.wanyol.com/936015996626579456_1028vIcon.jpeg', 'http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20201020/vrsa_hor9671008.jpg', 'https://dhfs-test-cpc.wanyol.com/936015996626579456_1028hIcon.jpeg', 'http://photocdn.tv.sohu.com/img/20201020/vrsa_ver9671008.jpg', 'https://dhfs-test-cpc.wanyol.com/936015996626579456_1028vImg.jpeg', 'http://photocdn.tv.sohu.com/img/20201020/vrsa_hor9671008.jpg', 'https://dhfs-test-cpc.wanyol.com/936015996626579456_1028hImg.jpeg', '#B0CBD6', '#406D80', '{\"v_color_s66_b60\":\"#347C99\"}', 1, 0, 1, 'sohu', '', 2, 0, 0, 0, 0, '', '', 0, 0, 'normal', '[{\"level\":1,\"payStatus\":0},{\"level\":2,\"payStatus\":0},{\"level\":3,\"payStatus\":0}]', '', 1, '', '1970-01-01 00:00:00', '1970-01-01 00:00:00', 0, 0.00, 0.00, 0.00, '202001', 0, '2023-10-31 00:15:13', '2023-10-28 09:05:37', '2023-10-31 00:15:13', '', 0, NULL, NULL, '爱情|喜剧', '');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `tuputag`) VALUES ('936393417615912960', '936393418253447168', '一杯上路 普通话版', '好兄弟的另类告别之旅', 0, 1, 1, 1, 'huashi', 'M05CUGTUPB', '', 2, '8.4', 'movie', 'movie', 1, '剧情|爱情', 7649, '纳塔吾·彭皮里亚', '塔纳波·里拉塔纳卡邹|纳塔拉·诺帕卢塔亚朋|茱蒂蒙·琼查容苏因|瓦尔蕾特·瓦帖儿| 普洛伊·霍旺', 2023, '中国香港', '剧情|爱情', '普通话', '', '《一杯上路 普通话版》：影片以亲情、友情、爱情为题，讲述一对好兄弟跨越14万公里，进行一场笑泪齐飞的另类告别之旅。身患绝症的阿德（纳塔拉·诺帕卢塔亚朋 饰）在人生尽头决定与旧时好友博（朋抔·里拉塔纳卡邹 饰）开展一场告别之旅，两人在倒计时的追逐下，横跨整个泰国，与阿德青春里错过的爱情和留有愧疚的女孩一一和解。在旅途尾声，阿德却道出了一个深藏多年的秘密，也唤起了博耿耿于怀的心事……', 1, 1, 1, '好兄弟的另类告别之旅', '', 'http://img.funshion.com/sdw?oid=774e171ff352029907178a87ce718677&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393417615912960_1029vIcon.png', 'http://img1.funshion.com/sdw?oid=7d11d649b2c11f565258c5cc4858e2fd&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393417615912960_1029hIcon.png', 'http://img.funshion.com/sdw?oid=774e171ff352029907178a87ce718677&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393417615912960_1029vImg.png', 'http://img1.funshion.com/sdw?oid=7d11d649b2c11f565258c5cc4858e2fd&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/936393417615912960_1029hImg.png', '#B0C5D6', '#406480', '{\"v_color_s66_b60\":\"#346D99\"}', 1, 0, 1, 'huashi', '', 2, 1, 1, 0, 0, '', '1054749', 0, 0, 'normal', '[{\"level\":2,\"payStatus\":1},{\"level\":3,\"payStatus\":1},{\"level\":4,\"payStatus\":1}]', '', 1, 'funshion_vip_1', '1970-01-01 00:00:00', '1970-01-01 00:00:00', 10000, 0.00, 6.00, 6.00, '202304', 0, '2023-10-31 03:42:53', '2023-10-29 10:05:21', '2023-10-31 03:42:53', '[\"952\"]', 0, NULL, '[]', '剧情|爱情', '');

-- for search YG
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `tuputag`) VALUES ('625490221256953866', NULL, '神探四豆星 第一部', '小侦探破星球案件', 0, 1, 1, 1, 'huashi', 'M05CGPZ1JB', '', 2, '7.4', 'kids', 'kids', 1, '童话', 0, '', '', 2014, '中国内地', '卤豆|正片|动漫|神探四豆星第一季|木木豆|侦探|童话|神探四豆星|益智|冒险|搞笑|4-6岁|阿布豆|冒险|益智|糖豆|搞笑', '普通话', '', '《神探四豆星 第一部》：该片讲述了以木木豆为首的四位小侦探各展所长破获各个星球离奇案件的故事。', 0, 52, 0, '小侦探破星球案件', '', 'http://img3.funshion.com/sdw?oid=55900431ff0574121d262fcf250e8805&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625490221256953866_vIcon.jpeg', 'http://img1.funshion.com/sdw?oid=61a45202e47b5a627dce2da47b48205e&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625490221256953866_hIcon.jpeg', 'http://img3.funshion.com/sdw?oid=55900431ff0574121d262fcf250e8805&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625490221256953866_vImg.jpeg', 'http://img1.funshion.com/sdw?oid=61a45202e47b5a627dce2da47b48205e&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/625490221256953866_hImg.jpeg', '#B0C6D6', '#406580', '{\"v_color_s66_b60\":\"#346F99\",\"fanZaiColor\":\"#336080\"}', 2, 0, 1, 'huashi', '', 2, 0, 0, 0, 0, '', '339341', 0, 0, 'normal', '[{\"level\":2,\"payStatus\":1},{\"level\":3,\"payStatus\":1},{\"level\":4,\"payStatus\":1}]', '', 1, '', '1970-01-01 08:00:00', '2022-07-21 11:27:18', 3, 0.00, 0.00, 0.00, '201401', 0, '2024-04-12 15:39:23', '2022-07-21 11:25:49', '2024-05-16 18:12:18', '[\"210\"]', 0, NULL, '[]', '童话|益智|冒险|搞笑', '');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `previewInfo`, `mapping_tags`, `tuputag`) VALUES ('630983657569591296', NULL, '神探四豆星 第一部', '小侦探破星球案件', 0, 1, 1, 1, 'senyu', 'M001JFNR8U', '', 2, '7.4', 'kids', 'kids', 1, '童话', 0, '', '', 2014, '中国内地', '卤豆|正片|动漫|神探四豆星第一季|木木豆|侦探|童话|神探四豆星|益智|冒险|搞笑|4-6岁|阿布豆|冒险|益智|糖豆|搞笑', '普通话', '', '《神探四豆星 第一部》：该片讲述了以木木豆为首的四位小侦探各展所长破获各个星球离奇案件的故事。', 52, 52, 1, '小侦探破星球案件', '', 'http://img3.funshion.com/sdw?oid=55900431ff0574121d262fcf250e8805&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/630983657569591296_vIcon.jpeg', 'http://img1.funshion.com/sdw?oid=61a45202e47b5a627dce2da47b48205e&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/630983657569591296_hIcon.jpeg', 'http://img3.funshion.com/sdw?oid=55900431ff0574121d262fcf250e8805&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/630983657569591296_vIcon.jpeg', 'http://img1.funshion.com/sdw?oid=61a45202e47b5a627dce2da47b48205e&w=0&h=0', 'https://dhfs-test-cpc.wanyol.com/630983657569591296_hIcon.jpeg', '#B0C6D6', '#406580', '{\"v_color_s66_b60\":\"#346F99\",\"fanZaiColor\":\"#336080\"}', 1, 0, 1, 'senyu', '全 52 集', 2, 0, 0, 0, 0, '', '339341', 0, 0, 'normal', '[{\"level\":2,\"payStatus\":1},{\"level\":3,\"payStatus\":1},{\"level\":4,\"payStatus\":1}]', '', 1, '', '1970-01-01 08:00:00', '2022-07-21 12:40:58', 3, 0.00, 0.00, 0.00, '201401', 0, '2024-04-24 17:41:48', '2022-07-21 12:40:27', '2024-05-16 18:13:05', '[\"210\"]', 0, NULL, '[]', '童话|益智|冒险|搞笑', '');
INSERT INTO `longvideo_media0`.`standard_album0` (`sid`, `high_light_vid`, `title`, `sub_title`, `manager_status`, `status`, `source_status`, `verify_status`, `source`, `source_album_id`, `source_web_url`, `source_type`, `source_score`, `cp_score`, `program_type`, `sub_program_type`, `unit`, `category`, `duration`, `director`, `actor`, `year`, `area`, `tags`, `language`, `honor`, `information`, `valid_episode`, `total_episode`, `completed`, `brief`, `period`, `vertical_icon`, `vertical_icon_ocs`, `horizontal_icon`, `horizontal_icon_ocs`, `vertical_image`, `vertical_image_ocs`, `horizontal_image`, `horizontal_image_ocs`, `back_ground_color`, `back_ground_color_two`, `back_ground_color_json`, `feature_type`, `risk_flag`, `copyright`, `copyright_code`, `program_info`, `medium`, `pay_status`, `vip_type`, `charge_type`, `price`, `keyword`, `extra_info`, `source_play_count`, `source_hot`, `supply_type`, `definition`, `download_markcode`, `download_able`, `mark_code`, `free_start_time`, `free_end_time`, `end_time`, `start_time`, `pay_effect_days`, `vip_price`, `now_price`, `former_price`, `show_time`, `process_status`, `publish_time`, `create_time`, `update_time`, `source_series_id`, `prePush`, `preOnlineTime`, `online_time`, `previewInfo`, `mapping_tags`, `tuputag`, `competition_id`, `competition_name`, `season_id`, `season_name`, `participant_type`, `competition_type`, `phase`, `match_group`, `match_start_time`, `round`, `participant_countrys`, `sport_name`, `event_name`, `presenter`, `fit_age_min`, `fit_age_max`, `fit_age`, `douban_score`, `oppo_score`) VALUES ('1076858964135825408', NULL, '解密中国顶级神探刑侦笔记', '特邀刑侦专家破案手记', 0, 1, 1, 1, 'youkumobile', 'fbfa33c9a75c4002b27a', 'hap://applet/fc2345027171510597?path=%2Fpages%2Fplay%2Findex&query=showId%3Dfbfa33c9a75c4002b27a', 2, '0.0', '0.0', 'doc', 'doc', 1, '纪录片', 0, '', '', 2024, '内地', '知识|刑侦', '', '', '一提起刑侦专家，大家脑海中首先想到的就是福尔摩斯、柯南，或是一些国外大片中侦探的形象，其实我国一样有着顶级神探，他们是神秘的存在，他们为破案而生，为真相而来，是建国后的福尔摩斯。本专辑为您解密中国顶级神探的神秘面纱。他们中有共和国首席刑侦专家、痕迹指纹鉴定专家、妙笔神探、共和国首席枪弹痕迹鉴定专家、法医人类学权威、爆炸分析专家、痕迹物证勘验专家、现场勘察鉴识专家！一枚弹壳就能抓到真凶？且看中国顶级神探如何破获大案要案！', 9, 9, 1, '特邀刑侦专家破案手记', '9期全', 'http://m.ykimg.com/052700006731A4A1C7BFE912ADB8B61A', 'https://dhfs-test-cpc.wanyol.com/1076858964135825408_1216vImg.jpeg', 'http://m.ykimg.com/053500006731A49313EB6612D5C188D2', 'https://dhfs-test-cpc.wanyol.com/1076858964135825408_1216hIcon.jpeg', 'http://m.ykimg.com/052700006731A4A1C7BFE912ADB8B61A', 'https://dhfs-test-cpc.wanyol.com/1076858964135825408_1216vImg.jpeg', 'http://m.ykimg.com/053500006731A49313EB6612D5C188D2', 'https://dhfs-test-cpc.wanyol.com/1076858964135825408_1216hImg.jpeg', '#B0B0D6', '#404080', '{\"v_color_s66_b60\":\"#343499\",\"fanZaiColor\":\"#333380\",\"blurVerImgUrl\":\"https://dhfs-test-cpc.wanyol.com/1076858964135825408_media_msn_0621vImg.png\",\"msnMaskLayerColor\":\"#852E2EA5\",\"msnButtonColor\":\"#381414CC\",\"searchCardLightColor\":\"#4D1717\",\"searchCardDarkColor\":\"#260000\"}', 1, 0, 1, 'youkumobile', '全 9 集', 2, 1, 1, 0, 0, '', '', 0, 481, 'normal', '[]', '', 0, 'youku_mobile_vip_1', NULL, NULL, '1970-01-01 00:00:00', '2024-12-08 11:23:09', 0, 0.00, 0.00, 0.00, '202412', 0, '2025-07-16 00:15:25', '2024-11-20 00:48:58', '2025-07-16 00:15:25', '', 0, NULL, NULL, NULL, '其它', '', NULL, NULL, NULL, NULL, -1, -1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '-1.0', '-1.0');




CREATE TABLE `standard_video0` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vid` varchar(32) NOT NULL COMMENT '视频唯一ID',
  `sid` varchar(32) NOT NULL COMMENT '剧头唯一ID',
  `title` varchar(256) NOT NULL DEFAULT '' COMMENT '标题',
  `source_episode_id` varchar(32) DEFAULT NULL COMMENT '风行专用,风行长视频分集ID，video不是分集时代表高能看点关联的长视频分集ID（不是分集同时不存在长短关联关系时为0）',
  `eid` varchar(32) DEFAULT NULL COMMENT '最新高能剧集ID',
  `source_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '-1：删除，0：不可用， 1：可用',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '-2:黑名单，-1：删除，0：失效，            \r\n	1：生效，2:系统失效，3：合并失效，4：探测失效，5：源下线，6：注入失效',
  `pay_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0：免费，1：收费，2：单点，3：用券',
  `vip_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:免费，1：会员',
  `charge_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:免费，1：单点',
  `program_type` varchar(32) NOT NULL DEFAULT '' COMMENT '标准化节目类型',
  `video_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:正片,1.预告片2.花絮3.智能看点 4.剧集看点 5.独家策划 6.粉丝饭制 7.精彩速看 8.首映式 9.MV 10.其他 11.资讯 12.普通视频 13.彩蛋 14.高能看点',
  `category` varchar(1024) NOT NULL DEFAULT '' COMMENT '视频分类信息（用于分类标签）',
  `language` varchar(32) NOT NULL DEFAULT '' COMMENT '当前节目语言版本',
  `keyword` varchar(1024) NOT NULL DEFAULT '' COMMENT '关键词列表（一般用于搜索）',
  `vertical_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '竖图',
  `horizontal_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '横图',
  `source` varchar(16) NOT NULL DEFAULT '' COMMENT '节目来源标识code，如youku，tencent',
  `source_album_id` varchar(64) NOT NULL COMMENT '源专辑id',
  `source_video_id` varchar(64) NOT NULL DEFAULT '' COMMENT '源视频id',
  `play_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '播放类型 0：解析播放地址播放 1：播放地址播放',
  `play_url` varchar(512) NOT NULL DEFAULT '' COMMENT '播放地址',
  `definition` varchar(1024) NOT NULL DEFAULT '' COMMENT '清晰度，ST：标清SD：高清HD：超清XD：原画',
  `manmade` tinyint(4) NOT NULL DEFAULT '0' COMMENT '入库方式,0:系统录入 1:人工录入',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '时长（单位秒）',
  `episode_no` int(11) NOT NULL DEFAULT '0' COMMENT '剧集号',
  `episode_term` varchar(16) NOT NULL DEFAULT '' COMMENT '期号',
  `verify_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '牌照方审核状态，0:审核不通过，1审核通过',
  `tags` varchar(1024) NOT NULL DEFAULT '' COMMENT '标签列表（用于标签自动化）',
  `copyright` tinyint(1) NOT NULL DEFAULT '1' COMMENT '版权标识,0-否，1-是',
  `copyright_code` varchar(32) NOT NULL DEFAULT '' COMMENT '品牌方',
  `risk_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '高危标识,0-否，1-是',
  `platform_str` varchar(32) NOT NULL DEFAULT '0' COMMENT '0:全部,1:one_plus',
  `head_time` int(11) NOT NULL DEFAULT '0' COMMENT '片头时间（单位秒）',
  `tail_time` int(11) NOT NULL DEFAULT '0' COMMENT '片尾时间（单位秒）',
  `source_play_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '原始播放次数',
  `supply_type` varchar(32) NOT NULL DEFAULT 'normal' COMMENT '供应类型:special：特供，sole：独家，normal：普通',
  `download_able` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1=不可下载,0=无限制,1=会员下载,2=单点下载',
  `isMain` int(11) NOT NULL DEFAULT '0' COMMENT '直播是否主场景',
  `source_site` varchar(30) DEFAULT '' COMMENT '搜狐专用,视频来源，1本站，4优酷，5爱奇艺，6乐视，7腾讯视频，8土豆，10pptv，12cntv，11pps',
  `publish_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '向下游分发时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `mark_code` varchar(30) DEFAULT '' COMMENT '会员角标CODE',
  `is_project` int(4) NOT NULL DEFAULT '-1' COMMENT '是否允许投屏',
  `sourcePvId` varchar(32) NOT NULL DEFAULT '' COMMENT '预告片原始视频Id',
  `clip_duration` int(11) NOT NULL DEFAULT '0' COMMENT '试看时长',
  `relate_start` int(11) DEFAULT '0' COMMENT '短视频对应长媒体的起播时间',
  `relate_end` int(11) DEFAULT '0' COMMENT '短视频对应长媒体的止播时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index_sid_vid` (`sid`,`vid`) USING BTREE,
  KEY `index_update_time` (`update_time`) USING BTREE,
  KEY `index_source_album_id` (`source_album_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1648596168407359490 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;



INSERT INTO `longvideo_media0`.`standard_video0` (`id`, `vid`, `sid`, `title`, `source_episode_id`, `eid`, `source_status`, `status`, `pay_status`, `vip_type`, `charge_type`, `program_type`, `video_type`, `category`, `language`, `keyword`, `vertical_icon`, `horizontal_icon`, `source`, `source_album_id`, `source_video_id`, `play_type`, `play_url`, `definition`, `manmade`, `duration`, `episode_no`, `episode_term`, `verify_status`, `tags`, `copyright`, `copyright_code`, `risk_flag`, `platform_str`, `head_time`, `tail_time`, `source_play_count`, `supply_type`, `download_able`, `isMain`, `source_site`, `publish_time`, `create_time`, `update_time`, `mark_code`, `is_project`, `sourcePvId`, `clip_duration`, `relate_start`, `relate_end`) VALUES ('1534446953178652674', '749030003991322624', '506311924435210240', '法医狂妃第10集', NULL, NULL, '1', '1', '0', '0', '0', 'comic', '0', '悬疑|恋爱|美少女', '普通话', '', 'http://photocdn.tv.sohu.com/img/20200917/vrsa_hor_1600300400483_207002107_C7N0w_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200917/vrsExtend_1600300400552_207002107.jpg', 'sohu', '9649121', '6460843', '0', 'http://tv.sohu.com/v/MjAyMDA5MTcvbjYwMDkwMzkyNi5zaHRtbA==.html', '[{\"level\":1,\"payStatus\":0,\"fileSize\":24,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":49,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":74,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '0', '684', '10', '10', '1', '悬疑|恋爱|美少女', '1', 'sohu', '0', '0', '0', '0', '34191', 'normal', '1', '0', '1', '2022-05-30 09:33:01', '2022-05-30 09:33:01', '2023-03-25 00:51:46', '', '-1', '', '0', '0', '0');
INSERT INTO `longvideo_media0`.`standard_video0` (`id`, `vid`, `sid`, `title`, `source_episode_id`, `eid`, `source_status`, `status`, `pay_status`, `vip_type`, `charge_type`, `program_type`, `video_type`, `category`, `language`, `keyword`, `vertical_icon`, `horizontal_icon`, `source`, `source_album_id`, `source_video_id`, `play_type`, `play_url`, `definition`, `manmade`, `duration`, `episode_no`, `episode_term`, `verify_status`, `tags`, `copyright`, `copyright_code`, `risk_flag`, `platform_str`, `head_time`, `tail_time`, `source_play_count`, `supply_type`, `download_able`, `isMain`, `source_site`, `publish_time`, `create_time`, `update_time`, `mark_code`, `is_project`, `sourcePvId`, `clip_duration`, `relate_start`, `relate_end`) VALUES ('1534446953237245953', '749031262232190976', '506311924435210240', '法医狂妃第2集', NULL, NULL, '1', '1', '0', '0', '0', 'comic', '0', '悬疑|恋爱|美少女', '普通话', '', 'http://photocdn.tv.sohu.com/img/20200528/vrsa_hor_1590632696848_196017053_T0MbX_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200528/vrsExtend_1590632696913_196017053.jpg', 'sohu', '9649121', '6313836', '0', 'http://tv.sohu.com/v/MjAyMDA1MjgvbjYwMDg1Njc1Ni5zaHRtbA==.html', '[{\"level\":1,\"payStatus\":0,\"fileSize\":23,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":49,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":75,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '0', '709', '2', '2', '1', '悬疑|恋爱|美少女', '1', 'sohu', '0', '0', '0', '0', '36511', 'normal', '1', '0', '1', '2022-05-30 09:36:01', '2022-05-30 09:36:01', '2023-03-25 00:51:46', '', '-1', '', '0', '0', '0');
INSERT INTO `longvideo_media0`.`standard_video0` (`id`, `vid`, `sid`, `title`, `source_episode_id`, `eid`, `source_status`, `status`, `pay_status`, `vip_type`, `charge_type`, `program_type`, `video_type`, `category`, `language`, `keyword`, `vertical_icon`, `horizontal_icon`, `source`, `source_album_id`, `source_video_id`, `play_type`, `play_url`, `definition`, `manmade`, `duration`, `episode_no`, `episode_term`, `verify_status`, `tags`, `copyright`, `copyright_code`, `risk_flag`, `platform_str`, `head_time`, `tail_time`, `source_play_count`, `supply_type`, `download_able`, `isMain`, `source_site`, `publish_time`, `create_time`, `update_time`, `mark_code`, `is_project`, `sourcePvId`, `clip_duration`, `relate_start`, `relate_end`) VALUES ('1534446953283383297', '749031262416740352', '506311924435210240', '法医狂妃第3集', NULL, NULL, '1', '1', '0', '0', '0', 'comic', '0', '悬疑|恋爱|美少女', '普通话', '', 'http://photocdn.tv.sohu.com/img/20200525/vrs196017054_1590391602947_F82Q8_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200525/vrs196017054_1590391602947_Rqu0A_pic2.jpg', 'sohu', '9649121', '6313805', '0', 'http://tv.sohu.com/v/MjAyMDA2MTEvbjYwMDg2NjI0NS5zaHRtbA==.html', '[{\"level\":1,\"payStatus\":0,\"fileSize\":23,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":49,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":76,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '0', '698', '3', '3', '1', '悬疑|恋爱|美少女', '1', 'sohu', '0', '0', '0', '0', '32631', 'normal', '1', '0', '1', '2022-05-30 09:36:01', '2022-05-30 09:36:01', '2023-03-25 00:51:46', '', '-1', '', '0', '0', '0');
INSERT INTO `longvideo_media0`.`standard_video0` (`id`, `vid`, `sid`, `title`, `source_episode_id`, `eid`, `source_status`, `status`, `pay_status`, `vip_type`, `charge_type`, `program_type`, `video_type`, `category`, `language`, `keyword`, `vertical_icon`, `horizontal_icon`, `source`, `source_album_id`, `source_video_id`, `play_type`, `play_url`, `definition`, `manmade`, `duration`, `episode_no`, `episode_term`, `verify_status`, `tags`, `copyright`, `copyright_code`, `risk_flag`, `platform_str`, `head_time`, `tail_time`, `source_play_count`, `supply_type`, `download_able`, `isMain`, `source_site`, `publish_time`, `create_time`, `update_time`, `mark_code`, `is_project`, `sourcePvId`, `clip_duration`, `relate_start`, `relate_end`) VALUES ('1534460262703362049', '749031262626455552', '506311924435210240', '法医狂妃第4集', NULL, NULL, '1', '1', '0', '0', '0', 'comic', '0', '悬疑|恋爱|美少女', '普通话', '', 'http://photocdn.tv.sohu.com/img/20200625/vrsa_hor_1593048302658_196017055_eeQ8Z_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200625/vrsExtend_1593048302712_196017055.jpg', 'sohu', '9649121', '6313849', '0', 'http://tv.sohu.com/v/MjAyMDA2MjUvbjYwMDg3NDQ3MC5zaHRtbA==.html', '[{\"level\":1,\"payStatus\":0,\"fileSize\":26,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":54,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":82,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '0', '703', '4', '4', '1', '悬疑|恋爱|美少女', '1', 'sohu', '0', '0', '0', '0', '27632', 'normal', '1', '0', '1', '2022-05-30 09:36:01', '2022-05-30 09:36:01', '2023-03-25 00:51:47', '', '-1', '', '0', '0', '0');
INSERT INTO `longvideo_media0`.`standard_video0` (`id`, `vid`, `sid`, `title`, `source_episode_id`, `eid`, `source_status`, `status`, `pay_status`, `vip_type`, `charge_type`, `program_type`, `video_type`, `category`, `language`, `keyword`, `vertical_icon`, `horizontal_icon`, `source`, `source_album_id`, `source_video_id`, `play_type`, `play_url`, `definition`, `manmade`, `duration`, `episode_no`, `episode_term`, `verify_status`, `tags`, `copyright`, `copyright_code`, `risk_flag`, `platform_str`, `head_time`, `tail_time`, `source_play_count`, `supply_type`, `download_able`, `isMain`, `source_site`, `publish_time`, `create_time`, `update_time`, `mark_code`, `is_project`, `sourcePvId`, `clip_duration`, `relate_start`, `relate_end`) VALUES ('1534460263050493953', '749031262861336576', '506311924435210240', '法医狂妃第5集', NULL, NULL, '1', '1', '0', '0', '0', 'comic', '0', '悬疑|恋爱|美少女', '普通话', '', 'http://photocdn.tv.sohu.com/img/20200709/vrs_ver196017056_1594252076793_32360_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200709/vrsNew196017056_1594252069273.jpg', 'sohu', '9649121', '6313847', '0', 'http://tv.sohu.com/v/MjAyMDA3MDkvbjYwMDg3OTQ3MS5zaHRtbA==.html', '[{\"level\":1,\"payStatus\":0,\"fileSize\":23,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":46,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":69,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '0', '663', '5', '5', '1', '悬疑|恋爱|美少女', '1', 'sohu', '0', '0', '0', '0', '25270', 'normal', '1', '0', '1', '2022-05-30 09:36:01', '2022-05-30 09:36:01', '2023-03-25 00:51:47', '', '-1', '', '0', '0', '0');
INSERT INTO `longvideo_media0`.`standard_video0` (`id`, `vid`, `sid`, `title`, `source_episode_id`, `eid`, `source_status`, `status`, `pay_status`, `vip_type`, `charge_type`, `program_type`, `video_type`, `category`, `language`, `keyword`, `vertical_icon`, `horizontal_icon`, `source`, `source_album_id`, `source_video_id`, `play_type`, `play_url`, `definition`, `manmade`, `duration`, `episode_no`, `episode_term`, `verify_status`, `tags`, `copyright`, `copyright_code`, `risk_flag`, `platform_str`, `head_time`, `tail_time`, `source_play_count`, `supply_type`, `download_able`, `isMain`, `source_site`, `publish_time`, `create_time`, `update_time`, `mark_code`, `is_project`, `sourcePvId`, `clip_duration`, `relate_start`, `relate_end`) VALUES ('1534446954038358017', '749031263003942912', '506311924435210240', '法医狂妃第6集', NULL, NULL, '1', '1', '0', '0', '0', 'comic', '0', '悬疑|恋爱|美少女', '普通话', '', 'http://photocdn.tv.sohu.com/img/20200723/vrs196017057_1595472362074_W1q24_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200723/vrsNew196017057_1595472381147.jpg', 'sohu', '9649121', '6313869', '0', 'http://tv.sohu.com/v/MjAyMDA3MjMvbjYwMDg4NDUyMi5zaHRtbA==.html', '[{\"level\":1,\"payStatus\":0,\"fileSize\":22,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":44,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":66,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '0', '693', '6', '6', '1', '悬疑|恋爱|美少女', '1', 'sohu', '0', '0', '0', '0', '24067', 'normal', '1', '0', '1', '2022-05-30 09:36:01', '2022-05-30 09:36:01', '2023-03-25 00:51:48', '', '-1', '', '0', '0', '0');



CREATE TABLE `standard_episode0` (
  `eid` varchar(32) NOT NULL COMMENT '剧集唯一ID',
  `sid` varchar(32) NOT NULL COMMENT '剧头唯一ID',
  `vid` varchar(32) NOT NULL COMMENT '视频唯一ID',
  `source` varchar(32) NOT NULL COMMENT '当前剧集来源',
  `url` text COMMENT '播放地址',
  `source_video_id` varchar(64) NOT NULL DEFAULT '' COMMENT '源视频id',
  `source_site` varchar(11) NOT NULL DEFAULT '' COMMENT '搜狐专用,视频来源，1本站，4优酷，5爱奇艺，6乐视，7腾讯视频，8土豆',
  `tail_time` int(11) NOT NULL DEFAULT '0' COMMENT '片尾时长（单位秒）',
  `head_time` int(11) NOT NULL DEFAULT '0' COMMENT '片头时长（单位秒）',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '时长（单位秒）',
  `title` varchar(128) NOT NULL DEFAULT '' COMMENT '剧集标题',
  `feature_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1：正片， 2：预告片，3：微电影，4，精彩看点',
  `order_num` int(11) NOT NULL DEFAULT '1' COMMENT '后台使用的排序序号(后台剧集号)',
  `episode` int(11) NOT NULL DEFAULT '0' COMMENT '当前剧集号',
  `episode_term` varchar(16) NOT NULL DEFAULT '' COMMENT '当前剧期号',
  `vertical_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '海报小竖图',
  `horizontal_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '海报小横图',
  `copyright` tinyint(1) NOT NULL DEFAULT '1' COMMENT '版权标识,0-否，1-是',
  `copyright_code` varchar(32) NOT NULL DEFAULT '' COMMENT '版权方',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '标准化状态：-2:黑名单，-1：删除，0：失效，           \r\n	1：生效，2:系统失效，3：合并失效，4：探测失效，5：源下线，6：注入失效''',
  `source_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '原始状态：-1：删除，0：不可用， 1：可用',
  `pay_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '付费类型，0免费，1会员，2单点',
  `vip_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '会员类型，0免费，1会员',
  `charge_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '付费类型，0免费，1单点',
  `subscript_code` varchar(32) NOT NULL DEFAULT '' COMMENT '左角标code（非会员角标',
  `verify_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '牌照方审核状态，0:审核不通过，1审核通过',
  `risk_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '高危标识，0-低危（有版权），1-中危， 2-高危',
  `source_play_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '原始播放量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `mark_code` varchar(30) DEFAULT '' COMMENT '会员角标CODE',
  `definition` varchar(1024) NOT NULL DEFAULT '' COMMENT '清晰度',
  `is_project` int(4) NOT NULL DEFAULT '-1' COMMENT '是否允许投屏 -1=合作方未提供，0=不允许投屏，1=允许投屏',
  `source_episode_id` varchar(32) DEFAULT NULL COMMENT '风行专用,风行长视频分集ID',
  `sourcePvId` varchar(32) NOT NULL DEFAULT '' COMMENT '预告片原始视频Id',
  `clip_duration` int(11) NOT NULL DEFAULT '0' COMMENT '试看时长',
  `play_url_expire_time` datetime DEFAULT NULL COMMENT '播放地址过期时间',
  `sports_video_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '体育视频类型，1 推荐 2 精彩集锦 3 预告垫片 4 回看列表 5 赛事前瞻 6 多解说 7 多视角',
  PRIMARY KEY (`eid`) USING BTREE,
  UNIQUE KEY `index_sid_vid` (`sid`,`vid`) USING BTREE,
  KEY `index_update_time` (`update_time`) USING BTREE,
  KEY `index_vid` (`vid`) USING BTREE,
  KEY `index_sid_update_time` (`sid`,`update_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`) VALUES ('576196693050085376', '506311924435210240', '576196692630654976', NULL, 'sohu', 'http://tv.sohu.com/v/MjAyMDA1MjgvbjYwMDg1Njc1NC5zaHRtbA==.html', '0', '0', '646', '1', '6313781', '法医狂妃第1集', '1', '1', '1', '1', 'http://photocdn.tv.sohu.com/img/20200528/vrsa_hor_1590632652004_196017052_r0lnx_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200528/vrsExtend_1590632652076_196017052.jpg', '1', 'sohu', '1', '1', '0', '0', '0', '', '1', '0', '268840', '2022-04-28 12:15:14', '2023-04-12 19:55:11', '', '[{\"level\":1,\"payStatus\":0,\"fileSize\":18,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":39,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":61,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '-1', '', '0');
INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`) VALUES ('749030004234592256', '506311924435210240', '749030003991322624', NULL, 'sohu', 'http://tv.sohu.com/v/MjAyMDA5MTcvbjYwMDkwMzkyNi5zaHRtbA==.html', '0', '0', '684', '1', '6460843', '法医狂妃第10集', '1', '10', '10', '10', 'http://photocdn.tv.sohu.com/img/20200917/vrsa_hor_1600300400483_207002107_C7N0w_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200917/vrsExtend_1600300400552_207002107.jpg', '1', 'sohu', '1', '1', '0', '0', '0', '', '1', '0', '34191', '2022-05-30 09:33:01', '2023-04-12 19:55:11', '', '[{\"level\":1,\"payStatus\":0,\"fileSize\":24,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":49,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":74,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '-1', '', '0');

INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`, `play_url_expire_time`, `sports_video_type`) VALUES ('1000021575682142208', '993351447016026112', '1000021567381614592', '12111531', 'huashi', NULL, 0, 0, 2489, '', 'E05CG7SA9U', '我们的队伍向太阳 第33集', 1, 33, 33, '33', 'http://img3.funshion.com/sdw?oid=dbbc51e0fa5fb400025847b4a96f3ac5&w=0&h=0', 'http://img3.funshion.com/sdw?oid=91599363d0a3ec6498c8468e30d05ea5&w=0&h=0', 1, 'huashi', 1, 1, 0, 0, 0, '', 1, 0, 0, '2024-04-22 00:00:56', '2025-04-16 10:30:32', '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"payStatus\":1,\"fileSize\":198,\"downloadAble\":1,\"downLoadMarkCode\":\"funshion_vip_1\",\"downloadVipType\":\"video_vip\"},{\"level\":3,\"payStatus\":1,\"fileSize\":320,\"downloadAble\":1,\"downLoadMarkCode\":\"funshion_vip_1\",\"downloadVipType\":\"video_vip\"},{\"level\":4,\"payStatus\":1,\"fileSize\":677,\"downloadAble\":1,\"downLoadMarkCode\":\"funshion_vip_1\",\"downloadVipType\":\"video_vip\"},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', 1, '391940129', 600, NULL, 0);
INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`, `play_url_expire_time`, `sports_video_type`) VALUES ('1000067937715007488', '962974740115533824', '1000067933562646528', NULL, 'sohu', 'http://tv.sohu.com/v/MjAyNDA0MjIvbjYwMTQwMTk2OC5zaHRtbA==.html', 0, 0, 1490, '1', '8989315', '女子蹊跷被害，独居男子承认是凶手', 1, 94, 94, '94', 'http://photocdn.tv.sohu.com/img/20240422/vrsa_ver_1713748590317_534399543_4o0tl_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20240422/vrsExtend_1713748590331_534399543.jpg', 1, 'sohu', 1, 1, 0, 0, 0, '', 1, 0, 0, '2024-04-22 03:05:09', '2025-02-10 22:01:23', '', '[{\"level\":1,\"payStatus\":0,\"fileSize\":31,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":68,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":114,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, '', 0, NULL, 0);
INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`, `play_url_expire_time`, `sports_video_type`) VALUES ('1000147267698733056', '914325851787669504', '1000147267291885568', NULL, 'mgmobile', 'https://m.mgtv.com/h/576697/20857472.html', 0, 0, 189, '', '6aqqYa.O7N6alaO.5386f1ba4f714d96', '制作冰柠玫瑰', 1, 1148, 1148, '1148', 'https://gwimg.hitv.com/snapshot/2023/2404/2122/1705/6FDA75B9A60F4EA58765342096C6AF6B/283723351450857472_1.03_0_0.jpg', 'https://gwimg.hitv.com/snapshot/2023/2404/2122/1705/6FDA75B9A60F4EA58765342096C6AF6B/283723351450857472_1.03_0_0.jpg', 1, 'mgmobile', 5, 0, 0, 0, 0, '', 0, 0, 0, '2024-04-22 08:20:23', '2025-02-11 19:54:32', '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"payStatus\":0,\"fileSize\":21,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":42,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, '', 0, NULL, 0);
INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`, `play_url_expire_time`, `sports_video_type`) VALUES ('1000159850199691264', '914325851787669504', '1000159849838981120', NULL, 'mgmobile', 'https://m.mgtv.com/h/576697/20858484.html', 0, 0, 177, '', '6aqqYa.O7N6NlNl.1e11cf72e6ea9d10', '金鹰小学乐创TV栏目第一期：“学雷锋”护绿\n行动—春播节', 1, 1149, 1149, '1149', 'https://gwimg.hitv.com/snapshot/2023/2404/2207/5425/6FDA75B9A60F4EA58765342096C6AF6B/283868807424106496_1.03_0_0.jpg', 'https://gwimg.hitv.com/snapshot/2023/2404/2207/5425/6FDA75B9A60F4EA58765342096C6AF6B/283868807424106496_1.03_0_0.jpg', 1, 'mgmobile', 5, 0, 0, 0, 0, '', 0, 0, 0, '2024-04-22 09:10:24', '2025-02-11 19:54:32', '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"payStatus\":0,\"fileSize\":20,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":39,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, '', 0, NULL, 0);
INSERT INTO `longvideo_media0`.`standard_episode0` (`eid`, `sid`, `vid`, `source_episode_id`, `source`, `url`, `tail_time`, `head_time`, `duration`, `source_site`, `source_video_id`, `title`, `feature_type`, `order_num`, `episode`, `episode_term`, `vertical_icon`, `horizontal_icon`, `copyright`, `copyright_code`, `status`, `source_status`, `pay_status`, `vip_type`, `charge_type`, `subscript_code`, `verify_status`, `risk_flag`, `source_play_count`, `create_time`, `update_time`, `mark_code`, `definition`, `is_project`, `sourcePvId`, `clip_duration`, `play_url_expire_time`, `sports_video_type`) VALUES ('1000176268546846720', '985330337217425408', '1000176267989004288', NULL, 'mgmobile', 'https://m.mgtv.com/h/633195/20768210.html', 68, 44, 774, '', 'qyyEY6.O7aqNOE7.712d8aeb8797fbb5', '福星八戒 第2集', 1, 2, 2, '2', 'https://1vimg.hitv.com/100/2404/0212/4235/AQ6Zuxcy9VX/276693852886392832.jpg', 'https://2vimg.hitv.com/100/2404/0212/4234/AQ6Zuxcy9VX/276693849650638848.jpg', 1, 'mgmobile', 5, 0, 0, 0, 0, '', 0, 0, 0, '2024-04-22 10:15:37', '2025-02-11 19:55:38', '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"downloadAble\":0},{\"level\":3,\"downloadAble\":0},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, '', 0, NULL, 0);



CREATE TABLE `standard_trailer0` (
  `tid` varchar(32) NOT NULL COMMENT '全局唯一id',
  `sid` varchar(32) NOT NULL COMMENT '剧头id',
  `eid` varchar(32) DEFAULT NULL COMMENT '最新高能剧集ID',
  `vid` varchar(32) NOT NULL COMMENT '视频id',
  `source` varchar(32) NOT NULL COMMENT '合作方源',
  `url` text COMMENT '播放地址',
  `tail_time` int(11) NOT NULL DEFAULT '0' COMMENT '片尾时长',
  `head_time` int(11) NOT NULL DEFAULT '0' COMMENT '片头时长',
  `source_site` varchar(30) NOT NULL DEFAULT '' COMMENT '搜狐专用,视频来源:1本站，4优酷，5爱奇艺，6乐视，7腾讯视频，8土豆，10pptv，12cntv，11pps',
  `source_video_id` varchar(64) NOT NULL DEFAULT '' COMMENT '源视频id',
  `title` varchar(512) NOT NULL DEFAULT '' COMMENT '视频的标题',
  `vertical_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '海报小竖图',
  `horizontal_icon` varchar(512) NOT NULL DEFAULT '' COMMENT '海报小横图',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '标准化状态-2:黑名单，-1：删除，0：失效，        \r\n	1：生效，2:系统失效，3：合并失效，4：探测失效，5：源下线，6：注入失效''',
  `source_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '原状态:-1：删除，0：不可用， 1：可用',
  `trailer_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1.预告片2.花絮3.智能看点 4.剧集看点 5.独家策划  \r\n	6.粉丝饭制 7.精彩速看 8.首映式 9.MV 10.其他 11.资讯 12.普通视频',
  `order_num` int(11) NOT NULL DEFAULT '1' COMMENT '看点排序              \r\n	同一个节目下面序号递增',
  `risk_flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '高危标识，0-低危（有版权），1-中危， 2-高危',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '时长',
  `source_play_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '原始播放量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `pay_status` tinyint(2) DEFAULT '0' COMMENT '0-免费,1-会员免费,2-单点付费',
  `mark_code` varchar(30) DEFAULT '' COMMENT '会员角标CODE',
  `definition` varchar(1024) NOT NULL DEFAULT '' COMMENT '清晰度',
  `is_project` int(4) NOT NULL DEFAULT '-1' COMMENT '是否允许投屏 -1=合作方未提供，0=不允许投屏，1=允许投屏',
  `relate_start` int(11) DEFAULT '0' COMMENT '短视频对应长媒体的起播时间',
  `relate_end` int(11) DEFAULT '0' COMMENT '短视频对应长媒体的止播时间',
  `source_episode_id` varchar(32) DEFAULT NULL COMMENT '风行专用,风行长视频分集ID',
  `clip_duration` int(11) NOT NULL DEFAULT '0' COMMENT '试看时长',
  `play_url_expire_time` datetime DEFAULT NULL COMMENT '播放地址过期时间',
  `sports_video_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '体育视频类型，1 推荐 2 精彩集锦 3 预告垫片 4 回看列表 5 赛事前瞻 6 多解说 7 多视角',
  `fit_age_min` tinyint(3) DEFAULT NULL COMMENT '最小适龄',
  `fit_age_max` tinyint(3) DEFAULT NULL COMMENT '最大适龄',
  `fit_age` varchar(255) DEFAULT NULL COMMENT 'cp方适龄原始内容',
  PRIMARY KEY (`tid`) USING BTREE,
  UNIQUE KEY `index_sid_vid` (`sid`,`vid`) USING BTREE,
  KEY `index_update_time` (`update_time`) USING BTREE,
  KEY `index_sid_update_time` (`sid`,`update_time`) USING BTREE,
  KEY `index_vid` (`vid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('670613294918344704', '506311924435210240', '670613294553440256', NULL, NULL, 'sohu', 'http://tv.sohu.com/v/MjAyMDA4MDQvbjYwMDg4ODAzMS5zaHRtbA==.html', '0', '0', '1', '6477279', '看点：老婆对待病号太关心，容都尉吃醋抓狂！', 'http://photocdn.tv.sohu.com/img/20200804/vrsa_hor_1596534627059_208078588_zOq05_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200804/vrsExtend_1596534627125_208078588.jpg', '1', '1', '9', '0', '0', '66', '16634', '2022-05-05 10:34:20', '2023-04-19 00:34:34', '0', '', '[{\"level\":1,\"payStatus\":0,\"fileSize\":2,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":4,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":6,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '-1', '0', '0', '0', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('670613295325192192', '506311924435210240', '670613295019008000', NULL, NULL, 'sohu', 'http://tv.sohu.com/v/MjAyMDA4MDQvbjYwMDg4ODAyNy5zaHRtbA==.html', '0', '0', '1', '6477005', '看点：如果实在想看，本王脱给你看！', 'http://photocdn.tv.sohu.com/img/20200804/vrsa_hor_1596530097636_208078558_RdBP5_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200804/vrsExtend_1596530097701_208078558.jpg', '1', '1', '9', '0', '0', '58', '15316', '2022-05-05 10:34:20', '2023-04-19 00:34:34', '0', '', '[{\"level\":1,\"payStatus\":0,\"fileSize\":2,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":4,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":6,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '-1', '0', '0', '0', 11, 18, '11-14岁,15-18岁');
INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('670613296164052992', '506311924435210240', '670613295794954240', NULL, NULL, 'sohu', 'http://tv.sohu.com/v/MjAyMDA4MDQvbjYwMDg4ODAyOS5zaHRtbA==.html', '0', '0', '1', '6477268', '看点：敢打我老婆的主意，用中药苦死你！', 'http://photocdn.tv.sohu.com/img/20200804/vrsa_hor_1596533824161_208078587_B5mV7_pic3.jpg', 'http://photocdn.tv.sohu.com/img/20200804/vrsExtend_1596533824236_208078587.jpg', '1', '1', '9', '0', '0', '89', '10192', '2022-05-05 10:34:20', '2023-04-19 00:34:34', '0', '', '[{\"level\":1,\"payStatus\":0,\"fileSize\":2,\"downloadAble\":1},{\"level\":2,\"payStatus\":0,\"fileSize\":5,\"downloadAble\":1},{\"level\":3,\"payStatus\":0,\"fileSize\":8,\"downloadAble\":1},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', '-1', '0', '0', '0', 11, 18, '11-14岁,15-18岁');

INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `play_url_expire_time`, `sports_video_type`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('1000027747294294016', '995537316594495488', '1000027746996498432', NULL, NULL, 'mgmobile', 'https://m.mgtv.com/h/641051/20806736.html', 0, 0, '', 'qlE76E.O7N7qayq.dda9e303e0be9b3b', '“零”智能生命想要摧毁智能次元世界，铠兽超人英勇出击！', 'https://0vimg.hitv.com/100/2404/1117/0643/AQ6Zuxcy9VX/280021817454776320.jpg', 'https://1vimg.hitv.com/100/2404/1117/0643/AQ6Zuxcy9VX/280021814304854016.jpg', 5, 0, 3, 1, 0, 79, 0, '2024-04-22 00:25:27', '2025-02-11 21:32:47', 0, '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"downloadAble\":0},{\"level\":3,\"downloadAble\":0},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, 0, 0, 0, NULL, 0, 4, 10, '4-6岁,7-10岁');
INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `play_url_expire_time`, `sports_video_type`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('1000027747420123136', '995537316594495488', '1000027747076190208', NULL, NULL, 'mgmobile', 'https://m.mgtv.com/h/641051/20806745.html', 0, 0, '', 'qlE76E.O7N7qal6.3f16337136cce6c8', '战棋合体，王者降临！铠兽超人，英雄出击！', 'https://1vimg.hitv.com/100/2404/1117/0756/AQ6Zuxcy9VX/280022121102696448.jpg', 'https://1vimg.hitv.com/100/2404/1117/0755/AQ6Zuxcy9VX/280022117952774144.jpg', 5, 0, 3, 1, 0, 79, 0, '2024-04-22 00:25:27', '2025-02-11 21:32:47', 0, '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"downloadAble\":0},{\"level\":3,\"downloadAble\":0},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, 0, 0, 0, NULL, 0, 4, 10, '4-6岁,7-10岁');
INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `play_url_expire_time`, `sports_video_type`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('1000028965710245888', '661439085889658880', '1000028965404061696', NULL, NULL, 'mgmobile', 'https://m.mgtv.com/h/391341/20841873.html', 0, 0, '', 'yYEylE.O7NlENay.6842a06022995009', '太伤心了，喜欢的人有青梅竹马', 'http://3img.hitv.com/preview/sp_images/2024/4/18/dianying/391341/20841873/20240418131403980.jpg', 'http://1img.hitv.com/preview/sp_images/2024/4/18/dianying/391341/20841873/20240418131403480.jpg', 5, 0, 3, 1, 0, 149, 0, '2024-04-22 00:30:18', '2025-02-11 20:38:56', 0, '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"downloadAble\":0},{\"level\":3,\"downloadAble\":0},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, 0, 0, 0, NULL, 0, NULL, NULL, '');
INSERT INTO `longvideo_media0`.`standard_trailer0` (`tid`, `sid`, `vid`, `source_episode_id`, `eid`, `source`, `url`, `tail_time`, `head_time`, `source_site`, `source_video_id`, `title`, `vertical_icon`, `horizontal_icon`, `status`, `source_status`, `trailer_type`, `order_num`, `risk_flag`, `duration`, `source_play_count`, `create_time`, `update_time`, `pay_status`, `mark_code`, `definition`, `is_project`, `relate_start`, `relate_end`, `clip_duration`, `play_url_expire_time`, `sports_video_type`, `fit_age_min`, `fit_age_max`, `fit_age`) VALUES ('1000030177973153792', '650701376703373312', '1000030177675358208', NULL, NULL, 'mgmobile', 'https://m.mgtv.com/h/44374/20841880.html', 0, 0, '', 'llyal.O7NlENN7.d97dc06b73d038ca', '教科书级演绎一个父亲的爱', 'http://1img.hitv.com/preview/sp_images/2024/4/18/dianying/44374/20841880/20240418131847057.jpg', 'http://0img.hitv.com/preview/sp_images/2024/4/18/dianying/44374/20841880/20240418131847297.jpg', 5, 0, 3, 1, 0, 115, 0, '2024-04-22 00:35:07', '2025-02-11 18:03:31', 0, '', '[{\"level\":1,\"downloadAble\":0},{\"level\":2,\"downloadAble\":0},{\"level\":3,\"downloadAble\":0},{\"level\":4,\"downloadAble\":0},{\"level\":5,\"downloadAble\":0},{\"level\":6,\"downloadAble\":0}]', -1, 0, 0, 0, NULL, 0, NULL, NULL, '');


CREATE TABLE `mis_video_org0` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `source` varchar(32) NOT NULL COMMENT 'cp方',
  `source_video_id` varchar(64) NOT NULL COMMENT 'CP方视频Id',
  `source_album_id` varchar(64) DEFAULT NULL COMMENT 'CP方剧头id',
  `sid` varchar(32) DEFAULT NULL COMMENT '标准化后的sid',
  `vid` varchar(32) DEFAULT NULL COMMENT '标准化视频Id',
  `feature_type` varchar(64) DEFAULT '' COMMENT '原始正片类型：正片、预告片',
  `time_lap` int(11) NOT NULL COMMENT '处理状态：0-待处理  2已处理',
  `source_status` varchar(32) DEFAULT '0' COMMENT '原始视频状态',
  `origin_info` mediumtext COMMENT '原始报文',
  `process_times` int(11) NOT NULL DEFAULT '0' COMMENT '标准化处理次数',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '数据版本号,使用时间戳',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_source_video_id` (`source_album_id`,`source_video_id`,`source`) USING BTREE,
  KEY `update_time_index` (`update_time`) USING BTREE,
  KEY `sourceAlbumId_timelap` (`source_album_id`,`time_lap`) USING BTREE,
  KEY `index_vid_source` (`vid`) USING BTREE,
  KEY `index_timelap` (`time_lap`) USING BTREE,
  KEY `index_source` (`source`) USING BTREE,
  KEY `index_source_video_id` (`source_video_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1912816983028359170 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='原始视频表';

INSERT INTO `longvideo_media0`.`mis_video_org0` (`id`, `source`, `source_video_id`, `source_album_id`, `sid`, `vid`, `feature_type`, `time_lap`, `source_status`, `origin_info`, `process_times`, `version`, `create_time`, `update_time`) VALUES (1524665979656982529, 'sohu', '7260405', '9725364', '655911415831535616', '747741719289864192', '5', 2, 'update', '{\"vid\":7260405,\"fee\":0,\"is_download\":1,\"ip_limit\":0,\"video_length_type\":1,\"video_type\":5,\"video_big_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_0W2L3_pic22.jpg\",\"ver_big_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_98fU0_pic7.jpg\",\"hor_big_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_qE747_pic6.jpg\",\"ver_high_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_49256_pic3.jpg\",\"hor_high_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_Nfv6K_pic2.jpg\",\"tv_preview_pic\":\"http://photocdn.tv.sohu.com/img/Sample2/BackUp_Sample2/images/svc/20210913/287504521_7260405_tv_H_141510_6/287504521_7260405_tv_H_141510_6_big_160_90_3.jpg\",\"tv_is_vr\":0,\"tip\":\"2分11秒\",\"video_desc\":\"因为烂尾、断更下了地狱，一代三流写手，竟然被打入了自己小说里面，穿书成反派赘婿的李雨果，后宫才刚营业，就被困于危机重重的黑森林之中，且看赘婿李雨果如何绝处逢生，与叶琼斗智斗勇，守护粉红佳人。\",\"video_name\":\"看点：人在家中坐锅从天上来！叶琼薛妮妮联手陷害李雨果\",\"video_sub_name\":\"看点：人在家中坐锅从天上来！叶琼薛妮妮联手陷害李雨果\",\"keyword\":\"被迫成为反派赘婿第二季片花;看点：人在家中坐锅从天上来！叶琼薛妮妮联手陷害李雨果;被迫成为反派赘婿第二季片花动漫;看点：人在家中坐锅从天上来！叶琼薛妮妮联手陷害李雨果;\",\"album_name\":\"被迫成为反派赘婿第二季片花\",\"is_original_code\":1,\"url_html5\":\"http://m.tv.sohu.com/v7260405.shtml?src=\",\"cid\":16,\"cate_code\":\"115118\",\"second_cate_name\":\"恋爱\",\"year\":\"2021\",\"score\":6.4,\"play_count\":905719,\"director\":\"浩克\",\"director_id\":\"1197590\",\"area\":\"大陆\",\"area_id\":30,\"language\":\"普通话\",\"aid\":9725365,\"publish_time\":\"2021-09-17\",\"update_time\":\"2021-09-17\",\"video_order\":0,\"latest_video_count\":2,\"total_video_count\":100,\"total_duration\":130.026,\"total_duration_double\":130.026,\"start_time\":0,\"end_time\":0,\"period\":\"\",\"site\":1,\"small_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_6vm84_pic8.jpg\",\"ver_small_pic\":\"http://photocdn.tv.sohu.com/img/20210913/vrsa_hor_1631513701293_287504521_F9172_pic9.jpg\",\"tv_id\":287504521,\"crid\":0,\"definition\":{\"nor\":\"标清360P\",\"super\":\"超清720P\",\"high\":\"高清480P\"},\"clarity_file_size\":{\"nor_file_size\":\"4877566\",\"high_file_size\":\"10020722\",\"super_file_size\":\"15088927\"},\"original_video_url\":\"http://tv.sohu.com/v/MjAyMTA5MTcvbjYwMTA0Nzg5OS5zaHRtbA==.html\",\"limit_play\":0,\"labelFirstId\":0,\"serious\":false,\"vHeight\":\"540\",\"vWidth\":\"960\",\"tvAge\":\"13岁-17岁;18岁以上\",\"tvPic169\":\"http://photocdn.tv.sohu.com/img/20210913/vrsExtend_1631513701357_287504521.jpg\",\"uploadFrom\":0,\"drm\":false}', 7, 1726256992008, '2022-05-12 16:21:11', '2025-02-10 20:36:12');
INSERT INTO `longvideo_media0`.`mis_video_org0` (`id`, `source`, `source_video_id`, `source_album_id`, `sid`, `vid`, `feature_type`, `time_lap`, `source_status`, `origin_info`, `process_times`, `version`, `create_time`, `update_time`) VALUES (1524665979658104834, 'sohu', '7250086', '9725364', '655911415831535616', '667232478418903040', '1', 2, 'del', '{\"vid\":7250086,\"fee\":0,\"is_download\":1,\"ip_limit\":1,\"video_length_type\":1,\"video_type\":1,\"video_big_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_hor_1631671812652_286523766_61Jm7_pic22.jpg\",\"ver_big_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_ver_1631671812714_286523766_R0A8O_pic7.jpg\",\"hor_big_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_hor_1631671812652_286523766_8SW50_pic6.jpg\",\"ver_high_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_ver_1631671812714_286523766_ZEPRt_pic3.jpg\",\"hor_high_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_hor_1631671812652_286523766_31959_pic2.jpg\",\"tv_preview_pic\":\"http://photocdn.tv.sohu.com/img/Sample2/BackUp_Sample2/images/svc/20210909/286523766_7250086_tv_H_161348_6/286523766_7250086_tv_H_161348_6_big_160_90_5.jpg\",\"tv_is_vr\":0,\"tip\":\"8分54秒\",\"video_desc\":\"因为烂尾、断更下了地狱，一代三流写手，竟然被打入了自己小说里面，穿书成反派赘婿的李雨果，后宫才刚营业，就被困于危机重重的黑森林之中，且看赘婿李雨果如何绝处逢生，与叶琼斗智斗勇，守护粉红佳人。\",\"video_name\":\"被迫成为反派赘婿第二季第1集\",\"keyword\":\"被迫成为反派赘婿第二季;被迫成为反派赘婿第二季第1集;被迫成为反派赘婿第二季全集高清;被迫成为反派赘婿第二季动漫;\",\"album_name\":\"被迫成为反派赘婿第二季\",\"is_original_code\":1,\"url_html5\":\"http://m.tv.sohu.com/v7250086.shtml?src=\",\"cid\":16,\"cate_code\":\"115118\",\"second_cate_name\":\"恋爱;其它;日常\",\"year\":\"2021\",\"score\":6.3,\"director\":\"浩克\",\"director_id\":\"1197590\",\"area\":\"大陆\",\"area_id\":30,\"language\":\"普通话\",\"aid\":9725364,\"publish_time\":\"2021-09-15\",\"update_time\":\"2021-09-15\",\"video_order\":1,\"latest_video_count\":1,\"total_video_count\":45,\"total_duration\":533.083,\"total_duration_double\":533.083,\"start_time\":0,\"end_time\":0,\"site\":1,\"small_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_hor_1631671812652_286523766_D2L2Y_pic8.jpg\",\"ver_small_pic\":\"http://photocdn.tv.sohu.com/img/20210915/vrsa_ver_1631671812714_286523766_ChDRF_pic9.jpg\",\"tv_id\":286523766,\"crid\":0,\"definition\":{\"nor\":\"标清360P\",\"super\":\"超清720P\",\"high\":\"高清480P\"},\"clarity_file_size\":{\"nor_file_size\":\"21618176\",\"high_file_size\":\"45024614\",\"super_file_size\":\"66699712\"},\"original_video_url\":\"http://tv.sohu.com/v/MjAyMTA5MTUvbjYwMTA0NzI4OC5zaHRtbA==.html\",\"limit_play\":0,\"labelFirstId\":0,\"serious\":false,\"vHeight\":\"540\",\"vWidth\":\"960\",\"tvAge\":\"13岁-17岁;18岁以上\",\"tvPic169\":\"http://photocdn.tv.sohu.com/img/20210915/vrsExtend_1631671812767_286523766.jpg\",\"uploadFrom\":0,\"drm\":false}', 9, 1725523544521, '2022-05-12 16:21:11', '2025-02-10 20:36:11');
INSERT INTO `longvideo_media0`.`mis_video_org0` (`id`, `source`, `source_video_id`, `source_album_id`, `sid`, `vid`, `feature_type`, `time_lap`, `source_status`, `origin_info`, `process_times`, `version`, `create_time`, `update_time`) VALUES (1524665979687464962, 'sohu', '7250156', '9725364', '655911415831535616', '750682134552977408', '5', 2, 'update', '{\"vid\":7250156,\"fee\":0,\"is_download\":1,\"ip_limit\":0,\"video_length_type\":1,\"video_type\":5,\"video_big_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_hor_1631178251550_286523773_424q9_pic22.jpg\",\"ver_big_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_ver_1631178251616_286523773_QO7g3_pic7.jpg\",\"hor_big_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_hor_1631178251550_286523773_A0D02_pic6.jpg\",\"ver_high_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_ver_1631178251616_286523773_03TxH_pic3.jpg\",\"hor_high_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_hor_1631178251550_286523773_56Dg0_pic2.jpg\",\"tv_preview_pic\":\"http://photocdn.tv.sohu.com/img/Sample2/BackUp_Sample2/images/svc/20210909/286523773_7250156_tv_H_170112_28191/286523773_7250156_tv_H_170112_28191_big_160_90_2.jpg\",\"tv_is_vr\":0,\"tip\":\"0分55秒\",\"video_desc\":\"因为烂尾、断更下了地狱，一代三流写手，竟然被打入了自己小说里面，穿书成反派赘婿的李雨果，后宫才刚营业，就被困于危机重重的黑森林之中，且看赘婿李雨果如何绝处逢生，与叶琼斗智斗勇，守护粉红佳人。\",\"video_name\":\"李雨果被困于黑森林之中，与叶琼斗智斗勇守护粉红佳人！\",\"video_sub_name\":\"李雨果被困于黑森林之中，与叶琼斗智斗勇守护粉红佳人！\",\"keyword\":\"被迫成为反派赘婿第二季片花;李雨果被困于黑森林之中;与叶琼斗智斗勇守护粉红佳人！;被迫成为反派赘婿第二季片花全集高清;被迫成为反派赘婿第二季片花动漫;李雨果被困于黑森林之中;与叶琼斗智斗勇守护粉红佳人！;\",\"album_name\":\"被迫成为反派赘婿第二季片花\",\"is_original_code\":1,\"url_html5\":\"http://m.tv.sohu.com/v7250156.shtml?src=\",\"cid\":16,\"cate_code\":\"115118\",\"second_cate_name\":\"恋爱\",\"year\":\"2021\",\"score\":6.4,\"play_count\":150995,\"director\":\"浩克\",\"director_id\":\"1197590\",\"area\":\"大陆\",\"area_id\":30,\"language\":\"普通话\",\"aid\":9725365,\"publish_time\":\"2021-09-17\",\"update_time\":\"2021-09-17\",\"video_order\":0,\"latest_video_count\":2,\"total_video_count\":100,\"total_duration\":54.64,\"total_duration_double\":54.64,\"start_time\":0,\"end_time\":0,\"period\":\"\",\"site\":1,\"small_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_hor_1631178251550_286523773_2lD56_pic8.jpg\",\"ver_small_pic\":\"http://photocdn.tv.sohu.com/img/20210909/vrsa_ver_1631178251616_286523773_S4014_pic9.jpg\",\"tv_id\":286523773,\"crid\":0,\"definition\":{\"nor\":\"标清360P\",\"super\":\"超清720P\",\"high\":\"高清480P\"},\"clarity_file_size\":{\"nor_file_size\":\"3030237\",\"high_file_size\":\"6402824\",\"super_file_size\":\"9508497\"},\"original_video_url\":\"http://tv.sohu.com/v/MjAyMTA5MTcvbjYwMTA0Nzg5Ny5zaHRtbA==.html\",\"limit_play\":0,\"labelFirstId\":0,\"serious\":false,\"vHeight\":\"540\",\"vWidth\":\"960\",\"tvAge\":\"13岁-17岁;18岁以上\",\"tvPic169\":\"http://photocdn.tv.sohu.com/img/20210909/vrsExtend_1631178251709_286523773.jpg\",\"uploadFrom\":0,\"drm\":false}', 8, 1726105652306, '2022-05-12 16:21:11', '2025-02-10 20:36:12');
INSERT INTO `longvideo_media0`.`mis_video_org0` (`id`, `source`, `source_video_id`, `source_album_id`, `sid`, `vid`, `feature_type`, `time_lap`, `source_status`, `origin_info`, `process_times`, `version`, `create_time`, `update_time`) VALUES (1524665979690536962, 'sohu', '6501578', '9571006', '513591130227560448', '513598683170820096', '1', 2, 'del', '{\"vid\":6501578,\"fee\":0,\"is_download\":1,\"ip_limit\":1,\"video_length_type\":1,\"video_type\":1,\"video_big_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_hor_1597740746104_210323594_5Oja1_pic22.jpg\",\"ver_big_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_ver_1597740746210_210323594_2MV6o_pic7.jpg\",\"hor_big_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_hor_1597740746104_210323594_E2eD9_pic6.jpg\",\"ver_high_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_ver_1597740746210_210323594_890Fu_pic3.jpg\",\"hor_high_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_hor_1597740746104_210323594_Wt691_pic2.jpg\",\"tv_preview_pic\":\"http://photocdn.tv.sohu.com/img/pic/20230215/210323594_6501578_tv_H_PAL_2p_110319_6/210323594_6501578_tv_H_PAL_2p_110319_6_big_160_128_15.jpg\",\"tv_is_vr\":0,\"tip\":\"26分54秒\",\"video_desc\":\"《创新之路》是一档电视访谈节目，其通过选取中国本土优秀企业家代表，挖掘企业创新的生存土壤，寻找创新和企业发展相互成就的关系，在历史与现实两大维度中，探讨企业创新战略实施的价值和意义，为企业家改变思维观念、改变经营模式提供一种思路，展现当代企业家们转型改革的气魄和格局。《创新之路》将集结众多创新发展的企业大咖，融汇创新思想精髓，企业的未来必定离不开创新，但各行各业创新之路在何方，创新的规律是什么，创新的竞争会在哪些领域爆发。《创新之路》将带领大众共赴一场对创新、对企业未来的探寻。中国，定位中国，把中国制度、中国理论、中国道路、中国文化的优势和先进性讲清楚，传达出“民族自信”的相关核心精神。\",\"video_name\":\"曹宝军：科技让学习更简单\",\"video_sub_name\":\"\",\"keyword\":\"创新之路;曹宝军;科技让学习更简单;创新之路全集;创新之路纪录片;历史;文化;\",\"album_name\":\"创新之路\",\"is_original_code\":1,\"url_html5\":\"http://m.tv.sohu.com/v6501578.shtml?src=\",\"cid\":8,\"cate_code\":\"107101;107111\",\"second_cate_name\":\"历史;文化\",\"year\":\"2019\",\"score\":6.3,\"area\":\"默认地区\",\"area_id\":-1,\"language\":\"普通话\",\"aid\":9571006,\"publish_time\":\"2020-08-18\",\"update_time\":\"2023-02-15\",\"video_order\":6,\"latest_video_count\":221,\"total_video_count\":500,\"total_duration\":1614.0,\"start_time\":0,\"end_time\":0,\"period\":\"\",\"site\":1,\"small_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_hor_1597740746104_210323594_CfGw9_pic8.jpg\",\"ver_small_pic\":\"http://photocdn.tv.sohu.com/img/20200818/vrsa_ver_1597740746210_210323594_p99O2_pic9.jpg\",\"tv_id\":210323594,\"crid\":0,\"definition\":{\"nor\":\"标清360P\",\"high\":\"高清480P\"},\"clarity_file_size\":{\"nor_file_size\":\"31317389\",\"high_file_size\":\"80402133\"},\"original_video_url\":\"http://tv.sohu.com/v/MjAyMDA4MTgvbjYwMDg5MzQ3My5zaHRtbA==.html\",\"limit_play\":0,\"labelFirstId\":0,\"serious\":false,\"vHeight\":\"576\",\"vWidth\":\"720\",\"tvPic169\":\"http://photocdn.tv.sohu.com/img/20200818/vrsExtend_1597740746317_210323594.jpg\",\"drm\":false}', 20, 1678444808309, '2022-05-12 16:21:11', '2025-02-10 20:35:27');
INSERT INTO `longvideo_media0`.`mis_video_org0` (`id`, `source`, `source_video_id`, `source_album_id`, `sid`, `vid`, `feature_type`, `time_lap`, `source_status`, `origin_info`, `process_times`, `version`, `create_time`, `update_time`) VALUES (1524665979700047873, 'sohu', '6506585', '9571006', '513591130227560448', '667228368047120384', '1', 2, 'del', '{\"vid\":6506585,\"fee\":0,\"is_download\":1,\"ip_limit\":1,\"video_length_type\":1,\"video_type\":1,\"video_big_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_hor_1597984900703_210765728_J2516_pic22.jpg\",\"ver_big_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_ver_1597984900750_210765728_9F0cx_pic7.jpg\",\"hor_big_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_hor_1597984900703_210765728_1mfLp_pic6.jpg\",\"ver_high_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_ver_1597984900750_210765728_3U2k9_pic3.jpg\",\"hor_high_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_hor_1597984900703_210765728_805Rs_pic2.jpg\",\"tv_preview_pic\":\"http://photocdn.tv.sohu.com/img/pic/20230215/210765728_6506585_tv_H_PAL_2p_110331_6/210765728_6506585_tv_H_PAL_2p_110331_6_big_160_128_15.jpg\",\"tv_is_vr\":0,\"tip\":\"30分1秒\",\"video_desc\":\"《创新之路》是一档电视访谈节目，其通过选取中国本土优秀企业家代表，挖掘企业创新的生存土壤，寻找创新和企业发展相互成就的关系，在历史与现实两大维度中，探讨企业创新战略实施的价值和意义，为企业家改变思维观念、改变经营模式提供一种思路，展现当代企业家们转型改革的气魄和格局。《创新之路》将集结众多创新发展的企业大咖，融汇创新思想精髓，企业的未来必定离不开创新，但各行各业创新之路在何方，创新的规律是什么，创新的竞争会在哪些领域爆发。《创新之路》将带领大众共赴一场对创新、对企业未来的探寻。中国，定位中国，把中国制度、中国理论、中国道路、中国文化的优势和先进性讲清楚，传达出“民族自信”的相关核心精神。\",\"video_name\":\"刘博瑢：艺术空间的眠梦\",\"video_sub_name\":\"\",\"keyword\":\"创新之路;刘博瑢;艺术空间的眠梦;创新之路全集;创新之路纪录片;历史;文化;\",\"album_name\":\"创新之路\",\"is_original_code\":1,\"url_html5\":\"http://m.tv.sohu.com/v6506585.shtml?src=\",\"cid\":8,\"cate_code\":\"107101;107111\",\"second_cate_name\":\"历史;文化\",\"year\":\"2019\",\"score\":6.3,\"area\":\"默认地区\",\"area_id\":-1,\"language\":\"普通话\",\"aid\":9571006,\"publish_time\":\"2020-08-21\",\"update_time\":\"2023-02-15\",\"video_order\":8,\"latest_video_count\":221,\"total_video_count\":500,\"total_duration\":1801.0,\"start_time\":0,\"end_time\":0,\"period\":\"\",\"site\":1,\"small_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_hor_1597984900703_210765728_X5E0Q_pic8.jpg\",\"ver_small_pic\":\"http://photocdn.tv.sohu.com/img/20200821/vrsa_ver_1597984900750_210765728_T5456_pic9.jpg\",\"tv_id\":210765728,\"crid\":0,\"definition\":{\"nor\":\"标清360P\",\"high\":\"高清480P\"},\"clarity_file_size\":{\"nor_file_size\":\"32378946\",\"high_file_size\":\"87972738\"},\"original_video_url\":\"http://tv.sohu.com/v/MjAyMDA4MjEvbjYwMDg5NDgzMS5zaHRtbA==.html\",\"limit_play\":0,\"labelFirstId\":0,\"serious\":false,\"vHeight\":\"576\",\"vWidth\":\"720\",\"tvPic169\":\"http://photocdn.tv.sohu.com/img/20200821/vrsExtend_1597984900803_210765728.jpg\",\"drm\":false}', 20, 1678444808332, '2022-05-12 16:21:11', '2025-02-10 20:35:27');
INSERT INTO `longvideo_media0`.`mis_video_org0` (`id`, `source`, `source_video_id`, `source_album_id`, `sid`, `vid`, `feature_type`, `time_lap`, `source_status`, `origin_info`, `process_times`, `version`, `create_time`, `update_time`) VALUES (1524665979704242178, 'sohu', '6510348', '9571006', '513591130227560448', '515778040966500352', '1', 2, 'del', '{\"vid\":6510348,\"fee\":0,\"is_download\":1,\"ip_limit\":1,\"video_length_type\":1,\"video_type\":1,\"video_big_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_hor_1598258829472_211351318_8BER5_pic22.jpg\",\"ver_big_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_ver_1598258829531_211351318_3D90s_pic7.jpg\",\"hor_big_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_hor_1598258829472_211351318_729e3_pic6.jpg\",\"ver_high_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_ver_1598258829531_211351318_IO1X3_pic3.jpg\",\"hor_high_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_hor_1598258829472_211351318_BkGk0_pic2.jpg\",\"tv_preview_pic\":\"http://photocdn.tv.sohu.com/img/pic/20230215/211351318_6510348_tv_H_PAL_2p_110321_6/211351318_6510348_tv_H_PAL_2p_110321_6_big_160_128_15.jpg\",\"tv_is_vr\":0,\"tip\":\"30分1秒\",\"video_desc\":\"《创新之路》是一档电视访谈节目，其通过选取中国本土优秀企业家代表，挖掘企业创新的生存土壤，寻找创新和企业发展相互成就的关系，在历史与现实两大维度中，探讨企业创新战略实施的价值和意义，为企业家改变思维观念、改变经营模式提供一种思路，展现当代企业家们转型改革的气魄和格局。《创新之路》将集结众多创新发展的企业大咖，融汇创新思想精髓，企业的未来必定离不开创新，但各行各业创新之路在何方，创新的规律是什么，创新的竞争会在哪些领域爆发。《创新之路》将带领大众共赴一场对创新、对企业未来的探寻。中国，定位中国，把中国制度、中国理论、中国道路、中国文化的优势和先进性讲清楚，传达出“民族自信”的相关核心精神。\",\"video_name\":\"王赫：练就好口才\",\"video_sub_name\":\"\",\"keyword\":\"创新之路;王赫;练就好口才;创新之路全集;创新之路纪录片;历史;文化;\",\"album_name\":\"创新之路\",\"is_original_code\":1,\"url_html5\":\"http://m.tv.sohu.com/v6510348.shtml?src=\",\"cid\":8,\"cate_code\":\"107101;107111\",\"second_cate_name\":\"历史;文化\",\"year\":\"2019\",\"score\":6.3,\"area\":\"默认地区\",\"area_id\":-1,\"language\":\"普通话\",\"aid\":9571006,\"publish_time\":\"2020-08-24\",\"update_time\":\"2023-02-15\",\"video_order\":10,\"latest_video_count\":221,\"total_video_count\":500,\"total_duration\":1801.0,\"start_time\":0,\"end_time\":0,\"period\":\"\",\"site\":1,\"small_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_hor_1598258829472_211351318_A2511_pic8.jpg\",\"ver_small_pic\":\"http://photocdn.tv.sohu.com/img/20200824/vrsa_ver_1598258829531_211351318_u44aS_pic9.jpg\",\"tv_id\":211351318,\"crid\":0,\"definition\":{\"nor\":\"标清360P\",\"high\":\"高清480P\"},\"clarity_file_size\":{\"nor_file_size\":\"36775159\",\"high_file_size\":\"97124756\"},\"original_video_url\":\"http://tv.sohu.com/v/MjAyMDA4MjQvbjYwMDg5NTY3My5zaHRtbA==.html\",\"limit_play\":0,\"labelFirstId\":0,\"serious\":false,\"vHeight\":\"576\",\"vWidth\":\"720\",\"tvPic169\":\"http://photocdn.tv.sohu.com/img/20200824/vrsExtend_1598258829604_211351318.jpg\",\"drm\":false}', 20, 1678444504174, '2022-05-12 16:21:11', '2025-02-10 20:35:27');


use oppo_media;
CREATE TABLE `media_series` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增Id',
  `series_id` varchar(15) NOT NULL COMMENT '系列id',
  `name` varchar(32) NOT NULL COMMENT '系列名称',
  `program_type` varchar(32) NOT NULL DEFAULT '' COMMENT '节目类型',
  `count` int(11) NOT NULL DEFAULT '0' COMMENT '影片数量',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
  `relation_sids` text COMMENT '系列剧内包含节目',
  `operator_name` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1140 DEFAULT CHARSET=utf8mb4 COMMENT='节目系列表';

CREATE TABLE `media_series_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增Id',
  `sid` varchar(20) NOT NULL COMMENT '剧头id',
  `series_id` varchar(15) NOT NULL COMMENT '系列id',
  `title` varchar(32) NOT NULL COMMENT '节目标题',
  `show_name` varchar(32) NOT NULL DEFAULT '' COMMENT '显示名称',
  `poster` varchar(512) NOT NULL DEFAULT '' COMMENT '海报',
  `source` varchar(11) NOT NULL DEFAULT '' COMMENT '版权方',
  `source_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '源状态',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
  `is_bind` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否绑定，绑定：1，解绑：0',
  `sort` int(10) NOT NULL DEFAULT '9999' COMMENT '排序',
  `language` varchar(255) NOT NULL DEFAULT '' COMMENT '语言',
  `operator_name` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4600 DEFAULT CHARSET=utf8mb4 COMMENT='系列节目内容表';

CREATE TABLE `mis_virtual_program_relation` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `virtual_sid` varchar(20) DEFAULT NULL,
                                                `sid` varchar(20) DEFAULT NULL,
                                                `source` varchar(10) DEFAULT NULL,
                                                `copyright_code` varchar(10) DEFAULT NULL,
                                                `product_line` varchar(32) DEFAULT NULL COMMENT '适用产品线，mobile,tv',
                                                `status` tinyint(2) DEFAULT '0' COMMENT '关系状态-1：删除，0：失效，1：生效',
                                                `origin_status` tinyint(2) DEFAULT '0' COMMENT '源状态，0：失效，1：生效',
                                                `create_time` datetime DEFAULT NULL,
                                                `update_time` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `index_virtual_sid` (`virtual_sid`),
                                                KEY `index_real_sid` (`sid`)
) ENGINE=InnoDB AUTO_INCREMENT=88326 DEFAULT CHARSET=utf8;

INSERT INTO oppo_media.mis_virtual_program_relation
(id, virtual_sid, sid, source, copyright_code, product_line, status, origin_status, create_time, update_time)
VALUES(45498, '625490260595331072', '506319464187383808', 'sohu', 'sohu', 'mobile', -1, 0, '2021-06-23 11:47:12', '2023-12-30 10:37:15');
INSERT INTO oppo_media.mis_virtual_program_relation
(id, virtual_sid, sid, source, copyright_code, product_line, status, origin_status, create_time, update_time)
VALUES(49306, '654220126173995008', '625229331555176448', 'huashi', 'huashi', 'mobile', 1, 1, '2021-09-10 18:29:25', '2024-03-11 00:14:32');
INSERT INTO oppo_media.mis_virtual_program_relation
(id, virtual_sid, sid, source, copyright_code, product_line, status, origin_status, create_time, update_time)
VALUES(49307, '654220126173995008', '654220125049921536', 'mgmobile', 'mgmobile', 'mobile', 1, 1, '2021-09-10 18:29:25', '2024-01-16 00:06:31');


