<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="jedisPoolConfig" class="org.apache.commons.pool2.impl.GenericObjectPoolConfig">
        <property name="maxTotal" value="${redis.settingcache.pool.maxTotal}"/>
        <property name="maxIdle" value="${redis.settingcache.pool.maxIdle}"/>
        <property name="minIdle" value="${redis.settingcache.pool.minIdle}"/>
        <property name="maxWaitMillis" value="${redis.settingcache.pool.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.settingcache.pool.testOnBorrow}"/>
        <property name="testOnReturn" value="${redis.settingcache.pool.testOnReturn}"/>
        <property name="testWhileIdle" value="${redis.settingcache.pool.testWhileIdle}"/>
    </bean>

    <bean id="redisNodes" class="com.oppo.browser.app.framework.cache.HPHashSet">
        <constructor-arg index="0" value="${redis.settingcache.host}"/>
    </bean>

    <bean id="binaryJedisCluster" class="com.oppo.browser.app.framework.cache.BinaryJedisCluster"
          destroy-method="close">
        <constructor-arg index="0" ref="redisNodes"/>
        <constructor-arg index="1" value="${redis.settingcache.timeout}"/>
        <constructor-arg index="2" value="${redis.settingcache.max_redirections}"/>
        <constructor-arg index="3" ref="jedisPoolConfig"/>
    </bean>

    <bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean" scope="singleton">
        <property name="shared" value="true"/>
        <property name="configLocation">
            <value>classpath:ehcache.xml</value>
        </property>
    </bean>

    <bean id="myCache" class="com.oppo.browser.app.framework.cache.MultilevelCache">
        <constructor-arg index="0" ref="cacheManager"/>
        <constructor-arg index="2" value="com.heytap.longvideo.search.cache"/>
        <constructor-arg index="1" value="com.canal.client"/>
        <constructor-arg index="3" ref="binaryJedisCluster"/>
    </bean>

</beans>