########################settingcache############################
#连接池的最大连接数			
redis.settingcache.pool.maxTotal=1024

#最大空闲连接数
redis.settingcache.pool.maxIdle=16

#最小空闲连接数
redis.settingcache.pool.minIdle=4

#最大等待时间
redis.settingcache.pool.maxWaitMillis=60000

#在获取连接的时候检查有效性
redis.settingcache.pool.testOnBorrow=true

#在返回连接的时候检查有效性
redis.settingcache.pool.testOnReturn=true

#在空闲时检查有效性
redis.settingcache.pool.testWhileIdle=true

#redis集群的连接地址,多个IP/PORT用英文逗号分隔
# redis.settingcache.host=**************:9736,**************:9736
redis.settingcache.host=************:6380
#redis读取超时时间,单位秒
redis.settingcache.timeout=2000

#redis重试次数
redis.settingcache.max_redirections=5
########################sessioncache############################
#连接池的最大连接数			
redis.session.pool.maxTotal=1024

#最大空闲连接数
redis.session.pool.maxIdle=16

#最小空闲连接数
redis.session.pool.minIdle=4

#最大等待时间
redis.session.pool.maxWaitMillis=60000

#在获取连接的时候检查有效性
redis.session.pool.testOnBorrow=true

#在返回连接的时候检查有效性
redis.session.pool.testOnReturn=true

#在空闲时检查有效性
redis.session.pool.testWhileIdle=true

#redis集群的连接地址,多个IP/PORT用英文逗号分隔
redis.session.host=**************:9736,**************:9736

#redis读取超时时间,单位秒
redis.session.timeout=100

#redis重试次数
redis.session.max_redirections=3