#rocketmq配置
rocketmq.name-server.mediaJins=10.177.110.43:9876;10.177.110.42:9876;10.177.110.41:9876
rocketmq.topic.mediaJins=longvideo_media_change_20220321


rocketmq.name-server.mediaFinal=10.177.110.43:9876;10.177.110.42:9876;10.177.110.41:9876	
rocketmq.topic.mediaFinal=oppomobileSycBroadcastAlbumTopic

rocketmq.name-server=10.177.110.43:9876;10.177.110.42:9876;10.177.110.41:9876
rocketmq.producer.group=longvideo-search-rest
rocketmq.producer.send-message-timeout=3000

rocketmq.name-server.saveLog=10.177.110.43:9876;10.177.110.42:9876;10.177.110.41:9876
rocketmq.topic.operationLog=operationLogTopic

# TV端 媒資同步 mq配置
rocketmq.tv.media.sync.name-server=10.177.71.27:9876
rocketmq.tv.media.sync.topic=meiziUniverseProgramAlbumDeliveryTopic
rocketmq.tv.media.sync.consumer.group=tvMediaSyncGroup
rocketmq.tv.media.sync.consumer.instanceName=search-tvMediaSyncConsumer

# 非合作内容方 媒资同步 mq配置
rocketmq.third-party-media.name-server=10.177.65.221:9876;10.177.65.220:9876;10.177.65.223:9876
rocketmq.third-party-media.topic=unofficial_media_topic
rocketmq.third-party-media.group=unofficial_media_consumer_group
rocketmq.third-party-media.instance-name=unofficial_media_instance_name
rocketmq.third-party-media.maxReconsumeTimes=5
rocketmq.third-party-media.threadMax=128
rocketmq.third-party-media.threadMin=20

# 非合作内容方死信队列 mq消费者配置
rocketmq.third-party-media.deadLetter.name-server=10.177.65.221:9876;10.177.65.220:9876;10.177.65.223:9876
rocketmq.third-party-media.deadLetter.topic=%DLQ%unofficial_media_topic
rocketmq.third-party-media.deadLetter.group=%DLQ%unofficial_media_consumer_group
rocketmq.third-party-media.deadLetter.instance-name=%DLQ%unofficial_media_instance_name

# 云合榜单数据推中台 kafka配置
kafka.yunheRank.servers=dgtest-content-test-kafka-1.global.yyy4fp.kafka.oppo.test:9095,dgtest-content-test-kafka-1.global.yyyjfp.kafka.oppo.test:9095,dgtest-content-test-kafka-1.global.yyywfp.kafka.oppo.test:9095
kafka.yunheRank.topic=trace-news-resource-hot-info-topic
kafka.yunheRank.key=YunHeChart

# 全网节目变更 rocketmq生产者配置
unofficial.album.rocketmq.name-server=tv-common-mq.rocketmq.oppo.test:9876
unofficial.album.rocketmq.producer.group=longvideo-search-rest-unofficial-album
unofficial.album.rocketmq.instance.name=longvideo-search-rest-unofficial-album-instance
unofficial.album.change.sync.topic=unofficial-album-change-sync-topic

# ???????????kafka??
third.party.external.search.servers=dgtest--st-kafka-vedio-list-ad-kafka.proxy.kafka.oppo.test:9095
third.party.external.search.topic=standard-album-simple-info-sync-topic
third.party.external.search.client.id=external_search_test_client_id
third.party.external.search.key=simpleInfoSync
third.party.external.search.filter.list=["douban"]
third.party.external.search.standard.filter.list=["movie","tv","show"]

# 爱奇艺移动端媒资同步es 消费端配置
rocketmq.iqiyi.media.sync.name-server=tv-common-mq.rocketmq.oppo.test:9876
rocketmq.iqiyi.media.sync.topic=iqiyimobile_media_sync_topic
rocketmq.iqiyi.media.sync.consumer.group=iqiyiMediaSyncGroup
rocketmq.iqiyi.media.sync.consumer.instanceName=search-iqiyiMediaSyncConsumer