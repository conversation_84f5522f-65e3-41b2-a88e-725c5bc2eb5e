# 接入芒果AppVersion
mg.appVersion=4
# 接入乐视AppVersion
letv.appVersion=6

yst.appVersion=7

miguOlympic.appVersion=8

#接入风行长视频和微迪欧
funshionlongvideoAndWeidiou.appVersion=10

# 单点购买版本
funshion.singleBuy.appVersion=3

searchCard.opt.version=80900
taskUnlockEpisodeExp.version=71300

#虚拟节目优先级
copyRight.priority={"video_vip":["huashi","letv","senyu","mgmobile","yst","funshion_lv","weidiou","sohu","youkumobile"],"mongo_video_vip":["mgmobile","huashi","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"],"video_vip,mongo_video_vip":["huashi","mgmobile","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"],"mongo_video_vip,video_vip":["huashi","mgmobile","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"],"default":["huashi","mgmobile","letv","senyu","yst","funshion_lv","weidiou","sohu","youkumobile"]}


filter.priority=["sohu","huashi","senyu","funshion","mgmobile","letv","yst","youkumobile","funshion_lv","weidiou"]

hot.title=["长津湖","战狼","李焕英","哪吒之魔童降世","流浪地球","唐人街探案","复仇者联盟","红海行动","美人鱼","我和我的祖国","八佰","我不是药神","中国机长","我和我的家乡","速度与激情","西虹市首富","捉妖记","羞羞的铁拳","疯狂的外星人","海王","变形金刚","前任","毒液：致命守护者","功夫瑜伽","飞驰人生","阿凡达","烈火英雄","侏罗纪世界","寻龙诀","西游伏妖篇","港囧","姜子牙","少年的你","变形金刚","疯狂动物城","我和我的父辈","魔兽","夏洛特烦恼","送你一朵小红花","芳华","侏罗纪世界","蜘蛛侠","头号玩家","人在囧途","西游降魔篇","西游记","湄公河行动","长城","心花路放","煎饼侠","澳门风云","乘风破浪","盗墓笔记","老炮儿","绝地逃亡","智取威虎山","十二生肖","从你的全世界路过","叶问","北京遇上西雅图","大闹天竺","无问西东","天将雄师","致我们终将逝去的青春","私人订制","画皮","狼图腾","爸爸去哪儿","环太平洋","悟空传","鬼吹灯之九层妖塔","寒战","唐山大地震","分手大师","让子弹飞","情圣","后会无期","金陵十三钗","使徒行者","狄仁杰","匆匆那年","追龙","大鱼海棠","杀破狼","龙门飞甲","熊出没","中国合伙人","警察故事","三生三世十里桃花","妖猫传","小时代","杀破狼","同桌的你","缝纫机乐队","甄嬛传","尚食","琅琊榜","红楼梦","三国演义","水浒传","知否知否","大明王朝","武林外传","庆余年","人民的名义","伪装者","仙剑奇侠传","长安十二时辰","亮剑","司藤","我的团长我的团","陈情令","爱情公寓","天下第一","三生三世","士兵突击","一人之下","欢乐颂","家有儿女","赘婿","神探狄仁杰","平凡的世界","少年包青天","闯关东","斗罗大陆","大明风华","完美世界","小猪佩奇","汪汪队","海底小纵队","喜洋洋与灰太狼","熊出没","猫和老鼠","宝宝巴士","贝乐虎","超级宝贝","爆笑虫子","奥特曼","海绵宝宝"]



run_task_ip=**************
media.datebaseEnd=0
media.tableEnd=0

mediaJdbc.url=***************************************************************************************************************
mediaJdbc.username=jupiter
mediaJdbc.password=jupiter

tvAlbum.search.url=http://manage-television-test.wanyol.com/media-rest/mediaAlbum/pageList?f=json
tvVideo.search.url=http://manage-television-test.wanyol.com/media-rest/mediaVideo/searchByPage?f=json
tvEpisode.search.url=http://manage-television-test.wanyol.com/media-rest/mediaAlbum/getEpisodeByPage?f=json
tvTrailer.search.url=http://manage-television-test.wanyol.com/media-rest/mediaAlbum/getAlbumPreview?f=json
tvAlbum.aqiyi.search.url=${GOBLIN_MOCKSERVER_ENDPOINT}/meizi/misIqiyiProgram/pageList
tvAlbum.tencent.search.url=${GOBLIN_MOCKSERVER_ENDPOINT}/meizi/misTencentProgram/pageList
#系列剧正则
series.regex=^.*?((第)(.*?)(季|期)|[1-9])

#全网搜播版本
third.search.version=2
#全网搜播配置（渠道顺序即优先级），switch 0代表不允许搜索 1代表搜索干预内容 2代表搜索全部内容; jump 0代表APP打开三方H5页 1代表浏览器打开百度搜索页 2代表浏览器打开三方H5页 3代表APP打开百度搜索页
third.search={"tencent":{"switch":2,"jump":2},"iqiyimobile":{"switch":2,"jump":0},"iqiyi":{"switch":2,"jump":2},"youku":{"switch":2,"jump":2},"douban":{"switch":2,"jump":2},"cupfox":{"switch":2,"jump":2},"keke":{"switch":2,"jump":2}}
#全网搜播机型（0为直板机），value代表是否允许搜索（0否1是）
third.search.switch={"0": 1,"1": 0,"2": 0,"3": 0}
#全网搜节目类型
third.search.range=["comic","tv","show","doc","movie","kids"]
#全网搜豆瓣版权优先级
third.search.douban=["tencent","iqiyi","youku","mgtv","sohu","bilibili"]
browser.search.deepLink=heytapbrowser://search/resultPage?search_content=%s&browser_partner=yolisearch
browser.url.deepLink=heytapbrowser://webpage/view?url=%s
iqiyi.search.url=https://m.iqiyi.com/search.html?source=input&key=%s
baidu.search.url=https://m.baidu.com/s?word=%s
#全网搜内容排序降权系数
third.search.score=0.7

search.feedback.option={"1":"搜不到我想要的","2":"搜索的结果排序不合理","3":"搜索的结果不够丰富","4":"搜索的结果不能直接播放","5":"希望增加筛选、排序等功能","9":"其他问题"}
search.feedback.webhook=https://mtp.myoas.com/gateway/robot/webhook/send

album.detail.deepLink=yoli://yoli.com/yoli/longvideo/videodetail?linkValue=%s
upgrade.page.deeplink=https://dhfs-test-cpc.wanyol.com/userfiles/cms/video_versio_altgradigon/index.html?

user.middle.relation.secretKey=longvideo
subscribe.getList.url=${GOBLIN_MOCKSERVER_ENDPOINT}/v1/relation/relationList

default.recommend.switch=1
outside.search.switch={"iqiyi":1,"youku":1,"tencent":1}
outside.search.size=15
outside.search.all.switch=1
outside.search.likeAppSearch=0
outside.search.minVersion=41900
outside.search.minVersion.str=4.19.0

#算法
recommend.algorithm.url=${GOBLIN_MOCKSERVER_ENDPOINT}/v2/recommend/lvAlgorithm/forward
recommend.algorithm.timeout=1000
recommend.algorithm.route=bjlongvideoprod
recommend.algorithm.cid=feeds
recommend.algorithm.bidlst=B1655276899013
default.recommend.algorithm.docid=cp_00000373
default.recommend.algorithm.num=12

recommend.minVersion=41000
########### deeplLink跳转地址模板 ###############
deeplink.template={"1": "yoli://yoli.com/yoli/longvideo/videodetail?linkValue=%s","4": "yoli://yoli.com/yoli/longvideo/videoSpecial?linkValue=%s","12": "yoli://yoli.com/yoli/h5?url=%s","17": "yoli://yoli.com/yoli/longvideo/videochannel?linkValue=%s","27": "yoli://yoli.com/live/detail?sid=%s&eid=%s","84": "yoli://yoli.com/yoli/longvideo/pagegroupchannel","95": "yoli://yoli.com/yoli/longvideo/videodetail?videoEid=%s","96": "yoli://yoli.com/yoli/longvideo/videodetail?videoEid=%s","98": "yoli://yoli.com/yoli/longvideo/videodetail?linkValue=%s","102": "yoli://yoli.com/yoli/longvideo/videochannel?linkValue=%s","109": "yoli://yoli.com/yoli/longvideo/videocategory?%s","1001": "yoli://yoli.com/yoli/longvideo/rank?linkValue=%s","10000": "%s","1002":"yoli://yoli.com/catch/longvideo/subscribeview?code=%s","10009":"yoli://yoli.com/catch/longvideo/catchingview?code=%s&headline=你正在追","1010":"yoli://yoli.com/sheet/longvideo/sheetview?linkValue=%s","168":"yoli://yoli.com/detail_feed_video_yoli/mix_video?fromId=youli_highlights&channelSource=youli&scene=1&poolCode=%s&pageCode=%s&lvChannelName=%s","10040":"yoli://yoli.com/YoliSearch/search/longSeriesDetail?code=%s&title=%s","10030":"yoli://yoli.com/YoliSearch/search/longTagDetail?code=%s&title=%s","10020":"yoli://yoli.com/YoliSearch/search/longRecommendDetail?code=%s&title=%s","1003":"yoli://yoli.com/yoli/bridge?linkValue=%s","1012":"yoli://yoli.com/yoli/bridge?linkValue=%s","10050":"yoli://yoli.com/YoliSearch/search/longTagDetail?type=filmmaker&code=%s&title=%s&sortType=%s"}
search.tag.sortType.list=[{"sortType":1,"sortName":"最热排序"},{"sortType":2,"sortName":"最新排序"}]
search.sort.list=[{"sortType":1, "sortName":"综合"},{"sortType":2, "sortName":"最新"},{"sortType":3, "sortName":"免费优先"}]
search.filter.list=tv,movie,show,kids,comic,doc,music
series.minVersion=70200
recommend.contents.max.pageIndex=10

subscribe.timeLimit=31536000000
support.scene.switch=1
support.scene.list=["2*N","3*N","loopBanner","material", "meizi", "landingPage"]
search.hot.keyword.switch=0

# 未成年模式内容池
minors.content.pool.map={"1":"cp_00000486","2":"cp_00000487","3":"cp_00000488","4":"cp_00000489","5":"cp_00000490","default":"cp_00000491"}

# ??????????
album.max.count=2100
# ????sid?????
album.max.limit=500

# 电视端媒资同步非合作内容库剧头索引 批量插入大小
unofficial.album.sync.batchSize=1000
# 电视端媒资同步非合作内容库剧头索引 失败最大重试次数
unofficial.album.sync.retryTimes=5
# 电视端媒资同步非合作内容库剧头索引 允许源
unofficial.album.sync.sources=tencent,iqiyi
# 电视端媒资同步非合作内容库剧头索引 全量查询滚动搜索时间 分钟
unofficial.album.sync.scrollTime=10

#根据titile搜索unofficalAlbumEs的最大节目个数
unofficial.album.max.number.for.similar=5

# 云合数据爬取url
yunhe.url=http://39.106.148.57:9667
# 云合接口鉴权
yunhe.name=QgghhJwsNHYJIYQQQsjJQmOXEEIIIYScLDR2CSGEEELIyUJjlxBCCCGEnCwiVBORw0KGgoAAAANSUhEUgAAArsAAAKkCAYAAAAA4fMAAAgAElEQVR4nOydPYgr2ZnptDJssA04WFhwsUgdNDwOeYJdS8MZSJ2H7bCzEhtJDjq7YWcdrBQtUnbD6cB7k
yunhe.pwd=fepXPrrrwEAPrRjDLX5yaD8Y9xL8y7gz3MwDgn7pnDzn8LlW8KIxvfneDEEIIIeQ4qXUZQlzvn85x3XffbX99913W0NXDEsIIYQQQkgIajV2PvsMVv3bWjIJ2h3jP1ohDSFvVcibYVUSmK5mGAnnqqc1QrT2UvrqFmN8vKbWUoneXxR7NA9YkUs1Elz

# 短剧搜索版本
duanju.search.version=80700
# 短剧搜索接口
duanju.search.url=https://xifan-test.wanyol.com/xifan/search/getSearchList
# 短剧播放地址
duanju.play.url=yoli://yoli.com/shortDrama/detail?dramaId=$s&source=$s
# 短剧黑名单(source_duanjuId)
duanju.sourceId.blacklist=source_xx,source_xxx
# ??????????url
relation.getstatus.url=http://ivideo.test-browser.wanyol.com/v1/relation/relationList
relation.timeout=1500
search.textmatch.radio=90
# ????????url??
douban.task.commit.path=/video/douban/crawler/searchNameForDouBanDelayExecute
# ????????????????
yunhe.douban.task.commit.num=100

search.card.button.num=2
default.vertical.icon=url
tag.deepLink.prefix=yoli://yoli.com/findfilm/longvideo/findfilm

# 精准匹配开关 true：开，false：关
outside.search.exact.match.switch={"magazine":true,"breeno":false}

# 对外 浏览器全网搜开关
outside.browser.search.all.web.switch=false

# 对外搜素--全网搜默认推荐语
outside.search.default.brief={"tv":["爆款剧集，一键直达","全网好剧，等你来看","剧迷专属，好戏连台","宝藏好剧，下饭必备","热门好剧，即刻开追"],"movie":["光影盛宴，即刻启程","全网佳片，立即去看","佳片荟萃，一键解锁","热片速递，先睹为快","宝藏影片，等你来看"],"show":["爆款综艺，一键畅看","综艺狂欢，即刻启程","全网热综，等你来看","汇聚全网，综艺畅看"],"kids":["童趣影视，欢乐起航","萌趣剧场，一键开启","动画乐园，快乐放映","动画时光，快乐成长","少儿剧场，快乐开演"],"comic":["热播番剧，立即去追","全网动漫，一键畅看","漫影集结，高能来袭","高能番剧，一键追番","全网番剧，即刻去追"],"other":["全网热门影视一键畅享","全网影视随心看","全网好片一键直达","全网好片等你来看"]}

#视频APP版本低于8.7，跳转到搜索结果页面，并把query词带入到页面内
outside.search.yoli.search.deeplink=yoli://yoli.com/YoliSearch/search?query=%s&openfrom=%s&showSplashAd=0
#AppV8.7及以上版本,跳转非在库内容详情页,下发广告
outside.search.non.instock.detail.deeplink=yoli://yoli.com/detail/longvideo/outofstockvideodetail?linkValue=%s&title=%s&openfrom=%s&showSplashAd=0

#端外全网搜播配置（渠道顺序即优先级），switch 0代表不允许搜索 1代表搜索干预内容 2代表搜索全部内容; jump 0代表APP打开三方H5页 1代表浏览器打开百度搜索页 2代表浏览器打开三方H5页 3代表APP打开百度搜索页
outside.third.search={"tencent":{"switch":2,"jump":2},"iqiyimobile":{"switch":2,"jump":0},"iqiyi":{"switch":2,"jump":2},"youku":{"switch":2,"jump":2},"douban":{"switch":2,"jump":2},"cupfox":{"switch":1,"jump":2},"keke":{"switch":2,"jump":2}}

# 剧头信息对外同步的视频类型
album.sync.programTypes=comic,show,movie,kids,doc,tv
# 剧头信息建议对外同步的源
magazine.suggest.sync.source=tencent,iqiyi,youku,douban

filter.douBan.top.apiKey=["DEq0g6jG1Vh6UgdU"]
#跳爱奇艺主端的dp地址
outside.search.iqiyiMmobile.deepLink=iqiyi://mobile/player?ftype=27&subtype=aqyOPP_30551629_27&aid=%s&tvid=%s
#对外 小布全网搜出移动端爱奇艺的开关
outside.breeno.search.iqiyiMmobile.enable=true


# 版权方图标
source.icon={"oppo":"https://activity-cpc.heytapimage.com/202110/22/10/afdf33131b58014e03f33bf80e717b72.png","tencent":"https://activity-cpc.heytapimage.com/2025/03/26/126e8689035673c5e3e2d388ac3861f6.png","iqiyi":"https://activity-cpc.heytapimage.com/2025/03/26/1638465c30179236b92be36e9c3cdbfe.png","youku":"https://activity-cpc.heytapimage.com/2025/03/26/d828458d73ff6f6992aa433cf1a4baa1.png","bilibili":"https://activity-cpc.heytapimage.com/2025/03/26/0ea164d83a66947772a76ec03f007259.png","sohu":"https://activity-cpc.heytapimage.com/2025/03/26/ac91991036ee625bba279fc2fee36ceb.png"}
# 视频APP下载链接
app.download.page={"20":"oaps://mk/dt?pkg=com.coloros.yoli","40":"oaps://mk/dt?pkg=com.heytap.yoli"}
# 视频APP升级页
app.upgrade.page=yoli://yoli.com/yoli/h5?url=https://actcpc.heytapimage.com/oh5/2214/1/index.html
# 浏览器播放场景下发剧集数量
browser.play.episode.startNum=8
browser.play.episode.endNum=3
browser.play.episode.descNum=5