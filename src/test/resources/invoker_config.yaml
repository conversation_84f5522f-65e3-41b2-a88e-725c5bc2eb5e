httpChannelConfig:
  httpClientType: 'ESA_HTTP_CLIENT'
  # 配置dns缓存，强烈建议配置此项,默认DEFAULT
  socketAddressResolverType: 'CACHED_DNS'
  httpClientName: 'HTTP_CAHNNEL_CLIENT'
  # 获取连接线程的超时时间
  connectRequestTimeout: 200
  # 建立连接超时的时间
  connectTimeout: 200
  # socket 读数据超时时间
  readTimeout: 1000
  # 轮训获取结果及超时检测的时间间隔
  #selectIntervalTime: 100
  connectionPoolSize: 1000
  connectPoolWaitQueueSize: 102400
  maxResponseSizeKB: 20480
  socketSendBuffer: 8192
  socketReceiveBuffer: 8192
  enhanceHeaderFromTraceAttachment: true
  treatAsExceptionHttpCodes:
  - '404'
  - '502'
  - '504'

endpointFacotryConfig:
  corePoolSize: 10
  maximumPoolSize: 10
  keepAliveTime: 120000
  extendMsgReceiverThreshold: 8

# 插件配置，可选
requestFilterConfig:
  requestFilterSwitch:
    RequestAttachmentBaseHostRewriteRequestFilter: false  #路由插件开关

responseFilterConfig:
  responseFilter:
    LogAccessFilter: true   #http requet response 日志记录插件开关，默认为false
    RattlesnakeLogUpdateFilter: false	#httpChannel配置全链路跟踪系统自动上报请求和响应数据的开关，默认为false