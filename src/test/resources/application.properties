restlight.server.port=${paas_port_1}
restlight.server.core-business-thread-count=${restlight.server.core-business-thread-count}
restlight.server.max-business-thread-count=${restlight.server.max-business-thread-count}

heracles.bootstrap.enabled=true	

spring.main.allow-bean-definition-overriding=true

spring.elasticsearch.rest.uris=http://localhost:9200
spring.elasticsearch.rest.username=elastic
spring.elasticsearch.rest.password=Xa7Ic6EhOPD4
spring.elasticsearch.rest.read-timeout=2s


esa.rpc.application.name=longvideo-search-rest
esa.rpc.registry.protocol=esa
#esa.rpc.registry.protocol=euler-multi
#ͬʱע�ᵽesa�Լ�polarisע������
#esa.rpc.registry.parameter.service-registry=esa,polaris
esa.rpc.registry.parameter.service-registry=esa
#ʹ��esaע�����ķ��ַ���
esa.rpc.registry.parameter.reference-registry=esa

esa.rpc.consumer.protocol=dubbo
esa.rpc.consumer.check=false
esa.rpc.consumer.cluster=failover
esa.rpc.consumer.retryableErrors=ALL
esa.rpc.consumer.route-mode=all
esa.rpc.consumer.connections=10
esa.rpc.consumer.parameter.writeBufferHighWaterMark=8000000
esa.rpc.consumer.parameter.ROUTING_DB=cms
# esa.rpc.consumer.group=${esa_rpc_provider_group:dev}
esa.rpc.consumer.enableEnhance=true
esa.rpc.consumer.routeTag=default

#cloudjob.namespace=longvideo-search-cloudjob
#cloudjob.psaAppKey=E2D75B02A9DB4E13ADB9314DB83EE9A1
#cloudjob.secretKey=D6DB3ED14716D94C5273CB1C69C51D2A
#cloudjob.confUrl=http://10.177.34.111:8143/cloudjobconf/config/pullCloudjobConf

liteflow.rule-source=liteflow/searchByKeyword.xml