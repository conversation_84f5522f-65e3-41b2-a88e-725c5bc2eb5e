<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" name="oneplustv-interact-rest" packages="">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%5p [%d] [%t] %c{1.}.%M(%F:%L) - %m%n" />
        </Console>
        <RollingFile name="RollingFile" immediateFlush="true" append="true"
                     fileName="d:/tmp/search.log" filePattern="d:/tmp/%d{yyyy-MM-dd}/search.log-%i.log.gz">
            <PatternLayout>
                <Pattern>%5p [%d] [%t] %c{1.}.%M(%F:%L) - %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="128 MB" />
            </Policies>
            <DefaultRolloverStrategy max="1024">
                <DeleteEmptyDirectory basePath="d:/tmp" maxDepth="2" followLinks="true">
                    <IfLastModified age="7d" />
                </DeleteEmptyDirectory>
            </DefaultRolloverStrategy>
        </RollingFile>
        <Console name="updatelog" target="SYSTEM_OUT">
            <PatternLayout pattern="%5p [%d] [%t] %c{1.}.%M(%F:%L) - %m%n" />
        </Console>
        <Console name="ugc_update_log" target="SYSTEM_OUT">
            <PatternLayout pattern="%5p [%d] [%t] %c{1.}.%M(%F:%L) - %m%n" />
        </Console>
    </Appenders>
    <Loggers>
        <Root level="ERROR">
            <AppenderRef ref="Console" />
        </Root>
        <logger name="com.heytap.*" level="INFO" />
        <logger name="updatelog" additivity="false" level="INFO" includeLocation="true">
            <AppenderRef ref="updatelog" />
        </logger>
        <logger name="ugc_update_log" additivity="false" level="INFO" includeLocation="true">
            <AppenderRef ref="ugc_update_log" />
        </logger>
    </Loggers>
</Configuration>