#会员接口appKey
vipInfo.appKey=NBggDyzc7i8w5WLwGH6XdA
#会员接口秘钥
vipInfo.appSecret=a4gX9NrhT0oQE6ZQFQFqXQ==
#会员信息查询接口
# vipInfo.url=http://vip3-open-test.wanyol.com/api/user/independent-vip/query-vip-info
# vipInfo.url=http://vip-open-test.wanyol.com/api/user/independent-vip/query-vip-info111
# vipInfo.url=http://vip-open-test.wanyol.com/api/user/independent-vip/query-vip-info
vipInfo.url=http://vod-mobile-test.wanyol.com/v1/vip/api/user/independent-vip/query-vip-info
#mock地址
#vipInfo.url=http://**************:48812/api/user/independent-vip/query-vip-info

vipInfo.timeout=1000
#会员商品查询接口
#vipProduct.url=http://**************:48812/api/independent-vip/query-first-product
# vipProduct.url=http://vip3-open-test.wanyol.com/api/independent-vip/product/query-first-product
vipProduct.url=http://vip-open-test.wanyol.com/api/independent-vip/product/query-first-product
vipProduct.timeout=1000
#会员购买页面链接
#vipPurchasePage.url=http://uc3-h5-test.wanyol.com/vip/video_pay.html?isGradualToolbar=false&isTranslucentBar=false&isHideToolbar=false
vipPurchasePage.url=http://uc-h5-test.wanyol.com/vip/video_pay.html?isGradualToolbar=false&isTranslucentBar=false&isHideToolbar=false
# vipPurchasePage.url=https://uc3-h5-test.wanyol.com/vipApp/pay/index.html?isHideToolbar=true&payVipCode=video_vip&source=

#会员身份查询接口
# vipIdentityInfo.url=http://vip3-open-test.wanyol.com/api/user/query-vip-identity
# vipIdentityInfo.url=http://vip-open-test.wanyol.com/api/user/query-vip-identity
vipIdentityInfo.url=http://vod-mobile-test.wanyol.com/v1/vip/api/user/independent-vip/query-vip-info
# 单查询底层改为多查询的开关；发布前为false，发布成功后才改为true
vipIdentityInfo.url.useNewUrl=true
vipIdentityInfo.timeout=1000
#会员身份查询接口appKey
vipIdentityInfo.appKey=HWzvHQiPJStXLJbb5RFAye
#会员身份查询接口秘钥
vipIdentityInfo.appSecret=WP7i7QR5ovigT6z5AcHO1g==

#芒果会员购买页面链接
#mongo_video_vip.vipPurchasePage.url=http://uc3-h5-test.wanyol.com/vip/mongoVideo_pay.html?isGradualToolbar=false&isTranslucentBar=false&isHideToolbar=false&sourceId=reg_btn_float&sourcePage=mongo_video_vip
mongo_video_vip.vipPurchasePage.url=https://uc-h5-test.wanyol.com/vipApp/pay/index.html?isHideToolbar=true&payVipCode=mongo_video_vip&source=

#非会员剧集预览时间
vipInfo.previewSeconds=300
#会员推广文案,后面废弃
vipInfo.promotionCopy=试看 5 分钟，{0} 元开通会员查看完整版

# 繁星对接配置
appKey.fanXin=3L3XeQAFxanTT2M6irJELr
appSecret.fanXin=l1S8GB2CA781DVDItNTTPA==
# vipPullMessage.url=https://uc3-market-gateway-client-bj-test.wanyol.com/open-api/reach/pull-message
vipPullMessage.url=http://uc-market-gateway-open-test.wanyol.com/open-api/reach/pull-message
vipPullMessage.timeout=1000

#VIP试看提示1
vipInfo.copy.previewTips1=试看 5 分钟，{0} 元开通欢太影视VIP可完整观看
#VIP试看提示2
vipInfo.copy.previewTips2={0} 元开欢太影视VIP完整观看
#VVIP试看结束提示1
vipInfo.copy.previewFinishTips1=试看结束，开通欢太影视VIP可完整观看
#VVIP试看结束提示2
vipInfo.copy.previewFinishTips2=还能享海量热门影视
#付费单片试看提示1
vipInfo.copy.singleMoviePreviewTips1=试看 5 分钟，购买本片可完整观看
#付费单片试看提示2
vipInfo.copy.singleMoviePreviewTips2=购买本片看完整版
#付费单片试看结束提示
vipInfo.copy.singleMoviePreviewFinishTips=应版权方要求，本片需购买后看完整版
#开通会员可跳过广告提示语
vipInfo.copy.skipAdTips=开影视VIP免广告
#会员身份播放会员剧集提示1
vipInfo.copy.vipViewPayTips1=您的欢太影视VIP还有 {0} 天过期\#点击立即续费\#
#会员身份播放会员剧集提示2
vipInfo.copy.vipViewPayTips2=当前为欢太影视VIP，已为您解锁完整内容
#会员身份播放免费剧集提示
vipInfo.copy.vipViewFreeTips1=当前为欢太影视VIP，已为您自动跳过广告
#VIP试看提示1 新协议
vipInfo.copy.previewTips1V2=试看 5 分钟，\#{0} 元开通欢太影视VIP可完整观看\#
#试看提示1 新字段 替换 previewTip1
vipInfo.copy.previewTips1Expired7Days=您的欢太影视VIP已过期，当前可试看 5 分钟


#芒果VIP试看提示1
mongo_video_vip.vipInfo.copy.previewTips1=试看 5 分钟，{0} 元开通芒果专区VIP可完整观看
#芒果VIP试看提示2
mongo_video_vip.vipInfo.copy.previewTips2={0} 元开芒果专区VIP完整观看
#芒果VVIP试看结束提示1
mongo_video_vip.vipInfo.copy.previewFinishTips1=试看结束，开通芒果专区VIP可完整观看
#芒果VVIP试看结束提示2
mongo_video_vip.vipInfo.copy.previewFinishTips2=还能享海量热门影视
#芒果付费单片试看提示1
mongo_video_vip.vipInfo.copy.singleMoviePreviewTips1=试看 5 分钟，购买芒果本片可完整观看
#芒果付费单片试看提示2
mongo_video_vip.vipInfo.copy.singleMoviePreviewTips2=购买芒果本片看完整版
#芒果付费单片试看结束提示
mongo_video_vip.vipInfo.copy.singleMoviePreviewFinishTips=应版权方芒果要求，本片需购买后看完整版
#芒果开通会员可跳过广告提示语
mongo_video_vip.vipInfo.copy.skipAdTips=开芒果专区VIP免广告
#会员身份播放会员剧集提示1
mongo_video_vip.vipInfo.copy.vipViewPayTips1=您的芒果专区VIP还有 {0} 天过期\#点击立即续费\#
#会员身份播放会员剧集提示2
mongo_video_vip.vipInfo.copy.vipViewPayTips2=当前为芒果专区VIP，已为您解锁完整内容
#会员身份播放免费剧集提示
mongo_video_vip.vipInfo.copy.vipViewFreeTips1=当前为芒果专区VIP，已为您自动跳过广告
#芒果VIP试看提示1
mongo_video_vip.vipInfo.copy.previewTips1V2=试看 5 分钟，\#{0} 元开通芒果专区VIP可完整观看\#
#试看提示1 新字段 替换 previewTip1
mongo_video_vip.vipInfo.copy.previewTips1Expired7Days=您的芒果专区VIP已过期，当前可试看 5 分钟

# 欢太影视VIP 名称
video_vip.productName=欢太影视VIP
# 芒果专区VIP 名称
mongo_video_vip.productName=芒果专区VIP

#账号用户接口appKey
userInfo.appKey=U2Z69fskh7JDgHZgvfKi2B
#账号用户接口秘钥
userInfo.appSecret=hD4EYUEqwoCB23hZoONa7g==
#账号用户信息查询接口
userInfo.url=https://uc-accountgateway-test.wanyol.com/user/detail-info
userInfo.timeout=1000
#账号用户安全信息查询接口
user.securityInfo.url=https://uc-accountgateway-test.wanyol.com/user/security-info
user.securityInfo.timeout=1000


# 签约会员自动续费 创建签约订单
# vipSignOrder.url=https://vip3-open-test.wanyol.com/api/customize-order/submit
vipSignOrder.url=https://vip-open-test.wanyol.com/api/customize-order/submit
# 改为调视频的接口
# vipSignOrder.url=https://vod-mobile-test.wanyol.com/v1/vip/api/customize-order/submit
vipSignOrder.timeout=3000

# 会员sdk打开h5的dp前缀
 heytap.vipOpenProtocol=https://dhfs.heytapimage.com/userfiles/uploads/jslib/videovip_member_agreement_dcd3b087.html
 #欢太影视会员续费协议
 heytap.vipRenewProtocol=https://dhfs.heytapimage.com/userfiles/uploads/jslib/videovip_autorenewal_agreement_774728e2.html

 #芒果会员开通协议
 mongo.vipOpenProtocol=https://dhfs.heytapimage.com/userfiles/uploads/jslib/mangovip_member_agreement_5b8d1c79.html
 #芒果会员续费协议
 mongo.vipRenewProtocol=https://dhfs.heytapimage.com/userfiles/uploads/jslib/mangovip_autorenewal_agreement_cf45b2c6.html

 # 会员sdk打开h5的dp前缀
vip.url.prefix=yoli://yoli.com/yoli/h5?url=

# 5.24 新增 非会员用户观看非单点内容 主
mongo_video_vip.vipInfo.copy.unVipPaidGuidTip1=开通会员，芒果全量内容随心看
video_vip.vipInfo.copy.unVipPaidGuidTip1=开通会员，欢太影视全量内容随心看

# 5.24 新增 非会员用户观看非单点内容 副
mongo_video_vip.vipInfo.copy.unVipPaidGuidTip2=限时特惠，只需 {0} 元钱
video_vip.vipInfo.copy.unVipPaidGuidTip2=限时特惠，只需 {0} 元钱

# 5.24 新增 非会员用户观看非单点内容 按钮
mongo_video_vip.vipInfo.copy.unVipPaidGuidTip3= {0} 元开通
video_vip.vipInfo.copy.unVipPaidGuidTip3= {0} 元开通

# 5.24 新增 权益即将到期的会员用户（剩余3天内）观看所有非单点内容 主
mongo_video_vip.vipInfo.copy.vipExpiredPaidGuidTip1=您的芒果专区VIP将在 {0} 天后到期
video_vip.vipInfo.copy.vipExpiredPaidGuidTip1=您的欢太影视VIP将在 {0} 天后到期

# 5.24 新增 权益即将到期的会员用户（剩余3天内）观看所有非单点内容 副
mongo_video_vip.vipInfo.copy.vipExpiredPaidGuidTip2=现在续费，只需 {0} 元钱
video_vip.vipInfo.copy.vipExpiredPaidGuidTip2=现在续费，只需 {0} 元钱

# 5.24 新增 权益即将到期的会员用户（剩余3天内）观看所有非单点内容 按钮
mongo_video_vip.vipInfo.copy.vipExpiredPaidGuidTip3={0} 元购买
video_vip.vipInfo.copy.vipExpiredPaidGuidTip3={0} 元购买

# 5.24 新增 未登录观看非单点内容 副
mongo_video_vip.vipInfo.copy.unLoginPaidGuidTip2=立享限时特惠
video_vip.vipInfo.copy.unLoginPaidGuidTip2=立享限时特惠

# 5.24 新增 非会员用户观看非单点内容 按钮
mongo_video_vip.vipInfo.copy.unLoginPaidGuidTip3= 成为会员
video_vip.vipInfo.copy.unLoginPaidGuidTip3= 成为会员

# 5.24 新增 单点内容 主
mongo_video_vip.vipInfo.copy.singlePaidGuidTip1=购买后观看完整版
video_vip.vipInfo.copy.singlePaidGuidTip1=购买后观看完整版

# 5.24 新增 单点内容 副
mongo_video_vip.vipInfo.copy.singlePaidGuidTip2=应版权方要求，本片需要购买
video_vip.vipInfo.copy.singlePaidGuidTip2=应版权方要求，本片需要购买

# 5.24 新增 单点内容 按钮
mongo_video_vip.vipInfo.copy.singlePaidGuidTip3=%s 元购买
video_vip.vipInfo.copy.singlePaidGuidTip3=%s 元购买

paidGuidTimes=0
# 欢太影视会员名称V2
video_vip.productNameV2=影视VIP

# 赠送会员接口
vip.innerAdd.url=https://vip-open-test.wanyol.com/api/order/v1.0/inner-add
# 赠送会员超时时间
vip.innerAdd.timeout=2000
vipInfo.expired.time=10

# 会员退款接口
vip.refund.url=https://vip-open-test.wanyol.com/api/customize-order/revoke
# 会员退款接口超时时间
vip.refund.timeout=60000

# 账号用户基本信息查询接口
user.basicInfo.url=https://uc-accountgateway-test.wanyol.com/user/basic-info
# 账号用户基本信息查询接口超时时间
user.basicInfo.timeout=1000
# 账号状态中的正常状态类型
user.account.normalStatus=OK

# 咪咕NBA联盟通会员（小屏 NBA联盟通）会员名称
nba_lianmengtong.productName=咪咕NBA联盟通VIP
# 咪咕足球通会员（小屏足球通）名称
zuqiuvip_zuqiutong.productName=咪咕足球通VIP

# productId加密密钥
vip.productId.aes.key=666c48adb67b450361a35e4d1ed98bc0
# 商品productId明文返回开关 true-返回明文，false-返回密文
vip.productId.return.plaintext=false
# 支持productId明文下单开关 true-支持明文下单，false-不支持
vip.productId.plaintext.switch=true

#视频赠送会员接口
video.vipAdd.url=http://vod-mobile-test.wanyol.com/v1/vip/api/addVip
#视频赠送会员接口超时时间
video.vipAdd.timeout=3000
#内部调用赠送会员接口key
video.vipAdd.appKey=3m2soujpVCHp
#内部调用赠送会员接口key
video.vipAdd.secret=unBDGXgCCU7nCJKmwfsm6e
#视频会员退单接口
video.vipRefund.url=http://vod-mobile-test.wanyol.com/v1/vip/api/vipRefund
#视频会员退单接口超时时间
video.vipRefund.timeout=63000