import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.service.app.RecommendInfoService;
import esa.rpc.test.support.mock.Mocker;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import util.ArrangeRpcMockerUtil;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class RecommendInfoServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private RecommendInfoService recommendInfoService;

    @Test
    public void testRecommendInfoService() {

        List<ProgramAlbumEs> programAlbumEsList = new ArrayList<>();
        ProgramAlbumEs programAlbumEs = new ProgramAlbumEs();
        programAlbumEs.setSid("testSid");
        programAlbumEsList.add(programAlbumEs);

        List<KeyWordSearchResponse > result = new ArrayList<>();
        KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
        keyWordSearchResponse.setSid("testSid");
        result.add(keyWordSearchResponse);

        String urlPack = "{\"cmd_vod\":{\"contentType\":\"all\",\"version_tag\":0}}\n";
        ListFilterParam param = new ListFilterParam();
        param.setUrlpack(urlPack);

        try {
            ArrangeRpcMockerUtil.getAlbumRecommendInfoMock();
            recommendInfoService.handleAlbumRankRecommendInfo(programAlbumEsList, result, param);
        } catch (Throwable e) {
            log.error("testStandAlbumService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }
}