import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.media.entity.StandardEpisode;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.executors.standard.CmsStandardAlbumExecutor;
import com.heytap.longvideo.search.model.entity.es.StandardEpisodeEs;
import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.heytap.longvideo.search.service.standard.StandardEpisodeService;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class StandEpisodeServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private CmsStandardAlbumExecutor cmsStandardAlbumExecutor;


    @Autowired
    private InitStandardDataService initStandardDataService;

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    @Autowired
    private ElasticSearchService elasticsearchService;

    /**
     * 新增和查询标准化的视频
     */
    @Test
    public void testStandEpisodeService() {
        try {
            elasticsearchService.deleteIndex(StandardEpisodeEs.class);
            elasticsearchService.createIndexAndMapping(StandardEpisodeEs.class);
            InitDataParam initDataParam=new InitDataParam();
            initDataParam.setType("episode");
            initDataParam.setDataBaseEnd(0);
            initDataParam.setTableEnd(0);
            initDataParam.setUpdateTime("2021-01-01 00:00:00");
            initStandardDataService.initData(initDataParam);

            SearchStandardEpisodeParams standardAlbumParams =new SearchStandardEpisodeParams();
            standardAlbumParams.setEid("576196693050085376");
            standardAlbumParams.setPageIndex(1);
            PageResponse<StandardEpisodeVo> result = standardEpisodeService.searchEpisode(standardAlbumParams);
            Assert.assertEquals("576196693050085376",result.getItemList().get(0).getEid());

            CmsSearchEpisodeParams cmsSearchEpisodeParams=new CmsSearchEpisodeParams();
            cmsSearchEpisodeParams.setPageIndex(1);
            cmsSearchEpisodeParams.setEid("576196693050085376");
            PageResponse<CmsEpisodeVo> cmsEpisodeVoPageResponse=standardEpisodeService.searchCmsEpisode(cmsSearchEpisodeParams);
            Assert.assertEquals("576196693050085376",cmsEpisodeVoPageResponse.getItemList().get(0).getEid());

        } catch (Throwable e) {
            log.error("testStandEpisodeService error", e);
        }
        log.info("test pass");
    }
}