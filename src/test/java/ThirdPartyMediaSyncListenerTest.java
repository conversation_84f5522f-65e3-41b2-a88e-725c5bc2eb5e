import com.alibaba.fastjson.JSON;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.arrange.MultiParamsRpcApi;
import com.heytap.longvideo.client.arrange.entity.MtvMultiparams;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.mq.ThirdPartyMediaSyncListener;
import com.heytap.longvideo.search.service.standard.unofficialalbum.service.MultiParamsRpcService;
import esa.rpc.test.support.mock.Mocker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.Map;

import static org.apache.commons.lang.CharEncoding.UTF_8;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/4/25 上午11:06
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class ThirdPartyMediaSyncListenerTest extends GoblinJunit4BaseTest {

    @Autowired
    private ThirdPartyMediaSyncListener thirdPartyMediaSyncListener;

    @Autowired
    private MultiParamsRpcService multiParamsRpcService;

    @Test
    public void findByContentTypeTest() throws IOException {
        mock();
        String contentType = "tv";
        Map<String, String> map = multiParamsRpcService.findByContentType(contentType);
        Assert.assertTrue(map.size() > 0);
    }

    public static Mocker mock() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(MultiParamsRpcApi.class.getName())
                .methodName("findByContentType");
        RpcResult<MtvMultiparams> resp = new RpcResult(0, "success");
        String str = FileUtils.readFileToString(new ClassPathResource("mock/Multiparams.json").getFile(), UTF_8);
        MtvMultiparams mtvMultiparams = JSON.parseObject(str, MtvMultiparams.class);
        resp.setData(mtvMultiparams);
        mocker.returnValue(resp).configure();
        return mocker;
    }
}
