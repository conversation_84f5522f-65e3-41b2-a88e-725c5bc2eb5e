//
//import com.heytap.longvideo.client.media.entity.MisVideoOrg;
//import com.heytap.longvideo.client.media.entity.StandardAlbum;
//import com.heytap.longvideo.search.bootstrap.Main;
//import com.heytap.longvideo.search.mapper.media.MisVideoOrgMapper;
//import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
//import com.heytap.longvideo.search.mapper.media.StandardEpisodeMapper;
//import com.heytap.longvideo.search.mapper.media.StandardTrailerMapper;
//import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
//import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
//import com.heytap.longvideo.search.model.entity.es.StandardEpisodeEs;
//import com.heytap.longvideo.search.model.entity.es.StandardTrailerEs;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
///**
// * <AUTHOR>
// * @date 2025/4/17 9:23
// */
//@RunWith(SpringRunner.class)
//@Slf4j
//@SpringBootTest(classes = {Main.class})
//public class SqlMapperTest {
//
//    @Autowired
//    private MisVideoOrgMapper misVideoOrgMapper;
//    @Autowired
//    private StandardAlbumMapper standardAlbumMapper;
//    @Autowired
//    private StandardEpisodeMapper standardEpisodeMapper;
//    @Autowired
//    private StandardTrailerMapper standardTrailerMapper;
//
//
//
//
//    @Test
//    public void testSqlMapperAll() {
//
//        try {
//            List<MisVideoOrg> orgVideoBatchByAlbumId = misVideoOrgMapper.getOrgVideoBatchByAlbumId(0, 0, "9725364", 1524665979656982429L, 2);
//            Assert.assertNotNull(orgVideoBatchByAlbumId);
//
//            List<MisVideoOrg> sohu = misVideoOrgMapper.getOrgVideoBatch(0, 0, "sohu", 1524665979656982429L, 2);
//            Assert.assertNotNull(sohu);
//            List<StandardAlbumEs> standardAlbumEs = standardAlbumMapper.selectStandardAlbumList(0, 0, "2025-02-07 00:17:36", 0, 1);
//            Assert.assertNotNull(standardAlbumEs);
//            List<ProgramAlbumEs> sohu1 = standardAlbumMapper.selectStandardAlbumListForApp(0, 0, Stream.of("sohu").collect(Collectors.toList()));
//            Assert.assertNotNull(sohu1);
//
//            StandardAlbumEs standardAlbumEs1 = standardAlbumMapper.selectStandardAlbumBySid(0, 0, "1000132162693017600", 6);
//            Assert.assertNotNull(standardAlbumEs1);
//
//            StandardAlbum standardAlbumBySid = standardAlbumMapper.getStandardAlbumBySid(0, 0, "1000132162693017600");
//            Assert.assertNotNull(standardAlbumBySid);
//
//
//            List<StandardEpisodeEs> standardEpisodeEs = standardEpisodeMapper.selectStandardEpisodeList(0,0,"2025-01-11 19:55:38","0",2);
//            Assert.assertNotNull(standardEpisodeEs);
//
//
//            List<StandardTrailerEs> standardTrailerEs1 = standardTrailerMapper.selectStandardTrailerList(0, 0, "2022-04-19 00:34:34", "0", 2);
//            Assert.assertNotNull(standardTrailerEs1);
//        } catch (Exception e) {
//            log.error("testSqlMapperAll error", e);
//            //校验有无异常，有异常则测试不通过，说明代码有问题
//            Assert.assertNull(e);
//        }
//    }
//
//}
