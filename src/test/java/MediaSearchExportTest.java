import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchExportVo;
import com.heytap.longvideo.search.model.param.standard.UgcSearchExportStatusVo;
import com.heytap.longvideo.search.model.param.standard.UgcSearchVideoParams;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.UgcStandardVideoService;
import com.heytap.longvideo.search.utils.CSVExportUtil;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;

import java.io.File;
import java.util.ArrayList;

/**
 * <AUTHOR> ye mengsheng
 * @date 2024/9/10 下午7:01
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class MediaSearchExportTest extends GoblinJunit4BaseTest {

    @Autowired
    private UgcStandardVideoService ugcStandardVideoService;

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private CsvExportConfig csvExportConfig;

    @Autowired
    UploadFileToOcsComponent fileToOcsComponent;

    /**
     * UGC视频导出测试
     */
    @Test
    public void testUgcSearchAndExport() {
        try {
            // ES数据mock
            ElasticSearchUtil.searchUgcStandardVideoMock(elasticSearchService, elasticsearchRestTemplate);

            UgcSearchVideoParams ugcSearchVideoParams = new UgcSearchVideoParams();
            ugcSearchVideoParams.setVideoManageName("芒果UGC视频");
            ugcSearchVideoParams.setSource("source");
            ugcSearchVideoParams.setTitle("11");
            SearchExportVo searchExportVo = ugcStandardVideoService.searchAndExportVideo(ugcSearchVideoParams);

            Assert.assertNotNull(searchExportVo.getSearchExportId());
            Assert.assertNotNull(searchExportVo.getUrl());

            StandardResult<UgcSearchExportStatusVo> ugcSearchExportStatusVoStandardResult = ugcStandardVideoService.searchExportStatus(searchExportVo.getSearchExportId());
            Assert.assertNotNull(ugcSearchExportStatusVoStandardResult.getData().getStatus());
        } catch (Exception e) {
            log.error("testStandVideoService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }


    @Test
    public void testCSVExportUtil() {
        try {

            String fileName = "test.csv";
            ArrayList<String> dataList = new ArrayList<>();
            dataList.add("1,2,3");

            //case 1
            CSVExportUtil.exportCsv(new File(fileName), dataList);
            File file = new File(fileName);
            Assert.assertTrue(file.exists());
            Assert.assertTrue(file.delete());

            //case 2
            CSVExportUtil.exportCsv(new File(fileName), dataList);
            File file1 = new File(fileName);
            Assert.assertTrue(file1.exists());
            Assert.assertTrue(file1.delete());

        } catch (Exception e) {
            log.error("testCSVExportUtil error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }


    @Test
    public void testUploadFileToOcsUtil() {
        try {
            String AccessKeyId = csvExportConfig.getAccessKeyId();
            String AccessKeySecret = csvExportConfig.getAccessKeySecret();
            String EndPoint = csvExportConfig.getEndPoint();
            String Region = csvExportConfig.getRegion();
            String BucketName = csvExportConfig.getBucketName();
            String ocsPath = csvExportConfig.getOcsPath();
            String localPath = csvExportConfig.getLocalPath();

            String fileName = "test.csv";
            ArrayList<String> dataList = new ArrayList<>();
            dataList.add("1,2,3");
            CSVExportUtil.exportCsv(new File(fileName), dataList);

            boolean uploadRes = fileToOcsComponent.uploadFileToOcs(AccessKeyId, AccessKeySecret, EndPoint, Region, BucketName, fileName, localPath, ocsPath);
            Assert.assertTrue(uploadRes);

        } catch (Exception e) {
            log.error("testUploadFileToOcsUtil error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }

    /**
     * 测试 Ocs删除文件
     */
    @Test
    public void testDeleteOcsFileJob() {
        try {
            String AccessKeyId = csvExportConfig.getAccessKeyId();
            String AccessKeySecret = csvExportConfig.getAccessKeySecret();
            String EndPoint = csvExportConfig.getEndPoint();
            String Region = csvExportConfig.getRegion();
            String BucketName = csvExportConfig.getBucketName();
            String OcsPath = csvExportConfig.getOcsPath();
            String localPath = csvExportConfig.getLocalPath();

            //1.上传一个文件
            String fileName = "test_123.csv";
            ArrayList<String> dataList = new ArrayList<>();
            dataList.add("1,2,3");
            CSVExportUtil.exportCsv(new File(fileName), dataList);
            boolean uploadRes = fileToOcsComponent.uploadFileToOcs(AccessKeyId, AccessKeySecret, EndPoint, Region, BucketName, fileName, localPath, OcsPath);
            Assert.assertTrue(uploadRes);

            //2.删除一个文件
            AWSCredentials awsCredentials = new BasicAWSCredentials(AccessKeyId, AccessKeySecret);
            AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
            ClientConfiguration clientConfiguration = new ClientConfiguration()
                    .withProtocol(Protocol.HTTP);

            AmazonS3 s3 = AmazonS3ClientBuilder.standard().withCredentials(awsCredentialsProvider)
                    .withPathStyleAccessEnabled(true)
                    .withClientConfiguration(clientConfiguration)
                    .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(EndPoint, Region))
                    .build();

            ListObjectsV2Request req = new ListObjectsV2Request().withBucketName(BucketName).withPrefix(OcsPath).withMaxKeys(1000);
            ListObjectsV2Result result = s3.listObjectsV2(req);
            if (result != null) {
                for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
                    //删除指定的文件
                    if (objectSummary.getKey().equals(OcsPath + "/" + fileName)) {
                        s3.deleteObject(BucketName, objectSummary.getKey());
                    }
                }
            }
        } catch (Exception e) {
            log.error("testDeleteOcsFileJob error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }

}