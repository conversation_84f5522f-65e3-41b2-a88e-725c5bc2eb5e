import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import esa.rpc.test.support.mock.Mocker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2024/11/18 下午7:58
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class OutSideSearchTest extends GoblinJunit4BaseTest {


    @Autowired
    ConvertResponseService convertResponseService;

    public void rpcMock(ArrayList<Mocker> mockers) {
        mockers.add(standardAlbumRpcApi());
    }

    public static Mocker standardAlbumRpcApi() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(StandardAlbumRpcApi.class.getName())
                .methodName("getBySidsFilterInvalid");
        String json = "{\"msg\":\"server success\",\"code\":0,\"data\":{\"1070270259531010049\":{\"copyright\":1,\"year\":2024,\"downloadAble\":0,\"totalEpisode\":21,\"formerPrice\":0.00,\"price\":0,\"sourceSeriesId\":\"\",\"horizontalImage\":\"http://m.ykimg.com/0535000066BEC15E13EB661302C9173C\",\"publishTime\":1731427687000,\"period\":\"21集全\",\"director\":\"\",\"participantType\":-1,\"completed\":1,\"riskFlag\":0,\"tags\":\"冒险|剧情\",\"sourceType\":2,\"extraInfo\":\"\",\"status\":1,\"sourcePlayCount\":0,\"nowPrice\":0.00,\"subProgramType\":\"tv\",\"verifyStatus\":1,\"supplyType\":\"normal\",\"startTime\":-28800000,\"downloadMarkcode\":\"\",\"programInfo\":\"全 21 集\",\"vipType\":1,\"updateTime\":1731427687000,\"sourceWebUrl\":\"hap://applet/fc2345027171510597?path=%2Fpages%2Fplay%2Findex&query=showId%3Ddece837b948642578ab1\",\"actor\":\"郭麒麟|吴镇宇|尤勇智|王迅|齐溪|江奇霖|宋家腾|王玉雯|姜珮瑶|李晓川|刘陆|赵君|陈松伶|尹子维|王海涛|颜北|张隽溢|董博睿|魏伟\",\"sourceScore\":\"9.0\",\"createTime\":1730470596000,\"information\":\"三边坡，一处鱼龙混杂的热带异域，一个繁茂与衰败并生的斑驳之地。意外流落三边坡的打工小白沈星遇到在多方势力间游走的三边坡和事佬猜叔，一场冒险，一段善良微光指引下的回归，在留与逃的挣扎中将如何上演。\",\"endTime\":-28800000,\"processStatus\":0,\"honor\":\"\",\"chargeType\":0,\"sourceAlbumId\":\"dece837b948642578ab1\",\"language\":\"\",\"source\":\"youkumobile\",\"horizontalIcon\":\"http://m.ykimg.com/0535000066BEC15E13EB661302C9173C\",\"subTitle\":\"郭麒麟吴镇宇生死逃杀\",\"mappingTags\":\"动作|剧情\",\"payEffectDays\":0,\"keyword\":\"\",\"area\":\"内地\",\"showTime\":\"202408\",\"competitionType\":-1,\"prePush\":0,\"unit\":1,\"managerStatus\":0,\"copyrightCode\":\"youkumobile\",\"medium\":2,\"title\":\"边水往事\",\"sid\":\"1070270259531010049\",\"tuputag\":\"\",\"duration\":0,\"featureType\":1,\"vipPrice\":0.00,\"definition\":\"[]\",\"verticalIcon\":\"http://m.ykimg.com/0527000066BEC16C13EB661302C6D644\",\"brief\":\"郭麒麟吴镇宇生死逃杀\",\"programType\":\"tv\",\"validEpisode\":21,\"sourceStatus\":1,\"sourceHot\":3598.0,\"markCode\":\"youku_mobile_vip_1\",\"verticalImage\":\"http://m.ykimg.com/0527000066BEC16C13EB661302C6D644\",\"category\":\"电视剧\",\"payStatus\":1}}}";
        RpcResult<Map<String, StandardAlbum>> ret = JSON.parseObject(json, new TypeReference<RpcResult<Map<String, StandardAlbum>>>(){});
        mocker.returnValue(ret).configure();
        return mocker;
    }

    @Test
    public void standardAlbumToSearchResponseTest() {
        ArrayList<Mocker> mockers = new ArrayList();
        rpcMock(mockers);
        String standardAlbumObject = "{\"copyright\":1,\"year\":2024,\"downloadAble\":0,\"totalEpisode\":21,\"formerPrice\":0.00,\"price\":0,\"sourceSeriesId\":\"\",\"horizontalImage\":\"http://m.ykimg.com/0535000066BEC15E13EB661302C9173C\",\"publishTime\":1731427687000,\"period\":\"21集全\",\"director\":\"\",\"participantType\":-1,\"completed\":1,\"riskFlag\":0,\"tags\":\"冒险|剧情\",\"sourceType\":2,\"extraInfo\":\"\",\"status\":1,\"sourcePlayCount\":0,\"nowPrice\":0.00,\"subProgramType\":\"tv\",\"verifyStatus\":1,\"supplyType\":\"normal\",\"startTime\":-28800000,\"downloadMarkcode\":\"\",\"programInfo\":\"全 21 集\",\"vipType\":1,\"updateTime\":1731427687000,\"sourceWebUrl\":\"hap://applet/fc2345027171510597?path=%2Fpages%2Fplay%2Findex&query=showId%3Ddece837b948642578ab1\",\"actor\":\"郭麒麟|吴镇宇|尤勇智|王迅|齐溪|江奇霖|宋家腾|王玉雯|姜珮瑶|李晓川|刘陆|赵君|陈松伶|尹子维|王海涛|颜北|张隽溢|董博睿|魏伟\",\"sourceScore\":\"9.0\",\"createTime\":1730470596000,\"information\":\"三边坡，一处鱼龙混杂的热带异域，一个繁茂与衰败并生的斑驳之地。意外流落三边坡的打工小白沈星遇到在多方势力间游走的三边坡和事佬猜叔，一场冒险，一段善良微光指引下的回归，在留与逃的挣扎中将如何上演。\",\"endTime\":-28800000,\"processStatus\":0,\"honor\":\"\",\"chargeType\":0,\"sourceAlbumId\":\"dece837b948642578ab1\",\"language\":\"\",\"source\":\"youkumobile\",\"horizontalIcon\":\"http://m.ykimg.com/0535000066BEC15E13EB661302C9173C\",\"subTitle\":\"郭麒麟吴镇宇生死逃杀\",\"mappingTags\":\"动作|剧情\",\"payEffectDays\":0,\"keyword\":\"\",\"area\":\"内地\",\"showTime\":\"202408\",\"competitionType\":-1,\"prePush\":0,\"unit\":1,\"managerStatus\":0,\"copyrightCode\":\"youkumobile\",\"medium\":2,\"title\":\"边水往事\",\"sid\":\"1070270259531010049\",\"tuputag\":\"\",\"duration\":0,\"featureType\":1,\"vipPrice\":0.00,\"definition\":\"[]\",\"verticalIcon\":\"http://m.ykimg.com/0527000066BEC16C13EB661302C6D644\",\"brief\":\"郭麒麟吴镇宇生死逃杀\",\"programType\":\"tv\",\"validEpisode\":21,\"sourceStatus\":1,\"sourceHot\":3598.0,\"markCode\":\"youku_mobile_vip_1\",\"verticalImage\":\"http://m.ykimg.com/0527000066BEC16C13EB661302C6D644\",\"category\":\"电视剧\",\"payStatus\":1}";
        StandardAlbum standardAlbum = JSONObject.parseObject(standardAlbumObject, StandardAlbum.class);
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(71600);
        param.setIsOut(1);
        param.setQuickEngineVersion(90200);
        KeyWordSearchResponse keyWordSearchResponse1 = convertResponseService.standardAlbumToSearchResponse(standardAlbum, param);
        Assert.assertTrue(keyWordSearchResponse1.getDeepLink().contains("yoli.com/yoli/bridge"));
        param.setVersion(71600);
        param.setIsOut(0);
        param.setQuickEngineVersion(90200);
        KeyWordSearchResponse keyWordSearchResponse2 = convertResponseService.standardAlbumToSearchResponse(standardAlbum, param);
        Assert.assertTrue(keyWordSearchResponse2.getDeepLink().contains("yoli.com/yoli/bridge"));
        param.setVersion(71500);
        param.setIsOut(0);
        param.setQuickEngineVersion(90200);
        KeyWordSearchResponse keyWordSearchResponse3 = convertResponseService.standardAlbumToSearchResponse(standardAlbum, param);
        Assert.assertTrue(keyWordSearchResponse3.getDeepLink().contains("yoli.com/yoli/h5"));

    }

}
