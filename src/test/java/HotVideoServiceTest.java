import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.entity.es.HotVideoEs;
import com.heytap.longvideo.search.service.app.*;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class HotVideoServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private HotVideoService hotVideoService;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;


    /**
     * 搜索
     */
    @Test
    public void testHotVideoService() {
        try {
            List<String> titleList =new ArrayList<>();
            titleList.add("test");
            hotVideoService.insertOutHotVideoToEs(titleList,"movie");
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery("title", "test"));
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQuery)
                    .build();
            //4.解析响应
            SearchHits<HotVideoEs> searchHits = restTemplate.search(searchQuery, HotVideoEs.class);
            Assert.assertEquals(searchHits.getSearchHits().get(0).getContent().getTitle(),"test");
        } catch (Throwable e) {
            log.error("testHotVideoService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }



}