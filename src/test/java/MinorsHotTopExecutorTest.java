import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.executors.app.MinorsHotTopExecutor;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.param.MinorsHotTopParam;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;

import java.util.List;
import java.util.concurrent.CompletableFuture;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
public class MinorsHotTopExecutorTest extends GoblinJunit4BaseTest {

    @Autowired
    MinorsHotTopExecutor minorsHotTopExecutor;

    @Autowired
    private InitService initService;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private StandardAlbumMapper standardAlbumMapper;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Test
    public void minorsHotTopTest() {
        String desc = this.getClass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName();
        log.info("------------------- " + desc + " -> start.");

        ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, true, null);

        MinorsHotTopParam request = new MinorsHotTopParam();
        request.setAgeCode("4");
        request.setVersionTag(4);
        try {
            CompletableFuture<List<String>> response = minorsHotTopExecutor.myExecute(request);
            Assert.assertNotNull(response.get());
            Assert.assertTrue(CollectionUtils.isNotEmpty(response.get()));
        } catch (Exception e) {
            log.error("[{}] unexpected error: ", desc, e);
        } finally {
            log.info("------------------- " + desc + " -> end.");
        }
    }
}
