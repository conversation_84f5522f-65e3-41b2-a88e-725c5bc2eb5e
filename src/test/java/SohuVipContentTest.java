import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.liteflow.cmp.SearchAlbumCmp;
import com.heytap.longvideo.search.liteflow.cmp.SearchTagCmp;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.common.CommonService;
import esa.rpc.test.support.mock.Mocker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;
import util.MediaRpcMockerUtil;
import util.UTLogUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 搜狐VIP内容不展示角标 - 测试类
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/7/2
 */
@Slf4j
@UsingDataSet
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
public class SohuVipContentTest extends GoblinJunit4BaseTest {

    @Autowired
    private CommonService commonService;

    @Autowired
    private SearchAlbumCmp searchAlbumCmp;

    @Autowired
    private SearchTagCmp searchTagCmp;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Test
    public void searchAlbumCmpTest() {
        String desc = this.getClass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName();
        log.info(UTLogUtil.logStartInfo(desc));

        List<Mocker> mockers = new ArrayList<>();
        try {

            ElasticSearchUtil.searchAlbumCmpMock(elasticSearchService, restTemplate);

            rpcMock(mockers);

            // 构造请求
            KeyWordSearchParamV2 paramV2 = buildParamV2();

            List<ProgramAlbumEs> res = searchAlbumCmp.search(paramV2);
            Assert.assertNotNull(res);
            Assert.assertEquals(2, res.size());
            Assert.assertEquals("大宋奇案", res.get(0).getTitle());
        } catch (Exception e) {
            log.error(UTLogUtil.getExceptionPrefix(), desc, e);
        } finally {
            ElasticSearchUtil.deleteAlbumEs(elasticSearchService);
            rpcClear(mockers);
            log.info(UTLogUtil.logEndInfo(desc));
        }
    }

    private KeyWordSearchParamV2 buildParamV2() {
        KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
        paramV2.setKeyword("大宋奇案");
        paramV2.setVipType("default");
        paramV2.setSearchType("1");
        paramV2.setVersion(71100);
        paramV2.setVersionTag(7);
        return paramV2;
    }

    private void rpcMock(List<Mocker> mockers) {
        mockers.add(MediaRpcMockerUtil.getBySidsFilterInvalid());
    }

    private void rpcClear(List<Mocker> mockers) {
        mockers.forEach(Mocker::clear);
    }
}
