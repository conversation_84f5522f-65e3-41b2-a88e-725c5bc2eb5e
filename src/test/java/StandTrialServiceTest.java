import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.executors.standard.CmsStandardAlbumExecutor;
import com.heytap.longvideo.search.model.entity.es.StandardTrailerEs;
import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.heytap.longvideo.search.service.standard.StandardTrailerService;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class StandTrialServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private CmsStandardAlbumExecutor cmsStandardAlbumExecutor;


    @Autowired
    private InitStandardDataService initStandardDataService;

    @Autowired
    private StandardTrailerService trailerService;

    @Autowired
    private ElasticSearchService elasticsearchService;

    /**
     * 新增和查询标准化的视频
     */
    @Test
    public void testStandTrialService() {
        try {
            elasticsearchService.deleteIndex(StandardTrailerEs.class);
            elasticsearchService.createIndexAndMapping(StandardTrailerEs.class);
            InitDataParam initDataParam=new InitDataParam();
            initDataParam.setType("trailer");
            initDataParam.setDataBaseEnd(0);
            initDataParam.setTableEnd(0);
            initDataParam.setUpdateTime("2021-01-01 00:00:00");
            initStandardDataService.initData(initDataParam);
            SearchStandardTrailerParams standardAlbumParams =new SearchStandardTrailerParams();
            standardAlbumParams.setTid("670613294918344704");
            standardAlbumParams.setPageIndex(1);
            standardAlbumParams.setAgeStart(0);


            PageResponse<StandardTrailerVo> result = trailerService.searchTrailer(standardAlbumParams);
            Assert.assertEquals("670613294918344704",(result.getItemList().get(0).getTid()));

            CmsSearchTrailerParams cmsSearchTrailerParams =new CmsSearchTrailerParams();
            cmsSearchTrailerParams.setPageIndex(1);
            cmsSearchTrailerParams.setTid("670613294918344704");
            PageResponse<CmsTrailerVo> cmsTrailerVoPageResponse = trailerService.searchCmsTrailer(cmsSearchTrailerParams);
            Assert.assertEquals("670613294918344704",(cmsTrailerVoPageResponse.getItemList().get(0).getTid()));

            List<TrailerTypeListVo> trailerTypeListVoList = trailerService.getTrailerType("506311924435210240");
            Assert.assertEquals("9",(trailerTypeListVoList.get(0).getTrailerType()));

        } catch (Throwable e) {
            log.error("testStandTrialService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }
}