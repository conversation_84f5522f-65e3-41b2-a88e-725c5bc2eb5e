import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardPersonRpcApi;
import com.heytap.longvideo.client.media.entity.StandardPerson;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.executors.standard.ListStandardPersonExecutor;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.ListStandardPersonRequest;
import esa.rpc.test.support.mock.Mocker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.util.concurrent.CompletableFuture;

/**
 * @Description: 标准影人相关接口测试类
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/5/23
 */
@Slf4j
@UsingDataSet
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
public class StandardPersonServiceTest extends GoblinJunit4BaseTest {

    @Autowired
    private ListStandardPersonExecutor listStandardPersonExecutor;

    /**
     * @Description: 影人分页列表查询测试用例
     */
    @Test
    public void listStandardPersonTest() {
        try {
            // mock数据
            IPage<StandardPerson> pages = JSONObject.parseObject(FileUtils.readFileToString(new ClassPathResource("\\mock\\listStandardPerson.json").getFile(), "UTF-8"), new TypeReference<Page<StandardPerson>>() {});
            Mocker.newBuilder()
                    .interfaceName(StandardPersonRpcApi.class.getName())
                    .methodName("pageList")
                    .returnValue(new RpcResult<>(0, "success", pages))
                    .configure();

            // 构造请求
            ListStandardPersonRequest request = new ListStandardPersonRequest();

            // 断言验证
            PageResponse<StandardPerson> response = listStandardPersonExecutor.myExecute(request).get().getData();
            Assert.assertNotNull(response);
            Assert.assertEquals(20, response.getItemList().size());
        } catch (Throwable e) {
            log.error("listStandardPersonTest test error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }
}
