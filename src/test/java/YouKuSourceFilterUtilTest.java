import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2024/10/23 下午7:53
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class YouKuSourceFilterUtilTest extends GoblinJunit4BaseTest {
    @Autowired
    private YoukuSourceFilterService youKuSourceFilterService;

    @Autowired
    SourceFilterConfig sourceFilterConfig;

    @Test
    public void testAppVersionAndQucikEngineFilter() {

        Integer version = 71000;
        boolean res1 = youKuSourceFilterService.filterItem(version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertTrue(res1);
        }

        boolean res2 = youKuSourceFilterService.filterItem(version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertTrue(res2);
        }

        version = 71600;
        boolean res3 = youKuSourceFilterService.filterItem(version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertFalse(res3);
        }
    }

    @Test
    public void testSourceFilter() {
        boolean res1 = youKuSourceFilterService.filterItemBySource("youkumobile", 7100);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertTrue(res1);
        }

        Integer quickEngineVersion = 90200;
        Integer version = 71000;
        boolean res2 = youKuSourceFilterService.filterItemBySource("youkumobile", version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertTrue(res2);
        }

        version = 71600;
        boolean res3 = youKuSourceFilterService.filterItemBySource("youkumobile", version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertFalse(res3);
        }
    }

    @Test
    public void testSetDeepLinkFilter() {
        boolean res1 = youKuSourceFilterService.setDeepLinkFilter("youkumobile", null, 7100);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertFalse(res1);
        }

        Integer quickEngineVersion = 90200;
        Integer version = 71000;
        boolean res2 = youKuSourceFilterService.setDeepLinkFilter("youkumobile", quickEngineVersion, version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertFalse(res2);
        }

        version = 71600;
        boolean res3 = youKuSourceFilterService.setDeepLinkFilter("youkumobile", quickEngineVersion, version);
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertTrue(res3);
        }
    }


    @Test
    public void testGetUpGradeUrl() {
        String upGradeUrl = youKuSourceFilterService.getUpGradeUrl("123", "youkumobile", "title");
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertNotNull(upGradeUrl);
        }
    }

    @Test
    public void test1() {
        boolean res1 = youKuSourceFilterService.thirdPartyFilter(90200, 71600, "");
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertFalse(res1);
        }
        boolean res2 = youKuSourceFilterService.thirdPartyFilter(null, 71600, "");
        if (1 == sourceFilterConfig.getYoukuSwitch() && 0 == sourceFilterConfig.getYoukuVersionSwitch()) {
            Assert.assertFalse(res2);
        }
    }

}
