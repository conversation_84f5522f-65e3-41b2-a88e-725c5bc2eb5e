import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.query.SearchAlbumRequest;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.heytap.longvideo.search.service.standard.SearchService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/11/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class SearchAlbumRpcApiTest extends GoblinJunit4BaseTest {

    @Autowired
    private SearchService searchService;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private InitStandardDataService initStandardDataService;

    @Test
    public void searchAlbumRpcApiTest() {
        try {
            elasticsearchService.deleteIndex(StandardAlbumEs.class);
            elasticsearchService.createIndexAndMapping(StandardAlbumEs.class);
            InitDataParam initDataParam=new InitDataParam();
            initDataParam.setType("album");
            initDataParam.setDataBaseEnd(0);
            initDataParam.setTableEnd(0);
            initDataParam.setUpdateTime("2021-01-01 00:00:00");
            initStandardDataService.initData(initDataParam);
            SearchAlbumRequest request = new SearchAlbumRequest();
//        request.setProgramType("tv");
            request.setScoreStart(0.1F);
            request.setScoreEnd(9.9F);
            request.setYearStart("2001");
            request.setYearEnd("2024");
            request.setSource("mgmobile,huashi,senyu,sohu");
//        request.setPayStatus("0");
            request.setSortColumn("3");
            request.setSortType("1");
            request.setDataRange("0");
            List<StandardAlbum> standardAlbums = searchService.searchAlbum(request);
            elasticsearchService.deleteIndex(StandardAlbumEs.class);
            Assert.assertFalse(standardAlbums.isEmpty());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
