import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.service.common.ImageUploadHelperComponet;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/7/9 下午4:49
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class ImageUploadHelperComponetTest extends GoblinJunit4BaseTest {

    @Autowired
    private ImageUploadHelperComponet imageUploadHelperComponet;

    @Test
    public void uploadPicTest() {
        String res = imageUploadHelperComponet.uploadPic("https://dhfs-test-cpc.wanyol.com/513571828262850561_0709vImg.jpeg", "123123-test-oppo-1.jpeg", "123123","iqiyi");
        Assert.assertNotNull(res);
    }
}
