import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.YunheRankRpcApi;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.model.request.MagazinePoolSuggestRequest;
import com.heytap.longvideo.search.model.request.OcsImageUploadAndEsSyncRequest;
import com.heytap.longvideo.search.rpc.consumer.LvContentItemRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.UnofficialAlbumImageMappingRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.YunheRankRpcApiProxy;
import com.heytap.longvideo.search.service.common.ImageUploadHelperComponet;
import com.heytap.longvideo.search.service.spider.UnofficialAlbumImageUrlTransformService;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.internal.util.reflection.Whitebox;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/7/4 下午2:08
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({SearchHits.class, SearchHit.class, TotalHits.class, RestHighLevelClient.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class UnofficialAlbumServiceTest {

    @InjectMocks
    private UnofficialAlbumService unofficialAlbumService;

    @Mock
    private YunheRankRpcApiProxy yunheRankRpcApiProxy;

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Mock
    private LvContentItemRpcApiProxy lvContentItemRpcApiProxy;

    @Mock
    private UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService;

    @Mock
    private UnofficialAlbumImageMappingRpcApiProxy unofficialAlbumImageMappingRpcApiProxy;

    @Mock
    private ImageUploadHelperComponet imageUploadHelperComponet;

    @Mock
    private YunheRankRpcApi yunheRankRpcApi;

    @Test
    public void magazinePoolSuggestTest() throws IOException, IllegalAccessException {
        // rpc mock
        List<String> distinctTitles = new ArrayList<>();
        RpcResult rpcResult = new RpcResult(distinctTitles);
        distinctTitles.add("活着");
        PowerMockito.when(yunheRankRpcApi.queryDistinctTitlesByDay(any(), any())).thenReturn(CompletableFuture.completedFuture(rpcResult));

        // es query mock
        TotalHits totalHits = PowerMockito.mock(TotalHits.class);
        PowerMockito.field(TotalHits.class, "value").set(totalHits, 1);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        UnofficialAlbumEs unofficialAlbumEs = PowerMockito.mock(UnofficialAlbumEs.class);
        SearchHit[] hitArray = {hit};
        SearchResponse response = mock(SearchResponse.class);
        PowerMockito.when(hit.getSourceAsString()).thenReturn("{\"title\":\"活着\"}");
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(hits.getTotalHits()).thenReturn(totalHits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(response);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        when(hits.getHits()).thenReturn(new SearchHit[]{hit});
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(unofficialAlbumEs.getSourceStatus()).thenReturn(1);
        PowerMockito.when(lvContentItemRpcApiProxy.searchExistSid(any(), any())).thenReturn(CompletableFuture.completedFuture(new RpcResult<>(Collections.emptyList())));
        Whitebox.setInternalState(unofficialAlbumService, "thirdSearchSourceMap", new LinkedHashMap());
        MagazinePoolSuggestRequest request = new MagazinePoolSuggestRequest();

        PageResponse<StandardAlbumVo> pageResponse = unofficialAlbumService.magazinePoolSuggest(request);
        Assert.assertNotNull(pageResponse);
    }

    @Test
    public void generateOcsImageTest() throws IOException, IllegalAccessException {
        // es query mock
        TotalHits totalHits = PowerMockito.mock(TotalHits.class);
        PowerMockito.field(TotalHits.class, "value").set(totalHits, 1);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        UnofficialAlbumEs unofficialAlbumEs = PowerMockito.mock(UnofficialAlbumEs.class);
        SearchHit[] hitArray = {hit};
        SearchResponse response = mock(SearchResponse.class);
        PowerMockito.when(hit.getSourceAsString()).thenReturn("{\"title\":\"活着\"}");
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(hits.getTotalHits()).thenReturn(totalHits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(response);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        when(hits.getHits()).thenReturn(new SearchHit[]{hit});
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(unofficialAlbumEs.getSourceStatus()).thenReturn(1);
        PowerMockito.when(unofficialAlbumImageUrlTransformService.generateImageAndSyncToEs(any(), anyBoolean(), anyBoolean())).thenReturn(true);

        OcsImageUploadAndEsSyncRequest request = new OcsImageUploadAndEsSyncRequest();
        List<String> sourceList = new ArrayList<>();
        request.setSourceList(sourceList);
        unofficialAlbumService.ocsImageUploadAndEsSync(request);
    }

    @Test
    public void sortBySourceTest() {
        List<StandardAlbumVo> voList = new ArrayList<>();
        StandardAlbumVo item1 = new StandardAlbumVo();
        item1.setTitle("title");
        item1.setProgramType("tv");
        item1.setSource("douban");
        StandardAlbumVo item2 = new StandardAlbumVo();
        item2.setTitle("title");
        item2.setProgramType("tv");
        item2.setSource("keke");
        voList.add(item1);
        voList.add(item2);
        Map<String, Map<String, Integer>> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("douban", new HashMap<>());
        linkedHashMap.put("keke", new HashMap<>());
        Whitebox.setInternalState(unofficialAlbumService, "thirdSearchSourceMap", linkedHashMap);
        unofficialAlbumService.sortBySource(voList);
        Assert.assertEquals("douban", voList.get(0).getSource());
    }
}