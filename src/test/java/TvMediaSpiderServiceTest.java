import com.heytap.longvideo.search.api.model.request.TvMediaScrollSearchRequest;
import com.heytap.longvideo.search.api.model.response.TvMediaScrollSearchResponse;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.service.spider.TvMediaSpiderService;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/5/23 下午6:38
 */


@RunWith(PowerMockRunner.class)
@PrepareForTest({SearchHits.class, SearchHit.class, TotalHits.class, RestHighLevelClient.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class TvMediaSpiderServiceTest {

    @InjectMocks
    private TvMediaSpiderService tvMediaSpiderService;

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Test
    public void tvMediaSpiderAllTest() throws Exception {
        TotalHits totalHits = PowerMockito.mock(TotalHits.class);
        PowerMockito.field(TotalHits.class, "value").set(totalHits, 1);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};
        SearchResponse response = mock(SearchResponse.class);
        UnofficialAlbumEs unofficialAlbumEs = mock(UnofficialAlbumEs.class);

        PowerMockito.when(hit.getSourceAsString()).thenReturn("{\"title\":\"若能与你共乘海浪之上\"}");
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(hits.getTotalHits()).thenReturn(totalHits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(response);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(unofficialAlbumEs.getTitle()).thenReturn("若能与你共乘海浪之上");
        TvMediaScrollSearchRequest request = new TvMediaScrollSearchRequest();
        TvMediaScrollSearchResponse response1 = tvMediaSpiderService.tvMediaSpiderAll(request);
        Assert.assertNotNull(response1);
        request.setScrollId(response1.getScrollId());
        TvMediaScrollSearchResponse response2 = tvMediaSpiderService.tvMediaSpiderAll(request);
        Assert.assertNotNull(response2);
        TvMediaScrollSearchResponse response3 = tvMediaSpiderService.tvMediaSpiderIncr(request);
        Assert.assertNotNull(response3);
    }
}
