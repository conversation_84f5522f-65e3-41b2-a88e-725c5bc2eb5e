import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.executors.standard.CmsStandardAlbumExecutor;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.model.param.standard.GetSourceAlbumIdsByMediaIdRequest;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class StandAlbumServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private CmsStandardAlbumExecutor cmsStandardAlbumExecutor;

    @Autowired
    private ListFilterService listFilterService;

    @Autowired
    private InitStandardDataService initStandardDataService;

    @Autowired
    private StandardAlbumService standardAlbumService;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    /**
     *  新增和查询标准化的剧头
     */
    @Test
    public void testStandAlbumService() {
        try {
            elasticsearchService.deleteIndex(StandardAlbumEs.class);
            elasticsearchService.createIndexAndMapping(StandardAlbumEs.class);
            InitDataParam initDataParam=new InitDataParam();
            initDataParam.setType("album");
            initDataParam.setDataBaseEnd(0);
            initDataParam.setTableEnd(0);
            initDataParam.setUpdateTime("2021-01-01 00:00:00");
            initStandardDataService.initData(initDataParam);

            SearchStandardAlbumParams standardAlbumParams =new SearchStandardAlbumParams();
            standardAlbumParams.setSortStrategy(1);
            standardAlbumParams.setPageIndex(1);
            standardAlbumParams.setSource("sohu");
            standardAlbumParams.setStatus("1");
            standardAlbumParams.setTitle("法医狂妃");
            standardAlbumParams.setAgeStart(0);
            PageResponse<StandardAlbumVo> result = standardAlbumService.searchAlbum(standardAlbumParams);
            Assert.assertEquals("506311924435210240",result.getItemList().get(0).getSid());

            StandardAlbum updateStandardAlbum =new StandardAlbum();
                BeanUtils.copyProperties(result.getItemList().get(0),updateStandardAlbum);
            updateStandardAlbum.setTitle("法医狂妃123");
            standardAlbumService.updateStandardAlbum(updateStandardAlbum);
            IndexOperations indexOperations = restTemplate.indexOps(StandardAlbumEs.class);
            indexOperations.refresh();
            Assert.assertEquals("法医狂妃123",standardAlbumService.searchAlbum(standardAlbumParams).getItemList().get(0).getTitle());

            StandardAlbumEs standardAlbumEs =new StandardAlbumEs();
            BeanUtils.copyProperties(updateStandardAlbum,standardAlbumEs);
            standardAlbumService.delete(standardAlbumEs);
            indexOperations.refresh();
            Assert.assertEquals(standardAlbumService.searchAlbum(standardAlbumParams).getItemList().size(),0);

            ListFilterParam listFilterParam =new ListFilterParam();
            listFilterParam.setVersion(60300);
            listFilterParam.setNumber(27);
            listFilterParam.setOffset(0);
            listFilterParam.setUrlpack("{\"cmd_vod\":{\"contentType\":\"all\",\"version_tag\":\"6\",\"source\":\"sohu\"}}");
            List<ProgramAlbumEs> listfilterResult =listFilterService.listFilter(listFilterParam);
            Assert.assertNotNull(listfilterResult);

            GetSourceAlbumIdsByMediaIdRequest getSourceAlbumIdsByMediaIdRequest = new GetSourceAlbumIdsByMediaIdRequest();
            getSourceAlbumIdsByMediaIdRequest.setMediaId("123456");
            List<String> sourceAlbumIds = standardAlbumService.getSourceAlbumIds(getSourceAlbumIdsByMediaIdRequest.getMediaId(),71600);
            Assert.assertEquals(sourceAlbumIds.size(), 1);

        } catch (Throwable e) {
            log.error("testStandAlbumService error", e);
        }
        log.info("test pass");
    }
}