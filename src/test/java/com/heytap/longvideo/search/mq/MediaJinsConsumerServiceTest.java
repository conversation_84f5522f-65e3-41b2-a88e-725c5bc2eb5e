package com.heytap.longvideo.search.mq;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.standard.*;
import com.heytap.longvideo.search.service.sync.AlbumSyncToSearchService;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class MediaJinsConsumerServiceTest {
    @InjectMocks
    MediaJinsConsumerService mediaJinsConsumerService;

    @Mock
    private InitService initService;
    @Mock
    private StandardAlbumMapper standardAlbumMapper;
    @Mock
    private StandardAlbumService albumService;
    @Mock
    private StandardVideoService standardVideoService;
    @Mock
    private StandardEpisodeService standardEpisodeService;
    @Mock
    private UgcStandardVideoService ugcStandardVideoService;
    @Mock
    private StandardTrailerService standardTrailerService;
    @Mock
    private KafkaProducer<String,String> kafkaProducer;
    @Mock
    private AlbumSyncToSearchService albumSyncToSearchService;

    @Test
    public void consumeMessageTest_empty() {
        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(null, null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_album_insert() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"standard_album01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_album_update() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"UPDATE\",\n" +
                "  \"tableName\": \"standard_album01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());
        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_album_delete() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DELETE\",\n" +
                "  \"tableName\": \"standard_album01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }," +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\"\n" +
                "  }" +
                "}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_album_default() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DEFAULT\",\n" +
                "  \"tableName\": \"standard_album01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }


    @Test
    public void consumeMessageTest_video_insert() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_video_update() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"UPDATE\",\n" +
                "  \"tableName\": \"standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_video_delete() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DELETE\",\n" +
                "  \"tableName\": \"standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }," +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\"\n" +
                "  }" +
                "}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_video_default() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DEFAULT\",\n" +
                "  \"tableName\": \"standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }


    @Test
    public void consumeMessageTest_episode_insert() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"standard_episode01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"eid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_episode_update() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"UPDATE\",\n" +
                "  \"tableName\": \"standard_episode01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"eid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_episode_delete() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DELETE\",\n" +
                "  \"tableName\": \"standard_episode01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"eid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }," +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"eid\": \"2001\"\n" +
                "  }" +
                "}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_episode_default() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DEFAULT\",\n" +
                "  \"tableName\": \"standard_episode01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"eid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }


    @Test
    public void consumeMessageTest_trailer_insert() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"standard_trailer01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"tid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_trailer_update() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"UPDATE\",\n" +
                "  \"tableName\": \"standard_trailer01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"tid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_trailer_delete() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DELETE\",\n" +
                "  \"tableName\": \"standard_trailer01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"tid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }," +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"tid\": \"2001\"\n" +
                "  }" +
                "}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_trailer_default() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DEFAULT\",\n" +
                "  \"tableName\": \"standard_trailer01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"tid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }


    @Test
    public void consumeMessageTest_ugc_insert() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"ugc_standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_ugc_update() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"UPDATE\",\n" +
                "  \"tableName\": \"ugc_standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_ugc_delete() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DELETE\",\n" +
                "  \"tableName\": \"ugc_standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }," +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\"\n" +
                "  }" +
                "}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_ugc_default() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DEFAULT\",\n" +
                "  \"tableName\": \"ugc_standard_video01\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"vid\": \"2001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        when(albumService.searchBySid(any())).thenReturn(new StandardAlbumEs());
        when(standardAlbumMapper.getStandardAlbumBySid(anyInt(), anyInt(), anyString())).thenReturn(new StandardAlbum());

        ConsumeConcurrentlyStatus result = mediaJinsConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }
}
