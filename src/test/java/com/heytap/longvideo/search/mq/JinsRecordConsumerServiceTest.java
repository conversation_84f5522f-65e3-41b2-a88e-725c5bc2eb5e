package com.heytap.longvideo.search.mq;

import com.google.common.collect.Lists;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.elasticsearch.client.IndicesClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class) // 1. 使用PowerMockRunner
@PrepareForTest({IndicesClient.class, CreateIndexResponse.class, RestHighLevelClient.class}) // 2. 准备要mock的final类
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class JinsRecordConsumerServiceTest {
    @InjectMocks
    JinsRecordConsumerService jinsRecordConsumerService;

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Mock
    private ElasticsearchRestTemplate restTemplate;

    @Test
    public void consumeMessageTest_empty() {
        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(null, null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_excludeTableSet() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"banTable\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        Set<String> excludeTableSet = new HashSet<>();
        excludeTableSet.add("banTable");
        Whitebox.setInternalState(jinsRecordConsumerService, "excludeTableSet", excludeTableSet);

        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_tableSet() {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"C\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        Set<String> excludeTableSet = new HashSet<>();
        excludeTableSet.add("A");
        Whitebox.setInternalState(jinsRecordConsumerService, "excludeTableSet", excludeTableSet);
        Set<String> tableSet = new HashSet<>();
        tableSet.add("B");
        Whitebox.setInternalState(jinsRecordConsumerService, "tableSet", tableSet);

        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_insert() throws IOException {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"INSERT\",\n" +
                "  \"tableName\": \"B\",\n" +
                "  \"schemaName\": \"DB\",\n" +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        Set<String> excludeTableSet = new HashSet<>();
        excludeTableSet.add("A");
        Whitebox.setInternalState(jinsRecordConsumerService, "excludeTableSet", excludeTableSet);
        Set<String> tableSet = new HashSet<>();
        tableSet.add("B");
        Whitebox.setInternalState(jinsRecordConsumerService, "tableSet", tableSet);

        IndicesClient indicesClient = PowerMockito.mock(IndicesClient.class);
        PowerMockito.when(indicesClient.exists(any(GetIndexRequest.class), any())).thenReturn(false);

        CreateIndexResponse createIndexResponse = PowerMockito.mock(CreateIndexResponse.class);
        PowerMockito.when(createIndexResponse.isAcknowledged()).thenReturn(true);
        PowerMockito.when(indicesClient.create(any(CreateIndexRequest.class), any())).thenReturn(createIndexResponse);
        PowerMockito.when(restHighLevelClient.indices()).thenReturn(indicesClient);

        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_update() throws IOException {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"UPDATE\",\n" +
                "  \"tableName\": \"B\",\n" +
                "  \"schemaName\": \"DB\",\n" +
                "  \"before\": {\n" +
                "    \"sid\": \"1002\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }," +
                "  \"after\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        Set<String> excludeTableSet = new HashSet<>();
        excludeTableSet.add("A");
        Whitebox.setInternalState(jinsRecordConsumerService, "excludeTableSet", excludeTableSet);
        Set<String> tableSet = new HashSet<>();
        tableSet.add("B");
        Whitebox.setInternalState(jinsRecordConsumerService, "tableSet", tableSet);

        IndicesClient indicesClient = PowerMockito.mock(IndicesClient.class);
        PowerMockito.when(indicesClient.exists(any(GetIndexRequest.class), any())).thenReturn(false);

        CreateIndexResponse createIndexResponse = PowerMockito.mock(CreateIndexResponse.class);
        PowerMockito.when(createIndexResponse.isAcknowledged()).thenReturn(true);
        PowerMockito.when(indicesClient.create(any(CreateIndexRequest.class), any())).thenReturn(createIndexResponse);
        PowerMockito.when(restHighLevelClient.indices()).thenReturn(indicesClient);

        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_delete() throws IOException {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DELETE\",\n" +
                "  \"tableName\": \"B\",\n" +
                "  \"schemaName\": \"DB\",\n" +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        Set<String> excludeTableSet = new HashSet<>();
        excludeTableSet.add("A");
        Whitebox.setInternalState(jinsRecordConsumerService, "excludeTableSet", excludeTableSet);
        Set<String> tableSet = new HashSet<>();
        tableSet.add("B");
        Whitebox.setInternalState(jinsRecordConsumerService, "tableSet", tableSet);

        IndicesClient indicesClient = PowerMockito.mock(IndicesClient.class);
        PowerMockito.when(indicesClient.exists(any(GetIndexRequest.class), any())).thenReturn(false);

        CreateIndexResponse createIndexResponse = PowerMockito.mock(CreateIndexResponse.class);
        PowerMockito.when(createIndexResponse.isAcknowledged()).thenReturn(true);
        PowerMockito.when(indicesClient.create(any(CreateIndexRequest.class), any())).thenReturn(createIndexResponse);
        PowerMockito.when(restHighLevelClient.indices()).thenReturn(indicesClient);

        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void consumeMessageTest_throw() throws IOException {
        MessageExt msg = new MessageExt();
        String data = "{  \"eventType\": \"DEFAULT\",\n" +
                "  \"tableName\": \"B\",\n" +
                "  \"schemaName\": \"DB\",\n" +
                "  \"before\": {\n" +
                "    \"sid\": \"1001\",\n" +
                "    \"previewInfo\": \"12345678900\"" +
                "  }}";
        msg.setBody(data.getBytes());

        Set<String> excludeTableSet = new HashSet<>();
        excludeTableSet.add("A");
        Whitebox.setInternalState(jinsRecordConsumerService, "excludeTableSet", excludeTableSet);
        Set<String> tableSet = new HashSet<>();
        tableSet.add("B");
        Whitebox.setInternalState(jinsRecordConsumerService, "tableSet", tableSet);

        IndicesClient indicesClient = PowerMockito.mock(IndicesClient.class);
        PowerMockito.when(indicesClient.exists(any(GetIndexRequest.class), any())).thenReturn(false);

        CreateIndexResponse createIndexResponse = PowerMockito.mock(CreateIndexResponse.class);
        PowerMockito.when(createIndexResponse.isAcknowledged()).thenReturn(true);
        PowerMockito.when(indicesClient.create(any(CreateIndexRequest.class), any())).thenReturn(createIndexResponse);
        PowerMockito.when(restTemplate.save()).thenThrow(new RuntimeException());

        ConsumeConcurrentlyStatus result = jinsRecordConsumerService.consumeMessage(Lists.newArrayList(msg), null);
        assertEquals(ConsumeConcurrentlyStatus.RECONSUME_LATER, result);
    }
}
