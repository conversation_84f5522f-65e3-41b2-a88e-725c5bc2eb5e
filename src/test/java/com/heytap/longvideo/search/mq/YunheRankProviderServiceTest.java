package com.heytap.longvideo.search.mq;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.enums.YunheRankKindEnum;
import com.heytap.longvideo.client.media.enums.YunheRankTypeEnum;
import com.heytap.longvideo.client.media.yunhe.YunheRank;
import com.heytap.longvideo.search.properties.MqProperties;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Collections;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class YunheRankProviderServiceTest {
    @InjectMocks
    YunheRankProviderService yunheRankProviderService;

    @Mock
    private MqProperties mqProperties;

    @Before
    public void setup() {
        when(mqProperties.getYunheRankTopic()).thenReturn("trace-news-resource-hot-info-topic");
        when(mqProperties.getYunheRankKey()).thenReturn("YunHeChart");
        when(mqProperties.getYunheRankServers()).thenReturn("dgtest-content-test-kafka-1.global.yyy4fp.kafka.oppo.test:9095,dgtest-content-test-kafka-1.global.yyyjfp.kafka.oppo.test:9095,dgtest-content-test-kafka-1.global.yyywfp.kafka.oppo.test:9095");
    }

    @Test
    public void sendMQTest_empty() {
        yunheRankProviderService.sendMQ(Collections.emptyList());
        verify(mqProperties, times(0)).getYunheRankTopic();
    }

    @Test
    public void sendMQTest() {
        YunheRank yunheRank1 = new YunheRank();
        yunheRank1.setType(YunheRankTypeEnum.TV.getCode());
        yunheRank1.setKind(YunheRankKindEnum.TOP.getCode());
        yunheRank1.setValue(new BigDecimal("19.5"));

        YunheRank yunheRank2 = new YunheRank();
        yunheRank2.setType(YunheRankTypeEnum.ART.getCode());
        yunheRank2.setKind(YunheRankKindEnum.HOT.getCode());
        yunheRank2.setValue(new BigDecimal("8860.0"));

        YunheRank yunheRank3 = new YunheRank();
        yunheRank3.setType(YunheRankTypeEnum.ANIMATION.getCode());
        yunheRank3.setKind(YunheRankKindEnum.TOP.getCode());

        YunheRank yunheRank4 = new YunheRank();
        yunheRank4.setType(YunheRankTypeEnum.MOVIE.getCode());

        yunheRankProviderService.sendMQ(Lists.newArrayList(yunheRank1, yunheRank2, yunheRank3, yunheRank4));
    }



    @Test
    public void sendMQTest_throw() {
        YunheRank yunheRank1 = new YunheRank();
        yunheRank1.setType(YunheRankTypeEnum.TV.getCode());
        yunheRank1.setKind(YunheRankKindEnum.TOP.getCode());
        yunheRank1.setValue(new BigDecimal("19.5"));


        when(mqProperties.getYunheRankKey()).thenThrow(new RuntimeException());

        yunheRankProviderService.sendMQ(Lists.newArrayList(yunheRank1));
    }
}
