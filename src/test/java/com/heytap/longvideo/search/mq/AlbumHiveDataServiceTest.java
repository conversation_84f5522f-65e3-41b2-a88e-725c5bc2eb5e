package com.heytap.longvideo.search.mq;


import com.google.common.collect.Lists;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.model.entity.LvAlbumConsumeHiveData;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.rpc.consumer.LvAlbumConsumeHiveDataRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.VirtualProgramRpcApiProxy;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class AlbumHiveDataServiceTest {
    @InjectMocks
    AlbumHiveDataService albumHiveDataService;

    @Mock
    private LvAlbumConsumeHiveDataRpcApiProxy lvAlbumConsumeHiveDataRpcApiProxy;

    @Mock
    private StandardAlbumService albumService;

    @Mock
    private VirtualProgramRpcApiProxy virtualProgramRpcApiProxy;

    @Mock
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    @Test
    public void processDataTest_virtualSidIsNull() {
        LvAlbumConsumeHiveData data = new LvAlbumConsumeHiveData();
        data.setDayno("20250410");
        data.setSid("123");
        data.setPlayPv(1L);
        data.setLast15DaysPlayPv(1L);
        data.setLast30DaysPlayPv(1L);
        data.setLast7DaysPlayPv(1L);
        data.setLast7DaysClickPv(1L);
        data.setLast15DaysClickPv(1L);
        data.setLast30DaysClickPv(1L);

        MisVirtualProgramRelation relation = new MisVirtualProgramRelation();
        RpcResult rpcResult = new RpcResult(relation);
        when(virtualProgramRpcApiProxy.queryBySid(any())).thenReturn(CompletableFuture.completedFuture(rpcResult));

        RpcResult rpcResult2 = new RpcResult(Lists.newArrayList(relation));
        when(virtualProgramRpcApiProxy.queryByVirtualSid(any())).thenReturn(CompletableFuture.completedFuture(rpcResult2));

        StandardAlbumEs standardAlbumEs = new StandardAlbumEs();
        standardAlbumEs.setDayNo(20250410);
        standardAlbumEs.setOppoHot(1L);
        standardAlbumEs.setLast15DaysPlayPv(1L);
        standardAlbumEs.setLast30DaysPlayPv(1L);
        standardAlbumEs.setLast7DaysPlayPv(1L);
        standardAlbumEs.setLast7DaysClickPv(1L);
        standardAlbumEs.setLast15DaysClickPv(1L);
        standardAlbumEs.setLast30DaysClickPv(1L);
        when(albumService.searchBySid(any())).thenReturn(standardAlbumEs);

        ProgramAlbumEs programAlbumEs = new ProgramAlbumEs();
        SearchHit<ProgramAlbumEs> hit1 = mock(SearchHit.class);
        PowerMockito.when(hit1.getScore()).thenReturn(1.5f);
        PowerMockito.when(hit1.getContent()).thenReturn(programAlbumEs);
        SearchHits<ProgramAlbumEs> searchHits = mock(SearchHits.class);
        PowerMockito.when(searchHits.iterator()).thenReturn(Arrays.asList(hit1).iterator());

        doReturn(searchHits).when(elasticsearchRestTemplate).search(any(NativeSearchQuery.class), any());

        albumHiveDataService.processData(Lists.newArrayList(data));
    }

    @Test
    public void processDataTest_virtualSidNotNull() {
        LvAlbumConsumeHiveData data = new LvAlbumConsumeHiveData();
        data.setDayno("20250410");
        data.setSid("123");
        data.setPlayPv(1L);
        data.setLast15DaysPlayPv(1L);
        data.setLast30DaysPlayPv(1L);
        data.setLast7DaysPlayPv(1L);
        data.setLast7DaysClickPv(1L);
        data.setLast15DaysClickPv(1L);
        data.setLast30DaysClickPv(1L);

        MisVirtualProgramRelation relation = new MisVirtualProgramRelation();
        relation.setVirtualSid("321");
        RpcResult rpcResult = new RpcResult(relation);
        when(virtualProgramRpcApiProxy.queryBySid(any())).thenReturn(CompletableFuture.completedFuture(rpcResult));

        RpcResult rpcResult2 = new RpcResult(Lists.newArrayList(relation));
        when(virtualProgramRpcApiProxy.queryByVirtualSid(any())).thenReturn(CompletableFuture.completedFuture(rpcResult2));

        StandardAlbumEs standardAlbumEs = new StandardAlbumEs();
        standardAlbumEs.setDayNo(20250410);
        standardAlbumEs.setOppoHot(1L);
        standardAlbumEs.setLast15DaysPlayPv(1L);
        standardAlbumEs.setLast30DaysPlayPv(1L);
        standardAlbumEs.setLast7DaysPlayPv(1L);
        standardAlbumEs.setLast7DaysClickPv(1L);
        standardAlbumEs.setLast15DaysClickPv(1L);
        standardAlbumEs.setLast30DaysClickPv(1L);
        when(albumService.searchBySid(any())).thenReturn(standardAlbumEs);

        ProgramAlbumEs programAlbumEs = new ProgramAlbumEs();
        SearchHit<ProgramAlbumEs> hit1 = mock(SearchHit.class);
        PowerMockito.when(hit1.getScore()).thenReturn(1.5f);
        PowerMockito.when(hit1.getContent()).thenReturn(programAlbumEs);
        SearchHits<ProgramAlbumEs> searchHits = mock(SearchHits.class);
        PowerMockito.when(searchHits.iterator()).thenReturn(Arrays.asList(hit1).iterator());

        RpcResult rpcResult3 = new RpcResult(data);
        when(lvAlbumConsumeHiveDataRpcApiProxy.getBySidAndDayno(any(), any())).thenReturn(CompletableFuture.completedFuture(rpcResult3));

        doReturn(searchHits).when(elasticsearchRestTemplate).search(any(NativeSearchQuery.class), any());

        albumHiveDataService.processData(Lists.newArrayList(data));
    }

    @Test
    public void processDataTest_dayNoNotEquals() {
        LvAlbumConsumeHiveData data = new LvAlbumConsumeHiveData();
        data.setDayno("20250410");
        data.setSid("123");
        data.setPlayPv(1L);
        data.setLast15DaysPlayPv(1L);
        data.setLast30DaysPlayPv(1L);
        data.setLast7DaysPlayPv(1L);
        data.setLast7DaysClickPv(1L);
        data.setLast15DaysClickPv(1L);
        data.setLast30DaysClickPv(1L);

        MisVirtualProgramRelation relation = new MisVirtualProgramRelation();
        relation.setVirtualSid("321");
        RpcResult rpcResult = new RpcResult(relation);
        when(virtualProgramRpcApiProxy.queryBySid(any())).thenReturn(CompletableFuture.completedFuture(rpcResult));

        RpcResult rpcResult2 = new RpcResult(Lists.newArrayList(relation));
        when(virtualProgramRpcApiProxy.queryByVirtualSid(any())).thenReturn(CompletableFuture.completedFuture(rpcResult2));

        StandardAlbumEs standardAlbumEs = new StandardAlbumEs();
        standardAlbumEs.setDayNo(20250411);
        when(albumService.searchBySid(any())).thenReturn(standardAlbumEs);

        ProgramAlbumEs programAlbumEs = new ProgramAlbumEs();
        SearchHit<ProgramAlbumEs> hit1 = mock(SearchHit.class);
        PowerMockito.when(hit1.getScore()).thenReturn(1.5f);
        PowerMockito.when(hit1.getContent()).thenReturn(programAlbumEs);
        SearchHits<ProgramAlbumEs> searchHits = mock(SearchHits.class);
        PowerMockito.when(searchHits.iterator()).thenReturn(Arrays.asList(hit1).iterator());

        RpcResult rpcResult3 = new RpcResult(data);
        when(lvAlbumConsumeHiveDataRpcApiProxy.getBySidAndDayno(any(), any())).thenReturn(CompletableFuture.completedFuture(rpcResult3));

        doReturn(searchHits).when(elasticsearchRestTemplate).search(any(NativeSearchQuery.class), any());

        albumHiveDataService.processData(Lists.newArrayList(data));
    }
}
