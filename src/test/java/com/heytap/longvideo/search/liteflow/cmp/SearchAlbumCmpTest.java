package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.search.constants.KeywordSearchTypeEnum;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SearchAlbumCmpTest {
    @InjectMocks
    private SearchAlbumCmp searchAlbumCmp;

    @Test
    public void buildBlankQueryTest() {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        KeywordSearchTypeEnum searchTypeEnum = searchAlbumCmp.buildBlankQuery("A B C", boolQueryBuilder);
        assertEquals(KeywordSearchTypeEnum.SPACE, searchTypeEnum);

    }
}
