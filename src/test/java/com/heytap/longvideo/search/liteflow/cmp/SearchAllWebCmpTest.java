package com.heytap.longvideo.search.liteflow.cmp;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.search.api.LvSearchKeywordRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchKeyword;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.CopyrightConstant;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.utils.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


import static com.heytap.longvideo.search.constants.SearchConstant.SEARCH_FROM_BREENO;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SearchAllWebCmpTest {
    @InjectMocks
    private SearchAllWebCmp searchAllWebCmp;

    @Mock
    private SearchProperties searchProperties;

    @Mock
    private LvSearchKeywordRpcApi lvSearchKeywordRpcApi;

    @Mock
    private ElasticsearchRestTemplate restTemplate;

    @Mock
    private HotVideoService hotVideoService;

    @Mock
    private ConvertResponseService convertResponseService;

    @Before
    public void build() {
        Map<String, Map<String, Integer>> thirdSearchMap = JacksonUtil.parseObject(
                "{\"tencent\":{\"switch\":2,\"jump\":2},\"iqiyimobile\":{\"switch\":2,\"jump\":0},\"iqiyi\":{\"switch\":2,\"jump\":2},\"youku\":{\"switch\":2,\"jump\":2},\"douban\":{\"switch\":2,\"jump\":2},\"cupfox\":{\"switch\":2,\"jump\":2},\"keke\":{\"switch\":2,\"jump\":2}}",
                Map.class);
        when(searchProperties.getThirdSearchMap()).thenReturn(thirdSearchMap);
        when(searchProperties.getOutSideThirdSearchMap()).thenReturn(thirdSearchMap);

        when(searchProperties.getDoubanPriority()).thenReturn(JacksonUtil.parseObject(
                "[\"tencent\",\"iqiyi\",\"youku\",\"mgtv\",\"sohu\",\"bilibili\"]",
                new TypeReference<List<String>>() {}));

        Map<String, Integer> searchSwitchConfig = JacksonUtil.parseObject(
                "{\"0\": 1,\"1\": 0,\"2\": 0,\"3\": 0}",
                Map.class);
        when(searchProperties.getSearchSwitchConfig()).thenReturn(searchSwitchConfig);

        when(searchProperties.getHotKeyWordSwitch()).thenReturn(0);
        when(searchProperties.getThirdSearchScore()).thenReturn(0.7f);

        when(searchProperties.getBrowserUrlLink()).thenReturn("heytapbrowser://webpage/view?url=%s");
        when(searchProperties.getBrowserDeepLink()).thenReturn("heytapbrowser://search/resultPage?search_content=%s&browser_partner=yolisearch");
        when(searchProperties.getIqiyiSearchUrl()).thenReturn("https://m.iqiyi.com/search.html?source=input&key=%s");
        when(searchProperties.getBaiduSearchUrl()).thenReturn("https://m.baidu.com/s?word=%s");

    }

    /**
     * 校验拦截场景
     */
    @Test
    public void checkTest() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(60299);
        param.setPageIndex(1);
        Assert.assertFalse(searchAllWebCmp.check(param));

        param = new KeyWordSearchParamV2();
        param.setVersion(60300);
        param.setPageIndex(2);
        Assert.assertFalse(searchAllWebCmp.check(param));

        param = new KeyWordSearchParamV2();
        param.setVersion(60300);
        param.setPageIndex(1);
        param.setKeyword("");
        Assert.assertFalse(searchAllWebCmp.check(param));

        param = new KeyWordSearchParamV2();
        param.setVersion(60300);
        param.setPageIndex(1);
        param.setKeyword("1");
        param.setDeviceType(1);
        Assert.assertFalse(searchAllWebCmp.check(param));
    }

    @Test
    public void queryInterveneResultTest() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(60300);
        param.setPageIndex(1);
        param.setKeyword("1");
        param.setDeviceType(0);
        param.setSearchType("1");
        param.setKeyword("爱的二八定律");

        // mock干预项查询接口
        RpcResult<List<LvSearchKeyword>> resp =new RpcResult(0,"success");
        List<LvSearchKeyword> lvSearchKeywords = JacksonUtil.parseObject("[{\n" +
                        "    \"sid\": \"sid1\",\n" +
                        "    \"title\": \"title\",\n" +
                        "    \"query_keyword\": \"pre_爱的二八定律\",\n" +
                        "    \"source\": \"tencent\",\n" +
                        "    \"orderIndex\": 1\n" +
                        "}]",
                new TypeReference<List<LvSearchKeyword>>() {});
        resp.setData(lvSearchKeywords);
        when(lvSearchKeywordRpcApi.selectThirdSearchKeyWordList(any())).thenReturn(resp);

        SearchHit<UnofficialAlbumEs> intentHit = buildSearchHit();
        intentHit.getContent().setSid("sid1");
        intentHit.getContent().setTitle("title");

        SearchHits intentHits = mock(SearchHits.class);
        when(intentHits.getSearchHits()).thenReturn(Lists.newArrayList(intentHit));

        when(restTemplate.search(argThat((ArgumentMatcher<NativeSearchQuery>) query ->
                query!=null && query.getQuery().toString().contains("sid1")), any()))
                .thenReturn(intentHits);

        // mock ES查询
        SearchHit<UnofficialAlbumEs> hit1 = buildSearchHit();
        hit1.getContent().setSid("sid2");
        hit1.getContent().setTitle("title2");
        hit1.getContent().setCopyrightCode("bilibili");

        SearchHit<UnofficialAlbumEs> hit2 = buildSearchHit();
        hit2.getContent().setSid("sid3");
        hit2.getContent().setTitle("title2");
        hit1.getContent().setCopyrightCode("tencent");

        SearchHits searchHits = mock(SearchHits.class);
        when(searchHits.getSearchHits()).thenReturn(Lists.newArrayList(hit1, hit2));
        when(restTemplate.search(argThat((ArgumentMatcher<NativeSearchQuery>) query ->
                query!=null && query.getQuery().toString().contains("爱的二八定律")), any()))
                .thenReturn(searchHits);

        List<KeyWordSearchResponse> baseSearchResult = new ArrayList<>();
        List<KeyWordSearchResponse> interveneResult = searchAllWebCmp.queryInterveneResult(param, baseSearchResult,null, null);
        Assert.assertEquals("sid1", interveneResult.get(0).getSid());

        List<KeyWordSearchResponse> searchResult = searchAllWebCmp.querySearchResult(param, baseSearchResult, interveneResult,null, null);
        Assert.assertEquals("sid3", searchResult.get(0).getSid());


        param.setKeyword("爱的二八 定律");
        when(restTemplate.search(argThat((ArgumentMatcher<NativeSearchQuery>) query ->
                query!=null && query.getQuery().toString().contains("爱的二八")), any()))
                .thenReturn(searchHits);
        searchResult = searchAllWebCmp.querySearchResult(param, baseSearchResult, interveneResult,null, null);
        Assert.assertEquals("sid3", searchResult.get(0).getSid());
    }

    // 测试jump=0的分支（APP打开三方H5页）
    @Test
    public void setJumpLinkTest_Iqiyi_jump0() {
        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        UnofficialAlbumEs es = new UnofficialAlbumEs();
        es.setTitle("标题");
        es.setSource(CopyrightConstant.COPYRIGHT_IQIYI);
        searchAllWebCmp.setJumpLinkForInSide(searchResponse, es,80000, 0);
        Assert.assertEquals("https://m.iqiyi.com/search.html?source=input&key=标题", es.getSourceWebUrl());
    }

    @Test
    public void setJumpLinkTest_Iqiyi_jump2() {
        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        UnofficialAlbumEs es = new UnofficialAlbumEs();
        es.setTitle("标题");
        es.setSource(CopyrightConstant.COPYRIGHT_IQIYI);
        searchAllWebCmp.setJumpLinkForInSide(searchResponse, es,80000, 2);
        Assert.assertEquals("heytapbrowser://webpage/view?url=https%3A%2F%2Fm.iqiyi.com%2Fsearch.html%3Fsource%3Dinput%26key%3D%E6%A0%87%E9%A2%98",
                searchResponse.getDeepLink());
    }

    @Test
    public void setJumpLinkTest_jump1() {
        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        UnofficialAlbumEs es = new UnofficialAlbumEs();
        searchResponse.setTitle("标题");
        searchAllWebCmp.setJumpLinkForInSide(searchResponse, es,80000, 1);
        Assert.assertEquals("heytapbrowser://search/resultPage?search_content=标题&browser_partner=yolisearch",
                searchResponse.getDeepLink());
    }

    @Test
    public void setJumpLinkTest_jump3() {
        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        UnofficialAlbumEs es = new UnofficialAlbumEs();
        searchResponse.setTitle("标题");
        searchAllWebCmp.setJumpLinkForInSide(searchResponse, es,80000, 3);
        Assert.assertEquals("https://m.baidu.com/s?word=标题",
                searchResponse.getWebUrl());
    }

    /**
     * 小布搜素 情况1：不返回全网搜 但是只返回移动端爱奇艺
     */
    @Test
    public void getThirdSearchMapTest_breeno1() {
        when(searchProperties.isBreenoSearchIqiyiMmobileSwitch()).thenReturn(true);
        Map<String, Integer> searchNetResults = new HashMap<>();
        searchNetResults.put(SEARCH_FROM_BREENO, 0);

        Map<String, Map<String, Integer>> thirdSearchMap =
                searchAllWebCmp.getThirdSearchMap(SEARCH_FROM_BREENO, searchNetResults);

        Assert.assertTrue(thirdSearchMap.containsKey(SourceEnum.IQIYI_MOBILE.getDataSource()));
        Assert.assertEquals(1, thirdSearchMap.size());
    }

    /**
     * 小布搜素 情况2：返回全网搜(根据配置决定不包含移动端爱奇艺)
     */
    @Test
    public void getThirdSearchMapTest_breeno2() {
        when(searchProperties.isBreenoSearchIqiyiMmobileSwitch()).thenReturn(false);
        Map<String, Integer> searchNetResults = new HashMap<>();
        searchNetResults.put(SEARCH_FROM_BREENO, 1);

        Map<String, Map<String, Integer>> thirdSearchMap =
                searchAllWebCmp.getThirdSearchMap(SEARCH_FROM_BREENO, searchNetResults);

        Assert.assertFalse(thirdSearchMap.containsKey(SourceEnum.IQIYI_MOBILE.getDataSource()));
        Assert.assertEquals(5, thirdSearchMap.size());
    }




    private SearchHit<UnofficialAlbumEs> buildSearchHit() {
        return JSON.parseObject("    {\n" +
                        "        \"content\": {\n" +
                        "            \"actor\": \"尊龙|陈冲|邬君梅|彼得·奥图尔|英若诚|吴涛|黄自强|丹尼斯·邓|坂本龙一|马吉·汉|里克·扬|田川洋行|苟杰德|理查德·吴|皱缇格|陈凯歌|卢燕|蒋锡礽|陈述|鲍皓昕|黄文捷|邵茹贞|亨利·基|张良斌|梁冬|康斯坦丁·格雷戈里|黄汉琪|王涛|宋怀桂|蔡鸿翔|程淑艳|张天民\",\n" +
                        "            \"area\": \"英国 / 意大利 / 中国大陆 / 法国\",\n" +
                        "            \"copyright\": 1,\n" +
                        "            \"copyrightCode\": \"tencent\",\n" +
                        "            \"createTime\": 1731552190236,\n" +
                        "            \"director\": \"贝纳尔多·贝托鲁奇\",\n" +
                        "            \"duration\": 163,\n" +
                        "            \"featureType\": 1,\n" +
                        "            \"horizontalIcon\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"horizontalImage\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"information\": \"溥仪（尊龙 饰）的一生在电影中娓娓道来。他从三岁起登基，年幼的眼光中只有大臣身上的一只蝈蝈，江山在他心中只是一个不明所以的名词。长大了，他以为可以变革，却被太监一把火烧了朝廷账本。他以为自己是大清江山的主人，却做了日本人的傀儡。解放后，他坐上了从苏联回来的火车，身边是押送监视他的解放军。他猜测自己难逃一死，便躲在狭小的卫生间里，割脉自杀。然而他没有死在火车上，命运的嘲笑还在等着他。文革的风风雨雨，在他身上留下了斑斑伤痕。\",\n" +
                        "            \"language\": \"英语 / 汉语普通话 / 日语\",\n" +
                        "            \"managerStatus\": 1,\n" +
                        "            \"programType\": \"movie\",\n" +
                        "            \"sid\": \"1074228711114592256\",\n" +
                        "            \"source\": \"douban\",\n" +
                        "            \"sourceAlbumId\": \"db_1293172\",\n" +
                        "            \"sourceScore\": \"9.3\",\n" +
                        "            \"sourceStatus\": 1,\n" +
                        "            \"sourceType\": 0,\n" +
                        "            \"sourceWebUrl\": \"https://v.qq.com/x/cover/29trop8s2ipr3es.html?ptag=newdouban.movie&subtype=1&type=online-video\",\n" +
                        "            \"status\": 1,\n" +
                        "            \"tags\": \"剧情|传记|历史\",\n" +
                        "            \"title\": \"末代皇帝\",\n" +
                        "            \"updateTime\": 1731552190236,\n" +
                        "            \"verticalIcon\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"verticalImage\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"year\": 1987\n" +
                        "        },\n" +
                        "        \"highlightFields\": {},\n" +
                        "        \"id\": \"1074228711114592256\",\n" +
                        "        \"score\": 46.82381,\n" +
                        "        \"sortValues\": []\n" +
                        "    }",
                new com.alibaba.fastjson.TypeReference<SearchHit<UnofficialAlbumEs>>() {});
    }

    @Test
    public void addPriorityByHotVideoTest() {
        when(hotVideoService.getHashMap(any())).thenReturn(new ConcurrentHashMap());

        SearchHit<UnofficialAlbumEs> searchHit = buildSearchHit();
        searchAllWebCmp.addPriorityByHotVideo(searchHit.getContent());
    }

    @Test
    public void setJumpLinkForInSideOrOutSideTest() {
        KeyWordSearchResponse response = new KeyWordSearchResponse();
        UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
        searchAllWebCmp.setJumpLinkForOutSide(response, unofficialAlbumEs, 5000, "quansou_sousuozhida");
        Assert.assertTrue(response.getDeepLink().startsWith("yoli://yoli.com/YoliSearch/s"));
        searchAllWebCmp.setJumpLinkForOutSide(response, unofficialAlbumEs, 80700, "quansou_sousuozhida");
        Assert.assertTrue(response.getDeepLink().startsWith("yoli://yoli.com/detail"));
        searchAllWebCmp.setJumpLinkForOutSide(response, unofficialAlbumEs, 5000, "browser_sousuozhida");
        Assert.assertTrue(response.getDeepLink().startsWith("yoli://yoli.com/detail"));
    }

    @Test
    public void setBriefTest() {
        UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
        KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
        unofficialAlbumEs.setBrief("123");
        searchAllWebCmp.setBrief(unofficialAlbumEs, keyWordSearchResponse);
        Assert.assertEquals("123", keyWordSearchResponse.getBrief());
        unofficialAlbumEs.setBrief(null);

        unofficialAlbumEs.setActor("1|2|3");
        searchAllWebCmp.setBrief(unofficialAlbumEs, keyWordSearchResponse);
        Assert.assertEquals("1 2 3", keyWordSearchResponse.getBrief());
        unofficialAlbumEs.setActor(null);

        Map<String, List<String>> mockDefaultBriefMap = new HashMap<>();
        ArrayList<String> list = new ArrayList<>();
        list.add("全网好片等你来看");
        mockDefaultBriefMap.put("other", list);
        ArrayList<String> list2 = new ArrayList<>();
        list2.add("爆款剧集，一键直达");
        mockDefaultBriefMap.put("tv", list2);
        ReflectionTestUtils.setField(searchAllWebCmp, "defaultBriefMap", mockDefaultBriefMap);
        searchAllWebCmp.setBrief(unofficialAlbumEs, keyWordSearchResponse);
        Assert.assertEquals("全网好片等你来看", keyWordSearchResponse.getBrief());

        unofficialAlbumEs.setProgramType("tv");
        searchAllWebCmp.setBrief(unofficialAlbumEs, keyWordSearchResponse);
        Assert.assertEquals("爆款剧集，一键直达", keyWordSearchResponse.getBrief());
    }
}
