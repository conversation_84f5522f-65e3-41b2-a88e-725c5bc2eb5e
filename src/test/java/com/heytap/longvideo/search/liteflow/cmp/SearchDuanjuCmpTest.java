package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.search.constants.SearchTabEnum;
import com.heytap.longvideo.search.model.duanju.DuanjuSearchResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SearchDuanjuCmpTest {
    @InjectMocks
    private SearchDuanjuCmp searchDuanjuCmp;

    @Mock
    private SearchProperties searchProperties;

    @Mock
    private HttpDataChannel httpDataChannel;

    @Before
    public void setup() {
        when(searchProperties.getDuanjuSearchVersion()).thenReturn(80700);
    }

    @Test
    public void searchDuanjuTest_noVersion() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80600);
        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void searchDuanjuTest_noJump() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void searchDuanjuTest_jumpFalse() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        param.setLastSearchTab(SearchTabEnum.DUANJU.getCode());
        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void searchDuanjuTest_searchType() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        param.setSearchType(SearchTabEnum.LONGVIDEO.getCode());
        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }


    @Test
    public void searchDuanjuTest_deviceType() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        param.setSearchType(SearchTabEnum.SUMMARY.getCode());
        param.setDeviceType(1);
        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void searchDuanjuTest_http_null() throws HttpDataChannelException {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        param.setSearchType(SearchTabEnum.SUMMARY.getCode());

        when(httpDataChannel.getForObject(any(),any(),any(),anyInt())).thenReturn(null);

        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void searchDuanjuTest_http_throw() throws HttpDataChannelException {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        param.setSearchType(SearchTabEnum.SUMMARY.getCode());

        when(httpDataChannel.getForObject(any(),any(),any(),anyInt())).thenThrow(new RuntimeException());

        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void searchDuanjuTest() throws HttpDataChannelException {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80700);
        param.setSearchType(SearchTabEnum.SUMMARY.getCode());

        when(httpDataChannel.getForObject(any(),any(),any(),anyInt())).thenReturn(buildResponse());
        when(searchProperties.getDuanjuSourceIdBlacklist()).thenReturn(Lists.newArrayList("source3_id3"));
        when(searchProperties.getDuanjuPlayUrl()).thenReturn("yoli://yoli.com/shortDrama/detail?source=%s&dramaId=%s");

        List<KeyWordSearchResponse> result = searchDuanjuCmp.searchDuanju(param);
        assertEquals(1, result.size());
    }

    private DuanjuSearchResponse buildResponse() {
        return JacksonUtil.parseObject(
                "{\n" +
                        "    \"ret\": 0,\n" +
                        "    \"timestamp\": 1744097662062,\n" +
                        "    \"result\": {\n" +
                        "        \"elements\": [\n" +
                        "            {\n" +
                        "                \"dataType\": \"dramaSearchList\",\n" +
                        "                \"showTitle\": false,\n" +
                        "                \"hasMore\": false,\n" +
                        "                \"contents\": [\n" +
                        "                    {\n" +
                        "                        \"type\": [\n" +
                        "                            \"duanju\"\n" +
                        "                        ],\n" +
                        "                        \"duanjuVo\": {\n" +
                        "                            \"id\": 5575,\n" +
                        "                            \"duanjuId\": \"111971\",\n" +
                        "                            \"title\": \"爱入山海\",\n" +
                        "                            \"coverImageUrl\": \"https://xifan.heytapimage.com/iot-duanju-public-hn1/duanju/fengxing/111971/image/imageFilter-111971_fengxing.jpg?x-amz-process=image/format,webp/quality,Q_20\",\n" +
                        "                            \"desc\": \"《爱入山海》是一部以爱情为主线的短剧作品，通过精彩的剧情和角色塑造，展现了一段深刻而动人的爱情故事。剧中主要演员尚未在参考文章中提及，但该剧以其紧凑的剧情和真挚的情感赢得了观众的喜爱。剧情围绕两位主角的爱情故事展开，他们在山海之间相遇、相识、相爱，经历了种种困难和挑战，最终走到了一起。在剧中，两人相互扶持、共同成长，展现了爱情的力量和美好。\",\n" +
                        "                            \"total\": 100,\n" +
                        "                            \"updateStatus\": \"over\",\n" +
                        "                            \"source\": \"fengxing\",\n" +
                        "                            \"categories\": [\n" +
                        "                                \"现代\",\n" +
                        "                                \"都市\"\n" +
                        "                            ],\n" +
                        "                            \"status\": \"up\",\n" +
                        "                            \"preloadAdCount\": 0,\n" +
                        "                            \"adPositions\": \"\",\n" +
                        "                            \"duanjuType\": 1,\n" +
                        "                            \"jumpUrl\": \"\",\n" +
                        "                            \"tags\": \"\",\n" +
                        "                            \"videoMaterialType\": 0,\n" +
                        "                            \"dramaUuid\": 5575,\n" +
                        "                            \"playViewText\": \"267万\",\n" +
                        "                            \"bingeWatchNumber\": 9625\n" +
                        "                        }\n" +
                        "                    },\n" +
                        "                    {\n" +
                        "                        \"type\": [\n" +
                        "                            \"duanju\"\n" +
                        "                        ],\n" +
                        "                        \"duanjuVo\": {\n" +
                        "                            \"id\": 180083,\n" +
                        "                            \"duanjuId\": \"XN08jpX6jmKA\",\n" +
                        "                            \"title\": \"山海情深\",\n" +
                        "                            \"coverImageUrl\": \"https://xifan.heytapimage.com/iot-duanju-public-hn1/duanju/yilan/XN08jpX6jmKA/image/imageFilter-WJ7lc4hCRsr75AovscGys-1389599553.jpeg?x-amz-process=image/format,webp/quality,Q_20\",\n" +
                        "                            \"desc\": \"小村中，沈倩父亲病重离世，留下她和哑巴母亲韩娟相依为命，灵堂上，村霸刘彪找上门，拿着改了金额的欠条讨债，欺负她们孤儿寡母，甚至想在沈父灵位前侵犯沈倩。韩娟拼命才阻止了他，刘彪临走前放出话，要是还不起钱，就把沈倩卖进大山抵债。发现真相的韩娟打算和沈倩趁夜离开村子，却被杨伯等人发现追捕，混乱之际，沈倩被路过的叶寒开车撞倒后失忆，被捡走带回公司，而韩娟被抓回村子，受尽折磨。五年后，已经是公司高层的沈倩决心亲自负责杨树村项目，寻找自己记忆中模糊的母亲，却不知母亲韩娟已经被逼得去住狗窝了。\",\n" +
                        "                            \"total\": 43,\n" +
                        "                            \"updateStatus\": \"over\",\n" +
                        "                            \"source\": \"web1\",\n" +
                        "                            \"categories\": [\n" +
                        "                                \"家庭\"\n" +
                        "                            ],\n" +
                        "                            \"status\": \"up\",\n" +
                        "                            \"preloadAdCount\": 0,\n" +
                        "                            \"adPositions\": \"\",\n" +
                        "                            \"duanjuType\": 3,\n" +
                        "                            \"jumpUrl\": \"\",\n" +
                        "                            \"tags\": \"\",\n" +
                        "                            \"videoMaterialType\": 0,\n" +
                        "                            \"dramaUuid\": 180083,\n" +
                        "                            \"playViewText\": \"74.5万\",\n" +
                        "                            \"bingeWatchNumber\": 4830\n" +
                        "                        }\n" +
                        "                    },\n" +
                        "                    {\n" +
                        "                        \"type\": [\n" +
                        "                            \"duanju\"\n" +
                        "                        ],\n" +
                        "                        \"duanjuVo\": {\n" +
                        "                            \"id\": 4405,\n" +
                        "                            \"duanjuId\": \"id3\",\n" +
                        "                            \"title\": \"星辰坠入我梦海\",\n" +
                        "                            \"coverImageUrl\": \"https://xifan.heytapimage.com/iot-duanju-public-hn1/duanju/qingting/100270/image/imageFilter-100270_qingting.jpg?x-amz-process=image/format,webp/quality,Q_20\",\n" +
                        "                            \"desc\": \"安竹自出生起便暗恋时年，但时年的冷静让她误以为他不懂爱情。在大学，时年对学姐岑绫一见钟情，安竹意识到时年只是不爱她。她选择出国进修，而时年却在失去安竹后意识到自己对她的依赖已变成爱。五年后，两人再次相遇，安竹与宋知寒已为情侣，岑绫的回归让四人的关系更加复杂。时年将如何抉择，他们的爱情又将何去何从？\",\n" +
                        "                            \"total\": 30,\n" +
                        "                            \"updateStatus\": \"over\",\n" +
                        "                            \"source\": \"source3\",\n" +
                        "                            \"categories\": [\n" +
                        "                                \"都市\"\n" +
                        "                            ],\n" +
                        "                            \"status\": \"up\",\n" +
                        "                            \"preloadAdCount\": 0,\n" +
                        "                            \"adPositions\": \"\",\n" +
                        "                            \"duanjuType\": 1,\n" +
                        "                            \"jumpUrl\": \"\",\n" +
                        "                            \"tags\": \"\",\n" +
                        "                            \"videoMaterialType\": 0,\n" +
                        "                            \"dramaUuid\": 4405,\n" +
                        "                            \"playViewText\": \"94.6万\",\n" +
                        "                            \"bingeWatchNumber\": 5677\n" +
                        "                        }\n" +
                        "                    }\n" +
                        "                ]\n" +
                        "            }\n" +
                        "        ],\n" +
                        "        \"hasMore\": true,\n" +
                        "        \"action\": \"showList\"\n" +
                        "    }\n" +
                        "}",
                DuanjuSearchResponse.class
        );
    }

}
