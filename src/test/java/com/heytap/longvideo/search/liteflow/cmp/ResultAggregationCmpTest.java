package com.heytap.longvideo.search.liteflow.cmp;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.client.arrange.search.entity.QuerySidSearch;
import com.heytap.longvideo.client.media.entity.StandardEpisodeBO;
import com.heytap.longvideo.client.media.enums.NewResultCardTypeEnum;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.constants.SearchTabEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.*;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.RelationListService;
import com.heytap.video.client.enums.HighLightTypeEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ResultAggregationCmpTest {
    @InjectMocks
    ResultAggregationCmp resultAggregationCmp;
    @Mock
    SearchProperties searchProperties;
    @Mock
    private TaskUnlockEpisodeService taskUnlockEpisodeService;
    @Mock
    private RelationListService relationListService;
    @Mock
    private QuerySidSearchRpcApiProxy querySidSearchRpcApiProxy;
    @Mock
    private AlbumRankRpcApiProxy albumRankRpcApiProxy;
    @Mock
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;
    @Mock
    private ConvertResponseService convertResponseService ;
    @Mock
    private StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy;
    @Mock
    private ImageTagRpcApiProxy imageTagRpcApiProxy;
    @Before
    public void build() {
        when(searchProperties.getSearchCardOptVersion()).thenReturn(80900);
        when(searchProperties.getDefaultVerticalIcon()).thenReturn("icon");
        when(searchProperties.getSearchCardButtonNum()).thenReturn(2);
        Map<String,Map<String,Integer>> thirdMap = new HashMap<>();
        thirdMap.put("douban",new HashMap<>());
        when(searchProperties.getThirdSearchMap()).thenReturn(thirdMap);
    }

    /**
     * 结果卡与推荐卡重复时，推荐卡去重
     */
    @Test
    public void resultAggregationTest1() {
        SearchByKeyWordContext context =new SearchByKeyWordContext();

        KeyWordSearchParamV2 keyWordSearchParamV2 = new KeyWordSearchParamV2();
        keyWordSearchParamV2.setPageIndex(1);
        keyWordSearchParamV2.setPageSize(2);
        keyWordSearchParamV2.setKeyword("1");
        context.setRequestParam(keyWordSearchParamV2);

        KeyWordSearchResponse response = buildKeyWordSearchResponse();
        context.setBaseSearchResult(Lists.newArrayList(response));

        SearchResponse searchResponse = new SearchResponse();
        SearchInterveneCardResponse recommend = new SearchInterveneCardResponse();
        recommend.setContents(Lists.newArrayList(response));
        searchResponse.setLongVideoRecommend(recommend);
        context.setSearchResponse(searchResponse);

        resultAggregationCmp.resultAggregation(context);

        Assert.assertNull(searchResponse.getLongVideoRecommend());
        Assert.assertEquals(1, searchResponse.getLongVideoSearchResult().size());
    }

    /**
     * 结果卡与影人卡重复时，结果卡去重
     */
    @Test
    public void resultAggregationTest2() {
        SearchByKeyWordContext context =new SearchByKeyWordContext();

        KeyWordSearchParamV2 keyWordSearchParamV2 = new KeyWordSearchParamV2();
        keyWordSearchParamV2.setPageIndex(1);
        keyWordSearchParamV2.setPageSize(2);
        keyWordSearchParamV2.setKeyword("1");
        context.setRequestParam(keyWordSearchParamV2);

        KeyWordSearchResponse response = buildKeyWordSearchResponse();
        context.setBaseSearchResult(Lists.newArrayList(response));

        SearchResponse searchResponse = new SearchResponse();
        SearchInterveneCardResponse actorCard = new SearchInterveneCardResponse();
        actorCard.setContents(Lists.newArrayList(response));
        searchResponse.setLongVideoActor(actorCard);
        context.setSearchResponse(searchResponse);

        resultAggregationCmp.resultAggregation(context);

        Assert.assertNull(searchResponse.getLongVideoRecommend());
        Assert.assertEquals(0, searchResponse.getLongVideoSearchResult().size());
    }


    /**
     * 先插入非合作匹配内容，再插入合作干预内容，后插入非合作干预内容
     */
    @Test
    public void resultAggregationTest3() {
        SearchByKeyWordContext context =new SearchByKeyWordContext();

        KeyWordSearchParamV2 keyWordSearchParamV2 = new KeyWordSearchParamV2();
        keyWordSearchParamV2.setSearchType("2");
        keyWordSearchParamV2.setPageIndex(1);
        keyWordSearchParamV2.setPageSize(10);
        keyWordSearchParamV2.setKeyword("1");
        context.setRequestParam(keyWordSearchParamV2);

        KeyWordSearchResponse r1 = buildKeyWordSearchResponse();
        context.setBaseSearchResult(Lists.newArrayList(r1));

        KeyWordSearchResponse r2 = buildKeyWordSearchResponse();
        r2.setSid("SID2");
        r2.setTitle("全网搜匹配内容");
        r2.setSortDefine(99.9);
        context.setAllWebSearchResult(Lists.newArrayList(r2));

        KeyWordSearchResponse r3 = buildKeyWordSearchResponse();
        r3.setSid("SID3");
        r3.setTitle("合作干预内容");
        r3.setSortIndex(1);
        context.setSearchInterveneResult(Lists.newArrayList(r3));

        KeyWordSearchResponse r4 = buildKeyWordSearchResponse();
        r4.setSid("SID4");
        r4.setTitle("全网搜干预内容");
        r4.setSortIndex(0);
        context.setAllWebInterveneResult(Lists.newArrayList(r4));

        SearchResponse searchResponse = new SearchResponse();
        context.setSearchResponse(searchResponse);

        resultAggregationCmp.resultAggregation(context);

        Assert.assertEquals("全网搜干预内容", searchResponse.getLongVideoSearchResult().get(0).getTitle());
        Assert.assertEquals("全网搜匹配内容", searchResponse.getLongVideoSearchResult().get(1).getTitle());
        Assert.assertEquals("合作干预内容", searchResponse.getLongVideoSearchResult().get(2).getTitle());
        Assert.assertEquals("标题1", searchResponse.getLongVideoSearchResult().get(3).getTitle());
    }

    @Test
    public void resultAggregationTest_duanju() {
        SearchByKeyWordContext context =new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setDuanjuHasMore(true);
        param.setPageIndex(1);

        KeyWordSearchResponse response = buildKeyWordSearchResponse();
        context.setRequestParam(param);
        context.setDuanjuSearchResult(Lists.newArrayList(response));

        resultAggregationCmp.resultAggregation(context);
        Assert.assertEquals(SearchTabEnum.DUANJU.getCode(), context.getSearchResponse().getSearchTab());

    }

    /**
     * 没有竖图下发默认 推荐理由列表下发评分和排名 推荐理由排除品类和其他标签
     * 测试用例id:12526508
     */
    @Test
    public void resultAggregationTest_searchCardV2(){
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setSearchType("1");
        param.setPageIndex(1);
        param.setVersion(80900);
        context.setRequestParam(param);
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponseCardV2();
        context.setBaseSearchResult(Lists.newArrayList(keyWordSearchResponse));
        //mock收藏的map
        when(relationListService.requestRelationList(any(),any())).thenReturn(new HashMap<>());
        //mock能订阅的list
        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any())).thenReturn(new HashMap<>());
        //mock获取数分收集的相关数据
        when(querySidSearchRpcApiProxy.getQuerySidSearch(any(),any())).thenReturn(null);
        //mock排行榜rpc结果
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(),any())).thenReturn(CompletableFuture.completedFuture(buildRankAlbumRecommendInfoMap()));
        //doCallRealMethod().when(convertResponseService).buildNewResultHighLights(keyWordSearchResponse,buildRankAlbumRecommendInfoMap());
        //doCallRealMethod().when(convertResponseService).buildNewResultButtons(keyWordSearchResponse,new HashMap<>(),new ArrayList<>(),new ArrayList<>());
        resultAggregationCmp.resultAggregation(context);
        KeyWordSearchResponse firstKeyWordSearchResponse = context.getBaseSearchResult().get(0);
        Assert.assertEquals("icon",keyWordSearchResponse.getVerticalIcon());
        // 验证cardType为SMALL_CARD
        Assert.assertEquals("SMALL_CARD", keyWordSearchResponse.getCardType());
//        Assert.assertEquals(HighLightTypeEnum.SCORE.getCode(),firstKeyWordSearchResponse.getHighlights().get(0).getType());
//        Assert.assertEquals(HighLightTypeEnum.RANK.getCode(),firstKeyWordSearchResponse.getHighlights().get(1).getType());
//        Assert.assertEquals(3, firstKeyWordSearchResponse.getHighlights().size());
    }

    /**
     * 华视源电视剧大于6集且剧集数量匹配时,下发剧集信息,下发moreType的剧集信息,hasMoreEpisodes为false hasMore下发true
     * 测试用例id:12526521
     */
    @Test
    public void resultAggregationTest1_searchCardV2(){
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setSearchType("1");
        param.setPageIndex(1);
        param.setVersion(80900);
        param.setPageSize(2);
        context.setRequestParam(param);
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponseCardV2();
        keyWordSearchResponse.setSource("huashi");
        keyWordSearchResponse.setContentType(ContentTypeEnum.TV.getCode());
        keyWordSearchResponse.setTotalEpisode(8);
        context.setBaseSearchResult(Lists.newArrayList(keyWordSearchResponse,new KeyWordSearchResponse()));
        //mock排行榜rpc结果
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(),any())).thenReturn(CompletableFuture.completedFuture(null));
        //mock可以订阅的列表
        when(convertResponseService.getCanSubscribeSid(any())).thenReturn(null);
        //mock获取其下标准剧集数量的rpc
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(8);
        //mock调用数分rpc
        when(querySidSearchRpcApiProxy.getQuerySidSearch(any(),any())).thenReturn(null);
        //mock获取剧集信息
        when(standardEpisodeRpcApiProxy.listStandardEpisodeBoBySid(any(),any())).thenReturn(buildStandardEpisodeBO());
        when(imageTagRpcApiProxy.getImageUrl(any())).thenReturn("rpc markcode URL");
        resultAggregationCmp.resultAggregation(context);
        Assert.assertEquals(6,keyWordSearchResponse.getEpisodes().size());
        Assert.assertEquals(NewResultCardTypeEnum.MORE.getValue(), (int) keyWordSearchResponse.getEpisodes().get(2).getType());
        Assert.assertFalse(keyWordSearchResponse.isHasMoreEpisodes());
        StandardEpisodeBO standardEpisodeBO = keyWordSearchResponse.getEpisodes().get(0);
        Assert.assertNotNull(standardEpisodeBO.getTitle());
        Assert.assertNotNull(standardEpisodeBO.getEid());
        Assert.assertNotNull(standardEpisodeBO.getMarkCode());
        Assert.assertNotNull(standardEpisodeBO.getSid());
        Assert.assertNotNull(standardEpisodeBO.getVid());
        Assert.assertNotNull(standardEpisodeBO.getMarkCodeUrl());
        // 验证cardType为BIG_CARD
        Assert.assertEquals("BIG_CARD", keyWordSearchResponse.getCardType());
    }

    /**
     * 剧集总集数与剧集集数相同 总集数大于3小于6  没有测试节目运营位
     * 测试用例id:12526522
     */
    @Test
    public void resultAggregationTest2_searchCardV2(){
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setSearchType("1");
        param.setPageIndex(1);
        param.setVersion(80900);
        param.setPageSize(2);
        context.setRequestParam(param);
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponseCardV2();
        keyWordSearchResponse.setSource("huashi");
        keyWordSearchResponse.setContentType(ContentTypeEnum.TV.getCode());
        keyWordSearchResponse.setTotalEpisode(5);
        context.setBaseSearchResult(Lists.newArrayList(keyWordSearchResponse,new KeyWordSearchResponse()));
        //mock排行榜rpc结果
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(),any())).thenReturn(CompletableFuture.completedFuture(null));
        //mock可以订阅的列表
        when(convertResponseService.getCanSubscribeSid(any())).thenReturn(null);
        //mock获取其下标准剧集数量的rpc
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(5);
        //mock调用数分rpc
        when(querySidSearchRpcApiProxy.getQuerySidSearch(any(),any())).thenReturn(null);
        //mock获取剧集信息
        when(standardEpisodeRpcApiProxy.listStandardEpisodeBoBySid(any(),any())).thenReturn(buildStandardEpisodeBO1());
        when(imageTagRpcApiProxy.getImageUrl(any())).thenReturn("rpc markcode URL");
        resultAggregationCmp.resultAggregation(context);
        Assert.assertEquals(5,keyWordSearchResponse.getEpisodes().size());
        Assert.assertFalse(keyWordSearchResponse.isHasMoreEpisodes());
        StandardEpisodeBO standardEpisodeBO = keyWordSearchResponse.getEpisodes().get(0);
        Assert.assertNotNull(standardEpisodeBO.getTitle());
        Assert.assertNotNull(standardEpisodeBO.getEid());
        Assert.assertNotNull(standardEpisodeBO.getMarkCode());
        Assert.assertNotNull(standardEpisodeBO.getSid());
        Assert.assertNotNull(standardEpisodeBO.getVid());
        Assert.assertNotNull(standardEpisodeBO.getMarkCodeUrl());
    }

    /**
     * 前置条件
     * 1.该搜索词存在于数分表中且关联有对应电视剧类型的华视源节目
     * 2.该节目的剧集表中实际总集数大于3且与剧头表中总集数字段相等
     * 3.version=80900
     * 4.search_type=1
     * 5.搜索词和该节目标题匹配率小于90%
     * 6.总集数大于6
     * 7.影视tab匹配结果大于1个
     * 8.该节目在长视频后台-运营资源管理-选集内容运营位管理中新增单内容的节目运营位(没测试)
     * ============================================
     * 测试用例id:12526525
     */
    @Test
    public void resultAggregationTest3_searchCardV2(){
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setSearchType("1");
        param.setPageIndex(1);
        param.setVersion(80900);
        param.setPageSize(2);
        context.setRequestParam(param);
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponseCardV2();
        keyWordSearchResponse.setSource("huashi");
        keyWordSearchResponse.setContentType(ContentTypeEnum.TV.getCode());
        keyWordSearchResponse.setReleScore(0.8f);
        keyWordSearchResponse.setTotalEpisode(8);
        context.setBaseSearchResult(Lists.newArrayList(keyWordSearchResponse,new KeyWordSearchResponse()));
        //mock排行榜rpc结果
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(),any())).thenReturn(CompletableFuture.completedFuture(null));
        //mock可以订阅的列表
        when(convertResponseService.getCanSubscribeSid(any())).thenReturn(null);
        //mock获取其下标准剧集数量的rpc
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(8);
        //mock调用数分rpc
        when(querySidSearchRpcApiProxy.getQuerySidSearch(any(),any())).thenReturn(new QuerySidSearch());
        //mock获取剧集信息
        when(standardEpisodeRpcApiProxy.listStandardEpisodeBoBySid(any(),any())).thenReturn(buildStandardEpisodeBO());
        when(imageTagRpcApiProxy.getImageUrl(any())).thenReturn("rpc markcode URL");
        resultAggregationCmp.resultAggregation(context);
        Assert.assertEquals(6,keyWordSearchResponse.getEpisodes().size());
        Assert.assertEquals(NewResultCardTypeEnum.MORE.getValue(), (int) keyWordSearchResponse.getEpisodes().get(2).getType());
        Assert.assertFalse(keyWordSearchResponse.isHasMoreEpisodes());
        StandardEpisodeBO standardEpisodeBO = keyWordSearchResponse.getEpisodes().get(0);
        Assert.assertNotNull(standardEpisodeBO.getTitle());
        Assert.assertNotNull(standardEpisodeBO.getEid());
        Assert.assertNotNull(standardEpisodeBO.getMarkCode());
        Assert.assertNotNull(standardEpisodeBO.getSid());
        Assert.assertNotNull(standardEpisodeBO.getVid());
        Assert.assertNotNull(standardEpisodeBO.getMarkCodeUrl());
    }

    /**
     * 1.使用非合作源电视剧节目标题作为搜索词
     * 2.该节目的剧集表中实际总集数大于3且与剧头表中总集数字段相等
     * 3.version=80900
     * 4.search_type=1
     * 5.搜索结果第一位可以精准匹配出该节目
     * 6.总集数大于6
     * 7.影视tab匹配结果大于1个
     * 8.该节目在长视频后台-运营资源管理-选集内容运营位管理中新增单内容的节目运营位
     * =============================================================
     * 测试用例id:12526527
     */
    @Test
    public void resultAggregationTest4_searchCardV2(){
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setSearchType("1");
        param.setPageIndex(1);
        param.setVersion(80900);
        param.setPageSize(2);
        context.setRequestParam(param);
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponseCardV2();
        keyWordSearchResponse.setSource("douban");
        keyWordSearchResponse.setContentType(ContentTypeEnum.TV.getCode());
        keyWordSearchResponse.setReleScore(0.8f);
        keyWordSearchResponse.setTotalEpisode(8);
        context.setBaseSearchResult(Lists.newArrayList(keyWordSearchResponse,new KeyWordSearchResponse()));
        //mock排行榜rpc结果
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(),any())).thenReturn(CompletableFuture.completedFuture(null));
        //mock可以订阅的列表
        when(convertResponseService.getCanSubscribeSid(any())).thenReturn(null);
        //mock获取其下标准剧集数量的rpc
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(8);
        //mock调用数分rpc
        when(querySidSearchRpcApiProxy.getQuerySidSearch(any(),any())).thenReturn(new QuerySidSearch());
        //mock获取剧集信息
        when(standardEpisodeRpcApiProxy.listStandardEpisodeBoBySid(any(),any())).thenReturn(buildStandardEpisodeBO());
        when(imageTagRpcApiProxy.getImageUrl(any())).thenReturn("rpc markcode URL");
        resultAggregationCmp.resultAggregation(context);
        Assert.assertNull(keyWordSearchResponse.getEpisodes());
    }

    /**
     * 1.使用华视源电新闻节目标题作为搜索词
     * 2.该节目的剧集表中实际总集数大于3且与剧头表中总集数字段相等
     * 3.version=80900
     * 4.search_type=1
     * 5.搜索结果第一位可以精准匹配出该节目
     * 6.总集数大于6
     * 7.影视tab匹配结果大于1个
     * 8.该节目在长视频后台-运营资源管理-选集内容运营位管理中新增单内容的节目运营位
     * ===========================================================
     * 测试用例id:12526529
     */
    @Test
    public void resultAggregationTest5_searchCardV2(){
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("标题1");
        param.setSearchType("1");
        param.setPageIndex(1);
        param.setVersion(80900);
        param.setPageSize(2);
        context.setRequestParam(param);
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponseCardV2();
        keyWordSearchResponse.setSource("huashi");
        keyWordSearchResponse.setContentType(ContentTypeEnum.NEWS.getCode());
        keyWordSearchResponse.setReleScore(0.8f);
        keyWordSearchResponse.setTotalEpisode(8);
        context.setBaseSearchResult(Lists.newArrayList(keyWordSearchResponse,new KeyWordSearchResponse()));
        //mock排行榜rpc结果
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(),any())).thenReturn(CompletableFuture.completedFuture(null));
        //mock可以订阅的列表
        when(convertResponseService.getCanSubscribeSid(any())).thenReturn(null);
        //mock获取其下标准剧集数量的rpc
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(8);
        //mock调用数分rpc
        when(querySidSearchRpcApiProxy.getQuerySidSearch(any(),any())).thenReturn(new QuerySidSearch());
        //mock获取剧集信息
        when(standardEpisodeRpcApiProxy.listStandardEpisodeBoBySid(any(),any())).thenReturn(buildStandardEpisodeBO());
        when(imageTagRpcApiProxy.getImageUrl(any())).thenReturn("rpc markcode URL");
        resultAggregationCmp.resultAggregation(context);
        Assert.assertNull(keyWordSearchResponse.getEpisodes());
    }



    //构建排行榜的map信息
    private Map<String, AlbumRecommendInfo> buildRankAlbumRecommendInfoMap(){
        Map<String,AlbumRecommendInfo> mockResultMap = new HashMap<>();
        AlbumRecommendInfo albumRecommendInfo = new AlbumRecommendInfo();
        albumRecommendInfo.setRecommendInfo("科幻电影第二名");
        albumRecommendInfo.setRecommendInfoDp("deeplink");
        mockResultMap.put("SID1",albumRecommendInfo);
        return mockResultMap ;
    }

    private List<StandardEpisodeBO> buildStandardEpisodeBO1(){
        List<StandardEpisodeBO> resultList = new ArrayList<>();
        for (int i = 1 ; i <= 5 ; i ++){
            StandardEpisodeBO standardEpisodeBO = new StandardEpisodeBO();
            standardEpisodeBO.setEid("eid" + i);
            standardEpisodeBO.setTitle("title" + i);
            standardEpisodeBO.setEpisode(i);
            standardEpisodeBO.setSid("SID" + i);
            standardEpisodeBO.setMarkCodeUrl("markCodeUrl" + i);
            standardEpisodeBO.setMarkCode("markCode" + i);
            standardEpisodeBO.setVid("vid" + i);
            standardEpisodeBO.setType(0);
            resultList.add(standardEpisodeBO);
        }
        return resultList ;
    }

    private List<StandardEpisodeBO> buildStandardEpisodeBO(){
        List<StandardEpisodeBO> resultList = new ArrayList<>();
        for (int i = 1 ; i <= 6 ; i ++){
            StandardEpisodeBO standardEpisodeBO = new StandardEpisodeBO();
            if(i != 3){
                standardEpisodeBO.setEid("eid" + i);
                standardEpisodeBO.setTitle("title" + i);
                standardEpisodeBO.setEpisode(i);
                standardEpisodeBO.setSid("SID" + i);
                standardEpisodeBO.setMarkCodeUrl("markCodeUrl" + i);
                standardEpisodeBO.setMarkCode("markCode" + i);
                standardEpisodeBO.setVid("vid" + i);
                standardEpisodeBO.setType(0);
            }else{
                standardEpisodeBO.setType(1);
            }
            resultList.add(standardEpisodeBO);
        }
        return resultList ;
    }

    private KeyWordSearchResponse buildKeyWordSearchResponseCardV2(){
        return KeyWordSearchResponse.builder().
                sid("SID1").
                title("标题1").
                directors("导演A").
                stars("演员A,演员B").
                year(2025).
                sourceScore(9.7f).
                featureType(1).
                tags("美丽,其他,电影").
                releScore(9.1f).
                build();
    }

    private KeyWordSearchResponse buildKeyWordSearchResponse() {
        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSid("SID1");
        response.setTitle("标题1");
        response.setMarkCode("VIP");
        response.setMarkCodeUrl("http://example.com/vip.png");
        response.setDirectors("导演A, 导演B");
        response.setSource("来源");
        response.setVerticalIcon("http://example.com/vertical_icon.jpg");
        response.setArea("地区");
        response.setSourceScore(9.7f);
        response.setYear(2023);
        response.setPayStatus(1);
        response.setCopyrightCode("COPYRIGHT123");
        response.setContentType("电影");
        response.setHorizontalIcon("http://example.com/horizontal_icon.jpg");
        response.setProgramInfo("节目信息");
        response.setLanguages("中文, 英文");
        response.setReleScore(9.0f);
        response.setStars("明星A, 明星B");
        response.setFunctionScore(7.2f);
        response.setTags("标签1, 标签2");
        response.setVirtualSid("V_SID12345");
        response.setLinkValue("链接值");
        response.setFeatureType(1);
        response.setAlbumFeatureType(2);
        response.setMultipleSourceCode(Arrays.asList("SOURCE1", "SOURCE2"));
        response.setRecommendInfo("推荐信息");
        response.setRecommendInfoType("类型1");
        response.setRecommendInfoIconType("图标类型1");
        response.setRecommendInfoDp("DP123");
        response.setWebUrl("http://example.com/weburl");
        response.setDeepLink("deeplink://example");
        response.setContentTypeName("内容类型名称");
        response.setBrief("简介");
        response.setThirdDate(10);
        response.setSortIndex(1);
        response.setAllowChaseAlbum(1);
        response.setChaseAlbumStatus(1);
        response.setAiSource(1);
        response.setMinorsPoolCode("MINORS123");
        response.setButtonStatus(1);
        response.setButtonText("播放");
        response.setSortDefine(99.5);

        return response;
    }
}
