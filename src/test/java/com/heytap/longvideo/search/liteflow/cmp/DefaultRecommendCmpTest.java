package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.cpc.dfoob.goblin.core.Goblin;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockserver.client.MockServerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.MockServerContainer;
import util.HttpMockUtil;
import util.MediaRpcMockerUtil;

import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class DefaultRecommendCmpTest extends GoblinJunit4BaseTest {
    @Autowired
    private DefaultRecommendCmp defaultRecommendCmp;

    @Test
    public void getDefaultRecommendTest1() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80100);
        param.setPageIndex(2);
        SearchResponse searchResponse = new SearchResponse();
        defaultRecommendCmp.getDefaultRecommend(param, searchResponse);
        Assert.assertNull(searchResponse.getLongVideoDefaultRecommend());
    }

    @Test
    public void getDefaultRecommendTest2() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80100);
        param.setPageIndex(1);
        param.setDeviceType(0);

        SearchResponse searchResponse = new SearchResponse();
        searchResponse.setLongVideoTag(new SearchInterveneCardResponse());

        defaultRecommendCmp.getDefaultRecommend(param, searchResponse);
        Assert.assertNull(searchResponse.getLongVideoDefaultRecommend());
    }


    @Test
    public void getDefaultRecommendTest3() throws IOException {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80100);
        param.setPageIndex(1);
        param.setDeviceType(0);

        SearchResponse searchResponse = new SearchResponse();

        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");//实时获取mock server的ip和端口
        HttpMockUtil.mockAlgorithmResponse(new MockServerClient(mockServer.getHost(), mockServer.getServerPort()));

        MediaRpcMockerUtil.getBySidsFilterInvalid();

        defaultRecommendCmp.getDefaultRecommend(param, searchResponse);
        Assert.assertFalse(searchResponse.getLongVideoDefaultRecommend().getContents().isEmpty());
    }
}
