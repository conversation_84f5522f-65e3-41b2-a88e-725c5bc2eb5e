package com.heytap.longvideo.search.liteflow.cmp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.heytap.longvideo.client.arrange.entity.LvContentItem;
import com.heytap.longvideo.client.arrange.entity.MtvMultiparams;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.*;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.MultiParamsRpcApiProxy;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.entity.MultisearchNodeDetailVo;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SearchTagCmpTest {

    @Mock
    private SearchIntentService searchIntentService;

    @Mock
    private ListFilterService listFilterService;

    @Mock
    private SearchProperties searchProperties;

    @Mock
    private MultiParamsRpcApiProxy multiParamsRpcApiProxy;

    @Mock
    private ArrangeRpcApiProxy arrangeRpcApiProxy;

    @Mock
    private ThreadPoolTaskExecutor commonThreadPool;

    @Mock
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Mock
    private DeepLinkUtils deepLinkUtils;

    @Mock
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Mock
    private CommonService commonService;

    @Mock
    private ConvertResponseService convertResponseService;

    @InjectMocks
    private SearchTagCmp searchTagCmp;

    private KeyWordSearchParamV2 requestParam;
    private LvSearchIntervene lvSearchIntervene;

    @Before
    public void setUp() {
        // 设置线程池
        ThreadPoolTaskExecutor mockCommonThreadPool = new ThreadPoolTaskExecutor();
        mockCommonThreadPool.setCorePoolSize(20);
        mockCommonThreadPool.setMaxPoolSize(40);
        mockCommonThreadPool.setQueueCapacity(1000);
        mockCommonThreadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        mockCommonThreadPool.setThreadNamePrefix("commonThreadPool");
        mockCommonThreadPool.initialize();
        ReflectionTestUtils.setField(searchTagCmp, "commonThreadPool", mockCommonThreadPool);

        // 初始化测试数据
        requestParam = new KeyWordSearchParamV2();
        requestParam.setVersion(70200);
        requestParam.setVipType("default");
        requestParam.setPageIndex(1);

        lvSearchIntervene = new LvSearchIntervene();
        lvSearchIntervene.setId(123);
        lvSearchIntervene.setTitle("测试标签卡");
        lvSearchIntervene.setLinkValue("爱情:all");
    }

    @Test
    public void testGetTagContent_LinkTypeIsNull_ReturnsNull() {
        // 准备测试数据
        lvSearchIntervene.setLinkType(null);

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        assertNull(result.join());
    }

    @Test
    public void testGetTagContent_ContentPoolLinkType_ReturnsValidResponse() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.CONTENT_POOL.getCode());
        lvSearchIntervene.setLinkValue("content_pool_123");

        Page<LvContentItem> mockPage = new Page<>();
        mockPage.setCurrent(1);
        mockPage.setPages(2);
        List<LvContentItem> records = new ArrayList<>();
        LvContentItem item = new LvContentItem();
        item.setLinkValue("sid_123");
        records.add(item);
        mockPage.setRecords(records);

        List<KeyWordSearchResponse> mockContents = new ArrayList<>();
        KeyWordSearchResponse mockResponse = new KeyWordSearchResponse();
        mockResponse.setSid("sid_123");
        mockContents.add(mockResponse);

        // Mock依赖
        when(arrangeRpcApiProxy.getLvContentItems(anyString(), anyInt(), anyInt(), anyInt()))
                .thenReturn(CompletableFuture.completedFuture(mockPage));
        when(searchIntentService.handleContentPoolPageResult(any(), any(), any(), anyInt()))
                .thenReturn(mockContents);
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString()))
                .thenReturn("mock_deep_link");
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);
        when(searchProperties.getSortList()).thenReturn(new ArrayList<>());

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertEquals("测试标签卡", response.getTitle());
        assertEquals("123", response.getCode());
        assertEquals("mock_deep_link", response.getDeepLink());
        assertEquals(1, response.getContents().size());
        assertEquals("sid_123", response.getContents().get(0).getSid());

        // 验证调用
        verify(arrangeRpcApiProxy).getLvContentItems("content_pool_123", 1, 1, 12);
        verify(searchIntentService).handleContentPoolPageResult(mockPage, lvSearchIntervene, requestParam, 1);
        verify(deepLinkUtils).getDeeplinkByType(TemplateLinkTypeEnum.TAG.getCode(), "123", "测试标签卡");
        verify(commonService).handleSohuVipContents(response);
    }

    @Test
    public void testGetTagContent_ContentPoolLinkType_PageResultIsNull_ReturnsNull() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.CONTENT_POOL.getCode());
        lvSearchIntervene.setLinkValue("content_pool_123");

        // Mock依赖
        when(arrangeRpcApiProxy.getLvContentItems(anyString(), anyInt(), anyInt(), anyInt()))
                .thenReturn(CompletableFuture.completedFuture(null));

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        assertNull(result.get());
    }

    @Test
    public void testGetTagContent_ContentPoolLinkType_VersionGreaterThanOptVersion_IncludesSortTypeList() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.CONTENT_POOL.getCode());
        lvSearchIntervene.setLinkValue("content_pool_123");
        requestParam.setVersion(80000);

        Page<LvContentItem> mockPage = new Page<>();
        mockPage.setCurrent(2);
        mockPage.setPages(2);
        mockPage.setRecords(new ArrayList<>());

        List<SearchInterveneCardResponse.SortType> sortList = new ArrayList<>();
        SearchInterveneCardResponse.SortType sortType = new SearchInterveneCardResponse.SortType();
        sortType.setSortType(1);
        sortType.setSortName("最热排序");
        sortList.add(sortType);

        // Mock依赖
        when(arrangeRpcApiProxy.getLvContentItems(anyString(), anyInt(), anyInt(), anyInt()))
                .thenReturn(CompletableFuture.completedFuture(mockPage));
        when(searchIntentService.handleContentPoolPageResult(any(), any(), any(), anyInt()))
                .thenReturn(new ArrayList<>());
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString()))
                .thenReturn("mock_deep_link");
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);
        when(searchProperties.getSortList()).thenReturn(sortList);

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertNotNull(response.getSortTypeList());
        assertEquals(1, response.getSortTypeList().size());
        assertEquals(Integer.valueOf(1), response.getSortTypeList().get(0).getSortType());
    }

    @Test
    public void testGetTagContent_TagLinkType_ReturnsValidResponse() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(1);
        mockEs.setLanguage("中文");
        mockEs.setActor("演员1,演员2");
        mockEs.setDirector("导演1");
        mockEs.setBrief("简介");
        mockEs.setBackGroundColorJson("{\"color\":\"red\"}");
        mockEsList.add(mockEs);

        ListFilterParam mockListFilterParam = new ListFilterParam();
        mockListFilterParam.setHasMore(true);
        mockListFilterParam.setVersion(70200);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("tv", "movie"));


        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertEquals("测试标签卡", response.getTitle());
        assertEquals("123", response.getCode());
        assertEquals(1, response.getContents().size());
        assertEquals("sid_123", response.getContents().get(0).getSid());
        assertEquals("测试节目", response.getContents().get(0).getTitle());

        // 验证调用
        verify(multiParamsRpcApiProxy).findFilterAll();
        verify(listFilterService).listFilter(any(ListFilterParam.class));
        verify(listFilterService).listDistinctContentTypes(any(ListFilterParam.class));
        verify(convertResponseService).handleFilterList(response, Arrays.asList("tv", "movie"));
        verify(convertResponseService).handleSubTitle(anyList(), eq(70200));
        verify(commonService).handleSohuVipContents(response);
    }

    @Test
    public void testGetTagContent_TagLinkType_SingleContentType() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:tv");

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(1);
        mockEsList.add(mockEs);

        ListFilterParam mockListFilterParam = new ListFilterParam();
        mockListFilterParam.setHasMore(false);
        mockListFilterParam.setVersion(70200);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("tv"));

        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertFalse(response.getHasMore());
    }

    @Test
    public void testGetTagContent_TagLinkType_EmptyEsList_ReturnsNull() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService - 返回空列表
        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(new ArrayList<>());
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(new ArrayList<>());

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        assertNull(result.get());
    }

    @Test
    public void testGetTagContent_TagLinkType_EmptyMultiParamsList_ReturnsNull() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");

        // Mock MultiParamsRpcApiProxy - 返回空列表
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        assertNull(result.get());
    }

    @Test
    public void testGetTagContent_TagLinkType_InvalidLinkValue_ReturnsNull() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情"); // 不包含冒号，格式错误

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        assertNull(result.get());
    }

    @Test
    public void testGetTagContent_TagLinkType_WithContentTypeOverride() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(1);
        mockEsList.add(mockEs);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("movie"));

        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 执行测试 - 传入contentType参数
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, "movie");

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertEquals(1, response.getContents().size());
    }

    @Test
    public void testGetTagContent_TagLinkType_WithShowMarkCode() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");
        lvSearchIntervene.setIsShowMarkCode(1); // 显示角标

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(1);
        mockEs.setMarkCode("VIP"); // 设置角标
        mockEsList.add(mockEs);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("tv"));

        // Mock ImageTagRpcApiProxy
        when(imageTagRpcApiProxy.getImageUrl(anyString())).thenReturn("mock_image_url");

        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertEquals(1, response.getContents().size());
        
        // 验证角标处理
        verify(imageTagRpcApiProxy).getImageUrl("VIP");
    }

    @Test
    public void testGetTagContent_TagLinkType_WithYgFeatureType() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(2); // YG类型
        mockEsList.add(mockEs);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("tv"));

        // Mock ImageTagRpcApiProxy
        when(imageTagRpcApiProxy.getImageUrl(anyString())).thenReturn("mock_image_url");

        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertEquals(1, response.getContents().size());
        
        // 验证YG角标处理
        verify(imageTagRpcApiProxy).getImageUrl("YG");
    }

    @Test
    public void testGetTagContent_UnknownLinkType_ReturnsNull() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(999); // 未知的链接类型

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        assertNull(result.get());
    }

    @Test
    public void testGetTagContent_WithDifferentSortTypes() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(1);
        mockEsList.add(mockEs);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("tv"));

        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 测试最新排序
        CompletableFuture<SearchInterveneCardResponse> result1 = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, SortEnum.NEW.getType(), null);
        assertNotNull(result1.get());

        // 测试免费优先排序
        CompletableFuture<SearchInterveneCardResponse> result2 = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, SortEnum.FREE.getType(), null);
        assertNotNull(result2.get());
    }

    @Test
    public void testGetTagContent_WithVersionComparison() throws ExecutionException, InterruptedException {
        // 准备测试数据
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue("爱情:all");
        requestParam.setVersion(80000); // 高版本

        // Mock MultiParamsRpcApiProxy
        List<MtvMultiparams> multiParamsList = new ArrayList<>();
        MtvMultiparams multiParams = new MtvMultiparams();
        multiParams.setId(1);
        multiParams.setMultiparamsJson("{\"code\":\"tv\",\"children\":[{\"code\":\"tag\",\"children\":[{\"code\":\"爱情|恋爱|情爱\",\"name\":\"爱情\"}]}]}");
        multiParamsList.add(multiParams);
        when(multiParamsRpcApiProxy.findFilterAll()).thenReturn(CompletableFuture.completedFuture(multiParamsList));

        // Mock ListFilterService
        List<ProgramAlbumEs> mockEsList = new ArrayList<>();
        ProgramAlbumEs mockEs = new ProgramAlbumEs();
        mockEs.setSid("sid_123");
        mockEs.setTitle("测试节目");
        mockEs.setFeatureType(1);
        mockEsList.add(mockEs);

        when(listFilterService.listFilter(any(ListFilterParam.class))).thenReturn(mockEsList);
        when(listFilterService.listDistinctContentTypes(any(ListFilterParam.class))).thenReturn(Arrays.asList("tv"));

        // Mock SearchProperties
        when(searchProperties.getSearchCardOptVersion()).thenReturn(70000);
        List<SearchInterveneCardResponse.SortType> sortList = new ArrayList<>();
        SearchInterveneCardResponse.SortType sortType = new SearchInterveneCardResponse.SortType();
        sortType.setSortType(1);
        sortType.setSortName("最热排序");
        sortList.add(sortType);
        when(searchProperties.getSortList()).thenReturn(sortList);

        // Mock DeepLinkUtils
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString(), anyString())).thenReturn("mock_deep_link");

        // 执行测试
        CompletableFuture<SearchInterveneCardResponse> result = searchTagCmp.getTagContent(
                requestParam, lvSearchIntervene, 1, 1, null);

        // 验证结果
        SearchInterveneCardResponse response = result.get();
        assertNotNull(response);
        assertNotNull(response.getSortTypeList());
        assertEquals(1, response.getSortTypeList().size());
        assertEquals(Integer.valueOf(1), response.getSortTypeList().get(0).getSortType());
    }
} 