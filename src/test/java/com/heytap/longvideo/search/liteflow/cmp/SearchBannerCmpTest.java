package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.entity.LvPage;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SearchBannerCmpTest {
    @InjectMocks
    SearchBannerCmp searchBannerCmp;

    @Mock
    private DeepLinkUtils deepLinkUtils;
    @Mock
    private ArrangeRpcApiProxy arrangeRpcApiProxy;

    @Test
    public void buildBannerTest() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setVersion(80200);
        param.setPageIndex(1);
        param.setKeyword("1");
        param.setDeviceType(0);
        param.setSearchType("1");

        LvSearchIntervene bannerIntervene = new LvSearchIntervene();
        bannerIntervene.setLinkType(TemplateLinkTypeEnum.COMMON_PAGE.getCode());

        List<LvDrawerItemVO> list = searchBannerCmp.buildBanner(param, null);
        Assert.assertEquals(0, list.size());

        when(deepLinkUtils.getDeeplinkByType(any(),any(),any())).thenReturn(null);
        LvPage lvPage = new LvPage();
        lvPage.setTitle("123");
        when(arrangeRpcApiProxy.findPageByCode(any())).thenReturn(CompletableFuture.completedFuture(lvPage));

        list = searchBannerCmp.buildBanner(param, bannerIntervene);
        Assert.assertEquals(1, list.size());
    }
}
