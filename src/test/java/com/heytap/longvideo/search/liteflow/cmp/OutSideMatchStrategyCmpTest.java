package com.heytap.longvideo.search.liteflow.cmp;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.service.common.StrategyService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;


/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/6/11 下午8:02
 */

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class OutSideMatchStrategyCmpTest {

    @InjectMocks
    private OutSideMatchStrategyCmp outSideMatchStrategyCmp;

    @Mock
    private StrategyService strategyService;

    @Mock
    private VipRelatedService vipRelatedService;

    @Before
    public void setup() {
        // 将InjectMocks对象转换为Spy
        outSideMatchStrategyCmp = spy(outSideMatchStrategyCmp);
    }

    @Test
    public void strategyTest() throws Exception {
        String vipInfoJson = "{\"videoVipStatus\":\"0\",\"videoPayStatus\":\"0\",\"mongoVideoVipStatus\":\"1\",\"mongoVideoPayStatus\":\"0\"}";
        VideoVipInfo videoVipInfo = JacksonUtil.parseObject(vipInfoJson, VideoVipInfo.class);
        Mockito.when(vipRelatedService.getVipInfo(any())).thenReturn(CompletableFuture.completedFuture(videoVipInfo));
        String contextJson = "{\"requestParam\":{\"requestId\":\"1660875599357ab30072098549524\",\"method\":\"\",\"f\":\"json\",\"keyword\":\"逆鳞\",\"version\":80901,\"attributeValues\":{\"channel\":\"OPPO\",\"browserLanguage\":\"zh-CN\",\"systemLanguage\":\"zh-CN\",\"newsSource\":\"yidian\",\"buuid\":0,\"pkg\":\"com.android.browser\",\"nightModel\":false,\"noImageMode\":false,\"ouidStatus\":true,\"deviceType\":0,\"defaultChannel\":true},\"attachments\":{\"ssoId\":\"2086849041\",\"content-length\":\"0\",\"Accept\":\"*/*\",\"Cache-Control\":\"no-cache\",\"t-request-id\":\"17496454595510ab1799e00005571726\",\"t-request-currentTimeMillis\":\"1749645459554\",\"User-Agent\":\"PostmanRuntime/7.29.3\",\"Connection\":\"keep-alive\",\"Postman-Token\":\"c662339d-bd1b-4c06-ba6a-2ceab68b0b95\",\"Host\":\"**************:8081\",\"Accept-Encoding\":\"gzip, deflate, br\"},\"appId\":\"quansou_sousuozhida\",\"strategyResult\":{},\"refreshTimes\":0,\"pageIndex\":1,\"pageSize\":4,\"vipType\":\"default\",\"searchType\":\"1\",\"deviceType\":0,\"isOut\":1,\"versionName\":\"********\",\"appVersion\":\"8.9.1\",\"versionTag\":10,\"hasMore\":0,\"duanjuHasMore\":false,\"searchTypeEnum\":\"DEFAULT\"},\"baseSearchResult\":[],\"allWebInterveneResult\":[],\"allWebSearchResult\":[],\"searchInterveneResult\":[],\"bannerList\":[],\"searchResponse\":{\"hasMore\":0,\"pageIndex\":0,\"pageSize\":0},\"duanjuSearchResult\":[],\"dpDetailPageStyle\":0,\"exactMatch\":false,\"unlockEpisode\":false,\"mongoUnlockEpisode\":false}";
        SearchByKeyWordContext context = JacksonUtil.parseObject(contextJson, SearchByKeyWordContext.class);
        doReturn(context).when(outSideMatchStrategyCmp).getContextBean(any());
        String strategyJson = "{\"set_taskForEpisodeMongo\":{\"sid\":162794,\"attachment\":\"{\\\"activityId\\\":13131,\\\"searchPreparationPageOpen\\\":true,\\\"searchPageOpen\\\":true,\\\"filterPageOpen\\\":true,\\\"topPageOpen\\\":true,\\\"detailPageOpen\\\":true,\\\"localEndPlay\\\":true,\\\"historyPageOpen\\\":true,\\\"favoritePageOpen\\\":true,\\\"chasingPageOpen\\\":true}\",\"abtest\":false},\"set_taskForEpisode\":{\"sid\":162565,\"attachment\":\"{\\\"activityId\\\":13070,\\\"searchPreparationPageOpen\\\":true,\\\"searchPageOpen\\\":true,\\\"filterPageOpen\\\":true,\\\"topPageOpen\\\":true,\\\"detailPageOpen\\\":true,\\\"localEndPlay\\\":true,\\\"historyPageOpen\\\":true,\\\"favoritePageOpen\\\":true,\\\"chasingPageOpen\\\":true}\",\"abtest\":false},\"set_quanSouButtonConfig\":{\"sid\":162841,\"attachment\":\"{   \\\"rank\\\": {     \\\"showType\\\": \\\"button\\\",     \\\"freeText\\\": \\\"0\\\",     \\\"vipText\\\": \\\"1\\\",     \\\"taskForEpisodeText\\\": \\\"2\\\",     \\\"timeLimitedFreeText\\\": \\\"3\\\",     \\\"payText\\\": \\\"4\\\",     \\\"defaultText\\\": \\\"5\\\"   },   \\\"search\\\": {     \\\"freeText\\\": \\\"0\\\",     \\\"vipText\\\": \\\"1\\\",     \\\"taskForEpisodeText\\\": \\\"2\\\",     \\\"timeLimitedFreeText\\\": \\\"3\\\",     \\\"outOfStockText\\\": \\\"4\\\",     \\\"payText\\\": \\\"5\\\",     \\\"defaultText\\\": \\\"6\\\"   } }\",\"abtest\":false},\"set_SearchNetResults\":{\"sid\":162843,\"attachment\":\"{\\\"magazine\\\":1,\\\"breeno\\\":1,\\\"quansou_sousuozhida\\\":1,\\\"browser_sousuozhida\\\":1}\",\"abtest\":false}}";
        Map<String, MatchStrategyResponseItem> strategy = JacksonUtil.parseObject(strategyJson, new TypeReference<Map<String, MatchStrategyResponseItem>>() {
        });
        doReturn(CompletableFuture.completedFuture(strategy)).when(strategyService).matchSearchStrategy(any(), any());
        outSideMatchStrategyCmp.process();
    }

}
