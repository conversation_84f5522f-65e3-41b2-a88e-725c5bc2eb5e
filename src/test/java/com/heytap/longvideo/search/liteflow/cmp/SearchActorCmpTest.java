package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SearchActorCmpTest {
    @InjectMocks
    SearchActorCmp searchActorCmp;

    @Mock
    private ListFilterService listFilterService;
    @Mock
    private ConvertResponseService convertResponseService;
    @Mock
    private SearchProperties searchConfig;
    @Mock
    private DeepLinkUtils deepLinkUtils;
    @Mock
    private CommonService commonService;

    @Before
    public void build() {
        when(searchConfig.getDetailDeepLink()).thenReturn("yoli://yoli.com/yoli/longvideo/videodetail?linkValue=%s");
    }

    @Test
    public void checkTest() {
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        interveneConfigMap.put(InterveneTypeEnum.RECOMMEND, new LvSearchIntervene());
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        Assert.assertFalse(searchActorCmp.check(interveneConfigMap, param));

        param.setVersion(80200);
        param.setPageIndex(1);
        param.setKeyword("1");
        param.setDeviceType(0);
        param.setSearchType("2");
        Assert.assertFalse(searchActorCmp.check(new HashMap<>(), param));

        param.setSearchType("1");
        Assert.assertTrue(searchActorCmp.check(new HashMap<>(), param));
    }

    @Test
    public void queryActorCardTest() {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();

        SearchInterveneCardResponse response = searchActorCmp.queryActorCard(param, 2, "all");

        Assert.assertNull(response);

        List<ProgramAlbumEs> esList = new ArrayList<>();
        esList.add(new ProgramAlbumEs());
        when(listFilterService.listFilter(any())).thenReturn(esList);
        when(convertResponseService.programAlbumEsConvertResponse(any(), any(), any())).thenReturn(new KeyWordSearchResponse());
        when(deepLinkUtils.getDeeplinkByType(any(),any(),any())).thenReturn(null);
        doNothing().when(commonService).handleSohuVipContents(any());

        response = searchActorCmp.queryActorCard(param, 2, "all");

        Assert.assertEquals(1, response.getContents().size());
    }

    @Test
    public void handleTest() {
        SearchInterveneCardResponse response = new SearchInterveneCardResponse();
        List<KeyWordSearchResponse> searchInterveneResult = new ArrayList<>();
        Assert.assertNull(searchActorCmp.handle(response, searchInterveneResult));

        response.getContents().add(buildSearchResponse("id1","标题1"));
        response.getContents().add(buildSearchResponse("id2","标题2"));
        response.getContents().add(buildSearchResponse("id3","标题3"));
        response.getContents().add(buildSearchResponse("id4","标题4"));
        response.getContents().add(buildSearchResponse("id5","标题5"));

        searchInterveneResult.add(buildSearchResponse("id2","标题2"));
        SearchInterveneCardResponse result = searchActorCmp.handle(response, searchInterveneResult);
        Assert.assertEquals(4, result.getContents().size());
        Assert.assertEquals("id3", result.getContents().get(1).getSid());
    }

    public KeyWordSearchResponse buildSearchResponse(String sid, String title) {
        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        searchResponse.setSid(sid);
        searchResponse.setTitle(title);
        return searchResponse;
    }
}
