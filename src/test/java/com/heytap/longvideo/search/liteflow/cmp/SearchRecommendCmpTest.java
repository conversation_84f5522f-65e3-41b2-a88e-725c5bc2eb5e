package com.heytap.longvideo.search.liteflow.cmp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.entity.LvAlbumListItem;
import com.heytap.longvideo.client.arrange.entity.MtvSubjectitem;
import com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.arrange.model.response.ItemForListCardWithMoreVO;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.common.media.programsource.SourceVersionService;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.RecommendAlgorithmData;
import com.heytap.longvideo.search.model.RecommendAlgorithmResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SearchRecommendCmpTest {

    @Mock
    private HttpDataChannel mockHttpDataChannel;
    @Mock
    private SearchProperties mockSearchProperties;
    @Mock
    private ArrangeRpcApiProxy mockArrangeRpcApiProxy;
    @Mock
    private ImageTagRpcApiProxy mockImageTagRpcApiProxy;
    @Mock
    private SearchIntentService mockSearchIntentService;
    @Mock
    private StandardAlbumRpcApiProxy mockStandardAlbumRpcApi;
    @Mock
    private DeepLinkUtils mockDeepLinkUtils;
    @Mock
    private CommonService mockCommonService;
    @Mock
    private YoukuSourceFilterService mockYoukuSourceFilterService;
    @Mock
    private FunshionLongVideoAndWeidiouFilterService mockFunshionLongVideoAndWeidiouFilterService;
    @Mock
    private ConvertResponseService convertResponseService;

    @Mock
    private SourceVersionService sourceVersionService;

    @InjectMocks
    private SearchRecommendCmp searchRecommendCmp;

    @Before
    public void setup() {
        ThreadPoolTaskExecutor mockCommonThreadPool = new ThreadPoolTaskExecutor();
        mockCommonThreadPool.setCorePoolSize(20);
        mockCommonThreadPool.setMaxPoolSize(40);
        mockCommonThreadPool.setQueueCapacity(1000);
        mockCommonThreadPool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        mockCommonThreadPool.setThreadNamePrefix("commonThreadPool");
        mockCommonThreadPool.setWaitForTasksToCompleteOnShutdown(true);
        mockCommonThreadPool.setAwaitTerminationSeconds(6);
        mockCommonThreadPool.initialize();
        ReflectionTestUtils.setField(searchRecommendCmp, "commonThreadPool", mockCommonThreadPool);

        ReflectionTestUtils.setField(searchRecommendCmp, "detailDeepLink", "%s");
    }

    @Test
    public void testCheck_InterveneConfigMapContainsSeriesOrTag_ReturnsFalse() {
        // 模拟 interveneConfigMap 包含 SERIES 或 TAG
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        interveneConfigMap.put(InterveneTypeEnum.SERIES, mock(LvSearchIntervene.class));
        KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();

        assertFalse(searchRecommendCmp.check(interveneConfigMap, requestParam));

        interveneConfigMap.clear();
        interveneConfigMap.put(InterveneTypeEnum.TAG, mock(LvSearchIntervene.class));
        assertFalse(searchRecommendCmp.check(interveneConfigMap, requestParam));
    }

    @Test
    public void testCheck_RequestParamVersionLessThanMin_ReturnsFalse() {
        // 模拟 requestParam 的 version 小于 recommendMinVersion
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
        requestParam.setVersion(61000);
        assertFalse(searchRecommendCmp.check(interveneConfigMap, requestParam));
    }

    @Test
    public void testCheck_RequestParamPageIndexNotOne_ReturnsFalse() {
        // 模拟 requestParam 的 pageIndex 不为 1
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
        requestParam.setVersion(61100);
        requestParam.setPageIndex(2);
        assertFalse(searchRecommendCmp.check(interveneConfigMap, requestParam));
    }

    @Test
    public void testCheck_RequestParamDeviceTypeNotZero_ReturnsFalse() {
        // 模拟 requestParam 的 deviceType 不为 0
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
        requestParam.setVersion(61100);
        requestParam.setPageIndex(1);
        requestParam.setDeviceType(1);
        assertFalse(searchRecommendCmp.check(interveneConfigMap, requestParam));
    }

    @Test
    public void testCheck_RequestParamSearchTypeNotOne_ReturnsFalse() {
        // 模拟 requestParam 的 searchType 不为 "1"
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
        requestParam.setVersion(61100);
        requestParam.setPageIndex(1);
        requestParam.setDeviceType(0);
        requestParam.setSearchType("2");
        assertFalse(searchRecommendCmp.check(interveneConfigMap, requestParam));
    }

    @Test
    public void testCheck() {
        // 模拟 requestParam 的 searchType 不为 "1"
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
        requestParam.setVersion(61100);
        requestParam.setPageIndex(1);
        requestParam.setDeviceType(0);
        requestParam.setSearchType("1");
        assertTrue(searchRecommendCmp.check(interveneConfigMap, requestParam));
    }

    @Test
    public void handleSearchRecommend_getItemByOperation() {
        SearchByKeyWordContext context = buildContext();

        ItemForListCardWithMoreVO vo = new ItemForListCardWithMoreVO();
        MtvSubjectitem item1 = new MtvSubjectitem();
        item1.setLinkValue("sid1");
        MtvSubjectitem item2 = new MtvSubjectitem();
        item2.setLinkValue("sid2");
        MtvSubjectitem item3 = new MtvSubjectitem();
        item3.setLinkValue("sid3");
        MtvSubjectitem item4 = new MtvSubjectitem();
        item4.setLinkValue("sid4");
        MtvSubjectitem item5 = new MtvSubjectitem();
        vo.setMtvSubjectItemList(Lists.newArrayList(item1, item2, item3, item4, item5));
        vo.setHasMore(0);
        when(mockArrangeRpcApiProxy.getSubjectItemForListCardWithMore(any(), any(), any(), any()))
                .thenReturn(CompletableFuture.completedFuture(vo));

        Map<String, StandardAlbum> map = new HashMap<>();
        StandardAlbum album1 = new StandardAlbum();
        album1.setFeatureType(1);
        album1.setPayStatus(1);
        album1.setMarkCode("markCode");
        map.put("sid1", album1);
        StandardAlbum album2 = new StandardAlbum();
        album2.setFeatureType(0);
        album2.setPayStatus(1);
        map.put("sid2", album2);
        StandardAlbum album3 = new StandardAlbum();
        album3.setFeatureType(1);
        album3.setPayStatus(1);
        map.put("sid3", album3);
        StandardAlbum album4 = new StandardAlbum();
        album4.setFeatureType(1);
        album4.setPayStatus(1);
        map.put("sid4", album4);
        when(mockStandardAlbumRpcApi.getBySidsFilterInvalid(any()))
                .thenReturn(map);

        when(mockFunshionLongVideoAndWeidiouFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(mockYoukuSourceFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(mockYoukuSourceFilterService.setDeepLinkFilter(any(), any(), any())).thenReturn(false);

        searchRecommendCmp.handleSearchRecommend(context, null);
        assertEquals(4, context.getSearchResponse().getLongVideoRecommend().getContents().size());

    }


    @Test
    public void handleSearchRecommend_getItemByAlgorithm_null() {
        SearchByKeyWordContext context = buildContext();
        context.setInterveneConfigMap(new HashMap<>());

        searchRecommendCmp.handleSearchRecommend(context, null);
        assertNull(context.getSearchResponse().getLongVideoRecommend());
    }

    @Test
    public void handleSearchRecommend_getItemByAlgorithm() throws HttpDataChannelException {
        SearchByKeyWordContext context = buildContext();
        context.setInterveneConfigMap(new HashMap<>());

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setThirdDate(0);

        RecommendAlgorithmResponse algorithmResponse = new RecommendAlgorithmResponse();
        RecommendAlgorithmData algorithmData1 = new RecommendAlgorithmData();
        algorithmData1.setId("sid1");
        RecommendAlgorithmData algorithmData2 = new RecommendAlgorithmData();
        algorithmData2.setId("sid2");
        RecommendAlgorithmData algorithmData3 = new RecommendAlgorithmData();
        algorithmData3.setId("sid3");
        RecommendAlgorithmData algorithmData4 = new RecommendAlgorithmData();
        algorithmData4.setId("sid4");
        algorithmResponse.setData(Lists.newArrayList(algorithmData1, algorithmData2, algorithmData3, algorithmData4));
        algorithmResponse.setStatus(0);
        when(mockHttpDataChannel.asyncGetForObject(any(), any(), anyMap(), anyInt())).thenReturn(CompletableFuture.completedFuture(algorithmResponse));


        Map<String, StandardAlbum> map = new HashMap<>();
        StandardAlbum album1 = new StandardAlbum();
        album1.setFeatureType(1);
        album1.setPayStatus(1);
        album1.setMarkCode("markCode");
        map.put("sid1", album1);
        when(mockStandardAlbumRpcApi.getBySidsFilterInvalid(any()))
                .thenReturn(map);

        Map<String, List<LvContentMaterialVO>> materialMap = new HashMap<>();
        LvContentMaterialVO materialVO = new LvContentMaterialVO();
        materialVO.setImgStyle(2);
        materialMap.put("sid1", Lists.newArrayList(materialVO));
        when(mockArrangeRpcApiProxy.getMaterials(anyList(),any()))
                .thenReturn(CompletableFuture.completedFuture(new RpcResult<>(materialMap)));

        searchRecommendCmp.handleSearchRecommend(context, Lists.newArrayList(response));
    }

    @Test
    public void getItemByAlbumListTest_linkType2() throws ExecutionException, InterruptedException {
        LvAlbumListItem item = new LvAlbumListItem();
        item.setLinkType(2);
        Page<LvAlbumListItem> page = new Page<>(1, 10, 1);
        page.setRecords(Lists.newArrayList(item));

        when(mockArrangeRpcApiProxy.getLvAlbumListByAlbumListCode(any(),anyInt(), any(), anyInt()))
                .thenReturn(CompletableFuture.completedFuture(page));

        when(mockSearchIntentService.getItemByContentPool(any(), any(), anyInt()))
                .thenReturn(CompletableFuture.completedFuture(null));

        Pair<List<KeyWordSearchResponse>, Boolean> result = searchRecommendCmp.getItemByAlbumList("1", 1).get();
        assertNull(result);
    }

    @Test
    public void getItemByAlbumListTest_linkType1() throws ExecutionException, InterruptedException {
        LvAlbumListItem item = new LvAlbumListItem();
        item.setLinkType(1);
        item.setLinkValue("sid1");
        Page<LvAlbumListItem> page = new Page<>(1, 10, 1);
        page.setRecords(Lists.newArrayList(item));

        when(mockArrangeRpcApiProxy.getLvAlbumListByAlbumListCode(any(),anyInt(), any(), anyInt()))
                .thenReturn(CompletableFuture.completedFuture(page));

        Pair<List<KeyWordSearchResponse>, Boolean> result = searchRecommendCmp.getItemByAlbumList("1", 1).get();
        assertEquals("sid1", result.getLeft().get(0).getSid());
    }

    private SearchByKeyWordContext buildContext() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();

        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        context.setRequestParam(param);

        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
        LvSearchIntervene lvSearchIntervene = new LvSearchIntervene();
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.SUBJECT.getCode());
        lvSearchIntervene.setLinkValue("subjectId");
        interveneConfigMap.put(InterveneTypeEnum.RECOMMEND, lvSearchIntervene);
        context.setInterveneConfigMap(interveneConfigMap);

        SearchResponse searchResponse = new SearchResponse();
        context.setSearchResponse(searchResponse);

        return context;
    }
}
