package com.heytap.longvideo.search.liteflow.cmp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SearchInterveneCmpTest {
    @InjectMocks
    private SearchInterveneCmp searchInterveneCmp;
    @Mock
    private ConvertResponseService beanConvertService;
    @Mock
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;
    @Mock
    private YoukuSourceFilterService youkuSourceFilterService;
    @Mock
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Test
    public void getSearchInterveneTest_detailBOListEmpty() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getSearchInterveneTest_rpcEmpty() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"123\"}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(Collections.emptyMap());

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getSearchInterveneTest_rpcNotContains() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"sid2\"}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(buildStandardAlbumMap());

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getSearchInterveneTest_standardAlbumToSearchResponseNull() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
        paramV2.setIsOut(1);
        context.setRequestParam(paramV2);

        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"sid1\",\"orderIndex\":1}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(buildStandardAlbumMap());

        when(beanConvertService.standardAlbumToSearchResponse(any(), any())).thenReturn(null);

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getSearchInterveneTest_thirdPartyFilterTrue() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
        paramV2.setIsOut(1);
        context.setRequestParam(paramV2);

        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"sid1\",\"orderIndex\":1}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(buildStandardAlbumMap());

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSource(SourceEnum.YOUKU_MOBILE.getDataSource());
        when(beanConvertService.standardAlbumToSearchResponse(any(), any())).thenReturn(response);

        when(youkuSourceFilterService.thirdPartyFilter(any(), any(), any())).thenReturn(true);

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getSearchInterveneTest_filterItemTrue() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
        paramV2.setIsOut(0);
        context.setRequestParam(paramV2);

        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"sid1\",\"orderIndex\":1}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(buildStandardAlbumMap());

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSource(SourceEnum.YOUKU_MOBILE.getDataSource());
        when(beanConvertService.standardAlbumToSearchResponse(any(), any())).thenReturn(response);

        when(youkuSourceFilterService.filterItem(any())).thenReturn(true);

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }


    @Test
    public void getSearchInterveneTest_filterItemBySourceTrue() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
        paramV2.setIsOut(0);
        context.setRequestParam(paramV2);

        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"sid1\",\"orderIndex\":1}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(buildStandardAlbumMap());

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSource(SourceEnum.MG_MOBILE.getDataSource());
        when(beanConvertService.standardAlbumToSearchResponse(any(), any())).thenReturn(response);

        when(funshionLongVideoAndWeidiouFilterService.filterItemBySource(any(),any())).thenReturn(true);

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getSearchInterveneTest() {
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
        paramV2.setIsOut(0);
        context.setRequestParam(paramV2);

        KeyWordSearchResponse baseSearchResult = new KeyWordSearchResponse();
        baseSearchResult.setTitle("小巷人家");
        context.setBaseSearchResult(Lists.newArrayList(baseSearchResult));

        Map<InterveneTypeEnum, LvSearchIntervene> interveneMap = new HashMap<>();
        LvSearchIntervene intervene = new LvSearchIntervene();
        intervene.setInterveneDetail("[{\"sid\":\"sid1\",\"orderIndex\":1}]");
        interveneMap.put(InterveneTypeEnum.NORMAL, intervene);
        context.setInterveneConfigMap(interveneMap);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(any()))
                .thenReturn(buildStandardAlbumMap());

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSource(SourceEnum.MG_MOBILE.getDataSource());
        response.setTitle("小巷人家");
        when(beanConvertService.standardAlbumToSearchResponse(any(), any())).thenReturn(response);

        when(funshionLongVideoAndWeidiouFilterService.filterItemBySource(any(),any())).thenReturn(false);

        List<KeyWordSearchResponse> result = searchInterveneCmp.getSearchIntervene(context);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
        Assert.assertTrue(CollectionUtils.isEmpty(context.getBaseSearchResult()));
    }

    private Map<String, StandardAlbum> buildStandardAlbumMap() {
        return JSON.parseObject(
                "{\n" +
                        "        \"sid1\": {\n" +
                        "            \"actor\": \"闫妮|李光洁|郭晓东|蒋欣|范丞丞|关晓彤|王安宇|卢昱晓\",\n" +
                        "            \"area\": \"内地\",\n" +
                        "            \"backGroundColor\": \"#B0D6CD\",\n" +
                        "            \"backGroundColorJson\": \"{\\\"v_color_s66_b60\\\":\\\"#349981\\\",\\\"fanZaiColor\\\":\\\"#33806D\\\"}\",\n" +
                        "            \"backGroundColorTwo\": \"#408070\",\n" +
                        "            \"brief\": \"时代变迁里的小巷故事\",\n" +
                        "            \"category\": \"电视剧\",\n" +
                        "            \"chargeType\": 0,\n" +
                        "            \"competitionType\": -1,\n" +
                        "            \"completed\": 1,\n" +
                        "            \"copyright\": 1,\n" +
                        "            \"copyrightCode\": \"mgmobile\",\n" +
                        "            \"cpScore\": \"8.0\",\n" +
                        "            \"createTime\": 1729137210000,\n" +
                        "            \"definition\": \"[{\\\"level\\\":1,\\\"payStatus\\\":0},{\\\"level\\\":2,\\\"payStatus\\\":0},{\\\"level\\\":3,\\\"payStatus\\\":0},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\n" +
                        "            \"director\": \"张开宙\",\n" +
                        "            \"doubanScore\": \"8.3\",\n" +
                        "            \"downloadAble\": 1,\n" +
                        "            \"downloadMarkcode\": \"\",\n" +
                        "            \"duration\": 120693,\n" +
                        "            \"endTime\": -28800000,\n" +
                        "            \"extraInfo\": \"\",\n" +
                        "            \"featureType\": 1,\n" +
                        "            \"fitAge\": \"\",\n" +
                        "            \"formerPrice\": 0.00,\n" +
                        "            \"honor\": \"\",\n" +
                        "            \"horizontalIcon\": \"http://0img.hitv.com/preview/sp_images/2024/10/17/202410171131482354435.jpg\",\n" +
                        "            \"horizontalIconOcs\": \"https://dhfs-test-cpc.wanyol.com/1064704664643227648_1029hIcon.jpeg\",\n" +
                        "            \"horizontalImage\": \"http://0img.hitv.com/preview/sp_images/2024/10/17/202410171131482354435.jpg\",\n" +
                        "            \"horizontalImageOcs\": \"https://dhfs-test-cpc.wanyol.com/1064704664643227648_1029hImg.jpeg\",\n" +
                        "            \"information\": \"该剧改编自大米所著同名小说，讲述了20世纪70年代末，苏州棉纺厂家属区一条小巷里住着庄家、林家、吴家三个家庭，时代变迁让三家家长和孩子们的命运都发生了翻天覆地的变化的故事。\",\n" +
                        "            \"keyword\": \"\",\n" +
                        "            \"language\": \"普通话\",\n" +
                        "            \"managerStatus\": 0,\n" +
                        "            \"mappingTags\": \"剧情\",\n" +
                        "            \"markCode\": \"mgmobile_vip_1\",\n" +
                        "            \"medium\": 2,\n" +
                        "            \"nowPrice\": 0.00,\n" +
                        "            \"oppoScore\": \"9.2\",\n" +
                        "            \"participantType\": -1,\n" +
                        "            \"payEffectDays\": 0,\n" +
                        "            \"payStatus\": 1,\n" +
                        "            \"period\": \"\",\n" +
                        "            \"prePush\": 0,\n" +
                        "            \"price\": 0,\n" +
                        "            \"processStatus\": 0,\n" +
                        "            \"programInfo\": \"全 47 集\",\n" +
                        "            \"programType\": \"tv\",\n" +
                        "            \"publishTime\": 1742457206000,\n" +
                        "            \"riskFlag\": 0,\n" +
                        "            \"showTime\": \"202410\",\n" +
                        "            \"sid\": \"1064704664643227648\",\n" +
                        "            \"source\": \"mgmobile\",\n" +
                        "            \"sourceAlbumId\": \"qYN6EY.7.8861d36cb851cb1a\",\n" +
                        "            \"sourceHot\": 0.0,\n" +
                        "            \"sourcePlayCount\": 0,\n" +
                        "            \"sourceScore\": \"9.2\",\n" +
                        "            \"sourceSeriesId\": \"[]\",\n" +
                        "            \"sourceStatus\": 1,\n" +
                        "            \"sourceType\": 2,\n" +
                        "            \"sourceWebUrl\": \"https://m.mgtv.com/h/698519.html\",\n" +
                        "            \"startTime\": 1732976040000,\n" +
                        "            \"status\": 1,\n" +
                        "            \"subProgramType\": \"tv\",\n" +
                        "            \"subTitle\": \"\",\n" +
                        "            \"supplyType\": \"normal\",\n" +
                        "            \"tags\": \"剧情\",\n" +
                        "            \"title\": \"小巷人家 卫视版\",\n" +
                        "            \"totalEpisode\": 47,\n" +
                        "            \"tuputag\": \"\",\n" +
                        "            \"unit\": 1,\n" +
                        "            \"updateTime\": 1742457206000,\n" +
                        "            \"validEpisode\": 47,\n" +
                        "            \"verifyStatus\": 1,\n" +
                        "            \"verticalIcon\": \"http://0img.hitv.com/preview/sp_images/2024/10/17/202410171131585186753.jpg\",\n" +
                        "            \"verticalIconOcs\": \"https://dhfs-test-cpc.wanyol.com/1064704664643227648_1029vIcon.jpeg\",\n" +
                        "            \"verticalImage\": \"http://0img.hitv.com/preview/sp_images/2024/10/17/202410171131585186753.jpg\",\n" +
                        "            \"verticalImageOcs\": \"https://dhfs-test-cpc.wanyol.com/1064704664643227648_1029vImg.jpeg\",\n" +
                        "            \"vipPrice\": 0.00,\n" +
                        "            \"vipType\": 1,\n" +
                        "            \"year\": 2024\n" +
                        "        }\n" +
                        "    }",
                new TypeReference<Map<String, StandardAlbum>>() {}
        );
    }

}
