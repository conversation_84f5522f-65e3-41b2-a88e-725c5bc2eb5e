package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.media.entity.StandardEpisodeBO;
import com.heytap.longvideo.client.media.enums.NewResultCardTypeEnum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.common.media.programsource.CommonSourceFilterConfig;
import com.heytap.longvideo.search.config.ConfigurationShowBrandConfig;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.constants.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.StandardEpisodeRpcApiProxy;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.service.thirdparty.ThirdPartyCommonService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.common.media.programsource.SourceVersionService;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static com.heytap.longvideo.search.constants.SearchConstant.SEARCH_FROM_BREENO;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class OutSideResultAggregationCmpTest {

    @InjectMocks
    private OutSideResultAggregationCmp outSideResultAggregationCmp;

    @Mock
    private SearchProperties searchProperties;

    @Mock
    private ConfigurationShowBrandConfig configurationShowBrandConfig;

    @Mock
    private ResultAggregationCmp resultAggregationCmp;

    @Mock
    private ThirdPartyCommonService thirdPartyCommonService;

    @Mock
    private YoukuSourceFilterService youkuSourceFilterService;

    @Mock
    private SourceFilterConfig sourceFilterConfig;

    @Mock
    private OutSidePostHandleCmp outSidePostHandleCmp;

    @Mock
    private StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy;

    @Mock
    private VipRelatedService vipRelatedService;

    @Mock
    private TaskUnlockEpisodeService taskUnlockEpisodeService;

    @Mock
    private  DeepLinkUtils deepLinkUtils;
    
    @Mock
    private SourceVersionService sourceVersionService;

    @Mock
    private CommonSourceFilterConfig commonSourceFilterConfig;

    private SearchByKeyWordContext context;
    private KeyWordSearchParamV2 param;
    private SearchResponse response;
    private List<KeyWordSearchResponse> searchResult;

    @Before
    public void setUp() {
        context = new SearchByKeyWordContext();
        param = new KeyWordSearchParamV2();
        response = new SearchResponse();
        searchResult = new ArrayList<>();

        // 设置基础参数
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setHasMore(1);
        param.setKeyword("测试关键词");
        param.setVersion(80900);
        param.setVersionTag(8);
        param.setQuickEngineVersion(100);

        // 设置属性值
        AttributeValues attributeValues = new AttributeValues();
        attributeValues.setChannel("OPPO");
        attributeValues.setRom("Android10");
        param.setAttributeValues(attributeValues);

        context.setRequestParam(param);
        context.setSearchResponse(response);
        context.setBaseSearchResult(searchResult);
        context.setDpDetailPageStyle(1);

        // Mock基础配置
        Map<String, String> sourceIconMap = new HashMap<>();
        sourceIconMap.put("oppo", "oppo_icon");
        sourceIconMap.put("tencent", "tencent_icon");
        when(searchProperties.getSourceIconMap()).thenReturn(sourceIconMap);

        Map<String, String> appDownloadPageMap = new HashMap<>();
        appDownloadPageMap.put("40", "download_url_40");
        appDownloadPageMap.put("20", "download_url_20");
        when(searchProperties.getAppDownloadPageMap()).thenReturn(appDownloadPageMap);

        when(searchProperties.getDetailDeepLink()).thenReturn("yoli://yoli.com/detail?linkValue=%s");
        when(searchProperties.getMgAppVersion()).thenReturn(80500);
        when(searchProperties.getLetvAppVersion()).thenReturn(80600);
        when(searchProperties.getYstAppVersion()).thenReturn(80700);
        when(searchProperties.getMiguOlympicAppVersion()).thenReturn(80800);
        when(searchProperties.getFunshionlongvideoAndWeidiouAppVersion()).thenReturn(80900);

        when(sourceFilterConfig.getYoukuMobileVersion()).thenReturn(71600);
        when(sourceFilterConfig.getMinWebQuickVersion()).thenReturn(100);

        when(searchProperties.getBrowserPlayEpisodeStartNum()).thenReturn(1);
        when(searchProperties.getBrowserPlayEpisodeEndNum()).thenReturn(3);
        when(searchProperties.getBrowserPlayEpisodeDescNum()).thenReturn(2);

        // Mock升级页面URL
        when(searchProperties.getAppUpgradePage()).thenReturn("upgrade_url");

        when(searchProperties.getIqiyiMmobileDeepLink()).thenReturn("iqiyi://mobile/player?ftype=27&subtype=aqyOPP_30551629_27&aid=%s&tvid=%s");

        // Mock SourceVersionService
        when(sourceVersionService.getSourceListByVersion(anyInt())).thenReturn(Arrays.asList("tencent", "iqiyi", "youku"));

        // Mock handleUpgradePageDp - 默认返回原deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> invocation.getArgument(0));

        when(deepLinkUtils.getDeeplinkByType(any())).thenReturn("");

        String sourceVersionMapJson = "\t\n" +
                "{\"huashi\":41900,\"senyu\":41900,\"sohu\":52600,\"mgmobile\":50000,\"miguolympic\":60200,\"letv\":60300,\"yst\":60800,\"ztv\":70200,\"youkumobile\":71600,\"weidiou\": 80100, \"funshion_lv\": 80100}";
        Map<String, Integer> sourceVersionMap = JacksonUtil.parseObject(sourceVersionMapJson, Map.class);
        when(commonSourceFilterConfig.getSourceAndVersionMapping()).thenReturn(sourceVersionMap);
    }

    /**
     * 构建测试用的搜索结果
     */
    private KeyWordSearchResponse buildKeyWordSearchResponse(String sid, String title, int index) {
        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSid(sid);
        response.setTitle(title);
        response.setSortIndex(index);
        response.setSource("tencent");
        response.setContentType("tv");
        response.setSourceKind(1);
        response.setSourceWebUrl("http://test.com"); // 设置默认的sourceWebUrl
        return response;
    }

    /**
     * 构建测试用的剧集列表
     */
    private List<StandardEpisodeBO> buildEpisodeList() {
        List<StandardEpisodeBO> episodes = new ArrayList<>();

        StandardEpisodeBO episode1 = new StandardEpisodeBO();
        episode1.setEid("EID001");
        episode1.setEpisode(1);
        episode1.setType(NewResultCardTypeEnum.NORMAL.getValue());
        episodes.add(episode1);

        StandardEpisodeBO episode2 = new StandardEpisodeBO();
        episode2.setEid("EID002");
        episode2.setEpisode(2);
        episode2.setType(NewResultCardTypeEnum.NORMAL.getValue());
        episodes.add(episode2);

        return episodes;
    }

    /**
     * 测试浏览器播放场景 - 首位节目且合作节目
     */
    @Test
    public void testResultAggregation_BrowserPlay_FirstItem_Cooperation() {
        // 设置浏览器播放场景
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());

        // 创建首位合作节目
        KeyWordSearchResponse firstItem = buildKeyWordSearchResponse("SID001", "测试节目1", 1);
        firstItem.setSourceKind(1); // 合作节目
        searchResult.add(firstItem);

        // 创建非首位节目
        KeyWordSearchResponse secondItem = buildKeyWordSearchResponse("SID002", "测试节目2", 2);
        secondItem.setSourceKind(2); // 非合作节目
        searchResult.add(secondItem);

        // Mock剧集数据
        List<StandardEpisodeBO> episodeList = buildEpisodeList();
        when(standardEpisodeRpcApiProxy.getStartEndEpisodeBySid(
                eq("SID001"), anyInt(), anyInt(), anyInt(), anyBoolean()))
                .thenReturn(episodeList);

        // Mock VIP信息
        VideoVipInfo vipInfo = new VideoVipInfo();
        context.setVipInfo(vipInfo);
        when(vipRelatedService.getVipType(any(VideoVipInfo.class))).thenReturn("VIP");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证结果
        Assert.assertEquals(searchResult, response.getLongVideoSearchResult());
        Assert.assertEquals(Integer.valueOf(1), response.getHasMore());
        Assert.assertEquals(10, response.getPageSize());
        Assert.assertEquals(1, response.getPageIndex());

        // 验证首位节目处理了剧集
        verify(standardEpisodeRpcApiProxy).getStartEndEpisodeBySid(
                eq("SID001"), anyInt(), anyInt(), anyInt(), anyBoolean());
        Assert.assertNotNull(firstItem.getEpisodes());
        Assert.assertEquals(2, firstItem.getEpisodes().size());

        // 验证播放源处理
        Assert.assertEquals("OPPO视频", firstItem.getPlaySourceName());
        Assert.assertEquals("oppo_icon", firstItem.getPlaySourceIcon());
    }

    /**
     * 测试浏览器播放场景 - 非首位节目
     */
    @Test
    public void testResultAggregation_BrowserPlay_NonFirstItem() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(100);

        // 创建首位节目（用于触发剧集处理）
        KeyWordSearchResponse firstItem = buildKeyWordSearchResponse("SID001", "测试节目1", 1);
        firstItem.setSourceKind(1);
        searchResult.add(firstItem);

        // 创建非首位合作节目
        KeyWordSearchResponse secondItem = buildKeyWordSearchResponse("SID002", "测试节目2", 2);
        secondItem.setSourceKind(1);
        searchResult.add(secondItem);

        // Mock剧集数据
        List<StandardEpisodeBO> mockEpisodes = new ArrayList<>();
        StandardEpisodeBO episodeBO = new StandardEpisodeBO();
        episodeBO.setEid("EID001");
        episodeBO.setSource("tencent");
        episodeBO.setEpisode(1);
        episodeBO.setType(NewResultCardTypeEnum.NORMAL.getValue());
        mockEpisodes.add(episodeBO);

        when(standardEpisodeRpcApiProxy.getStartEndEpisodeBySid(
                eq("SID001"), anyInt(), anyInt(), anyInt(), anyBoolean()))
                .thenReturn(mockEpisodes);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证首位节目处理了剧集，非首位节目不处理剧集
        verify(standardEpisodeRpcApiProxy).getStartEndEpisodeBySid(
                eq("SID001"), anyInt(), anyInt(), anyInt(), anyBoolean());
        verify(standardEpisodeRpcApiProxy, never()).getStartEndEpisodeBySid(
                eq("SID002"), anyInt(), anyInt(), anyInt(), anyBoolean());
    }

    /**
     * 测试浏览器播放场景 - 非合作节目
     */
    @Test
    public void testResultAggregation_BrowserPlay_NonCooperation() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());

        // 创建首位非合作节目
        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSourceKind(2); // 非合作节目
        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证非合作节目不处理剧集
        verify(standardEpisodeRpcApiProxy, never()).getStartEndEpisodeBySid(
                anyString(), anyInt(), anyInt(), anyInt(), anyBoolean());
    }

    /**
     * 测试非浏览器播放场景 - 处理DP（有webUrl的情况）
     */
    @Test
    public void testResultAggregation_NonBrowserPlay_HandleDP() {
        param.setAppId("other_app");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setButtonStatus(1); // 非全网搜节目
        item.setContentType("tv");
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock showBrand配置
        when(configurationShowBrandConfig.getShowBrandConfigByAppId(anyString())).thenReturn("&showBrand=1");

        // Mock第三方媒体参数
        when(thirdPartyCommonService.getThirdPartyMediaParams(
                anyString(), anyInt(), anyInt(), anyString(), anyBoolean()))
                .thenReturn("&yoliDpDetailPageStyle=1");

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证DP处理 - 有webUrl时走h5链接
        Assert.assertTrue(item.getDeepLink().contains("yoli://yoli.com/yoli/h5"));
        Assert.assertTrue(item.getDeepLink().contains("openFrom=other_app"));
        Assert.assertTrue(item.getDeepLink().contains("url=http://test.com"));
        Assert.assertTrue(item.getDeepLink().contains("&showBrand=1"));
        Assert.assertTrue(item.getDeepLink().contains("&yoliDpDetailPageStyle=1"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试非浏览器播放场景 - 第三方搜索处理DP
     */
    @Test
    public void testResultAggregation_NonBrowserPlay_ThirdSearch_HandleDP() {
        param.setAppId("other_app");
        param.setVersion(80700); // 设置高版本，确保走高版本逻辑

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setButtonStatus(1); // 非全网搜节目
        item.setContentType("tv");
        item.setSource("iqiyi"); // 使用不同的第三方搜索源
        searchResult.add(item);

        // Mock第三方搜索配置
        Map<String, Map<String, Integer>> thirdSearchMap = new HashMap<>();
        thirdSearchMap.put("iqiyi", new HashMap<>());
        when(searchProperties.getThirdSearchMap()).thenReturn(thirdSearchMap);

        // Mock showBrand配置
        when(configurationShowBrandConfig.getShowBrandConfigByAppId(anyString())).thenReturn("&showBrand=1");

        // Mock第三方媒体参数
        when(thirdPartyCommonService.getThirdPartyMediaParams(
                anyString(), anyInt(), anyInt(), anyString(), anyBoolean()))
                .thenReturn("&yoliDpDetailPageStyle=1");

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证第三方搜索DP处理
        Assert.assertTrue(item.getDeepLink().contains("yoli://yoli.com/detail/longvideo/outofstockvideodetail"));
        Assert.assertTrue(item.getDeepLink().contains("linkValue=SID001"));
        Assert.assertTrue(item.getDeepLink().contains("openFrom=other_app"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试全网搜节目DP处理 - 已处理的情况
     */
    @Test
    public void testResultAggregation_AllWebSearch_AlreadyHandled() {
        param.setAppId(SearchConstant.SEARCH_FROM_QUANSOU);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setButtonStatus(2); // 全网搜节目
        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证不处理DP
        Assert.assertNull(item.getDeepLink());
    }

    /**
     * 测试锁屏场景 - 特殊appId转换
     */
    @Test
    public void testResultAggregation_Magazine_AppIdConversion() {
        param.setAppId(SearchConstant.SEARCH_FROM_MAGAZINE);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证appId转换
        Assert.assertTrue(item.getDeepLink().contains("openFrom=screenoff_searchbanner"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试小布场景 - 特殊appId转换
     */
    @Test
    public void testResultAggregation_Breeno_AppIdConversion() {
        param.setAppId(SearchConstant.SEARCH_FROM_BREENO);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证appId转换
        Assert.assertTrue(item.getDeepLink().contains("openFrom=breeno_searchresults"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试优酷移动端处理
     */
    @Test
    public void testResultAggregation_YoukuMobile() {
        param.setAppId("other_app");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(SourceEnum.YOUKU_MOBILE.getDataSource());
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证优酷移动端处理
        Assert.assertTrue(item.getDeepLink().contains("openFrom=other_app"));
        Assert.assertTrue(item.getDeepLink().contains("url=http://test.com"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试非优酷移动端处理 - 有webUrl
     */
    @Test
    public void testResultAggregation_NonYoukuMobile_WithWebUrl() {
        param.setAppId("other_app");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource("tencent");
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证处理
        Assert.assertTrue(item.getDeepLink().contains("yoli://yoli.com/yoli/h5"));
        Assert.assertTrue(item.getDeepLink().contains("url=http://test.com"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试非优酷移动端处理 - 无webUrl且非第三方搜索
     */
    @Test
    public void testResultAggregation_NonYoukuMobile_NoWebUrl_NotThirdSearch() {
        param.setAppId("other_app");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource("tencent");
        searchResult.add(item);

        // Mock空第三方搜索配置
        when(searchProperties.getThirdSearchMap()).thenReturn(new HashMap<>());

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证处理
        Assert.assertTrue(item.getDeepLink().contains("yoli://yoli.com/detail?linkValue=SID001"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试第三方搜索处理 - 高版本
     */
    @Test
    public void testResultAggregation_ThirdSearch_HighVersion() {
        param.setAppId("other_app");
        param.setVersion(80700);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource("tencent");
        searchResult.add(item);

        // Mock第三方搜索配置
        Map<String, Map<String, Integer>> thirdSearchMap = new HashMap<>();
        thirdSearchMap.put("tencent", new HashMap<>());
        when(searchProperties.getThirdSearchMap()).thenReturn(thirdSearchMap);

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证高版本处理
        Assert.assertTrue(item.getDeepLink().contains("yoli://yoli.com/detail/longvideo/outofstockvideodetail"));
        Assert.assertTrue(item.getDeepLink().contains("linkValue=SID001"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试第三方搜索处理 - 低版本
     */
    @Test
    public void testResultAggregation_ThirdSearch_LowVersion() {
        param.setAppId("other_app");
        param.setVersion(80600);
        param.setAppVersion("8.6.0");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource("iqiyi"); // 使用不同的第三方搜索源
        searchResult.add(item);

        // Mock第三方搜索配置
        Map<String, Map<String, Integer>> thirdSearchMap = new HashMap<>();
        thirdSearchMap.put("iqiyi", new HashMap<>());
        when(searchProperties.getThirdSearchMap()).thenReturn(thirdSearchMap);

        // Mock showBrand配置
        when(configurationShowBrandConfig.getShowBrandConfigByAppId(anyString())).thenReturn("&showBrand=1");

        // Mock第三方媒体参数
        when(thirdPartyCommonService.getThirdPartyMediaParams(
                anyString(), anyInt(), anyInt(), anyString(), anyBoolean()))
                .thenReturn("&yoliDpDetailPageStyle=1");

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证低版本处理
        Assert.assertTrue(item.getDeepLink().contains("yoli://yoli.com/YoliSearch/search"));
        Assert.assertTrue(item.getDeepLink().contains("openFrom=other_app"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试小布场景 - 外显来源处理
     */
    @Test
    public void testResultAggregation_Breeno_TargetAppName() {
        param.setAppId(SearchConstant.SEARCH_FROM_BREENO);
        param.getAttributeValues().setChannel("oppo");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource("tencent");
        searchResult.add(item);

        // Mock空第三方搜索配置
        when(searchProperties.getThirdSearchMap()).thenReturn(new HashMap<>());

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证外显来源处理
        Assert.assertNotNull(item.getTargetAppName());
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试锁屏和小布场景 - 无感调端参数
     */
    @Test
    public void testResultAggregation_ExactMatchSource_SWLParams() {
        param.setAppId(SearchConstant.SEARCH_FROM_MAGAZINE);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_YOUKU_MOBILE);
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock handleUpgradePageDp - 返回处理后的deepLink
        when(thirdPartyCommonService.handleUpgradePageDp(anyString(), anyInt(), any(), anyString(), anyString(), any()))
                .thenAnswer(invocation -> {
                    String deepLink = invocation.getArgument(0);
                    return deepLink + "&normalDp=1";
                });

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证无感调端参数
        Assert.assertTrue(item.getDeepLink().contains("&showWhenLocked=1"));
        Assert.assertTrue(item.getDeepLink().contains("&swl=1"));
        Assert.assertTrue(item.getDeepLink().contains("&normalDp=1"));
    }

    /**
     * 测试页面索引>=10的情况
     */
    @Test
    public void testResultAggregation_PageIndexGreaterThan10() {
        param.setPageIndex(10);
        param.setHasMore(1);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证hasMore设置为0
        Assert.assertEquals(Integer.valueOf(0), response.getHasMore());
    }

    /**
     * 测试空搜索结果的情况
     */
    @Test
    public void testResultAggregation_EmptySearchResult() {
        // 不添加任何搜索结果
        param.setHasMore(1);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证hasMore不设置
        Assert.assertEquals(Integer.valueOf(0), response.getHasMore());
    }

    /**
     * 测试前三页数据截取 - 正常情况
     */
    @Test
    public void testResultAggregation_FirstThreePages_DataTruncation() {
        param.setPageIndex(2);
        param.setPageSize(5);

        // 添加10条数据
        for (int i = 0; i < 10; i++) {
            searchResult.add(buildKeyWordSearchResponse("SID" + i, "测试节目" + i, i + 1));
        }

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证数据截取：第2页，每页5条，应该返回索引5-9的数据
        Assert.assertEquals(5, response.getLongVideoSearchResult().size());
        Assert.assertEquals("SID5", response.getLongVideoSearchResult().get(0).getSid());
        Assert.assertEquals("SID9", response.getLongVideoSearchResult().get(4).getSid());
    }

    /**
     * 测试前三页数据截取 - 起始索引超出范围
     */
    @Test
    public void testResultAggregation_FirstThreePages_StartIndexOutOfRange() {
        param.setPageIndex(3);
        param.setPageSize(10);

        // 只添加5条数据
        for (int i = 0; i < 5; i++) {
            searchResult.add(buildKeyWordSearchResponse("SID" + i, "测试节目" + i, i + 1));
        }

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证返回空列表
        Assert.assertEquals(0, response.getLongVideoSearchResult().size());
    }

    /**
     * 测试前三页数据截取 - 结束索引超出范围
     */
    @Test
    public void testResultAggregation_FirstThreePages_EndIndexOutOfRange() {
        param.setPageIndex(1);
        param.setPageSize(15);

        // 只添加10条数据
        for (int i = 0; i < 10; i++) {
            searchResult.add(buildKeyWordSearchResponse("SID" + i, "测试节目" + i, i + 1));
        }

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证返回所有数据
        Assert.assertEquals(10, response.getLongVideoSearchResult().size());
    }

    /**
     * 测试第四页及以后 - 不进行数据截取
     */
    @Test
    public void testResultAggregation_FourthPageAndLater_NoTruncation() {
        param.setPageIndex(4);
        param.setPageSize(5);

        // 添加10条数据
        for (int i = 0; i < 10; i++) {
            searchResult.add(buildKeyWordSearchResponse("SID" + i, "测试节目" + i, i + 1));
        }

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证不进行数据截取，返回所有数据
        Assert.assertEquals(10, response.getLongVideoSearchResult().size());
    }

    /**
     * 测试浏览器播放场景 - 芒果源需要升级
     */
    @Test
    public void testResultAggregation_BrowserPlay_MgMobile_NeedUpgrade() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(40900); // 低于芒果升级版本

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_MGMOBILE);
        item.setSourceKind(1);
        searchResult.add(item);

        // Mock升级页面URL
        when(searchProperties.getAppUpgradePage()).thenReturn("upgrade_url");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证设置升级链接
        Assert.assertEquals("upgrade_url", item.getDeepLink());
    }

    /**
     * 测试浏览器播放场景 - 乐视源需要升级
     */
    @Test
    public void testResultAggregation_BrowserPlay_Letv_NeedUpgrade() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(60200); // 低于乐视升级版本

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_LETV);
        item.setSourceKind(1);
        searchResult.add(item);

        // Mock升级页面URL
        when(searchProperties.getAppUpgradePage()).thenReturn("upgrade_url");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证设置升级链接
        Assert.assertEquals("upgrade_url", item.getDeepLink());
    }

    /**
     * 测试浏览器播放场景 - 优酷源需要升级
     */
    @Test
    public void testResultAggregation_BrowserPlay_Youku_NeedUpgrade() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71500); // 低于优酷升级版本

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_YOUKU_MOBILE);
        item.setSourceKind(1);
        searchResult.add(item);

        // Mock SourceVersionService - 返回不包含优酷的源列表
        when(sourceVersionService.getSourceListByVersion(anyInt())).thenReturn(Arrays.asList("tencent", "iqiyi"));
        when(youkuSourceFilterService.getUpGradeUrl(anyString(), anyString(), anyString()))
                .thenReturn("upgrade_url");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证设置升级链接
        Assert.assertEquals("upgrade_url", item.getDeepLink());
    }

    /**
     * 测试浏览器播放场景 - 优酷源快应用版本为空
     */
    @Test
    public void testResultAggregation_BrowserPlay_Youku_QuickEngineVersionNull() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(null); // 快应用版本为空

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_YOUKU_MOBILE);
        item.setSourceKind(1);
        searchResult.add(item);

        // Mock SourceVersionService - 返回包含优酷的源列表，但快应用版本为空
        when(sourceVersionService.getSourceListByVersion(anyInt())).thenReturn(Arrays.asList("tencent", "iqiyi", "youku"));
        when(youkuSourceFilterService.getUpGradeUrl(anyString(), anyString(), anyString()))
                .thenReturn("upgrade_url");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证设置升级链接
        Assert.assertEquals("upgrade_url", item.getDeepLink());
    }

    /**
     * 测试浏览器播放场景 - 优酷源快应用版本过低
     */
    @Test
    public void testResultAggregation_BrowserPlay_Youku_QuickEngineVersionLow() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(50); // 低于最低快应用版本

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_YOUKU_MOBILE);
        item.setSourceKind(1);
        searchResult.add(item);

        // Mock SourceVersionService - 返回包含优酷的源列表，但快应用版本过低
        when(sourceVersionService.getSourceListByVersion(anyInt())).thenReturn(Arrays.asList("tencent", "iqiyi", "youku"));
        when(youkuSourceFilterService.getUpGradeUrl(anyString(), anyString(), anyString()))
                .thenReturn("upgrade_url");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证设置升级链接
        Assert.assertEquals("upgrade_url", item.getDeepLink());
    }

    /**
     * 测试浏览器播放场景 - 合作节目正常播放
     */
    @Test
    public void testResultAggregation_BrowserPlay_Cooperation_NormalPlay() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(100);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource("tencent");
        item.setSourceKind(1);
        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证设置正常播放链接
        Assert.assertTrue(item.getDeepLink().contains("openFrom=browser_play_page"));
    }

    /**
     * 测试浏览器播放场景 - 优酷源合作节目
     */
    @Test
    public void testResultAggregation_BrowserPlay_Youku_Cooperation() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(100);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSource(CopyrightConstant.COPYRIGHT_YOUKU_MOBILE);
        item.setSourceKind(1);
        // 为优酷移动端设置初始的deepLink，因为handleYoukuMobileSuffix需要在现有deepLink基础上添加参数
        item.setDeepLink("yoli://yoli.com/detail?linkValue=SID001");
        searchResult.add(item);

        // Mock SourceVersionService - 返回包含优酷移动端的源列表，避免触发升级逻辑
        when(sourceVersionService.getSourceListByVersion(anyInt())).thenReturn(Arrays.asList("tencent", "iqiyi", "youkumobile"));

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证优酷源处理
        Assert.assertTrue(item.getDeepLink().contains("openFrom=browser_play_page"));
    }

    /**
     * 测试浏览器播放场景 - 剧集链接设置
     */
    @Test
    public void testResultAggregation_BrowserPlay_EpisodeDeepLink() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(100);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSourceKind(1);
        item.setSource("tencent");
        item.setDeepLink("album_deeplink");

        // Mock剧集数据
        List<StandardEpisodeBO> mockEpisodes = new ArrayList<>();
        StandardEpisodeBO episodeBO = new StandardEpisodeBO();
        episodeBO.setEid("EID001");
        episodeBO.setSource("tencent");
        episodeBO.setEpisode(1);
        episodeBO.setType(NewResultCardTypeEnum.NORMAL.getValue());
        mockEpisodes.add(episodeBO);

        when(standardEpisodeRpcApiProxy.getStartEndEpisodeBySid(
                eq("SID001"), anyInt(), anyInt(), anyInt(), anyBoolean()))
                .thenReturn(mockEpisodes);

        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证剧集链接设置
        Assert.assertNotNull(item.getEpisodes());
        Assert.assertFalse(item.getEpisodes().isEmpty());
        // 非优酷源的剧集链接应该包含videoEid和playSource参数
        Assert.assertTrue(item.getEpisodes().get(0).getDeepLink().contains("videoEid=EID001"));
        Assert.assertTrue(item.getEpisodes().get(0).getDeepLink().contains("playSource=tencent"));
        Assert.assertTrue(item.getEpisodes().get(0).getDeepLink().contains("openFrom=browser_play_page"));
    }

    /**
     * 测试浏览器播放场景 - 优酷源剧集链接
     */
    @Test
    public void testResultAggregation_BrowserPlay_Youku_EpisodeDeepLink() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71600);
        param.setQuickEngineVersion(100);

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSourceKind(1);
        item.setSource(CopyrightConstant.COPYRIGHT_YOUKU_MOBILE);
        item.setDeepLink("album_deeplink");

        // Mock剧集数据
        List<StandardEpisodeBO> mockEpisodes = new ArrayList<>();
        StandardEpisodeBO episodeBO = new StandardEpisodeBO();
        episodeBO.setEid("EID001");
        episodeBO.setSource("youkumobile");
        episodeBO.setEpisode(1);
        episodeBO.setType(NewResultCardTypeEnum.NORMAL.getValue());
        mockEpisodes.add(episodeBO);

        when(standardEpisodeRpcApiProxy.getStartEndEpisodeBySid(
                eq("SID001"), anyInt(), anyInt(), anyInt(), anyBoolean()))
                .thenReturn(mockEpisodes);

        // Mock SourceVersionService - 返回包含优酷移动端的源列表，避免触发升级逻辑
        when(sourceVersionService.getSourceListByVersion(anyInt())).thenReturn(Arrays.asList("tencent", "iqiyi", "youkumobile"));

        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证优酷源剧集链接与剧头保持一致
        Assert.assertNull(item.getEpisodes());
    }

    /**
     * 测试非OPPO渠道的播放源名称
     */
    @Test
    public void testResultAggregation_NonOppoChannel_PlaySourceName() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.getAttributeValues().setChannel("other");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSourceKind(1);
        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证非OPPO渠道显示"视频"
        Assert.assertEquals("视频", item.getPlaySourceName());
    }

    /**
     * 测试非合作节目的播放源处理
     */
    @Test
    public void testResultAggregation_NonCooperation_PlaySource() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSourceKind(2);
        item.setCopyrightCode("tencent");
        searchResult.add(item);

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证非合作节目播放源处理
        Assert.assertNotNull(item.getPlaySourceName());
        Assert.assertNotNull(item.getPlaySourceIcon());
    }

    /**
     * 测试异常处理
     */
    @Test
    public void testResultAggregation_ExceptionHandling() {
        param.setAppId("other_app");

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setWebUrl("http://test.com");
        searchResult.add(item);

        // Mock异常
        when(configurationShowBrandConfig.getShowBrandConfigByAppId(anyString()))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试 - 不应该抛出异常
        try {
            outSideResultAggregationCmp.resultAggregation(context);
            // 验证基本处理仍然完成
            Assert.assertEquals(searchResult, response.getLongVideoSearchResult());
        } catch (Exception e) {
            Assert.fail("不应该抛出异常: " + e.getMessage());
        }
    }

    /**
     * 测试extractLinkValue方法在实际业务场景中的调用 - 通过handleDeepLink4BrowserPlay间接测试
     */
    @Test
    public void testExtractLinkValue_ThroughBusinessLogic() {
        param.setAppId(ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());
        param.setVersion(71501); // 7.15.1以上版本

        KeyWordSearchResponse item = buildKeyWordSearchResponse("SID001", "测试节目", 1);
        item.setSourceKind(1); // 合作节目
        item.setSource("tencent");
        item.setDeepLink("yoli://yoli.com/detail?linkValue=testSid&showSplashAd=0");
        searchResult.add(item);

        // Mock deepLinkUtils返回新的链接
        when(deepLinkUtils.getDirectPageDeepLink(eq("tencent"), eq("SID001"), eq("testSid")))
                .thenReturn("new_deeplink");

        // 执行测试
        outSideResultAggregationCmp.resultAggregation(context);

        // 验证extractLinkValue被正确调用，并且deepLinkUtils.getDirectPageDeepLink被调用
        verify(deepLinkUtils).getDirectPageDeepLink("tencent", "SID001", "testSid");

        // 验证最终链接包含showBrand参数
        Assert.assertTrue(item.getDeepLink().contains("showBrand=1"));
        Assert.assertTrue(item.getDeepLink().contains("openFrom=browser_play_page"));
    }

    /**
     * 测试extractLinkValue方法 - URL为null
     */
    @Test
    public void testExtractLinkValue_NullUrl() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试null URL
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, (String) null);
        Assert.assertNull(result);
    }

    /**
     * 测试extractLinkValue方法 - URL为空字符串
     */
    @Test
    public void testExtractLinkValue_EmptyUrl() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试空字符串URL
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, "");
        Assert.assertNull(result);
    }

    /**
     * 测试extractLinkValue方法 - URL中不包含linkValue参数
     */
    @Test
    public void testExtractLinkValue_NoLinkValueParam() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试不包含linkValue的URL
        String url = "yoli://yoli.com/detail?showSplashAd=0&openMainActivity=1";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertNull(result);
    }

    /**
     * 测试extractLinkValue方法 - URL中包含linkValue参数，后面还有其他参数
     */
    @Test
    public void testExtractLinkValue_WithLinkValueAndMoreParams() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试包含linkValue且后面有其他参数的URL
        String url = "yoli://yoli.com/detail?linkValue=test123&showSplashAd=0&openMainActivity=1";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertEquals("test123", result);
    }

    /**
     * 测试extractLinkValue方法 - URL中包含linkValue参数，后面没有其他参数
     */
    @Test
    public void testExtractLinkValue_WithLinkValueNoMoreParams() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试包含linkValue且后面没有其他参数的URL
        String url = "yoli://yoli.com/detail?linkValue=test456";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertEquals("test456", result);
    }

    /**
     * 测试extractLinkValue方法 - URL中linkValue参数在中间位置
     */
    @Test
    public void testExtractLinkValue_LinkValueInMiddle() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试linkValue在URL中间位置的URL
        String url = "yoli://yoli.com/detail?showSplashAd=0&linkValue=middleValue&openMainActivity=1";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertEquals("middleValue", result);
    }

    /**
     * 测试extractLinkValue方法 - URL中linkValue参数在最后位置
     */
    @Test
    public void testExtractLinkValue_LinkValueAtEnd() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试linkValue在URL最后位置的URL
        String url = "yoli://yoli.com/detail?showSplashAd=0&openMainActivity=1&linkValue=endValue";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertEquals("endValue", result);
    }

    /**
     * 测试extractLinkValue方法 - URL中linkValue参数值为空
     */
    @Test
    public void testExtractLinkValue_EmptyLinkValue() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试linkValue值为空的URL
        String url = "yoli://yoli.com/detail?linkValue=&showSplashAd=0";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertEquals("", result);
    }

    /**
     * 测试extractLinkValue方法 - URL中linkValue参数包含特殊字符
     */
    @Test
    public void testExtractLinkValue_SpecialCharacters() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method extractLinkValueMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("extractLinkValue", String.class);
        extractLinkValueMethod.setAccessible(true);

        // 测试linkValue包含特殊字符的URL
        String url = "yoli://yoli.com/detail?linkValue=test%20with%20spaces&showSplashAd=0";
        String result = (String) extractLinkValueMethod.invoke(outSideResultAggregationCmp, url);
        Assert.assertEquals("test%20with%20spaces", result);
    }

    /**
     * 测试setIqiyiMobileDeepLinkForBreeno方法 小布的移动端爱奇艺媒资跳转爱奇艺app
     */
    @Test
    public void testSetIqiyiMobileDeepLinkForBreeno_Movie() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method setIqiyiMobileDeepLinkForBreenoMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("setIqiyiMobileDeepLinkForBreeno", String.class, KeyWordSearchResponse.class);
        setIqiyiMobileDeepLinkForBreenoMethod.setAccessible(true);

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSource(SourceEnum.IQIYI_MOBILE.getDataSource());
        response.setContentType(ContentTypeEnum.MOVIE.getCode());
        response.setSourceAlbumId("sourceAlbumId");
        setIqiyiMobileDeepLinkForBreenoMethod.invoke(outSideResultAggregationCmp, SEARCH_FROM_BREENO, response);

        Assert.assertEquals("iqiyi://mobile/player?ftype=27&subtype=aqyOPP_30551629_27&aid=&tvid=sourceAlbumId", response.getDeepLink());
    }

    /**
     * 测试setIqiyiMobileDeepLinkForBreeno方法 小布的移动端爱奇艺媒资跳转爱奇艺app
     */
    @Test
    public void testSetIqiyiMobileDeepLinkForBreeno_Other() throws Exception {
        // 使用反射获取私有方法
        java.lang.reflect.Method setIqiyiMobileDeepLinkForBreenoMethod = OutSideResultAggregationCmp.class
                .getDeclaredMethod("setIqiyiMobileDeepLinkForBreeno", String.class, KeyWordSearchResponse.class);
        setIqiyiMobileDeepLinkForBreenoMethod.setAccessible(true);

        KeyWordSearchResponse response = new KeyWordSearchResponse();
        response.setSource(SourceEnum.IQIYI_MOBILE.getDataSource());
        response.setContentType(ContentTypeEnum.SHOW.getCode());
        response.setSourceAlbumId("sourceAlbumId");
        setIqiyiMobileDeepLinkForBreenoMethod.invoke(outSideResultAggregationCmp, SEARCH_FROM_BREENO, response);

        Assert.assertEquals("iqiyi://mobile/player?ftype=27&subtype=aqyOPP_30551629_27&aid=sourceAlbumId&tvid=", response.getDeepLink());
    }
}