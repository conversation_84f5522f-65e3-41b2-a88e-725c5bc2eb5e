package com.heytap.longvideo.search.service.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.YunheRankRpcApi;
import com.heytap.longvideo.client.media.yunhe.YunheRank;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.mq.YunheRankProviderService;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.YunheRankRpcApiProxy;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class YunheRankServiceTest {
    @Spy
    private YunheRankRpcApiProxy yunheRankRpcApiProxy = new YunheRankRpcApiProxy();
    @Mock
    private DouBanCommitTaskService douBanCommitTaskService;
    @Mock
    private YunheRankRpcApi yunheRankRpcApi;
    @Mock
    private HttpDataChannel httpDataChannel;
    @Mock
    private SearchProperties searchProperties;
    @Mock
    private UploadFileToOcsComponent fileToOcsComponent;
    @Mock
    private CsvExportConfig csvExportConfig;
    @Mock
    private YunheRankProviderService yunheRankProviderService;

    @InjectMocks
    private YunheRankService yunheRankService;

    @Before
    public void setup() throws HttpDataChannelException {
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForReboRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenReturn(buildReboResp());
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForTopRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenReturn(buildTopResp());
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForHotRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenReturn(buildHotResp());
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForDetail")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenReturn(buildDetailResp());
        when(httpDataChannel.getForObject(anyString(), any(), anyInt()))
                .thenReturn(new byte[0]);
    }

    @Test
    public void test_searchMedia4Spider() throws HttpDataChannelException {

        when(yunheRankRpcApi.queryListByDayAndLimitNum(any()))
                .thenReturn(CompletableFuture.completedFuture(new RpcResult(buildQueryListByDayAndLimitNumResp())));

        doNothing().when(douBanCommitTaskService).commitDouBanTask(anyList());
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/searchNameForDouBanDelayExecute")),
                any(), eq(String.class),  anyInt()))
                .thenReturn("{}");
        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.searchMedia4Spider();
    }

    @Test
    public void testCollect() throws HttpDataChannelException {
        when(yunheRankRpcApi.batchUpsert(anyList()))
                .thenReturn(CompletableFuture.completedFuture(new RpcResult(true)));
        when(yunheRankRpcApi.queryListByDayAndLimitNum(any()))
                .thenReturn(CompletableFuture.completedFuture(new RpcResult(buildQueryListByDayAndLimitNumResp())));

        doNothing().when(douBanCommitTaskService).commitDouBanTask(anyList());
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/searchNameForDouBanDelayExecute")),
                any(), eq(String.class),  anyInt()))
                .thenReturn("{}");

        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.collect();

        // 7个榜单 写入7次
        verify(yunheRankRpcApiProxy, times(7)).batchUpsert(anyList());
    }


    @Test
    public void testCollectTopRank4Tv_rpcError() {
        when(yunheRankRpcApi.batchUpsert(anyList()))
                .thenReturn(CompletableFuture.completedFuture(new RpcResult(404, "error")));
        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.collectTopRank4Tv();

        verify(yunheRankRpcApiProxy).batchUpsert(anyList());
    }

    @Test
    public void testCollectTopRank_null() throws HttpDataChannelException {
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForTopRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenReturn("{}");

        yunheRankService.collectTopRank("movie");

        // 不会执行
        verify(yunheRankRpcApiProxy, times(0)).batchUpsert(anyList());
    }

    @Test
    public void testCollectTopRank_rpc_throw() throws HttpDataChannelException {
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForTopRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenThrow(new RuntimeException());
        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.collectTopRank("movie");

        // 不会执行
        verify(yunheRankRpcApiProxy, times(0)).batchUpsert(anyList());
    }


    @Test
    public void testCollectTopRank_throw() {
        when(yunheRankRpcApi.batchUpsert(anyList()))
                .thenThrow(new RuntimeException());
        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.collectTopRank("movie");
    }

    @Test
    public void testCollectTopRank4Tv_null() throws HttpDataChannelException {
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForReboRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenReturn("{}");

        yunheRankService.collectTopRank4Tv();

        // 不会执行
        verify(yunheRankRpcApiProxy, times(0)).batchUpsert(anyList());
    }

    @Test
    public void testCollectHotRank_rpcThrow() throws HttpDataChannelException {
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/yunhe/postForHotRank")),
                isNull(), eq(String.class), any(), anyInt()))
                .thenThrow(new RuntimeException());
        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.collectHotRank("movie");

        // 不会执行
        verify(yunheRankRpcApiProxy, times(0)).batchUpsert(anyList());
    }

    @Test
    public void testCollectHotRank_throw() {
        when(yunheRankRpcApi.batchUpsert(anyList()))
                .thenThrow(new RuntimeException());
        Whitebox.setInternalState(yunheRankRpcApiProxy, "yunheRankRpcApi", yunheRankRpcApi);

        yunheRankService.collectHotRank("movie");
    }

    @Test
    public void testUploadImage2Ocs_throw() throws HttpDataChannelException {

        when(httpDataChannel.getForObject(anyString(), any(), anyInt()))
                .thenThrow(new RuntimeException());

        String url = yunheRankService.uploadImage2Ocs(123, "url1");

        Assert.assertEquals("url1", url);
    }


    private String buildReboResp() {
        return "[{\n" +
                "    \"reboRank\": 1,\n" +
                "    \"name\": \"北上\",\n" +
                "    \"nameID\": 8679709,\n" +
                "    \"marketShare\": 13.9,\n" +
                "    \"releaseTime\": \"2025-03-03\",\n" +
                "    \"dayPlatform\": \"iqiyi\",\n" +
                "    \"network\": 1,\n" +
                "    \"date\": \"2025-03-19\",\n" +
                "    \"occurDays\": 17,\n" +
                "    \"status\": 1,\n" +
                "    \"episodeUpdated\": {\n" +
                "        \"maxIndex\": 38,\n" +
                "        \"notvipIndex\": 26\n" +
                "    },\n" +
                "    \"yunheLevel\": [\n" +
                "        {\n" +
                "            \"date\": \"2025-03-19\",\n" +
                "            \"minValue\": 9.84,\n" +
                "            \"diff\": 0.96,\n" +
                "            \"position\": 10.15,\n" +
                "            \"level\": \"S+\",\n" +
                "            \"maxValue\": 11.61\n" +
                "        },\n" +
                "        {\n" +
                "            \"date\": \"2025-03-18\",\n" +
                "            \"minValue\": 9.86,\n" +
                "            \"diff\": 1,\n" +
                "            \"position\": 10.19,\n" +
                "            \"level\": \"S+\",\n" +
                "            \"maxValue\": 11.61\n" +
                "        }\n" +
                "    ]\n" +
                "}]";
    }

    private String buildTopResp() {
        return "{\n" +
                "    \"date\": 1742313600000,\n" +
                "    \"content\": [\n" +
                "        {\n" +
                "            \"playTimes\": 84434427,\n" +
                "            \"up\": 2540,\n" +
                "            \"commentCount\": 242,\n" +
                "            \"barrageCount\": 11363,\n" +
                "            \"rankPredictedAbs\": 1,\n" +
                "            \"rankPredictedRel\": 1,\n" +
                "            \"dataType\": \"inc\",\n" +
                "            \"nameID\": 8677573,\n" +
                "            \"occurDays\": 43,\n" +
                "            \"status\": 0,\n" +
                "            \"isNew\": 0,\n" +
                "            \"releaseTime\": 1738684800000,\n" +
                "            \"isVertical\": false,\n" +
                "            \"bcType\": 1,\n" +
                "            \"episodeNum\": 20250320,\n" +
                "            \"totalEpisodeNum\": 25,\n" +
                "            \"name\": \"大侦探·拾光季\",\n" +
                "            \"channel\": \"mangguo\",\n" +
                "            \"channelType\": \"art\",\n" +
                "            \"day\": 1,\n" +
                "            \"date\": \"Mar 19, 2025 12:00:00 AM\",\n" +
                "            \"isNet\": 0,\n" +
                "            \"marketShare\": null,\n" +
                "            \"goldIndex\": 5.3,\n" +
                "            \"yunheLevel\": [\n" +
                "                {\n" +
                "                    \"date\": \"2025-03-19\",\n" +
                "                    \"minValue\": 9.89,\n" +
                "                    \"diff\": 1,\n" +
                "                    \"position\": 10.42,\n" +
                "                    \"level\": \"S+\",\n" +
                "                    \"maxValue\": 11.34\n" +
                "                },\n" +
                "                {\n" +
                "                    \"date\": \"2025-03-18\",\n" +
                "                    \"minValue\": 9.89,\n" +
                "                    \"diff\": 1,\n" +
                "                    \"position\": 10.42,\n" +
                "                    \"level\": \"S+\",\n" +
                "                    \"maxValue\": 11.34\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
    }

    private String buildHotResp() {
        return "{\n" +
                "    \"date\": 1742313600000,\n" +
                "    \"content\": [\n" +
                "        {\n" +
                "            \"name\": \"乘风2025\",\n" +
                "            \"nameId\": 8681292,\n" +
                "            \"channelType\": \"art\",\n" +
                "            \"allHot\": 7897.0,\n" +
                "            \"feedbackHot\": 1962.0,\n" +
                "            \"spreadHot\": 5096.0,\n" +
                "            \"weiboHot\": 0.0,\n" +
                "            \"searchHot\": 839.0,\n" +
                "            \"isVertical\": false,\n" +
                "            \"bcType\": 1,\n" +
                "            \"releaseTime\": 1742486400000,\n" +
                "            \"channel\": \"mangguo\",\n" +
                "            \"occurDays\": -1,\n" +
                "            \"network\": 0,\n" +
                "            \"status\": 999\n" +
                "        }\n" +
                "    ]\n" +
                "}";
    }

    private String buildDetailResp() {
        return "{\n" +
                "    \"country\": \"中国大陆\",\n" +
                "    \"copyright\": null,\n" +
                "    \"isMemberFree\": \"8,9,7\",\n" +
                "    \"propman\": \"刘傲雷\",\n" +
                "    \"choreographer\": \"董杨\",\n" +
                "    \"briefStory\": \"温以凡（章若楠饰）回到家乡工作,意外遇到了高中同学桑延（白敬亭饰）。久别重逢,两人彼此装不认识,却又一次次巧遇。温以凡遭到隔壁男人骚扰,无奈决定搬家。桑延的公寓被邻居家失火殃及,也需要重新装修。阴差阳错之下,两人竟然合租到了一起。原本互相逃避的两人因此打破隔阂,忍不住向对方靠近。桑延无微不至的照顾让温以凡逐渐敞开心扉,决定鼓起勇气主动追求桑延,桑延也跨越城市给予她最真挚的告白,两人正式确定了关系。后来,温以凡向桑延坦诚当初违背约定没有跟他考同一所大学的原因。得知真相的桑延心疼不已,发誓不再让温以凡受到伤害,并用实际行动保护了她。最终,两人搬进了桑延亲手设计的新家,共同经营幸福的生活。温以凡（章若楠饰）回到家乡工作,意外遇到了高中同学桑延（白敬亭饰）。久别重逢,两人彼此装不认识,却又一次次巧遇。温以凡遭到隔壁男人骚扰,无奈决定搬家。桑延的公寓被邻居家失火殃及,也需要重新装修。阴差阳错之下,两人竟然合租到了一起。原本互相逃避的两人因此打破隔阂,忍不住向对方靠近。桑延无微不至的照顾让温以凡逐渐敞开心扉,决定鼓起勇气主动追求桑延,桑延也跨越城市给予她最真挚的告白,两人正式确定了关系。后来,温以凡向桑延坦诚当初违背约定没有跟他考同一所大学的原因。得知真相的桑延心疼不已,发誓不再让温以凡受到伤害,并用实际行动保护了她。最终,两人搬进了桑延亲手设计的新家,共同经营幸福的生活。\",\n" +
                "    \"rating\": 5.4,\n" +
                "    \"channelType\": \"tv\",\n" +
                "    \"logKeeper\": \"刘馨宇,赖庆昇,汪丹\",\n" +
                "    \"notVipDownTime\": \"2025-03-17\",\n" +
                "    \"offLineTime\": \"2025-03-10\",\n" +
                "    \"dubbing\": \"王勇,边江\",\n" +
                "    \"area\": \"中国\",\n" +
                "    \"soundtrack\": \"吴柏醇\",\n" +
                "    \"ratingTimes\": 151325,\n" +
                "    \"superintendent\": \"龙丹妮,关旭,谢颖,魏冬,权香兰\",\n" +
                "    \"presenter\": \"\",\n" +
                "    \"issue\": \"羊菲\",\n" +
                "    \"director\": \"瞿友宁\",\n" +
                "    \"vipDownTime\": \"2025-03-16\",\n" +
                "    \"hpURL\": \"http://m.ykimg.com/058400006795BD45200396107A5D0001\",\n" +
                "    \"productionCompany\": \"哇唧唧哇,优酷\",\n" +
                "    \"clothingDesign\": \"林蓉\",\n" +
                "    \"clock\": 12,\n" +
                "    \"lighting\": \"张勇,赵生飞,崔达达\",\n" +
                "    \"tags\": \"都市\",\n" +
                "    \"totalEpisodeNum\": 32,\n" +
                "    \"channels\": \"[\\\"youku\\\"]\",\n" +
                "    \"isVertical\": 0,\n" +
                "    \"name\": \"难哄\",\n" +
                "    \"nameId\": 8678675,\n" +
                "    \"publisher\": \"龙丹妮,孟钧\",\n" +
                "    \"producer\": \"曾赟,赵光亚,王高阳,王禹涵\",\n" +
                "    \"guest\": \"\",\n" +
                "    \"photography\": \"冯信华\",\n" +
                "    \"stylingDesign\": \"刘乙沫\",\n" +
                "    \"ratingTimesDiff\": 1179,\n" +
                "    \"englishName\": \"TheFirstFrost\",\n" +
                "    \"releaseTime\": 1739808000000,\n" +
                "    \"movieType\": \"\",\n" +
                "    \"role\": \"桑延,温以凡,苏浩安,钟思乔,向朗,穆承允,段嘉许,桑稚,苏皓家（苏爷爷）,江汝（苏奶奶）,车雁琴,苏恬,钱文华,赵媛冬,王琳琳,方梨,桑荣,黎萍,阿森,章文宏,付壮,甘鸿远,车兴德,钱飞,陈骏文,郑可佳,林雨晴,余卓,何明博,大军,甄玉,温良贤,温铭,曾宜,温良哲,郑华源,钟古龙（钟父）,金泳鑫（钟母）,钟建业（钟爷）,陈小芬（钟奶）,钟发白（钟哥）,蔡静（钟嫂）,钟无双（双胞胎侄女）,钟无艳（双胞胎侄女）,钟国强（钟弟）,石磊,李晓纯,石佳玉,陆仪,陈惜,郭铃,郭玲父,花菜,古峰,斯文男,胖大叔,马叔,芳姨,余小姐,崔静语,林雨晴母亲,8岁温以凡,5岁温以凡,5岁钟思乔,5岁向朗,7岁苏浩安,11岁穆承允,13岁郑可佳,小桑稚,郑可鑫,崔静语儿子,斯文男孩子,鬼轩轩,秋秋,加班餐吧歌手,加班餐吧歌手\",\n" +
                "    \"setDecorator\": \"李宝河\",\n" +
                "    \"notVipNum\": 3,\n" +
                "    \"visualEffects\": \"\",\n" +
                "    \"episodeNum\": 32,\n" +
                "    \"castingDirector\": \"仓子,张倍嘉\",\n" +
                "    \"recording\": \"许正一\",\n" +
                "    \"bcType\": null,\n" +
                "    \"network\": 0,\n" +
                "    \"duration\": 49,\n" +
                "    \"dayPlatform_all\": \"youku\",\n" +
                "    \"artDesign\": \"梁鸿鹄\",\n" +
                "    \"ratingDiff\": \"0.0\",\n" +
                "    \"producedPlatform\": null,\n" +
                "    \"duration2\": 2962,\n" +
                "    \"artDirection\": \"\",\n" +
                "    \"isEdition\": 0,\n" +
                "    \"release_time\": 20250218,\n" +
                "    \"timestamp\": 1742437855,\n" +
                "    \"screenwriter\": \"瞿友宁,陈龙,赵文晶,张可欣\",\n" +
                "    \"issuingCompany\": \"\",\n" +
                "    \"original\": \"竹已\",\n" +
                "    \"assistant\": \"姜瑞智,颜浩轩,吴蘅若,王丹,苏春雨\",\n" +
                "    \"continuity\": \"王雨薇,吴姿莹\",\n" +
                "    \"editedFlag\": 0,\n" +
                "    \"dayPlatform_start\": \"youku\",\n" +
                "    \"stageManager\": \"吴成达\",\n" +
                "    \"isVip\": 1,\n" +
                "    \"actor\": \"白敬亭,章若楠,陈昊森,张淼怡,翟潇闻,原野,吴宇恒,刘楚恬,秦沛,鲍起静,孔琳,陈嘉男,傅淼,冯韵之,索朗美淇,苏棋,魏子昕,邓英,魏浚笙,何炅,张珂源,蒋中炜,李虹辰,滕哲,苏逸阳,段星羽,张沐兮,张睿熙,张哲,韩建懿,汪奕乔,王盛,季鹏,阚昕,赵小东,周阳,高玉庆,李荣东,应宝驹,张裔春,裴国栋,刘思倩,虞舒熹,李舒悦,童俊傑,李肆童,杨云绚,周妍霏,王沚渊,张思敏,李一萌,成城,康克,赵子天,林杉,宁小花,张世宏,苏爱婷,闫可欣,苑立若心,徐薏雯,李美奕冰,余昕琪,姜鋆熙,达伟,王俊睿,罗梓烁,米妞,孟函羲,陈誉锦,程礼欧阳,谢林镐,肖乔严,彭号翔,张洢豪,赵磊,白敬亭,章若楠,陈昊森,张淼怡,翟潇闻,原野,吴宇恒,刘楚恬,秦沛,鲍起静,孔琳,陈嘉男,傅淼,冯韵之,索朗美淇,苏棋,魏子昕,邓英,魏浚笙,何炅,张珂源,蒋中炜,李虹辰,滕哲,苏逸阳,段星羽,张沐兮,张睿熙,张哲,韩建懿,汪奕乔,王盛,季鹏,阚昕,赵小东,周阳,高玉庆,李荣东,应宝驹,张裔春,裴国栋,刘思倩,虞舒熹,李舒悦,童俊傑,李肆童,杨云绚,周妍霏,王沚渊,张思敏,李一萌,成城,康克,赵子天,林杉,宁小花,张世宏,苏爱婷,闫可欣,苑立若心,徐薏雯,李美奕冰,余昕琪,姜鋆熙,达伟,王俊睿,罗梓烁,米妞,孟函羲,陈誉锦,程礼欧阳,谢林镐,肖乔严,彭号翔,张洢豪,赵磊\",\n" +
                "    \"doubanUrl\": \"https://movie.douban.com/subject/35874151/\",\n" +
                "    \"chineseName\": \"难哄\"\n" +
                "}";
    }


    private List<YunheRank> buildQueryListByDayAndLimitNumResp() {
       return JacksonUtil.parseObject("[\n" +
                "\t{\n" +
                "\t\t\"id\": 5382,\n" +
                "\t\t\"day\": \"20250511\",\n" +
                "\t\t\"kind\": \"hot\",\n" +
                "\t\t\"nameId\": 8681701,\n" +
                "\t\t\"title\": \"天赐的声音第6季\",\n" +
                "\t\t\"rank\": 1,\n" +
                "\t\t\"status\": 0,\n" +
                "\t\t\"value\": 5952,\n" +
                "\t\t\"type\": \"art\",\n" +
                "\t\t\"level\": null,\n" +
                "\t\t\"occurDays\": 24,\n" +
                "\t\t\"platform\": \"bilibili,iqiyi,qq,youku\",\n" +
                "\t\t\"posterUrl\": \"http://iot-long-video-original-hn2.s3v2.dg-access-test.wanyol.com/yunhe/yunhe_poster_8681701.jpg\",\n" +
                "\t\t\"releaseTime\": \"20250418\",\n" +
                "\t\t\"director\": \"孙竞\",\n" +
                "\t\t\"actor\": \"\",\n" +
                "\t\t\"area\": \"中国\",\n" +
                "\t\t\"brief\": null,\n" +
                "\t\t\"tag\": \"音乐\",\n" +
                "\t\t\"rating\": null,\n" +
                "\t\t\"episodeNum\": 11\n" +
                "\t},\n" +
                "\t{\n" +
                "\t\t\"id\": 5392,\n" +
                "\t\t\"day\": \"20250511\",\n" +
                "\t\t\"kind\": \"top\",\n" +
                "\t\t\"nameId\": 8665905,\n" +
                "\t\t\"title\": \"根本停不下来\",\n" +
                "\t\t\"rank\": 1,\n" +
                "\t\t\"status\": 2,\n" +
                "\t\t\"value\": null,\n" +
                "\t\t\"type\": \"movie\",\n" +
                "\t\t\"level\": null,\n" +
                "\t\t\"occurDays\": 2,\n" +
                "\t\t\"platform\": \"bilibili,iqiyi,letv,mangguo,qq,youku\",\n" +
                "\t\t\"posterUrl\": \"http://iot-long-video-original-hn2.s3v2.dg-access-test.wanyol.com/yunhe/yunhe_poster_8665905.jpg\",\n" +
                "\t\t\"releaseTime\": \"20250510\",\n" +
                "\t\t\"director\": \"林珍钊\",\n" +
                "\t\t\"actor\": \"包贝尔,王智,蔡明,唐人,陈卫,苏伊可,艾伦,常远,贾冰,克拉拉\",\n" +
                "\t\t\"area\": \"中国\",\n" +
                "\t\t\"brief\": null,\n" +
                "\t\t\"tag\": \"喜剧\",\n" +
                "\t\t\"rating\": null,\n" +
                "\t\t\"episodeNum\": null\n" +
                "\t},\n" +
                "\t{\n" +
                "\t\t\"id\": 5402,\n" +
                "\t\t\"day\": \"20250511\",\n" +
                "\t\t\"kind\": \"hot\",\n" +
                "\t\t\"nameId\": 8681174,\n" +
                "\t\t\"title\": \"我会好好的\",\n" +
                "\t\t\"rank\": 1,\n" +
                "\t\t\"status\": 3,\n" +
                "\t\t\"value\": 6854,\n" +
                "\t\t\"type\": \"movie\",\n" +
                "\t\t\"level\": null,\n" +
                "\t\t\"occurDays\": 4,\n" +
                "\t\t\"platform\": \"iqiyi,youku,qq,letv,mangguo,bilibili\",\n" +
                "\t\t\"posterUrl\": \"http://iot-long-video-original-hn2.s3v2.dg-access-test.wanyol.com/yunhe/yunhe_poster_8681174.jpg\",\n" +
                "\t\t\"releaseTime\": \"20250508\",\n" +
                "\t\t\"director\": \"董宏杰\",\n" +
                "\t\t\"actor\": \"张子枫,王景春,塔塔,张子贤,王骁,张佳宁,涓子,李晓川,杨恩又,陆思宇\",\n" +
                "\t\t\"area\": \"中国\",\n" +
                "\t\t\"brief\": null,\n" +
                "\t\t\"tag\": \"剧情\",\n" +
                "\t\t\"rating\": 6,\n" +
                "\t\t\"episodeNum\": null\n" +
                "\t}\n" +
                "]",  new TypeReference<List<YunheRank>>() {});
    }
}
