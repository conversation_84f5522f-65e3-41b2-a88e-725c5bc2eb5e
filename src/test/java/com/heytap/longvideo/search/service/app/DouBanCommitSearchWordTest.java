package com.heytap.longvideo.search.service.app;

import com.heytap.longvideo.client.arrange.search.entity.LvSearchFeedback;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.TopSearchWordRpcApiProxy;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
@ExtendWith(MockitoExtension.class)
public class DouBanCommitSearchWordTest {

    @Mock
    private SearchFeedbackService searchFeedbackService;

    @Mock
    private SearchProperties searchConfig;

    @Mock
    private DouBanCommitTaskService douBanCommitTaskService;

    @Mock
    private TopSearchWordRpcApiProxy searchWordRpcApiProxy;


    @InjectMocks
    private DouBanCommitTaskService douBanCommitTaskService1;
    @Mock
    private HttpDataChannel httpDataChannel;

    @BeforeEach
    void setUp() throws HttpDataChannelException {
        when(httpDataChannel.postForObjectWithGenerics(argThat(url -> url != null && url.contains("/searchNameForDouBanDelayExecute")),
                any(), eq(String.class),  anyInt()))
                .thenReturn("{}");
    }
    @Test
    public void testUrl() {
        String url = douBanCommitTaskService1.getUrl("/video/douban/crawler/searchNameForDouBanDelayExecute");
        Assert.assertTrue(url.contains("u-s303-name"));
        Assert.assertTrue(url.contains("pw-2001"));
    }


    private List<LvSearchFeedback> listByCreateTimeAndLimitNumResp() {
        List<LvSearchFeedback> list = new ArrayList<>();
        LvSearchFeedback lvSearchFeedback = new LvSearchFeedback();
        lvSearchFeedback.setCreateTime(new Date());
        lvSearchFeedback.setKeyword("keyword1");
        lvSearchFeedback.setUpdateTime(new Date());
        lvSearchFeedback.setType(1);

        LvSearchFeedback lvSearchFeedback1 = new LvSearchFeedback();
        lvSearchFeedback1.setCreateTime(new Date());
        lvSearchFeedback1.setKeyword("keyword2");
        lvSearchFeedback1.setUpdateTime(new Date());
        lvSearchFeedback1.setType(1);

        LvSearchFeedback lvSearchFeedback2 = new LvSearchFeedback();
        lvSearchFeedback2.setCreateTime(new Date());
        lvSearchFeedback2.setKeyword("keyword2");
        lvSearchFeedback2.setUpdateTime(new Date());
        lvSearchFeedback2.setType(1);

        list.add(lvSearchFeedback);
        list.add(lvSearchFeedback1);
        list.add(lvSearchFeedback2);
        return list;
    }

}
