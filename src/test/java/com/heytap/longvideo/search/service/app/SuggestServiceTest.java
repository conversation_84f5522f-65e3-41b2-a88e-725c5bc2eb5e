package com.heytap.longvideo.search.service.app;

import com.google.common.collect.Lists;
import com.heytap.longvideo.search.liteflow.cmp.SearchAlbumCmp;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParam;
import com.heytap.longvideo.search.model.param.app.SuggestionResponse;
import com.heytap.longvideo.search.service.search.SearchAlbumService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.search.suggest.Suggest;
import org.elasticsearch.search.suggest.term.TermSuggestion;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SuggestServiceTest {
    @InjectMocks
    SuggestService suggestService;

    @Mock
    private ElasticsearchRestTemplate restTemplate;
    @Mock
    private YoukuSourceFilterService youkuSourceFilterService;
    @Mock
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;
    @Mock
    private SearchAlbumCmp searchAlbumCmp;

    @Mock
    private SearchAlbumService searchAlbumService;

    @Before
    public void build() {
        SearchResponse searchResponse = mock(SearchResponse.class);
        Suggest suggest = mock(Suggest.class);
        TermSuggestion suggestion = mock(TermSuggestion.class);
        TermSuggestion.Entry entry = mock(TermSuggestion.Entry.class);
        TermSuggestion.Entry.Option option = mock(TermSuggestion.Entry.Option.class);
        when(option.getText()).thenReturn(new Text("12345"));
        when(option.getScore()).thenReturn(160f);
        when(entry.getOptions()).thenReturn(Lists.newArrayList(option));
        doReturn(Arrays.asList(entry)).when(suggestion).getEntries();
        when(suggest.getSuggestion(any())).thenReturn(suggestion);
        when(searchResponse.getSuggest()).thenReturn(suggest);
        when(restTemplate.suggest(any(), any())).thenReturn(searchResponse);
    }

    @Test
    public void suggestTest_longKeyword() {
        KeyWordSearchParam param = new KeyWordSearchParam();
        param.setKeyword("123456789012345678901");
        List<SuggestionResponse> result = suggestService.suggest(param);
        assertEquals(0, result.size());
    }

    @Test
    public void suggestTest_numberLessThan5() {
        KeyWordSearchParam param = new KeyWordSearchParam();
        param.setKeyword("12345");

        List<SuggestionResponse> result = suggestService.suggest(param);
        assertEquals(1, result.size());
    }

    @Test
    public void suggestTest_numberMoreThan5() {
        KeyWordSearchParam param = new KeyWordSearchParam();
        param.setKeyword("123456");

        List<SuggestionResponse> result = suggestService.suggest(param);
        assertEquals(1, result.size());
    }


    @Test
    public void suggestTest_lenthIs1() {
        KeyWordSearchParam param = new KeyWordSearchParam();
        param.setKeyword("一");

        when(funshionLongVideoAndWeidiouFilterService.filterItem(any())).thenReturn(true);
        when(youkuSourceFilterService.filterItem(any())).thenReturn(true);

        // 1. 创建模拟的 ProgramAlbumEs 对象
        ProgramAlbumEs album1 = new ProgramAlbumEs();
        album1.setSid("1");
        ProgramAlbumEs album2 = new ProgramAlbumEs();
        album2.setSid("2");

        // 2. 创建模拟的 SearchHit 对象
        SearchHit<ProgramAlbumEs> hit1 = mock(SearchHit.class);
        when(hit1.getScore()).thenReturn(1.5f);
        when(hit1.getContent()).thenReturn(album1);

        SearchHit<ProgramAlbumEs> hit2 = mock(SearchHit.class);
        when(hit2.getScore()).thenReturn(2.0f);
        when(hit2.getContent()).thenReturn(album2);

        // 3. 创建模拟的 SearchHits 对象
        SearchHits<ProgramAlbumEs> searchHits = mock(SearchHits.class);
        when(searchHits.iterator()).thenReturn(Arrays.asList(hit1, hit2).iterator());

        doReturn(searchHits).when(restTemplate).search(any(NativeSearchQuery.class), any());

        when(searchAlbumService.filterByMatch(any(), any())).thenReturn(Arrays.asList(album1, album2));
        when(searchAlbumService.keywordSearchSort(any(), any())).thenReturn(Arrays.asList(album1, album2));

        List<SuggestionResponse> result = suggestService.suggest(param);
        assertEquals(2, result.size());
    }


    @Test
    public void suggestTest_lenthIs3() {
        KeyWordSearchParam param = new KeyWordSearchParam();
        param.setKeyword("一二三");

        when(funshionLongVideoAndWeidiouFilterService.filterItem(any())).thenReturn(true);
        when(youkuSourceFilterService.filterItem(any())).thenReturn(true);

        // 1. 创建模拟的 ProgramAlbumEs 对象
        ProgramAlbumEs album1 = new ProgramAlbumEs();
        album1.setSid("1");
        ProgramAlbumEs album2 = new ProgramAlbumEs();
        album2.setSid("2");

        // 2. 创建模拟的 SearchHit 对象
        SearchHit<ProgramAlbumEs> hit1 = mock(SearchHit.class);
        when(hit1.getScore()).thenReturn(1.5f);
        when(hit1.getContent()).thenReturn(album1);

        SearchHit<ProgramAlbumEs> hit2 = mock(SearchHit.class);
        when(hit2.getScore()).thenReturn(2.0f);
        when(hit2.getContent()).thenReturn(album2);

        // 3. 创建模拟的 SearchHits 对象
        SearchHits<ProgramAlbumEs> searchHits = mock(SearchHits.class);
        when(searchHits.iterator()).thenReturn(Arrays.asList(hit1, hit2).iterator());

        doReturn(searchHits).when(restTemplate).search(any(NativeSearchQuery.class), any());

        when(searchAlbumService.filterByMatch(any(), any())).thenReturn(Arrays.asList(album1, album2));
        when(searchAlbumService.keywordSearchSort(any(), any())).thenReturn(Arrays.asList(album1, album2));

        List<SuggestionResponse> result = suggestService.suggest(param);
        assertEquals(2, result.size());
    }
}
