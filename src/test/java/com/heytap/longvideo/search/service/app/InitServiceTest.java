package com.heytap.longvideo.search.service.app;

import com.google.common.collect.Sets;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.mapper.media.VirtualProgramMapper;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.properties.HotVideoProperties;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.LvContentPoolRpcApiProxy;
import com.heytap.longvideo.search.service.common.ActorService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.common.SeriesAlbumService;
import com.heytap.longvideo.search.service.common.VirtualProgramService;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.cpc.video.framework.lib.jins.RowData;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.test.util.ReflectionTestUtils;
import redis.clients.jedis.JedisCluster;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class InitServiceTest {
    @InjectMocks
    private InitService initService;
    @Mock
    private VirtualProgramService virtualProgramService;
    @Mock
    private ElasticSearchService elasticsearchService;
    @Mock
    private ElasticsearchRestTemplate restTemplate;
    @Mock
    private LvContentPoolRpcApiProxy lvContentPoolRpcApiProxy;
    @Mock
    private SearchProperties searchConfig;
    @Mock
    private HotVideoProperties hotVideoProperties;
    @Mock
    private SeriesAlbumService seriesAlbumService;
    @Mock
    private HotVideoService hotVideoService;
    @Mock
    private VirtualProgramMapper virtualProgramMapper;
    @Mock
    private ActorService actorService;
    @Mock
    private StandardAlbumMapper standardAlbumMapper;
    @Mock
    private JedisCluster jedisCluster;

    @Before
    public void build() throws NoSuchFieldException, IllegalAccessException {
        when(searchConfig.getFilterPriority()).thenReturn(Lists.newArrayList("sohu"));

        ReflectionTestUtils.setField(initService, "datebaseEnd", 7);
        ReflectionTestUtils.setField(initService, "tableEnd", 15);
    }

    @Test
    public void initDataTest() {
        ProgramAlbumEs es = JacksonUtil.parseObject(getEs(), ProgramAlbumEs.class);
        when(virtualProgramService.createCacheAndReturnAllAlbum()).thenReturn(Lists.newArrayList(es));

        when(hotVideoProperties.getMovieHotTitle()).thenReturn(Sets.newHashSet());
        Map<String, String> seriesMap = new HashMap<>();
        seriesMap.put("末代皇帝", "末代皇帝系列");
        when(seriesAlbumService.getCache()).thenReturn(seriesMap);

        when(lvContentPoolRpcApiProxy.getMinorsPoolCode(any())).thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

        Map<String, ProgramAlbumEs> subMap = new HashMap<>();
        subMap.put("sohu", es);
        Map<String, Map<String, ProgramAlbumEs>> map = new HashMap<>();
        map.put("virtualId1", subMap);
        when(virtualProgramService.getAllCache()).thenReturn(map);

        IndexOperations operations = Mockito.mock(IndexOperations.class);
        when(restTemplate.indexOps(any(Class.class))).thenReturn(operations);

        initService.initData();

        verify(lvContentPoolRpcApiProxy, times(1)).getMinorsPoolCode(any());
    }

    @Test
    public void updateTest_notAllowSource() {
        StandardAlbum standardAlbum = new StandardAlbum();
        RowData rowData = new RowData();
        standardAlbum.setSource("ban");
        initService.update(standardAlbum, rowData);

        verify(virtualProgramMapper, times(0)).selectVirtualProgramBySid(any());
    }

    @Test
    public void updateTest_update() {
        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setStatus(1);
        standardAlbum.setSource("sohu");
        standardAlbum.setActor("a|b");
        standardAlbum.setDirector("c|d");
        standardAlbum.setSid("123");
        standardAlbum.setFeatureType(1);
        standardAlbum.setDoubanScore("9.8");
        standardAlbum.setOppoScore("9.9");

        RowData rowData = new RowData();
        rowData.setEventType("UPDATE");
        Map<String, Object> before = new HashMap<>();
        before.put("status", 1);
        rowData.setBefore(before);

        MisVirtualProgramRelation misVirtualProgramRelation = new MisVirtualProgramRelation();
        misVirtualProgramRelation.setSid("123");
        misVirtualProgramRelation.setVirtualSid("321");
        when(virtualProgramMapper.selectVirtualProgramBySid(any())).thenReturn(misVirtualProgramRelation);

        when(virtualProgramMapper.selectVirtualProgramByVirtualSid(anyString(), anyList()))
                .thenReturn(Lists.newArrayList(misVirtualProgramRelation));

        when(actorService.getActorProgramCount(any())).thenReturn(1L);

        StandardAlbumEs standardAlbumEs = JacksonUtil.parseObject(getEs(), StandardAlbumEs.class);
        when(standardAlbumMapper.selectStandardAlbumBySid(anyInt(), anyInt(), anyString(), anyInt()))
                .thenReturn(standardAlbumEs);

        when(jedisCluster.hget(anyString(), anyString())).thenReturn("{}");

        ProgramAlbumEs album1 = new ProgramAlbumEs();
        album1.setSid("1");
        ProgramAlbumEs album2 = new ProgramAlbumEs();
        album2.setSid("2");

        SearchHit<ProgramAlbumEs> hit1 = mock(SearchHit.class);
        when(hit1.getContent()).thenReturn(album1);
        SearchHits<ProgramAlbumEs> searchHits = mock(SearchHits.class);
        when(searchHits.iterator()).thenReturn(Arrays.asList(hit1).iterator());

        doReturn(searchHits).when(restTemplate).search(any(NativeSearchQuery.class), any());

        initService.update(standardAlbum, rowData);

        verify(restTemplate, atLeastOnce()).save(ArgumentMatchers.<ProgramAlbumEs>any());

    }

    private String getEs() {
        return "    {\n" +
                "            \"actor\": \"尊龙|陈冲|邬君梅|彼得·奥图尔|英若诚|吴涛|黄自强|丹尼斯·邓|坂本龙一|马吉·汉|里克·扬|田川洋行|苟杰德|理查德·吴|皱缇格|陈凯歌|卢燕|蒋锡礽|陈述|鲍皓昕|黄文捷|邵茹贞|亨利·基|张良斌|梁冬|康斯坦丁·格雷戈里|黄汉琪|王涛|宋怀桂|蔡鸿翔|程淑艳|张天民\",\n" +
                "            \"area\": \"英国 / 意大利 / 中国大陆 / 法国\",\n" +
                "            \"copyright\": 1,\n" +
                "            \"copyrightCode\": \"tencent\",\n" +
                "            \"contentType\": \"movie\",\n" +
                "            \"createTime\": 1731552190236,\n" +
                "            \"director\": \"贝纳尔多·贝托鲁奇\",\n" +
                "            \"duration\": 163,\n" +
                "            \"featureType\": 1,\n" +
                "            \"horizontalIcon\": \"https://img3.sohuio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                "            \"horizontalImage\": \"https://img3.sohuio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                "            \"information\": \"溥仪（尊龙 饰）的一生在电影中娓娓道来。他从三岁起登基，年幼的眼光中只有大臣身上的一只蝈蝈，江山在他心中只是一个不明所以的名词。长大了，他以为可以变革，却被太监一把火烧了朝廷账本。他以为自己是大清江山的主人，却做了日本人的傀儡。解放后，他坐上了从苏联回来的火车，身边是押送监视他的解放军。他猜测自己难逃一死，便躲在狭小的卫生间里，割脉自杀。然而他没有死在火车上，命运的嘲笑还在等着他。文革的风风雨雨，在他身上留下了斑斑伤痕。\",\n" +
                "            \"language\": \"英语 / 汉语普通话 / 日语\",\n" +
                "            \"managerStatus\": 1,\n" +
                "            \"payStatus\": 1,\n" +
                "            \"programType\": \"movie\",\n" +
                "            \"sid\": \"1074228711114592256\",\n" +
                "            \"source\": \"sohu\",\n" +
                "            \"sourceAlbumId\": \"db_1293172\",\n" +
                "            \"sourceScore\": \"9.3\",\n" +
                "            \"sourceStatus\": 1,\n" +
                "            \"sourceType\": 0,\n" +
                "            \"sourceWebUrl\": \"https://v.qq.com/x/cover/29trop8s2ipr3es.html?ptag=newsohu.movie&subtype=1&type=online-video\",\n" +
                "            \"status\": 1,\n" +
                "            \"tags\": \"剧情|传记|历史\",\n" +
                "            \"title\": \"末代皇帝\",\n" +
                "            \"updateTime\": 1731552190236,\n" +
                "            \"verticalIcon\": \"https://img3.sohuio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                "            \"verticalImage\": \"https://img3.sohuio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                "            \"year\": 1987\n" +
                "        }";
    }
}
