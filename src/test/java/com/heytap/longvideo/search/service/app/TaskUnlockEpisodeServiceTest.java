package com.heytap.longvideo.search.service.app;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.entity.MtvImageTag;
import com.heytap.longvideo.common.lib.constants.ActivityTaskUnlockEpisodeConstant;
import com.heytap.longvideo.common.lib.rpc.ActivityRpcApi;
import com.heytap.longvideo.common.lib.rpc.RpcResult;
import com.heytap.longvideo.common.lib.rpc.resp.activity.UnlockEpisodeInfo;
import com.heytap.longvideo.common.lib.rpc.resp.activity.UnlockEpisodeInfoList;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.service.common.StrategyService;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.apache.commons.lang.CharEncoding.UTF_8;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class TaskUnlockEpisodeServiceTest {
    @InjectMocks
    TaskUnlockEpisodeService taskUnlockEpisodeService;

    @Mock
    private SearchProperties searchProperties;
    @Mock
    private StrategyService strategyService;
    @Mock
    private ActivityRpcApi activityRpcApi;

    @Mock
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Before
    public void build() {
        when(searchProperties.getSearchCardOptVersion()).thenReturn(80900);
    }

    @Test
    public void handleMarkCode4InterveneCardTest_lowVersion() {
        InterveneCardParam req = new InterveneCardParam();
        req.setVersion(80800);
        SearchInterveneCardResponse interveneCardResponse = buildInterveneCardResponse();

        taskUnlockEpisodeService.handleMarkCode4InterveneCard(req, interveneCardResponse);

        assertNull(interveneCardResponse.getContents().get(0).getMarkCodeUrl());
    }

    @Test
    public void handleMarkCode4InterveneCardTest_emptyContents() {
        InterveneCardParam req = new InterveneCardParam();
        req.setVersion(80900);
        SearchInterveneCardResponse interveneCardResponse = buildInterveneCardResponse();
        interveneCardResponse.setContents(null);

        taskUnlockEpisodeService.handleMarkCode4InterveneCard(req, interveneCardResponse);

        assertNull(interveneCardResponse.getContents());
    }

    @Test
    public void handleMarkCode4InterveneCardTest_emptyStrategy() {
        InterveneCardParam req = mock(InterveneCardParam.class);
        when(req.getVersion()).thenReturn(80900);
        when(req.getScookieIgnoreException()).thenReturn(null);
        SearchInterveneCardResponse interveneCardResponse = buildInterveneCardResponse();

        when(strategyService.matchSearchStrategy(any(), any())).thenReturn(CompletableFuture.completedFuture(Collections.emptyMap()));

        taskUnlockEpisodeService.handleMarkCode4InterveneCard(req, interveneCardResponse);

        assertNull(interveneCardResponse.getContents().get(0).getMarkCodeUrl());
    }

    @Test
    public void handleMarkCode4InterveneCardTest_activityReturnNull() {
        InterveneCardParam req = mock(InterveneCardParam.class);
        when(req.getVersion()).thenReturn(80900);
        when(req.getScookieIgnoreException()).thenReturn(null);
        req.setVersion(80900);
        SearchInterveneCardResponse interveneCardResponse = buildInterveneCardResponse();

        Map<String, MatchStrategyResponseItem> strategyMap = new HashMap<>();
        MatchStrategyResponseItem strategyResponseItem = new MatchStrategyResponseItem();
        strategyResponseItem.setAttachment("{\"activityId\":\"123\", \"searchPageOpen\": true}");
        strategyMap.put(ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE, strategyResponseItem);
        when(strategyService.matchSearchStrategy(any(), any())).thenReturn(CompletableFuture.completedFuture(strategyMap));

        when(activityRpcApi.getUnlockEpisodeMarkCode(any())).thenReturn(CompletableFuture.completedFuture(null));

        taskUnlockEpisodeService.handleMarkCode4InterveneCard(req, interveneCardResponse);

        assertNull(interveneCardResponse.getContents().get(0).getMarkCodeUrl());
    }

    @Test
    public void handleMarkCode4InterveneCardTest_activityReturnEmpty() {
        InterveneCardParam req = mock(InterveneCardParam.class);
        when(req.getVersion()).thenReturn(80900);
        when(req.getScookieIgnoreException()).thenReturn(null);
        SearchInterveneCardResponse interveneCardResponse = buildInterveneCardResponse();

        Map<String, MatchStrategyResponseItem> strategyMap = new HashMap<>();
        MatchStrategyResponseItem strategyResponseItem = new MatchStrategyResponseItem();
        strategyResponseItem.setAttachment("{\"activityId\":\"123\", \"searchPageOpen\": true}");
        strategyMap.put(ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE, strategyResponseItem);
        when(strategyService.matchSearchStrategy(any(), any())).thenReturn(CompletableFuture.completedFuture(strategyMap));

        when(activityRpcApi.getUnlockEpisodeMarkCode(any())).thenReturn(CompletableFuture.completedFuture(new RpcResult<>(new UnlockEpisodeInfoList())));

        taskUnlockEpisodeService.handleMarkCode4InterveneCard(req, interveneCardResponse);

        assertNull(interveneCardResponse.getContents().get(0).getMarkCodeUrl());
    }


    @Test
    public void handleMarkCode4InterveneCardTest() {
        InterveneCardParam req = mock(InterveneCardParam.class);
        when(req.getVersion()).thenReturn(80900);
        when(req.getScookieIgnoreException()).thenReturn(null);
        when(req.getSortType()).thenReturn(SortEnum.FREE.getType());
        AttributeValues attributeValues = new AttributeValues();
        attributeValues.setFullBrowserVersion("8.9.0");
        when(req.getAttributeValues()).thenReturn(attributeValues);
        SearchInterveneCardResponse interveneCardResponse = buildInterveneCardResponse();

        Map<String, MatchStrategyResponseItem> strategyMap = new HashMap<>();
        MatchStrategyResponseItem strategyResponseItem = new MatchStrategyResponseItem();
        strategyResponseItem.setAttachment("{\"activityId\":\"123\", \"searchPageOpen\": true}");
        strategyMap.put(ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE, strategyResponseItem);
        when(strategyService.matchSearchStrategy(any(), any())).thenReturn(CompletableFuture.completedFuture(strategyMap));

        UnlockEpisodeInfoList unlockEpisodeInfoList = new UnlockEpisodeInfoList();
        UnlockEpisodeInfo unlockEpisodeInfo = new UnlockEpisodeInfo();
        unlockEpisodeInfo.setSid("1001");
        unlockEpisodeInfo.setMarkCodeUrl("免费解锁图片");
        unlockEpisodeInfoList.setTaskUnlockEpisodes(Lists.newArrayList(unlockEpisodeInfo));
        when(activityRpcApi.getUnlockEpisodeMarkCode(any())).thenReturn(CompletableFuture.completedFuture(new RpcResult<>(unlockEpisodeInfoList)));
        when(imageTagRpcApiProxy.getImageUrl(any(), any())).thenReturn("免费解锁图片2");

        taskUnlockEpisodeService.handleMarkCode4InterveneCard(req, interveneCardResponse);

        assertEquals("免费解锁图片2", interveneCardResponse.getContents().get(1).getMarkCodeUrl());
    }

    @Test
    public void handleMarkCode4SearchPageTest_lowVersion() {
        KeyWordSearchParamV2 req = new KeyWordSearchParamV2();
        req.setVersion(80800);
        SearchResponse searchResponse = buildSearchResponse();

        taskUnlockEpisodeService.handleMarkCode4SearchPage(req, searchResponse);

        assertNull(searchResponse.getLongVideoSearchResult().get(0).getMarkCodeUrl());
    }


    @Test
    public void handleMarkCode4SearchPageTest_emptyContents() {
        KeyWordSearchParamV2 req = new KeyWordSearchParamV2();
        req.setVersion(80900);
        req.setAttributeValues(new AttributeValues());
        SearchResponse searchResponse = buildSearchResponse();
        searchResponse.setLongVideoSearchResult(null);

        taskUnlockEpisodeService.handleMarkCode4SearchPage(req, searchResponse);

        assertNull(searchResponse.getLongVideoSearchResult());
    }

    @Test
    public void handleMarkCode4SearchPageCardTest() {
        KeyWordSearchParamV2 req = mock(KeyWordSearchParamV2.class);
        SearchResponse searchResponse = buildSearchResponse();
        when(req.getVersion()).thenReturn(80900);
        when(req.getAttributeValues()).thenReturn(new AttributeValues());
        when(req.getScookieIgnoreException()).thenReturn(null);

        Map<String, MatchStrategyResponseItem> strategyMap = new HashMap<>();
        MatchStrategyResponseItem strategyResponseItem = new MatchStrategyResponseItem();
        strategyResponseItem.setAttachment("{\"activityId\":\"123\", \"searchPageOpen\": true}");
        strategyMap.put(ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE, strategyResponseItem);
        when(strategyService.matchSearchStrategy(any(), any())).thenReturn(CompletableFuture.completedFuture(strategyMap));

        UnlockEpisodeInfoList unlockEpisodeInfoList = new UnlockEpisodeInfoList();
        UnlockEpisodeInfo unlockEpisodeInfo = new UnlockEpisodeInfo();
        unlockEpisodeInfo.setSid("1001");
        unlockEpisodeInfo.setMarkCodeUrl("免费解锁图片");
        unlockEpisodeInfoList.setTaskUnlockEpisodes(Lists.newArrayList(unlockEpisodeInfo));
        when(activityRpcApi.getUnlockEpisodeMarkCode(any())).thenReturn(CompletableFuture.completedFuture(new RpcResult<>(unlockEpisodeInfoList)));
        when(imageTagRpcApiProxy.getImageUrl(any(), any())).thenReturn("免费解锁图片2");

        taskUnlockEpisodeService.handleMarkCode4SearchPage(req, searchResponse);

        assertEquals("免费解锁图片2", searchResponse.getLongVideoSearchResult().get(0).getMarkCodeUrl());
    }


    private SearchInterveneCardResponse buildInterveneCardResponse() {
        SearchInterveneCardResponse response = new SearchInterveneCardResponse();
        KeyWordSearchResponse content1 = new KeyWordSearchResponse();
        content1.setSid("1001");
        content1.setPayStatus(1);
        KeyWordSearchResponse content2 = new KeyWordSearchResponse();
        content2.setSid("1002");
        content2.setPayStatus(0);
        KeyWordSearchResponse content3 = new KeyWordSearchResponse();
        content3.setSid("1003");
        content3.setPayStatus(1);
        response.setContents(Lists.newArrayList(content1, content2, content3));
        return response;
    }

    private SearchResponse buildSearchResponse() {
        SearchResponse response = new SearchResponse();
        KeyWordSearchResponse content1 = new KeyWordSearchResponse();
        content1.setSid("1001");
        content1.setPayStatus(1);
        KeyWordSearchResponse content2 = new KeyWordSearchResponse();
        content2.setSid("1002");
        content2.setPayStatus(0);
        KeyWordSearchResponse content3 = new KeyWordSearchResponse();
        content3.setSid("1003");
        content3.setPayStatus(1);
        response.setLongVideoSearchResult(Lists.newArrayList(content1, content2, content3));

        return response;
    }
}
