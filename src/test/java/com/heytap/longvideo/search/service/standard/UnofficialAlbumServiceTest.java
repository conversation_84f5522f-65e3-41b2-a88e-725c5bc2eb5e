package com.heytap.longvideo.search.service.standard;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.media.entity.UnofficialAlbum;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.model.unofficial.request.SearchUnofficialAlbumRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.lucene.search.TotalHits;
import org.assertj.core.util.Lists;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SearchHits.class, SearchHit.class, TotalHits.class, RestHighLevelClient.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class UnofficialAlbumServiceTest {
    @InjectMocks
    UnofficialAlbumService unofficialAlbumService;

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Test
    public void searchBySidTest_null() {
        UnofficialAlbumEs result = unofficialAlbumService.searchBySid("", UnofficialAlbumEs.class);
        Assert.assertNull(result);
    }

    @Test
    public void searchBySidTest_throw() throws IOException {
        PowerMockito.when(restHighLevelClient.get(any(GetRequest.class), any(RequestOptions.class)))
                .thenThrow(new RuntimeException());
        UnofficialAlbumEs result = unofficialAlbumService.searchBySid("1", UnofficialAlbumEs.class);
        Assert.assertNull(result);
    }

    @Test
    public void searchBySidTest() throws IOException {
        GetResponse response = mock(GetResponse.class);
        PowerMockito.when(response.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(restHighLevelClient.get(any(GetRequest.class), any(RequestOptions.class))).thenReturn(response);

        UnofficialAlbumEs result = unofficialAlbumService.searchBySid("1", UnofficialAlbumEs.class);
        Assert.assertEquals("1074228711114592256", result.getSid());
    }

    @Test
    public void searchBySourceAlbumIdTest_null() {
        UnofficialAlbumEs result = unofficialAlbumService.searchBySourceAlbumId("");
        Assert.assertNull(result);
    }


    @Test
    public void searchBySourceAlbumIdTest_throw() throws IOException {
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenThrow(new RuntimeException());
        UnofficialAlbumEs result = unofficialAlbumService.searchBySourceAlbumId("1");
        Assert.assertNull(result);
    }


    @Test
    public void searchBySourceAlbumIdTest_empty() throws IOException {
        SearchResponse response = mock(SearchResponse.class);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit[] hitArray = {};

        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenReturn(response);

        UnofficialAlbumEs result = unofficialAlbumService.searchBySourceAlbumId("1");
        Assert.assertNull(result);
    }

    @Test
    public void searchBySourceAlbumIdTest() throws IOException {
        SearchResponse response = mock(SearchResponse.class);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};

        PowerMockito.when(hit.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenReturn(response);

        UnofficialAlbumEs result = unofficialAlbumService.searchBySourceAlbumId("1");
        Assert.assertEquals("1074228711114592256", result.getSid());
    }

    @Test
    public void getAlbumListTest_empty() {
        List<UnofficialAlbumEs> result = unofficialAlbumService.getAlbumList(Lists.emptyList());
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getAlbumListTest_throw() throws IOException {
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenThrow(new RuntimeException());
        List<UnofficialAlbumEs> result = unofficialAlbumService.getAlbumList(Lists.newArrayList("1"));
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getAlbumListTest() throws IOException {
        SearchResponse response = mock(SearchResponse.class);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};

        PowerMockito.when(hit.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenReturn(response);
        List<UnofficialAlbumEs> result = unofficialAlbumService.getAlbumList(Lists.newArrayList("1"));
        Assert.assertEquals("1074228711114592256", result.get(0).getSid());
    }

    @Test
    public void searchUnofficialAlbumTest1() throws Exception {
        SearchStandardAlbumParams params = JSON.parseObject(
                "{\n" +
                        "  \"title\": \"1\",\n" +
                        "  \"searchSource\": \"1\",\n" +
                        "  \"programType\": \"movie\",\n" +
                        "  \"sortStrategy\": 1,\n" +
                        "  \"programStatus\": \"1\",\n" +
                        "  \"sourceStatus\": \"1\",\n" +
                        "  \"managerStatus\": \"0\",\n" +
                        "  \"featureType\": \"1\",\n" +
                        "  \"source\": \"douban\",\n" +
                        "  \"area\": \"3\",\n" +
                        "  \"director\": \"6\",\n" +
                        "  \"actor\": \"7\",\n" +
                        "  \"payStatus\": \"\",\n" +
                        "  \"yearStart\": \"2000\",\n" +
                        "  \"yearEnd\": \"2010\",\n" +
                        "  \"ageStart\": \"18\",\n" +
                        "  \"ageEnd\": \"80\",\n" +
                        "  \"supplyType\": \"\",\n" +
                        "  \"copyrightCode\": \"mgtv\",\n" +
                        "  \"sid\": \"2\",\n" +
                        "  \"tags\": \"4\",\n" +
                        "  \"year\": \"5\",\n" +
                        "  \"pageIndex\": \"1\",\n" +
                        "  \"pageSize\": \"20\"\n" +
                        "}",
                SearchStandardAlbumParams.class);
        SearchResponse response = mock(SearchResponse.class);

        TotalHits totalHits = PowerMockito.mock(TotalHits.class);
        // mock final对象的属性
        PowerMockito.field(TotalHits.class, "value").set(totalHits, 1);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};

        PowerMockito.when(hit.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(hits.getTotalHits()).thenReturn(totalHits);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(response);

        PageResponse<StandardAlbumVo> result = unofficialAlbumService.searchUnofficialAlbum(params);
        Assert.assertEquals(1, result.getTotalCount());
    }

    @Test(expected = Exception.class)
    public void searchUnofficialAlbumTest_throw() throws Exception {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setOrder("score");
        params.setSortStrategy(2);

        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenThrow(new RuntimeException());

        PageResponse<StandardAlbumVo> result = unofficialAlbumService.searchUnofficialAlbum(params);
    }

    @Test(expected = Exception.class)
    public void searchUnofficialAlbumTest_throw2() throws Exception {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setOrder("a_b");
        params.setSortStrategy(3);

        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenThrow(new RuntimeException());

        PageResponse<StandardAlbumVo> result = unofficialAlbumService.searchUnofficialAlbum(params);
    }

    @Test
    public void searchUnofficialAlbumTest2() throws Exception {
        SearchUnofficialAlbumRequest request = new SearchUnofficialAlbumRequest();
        request.setTitle("末代皇帝");
        request.setSource("douban");

        SearchResponse response = mock(SearchResponse.class);
        TotalHits totalHits = PowerMockito.mock(TotalHits.class);
        // mock final对象的属性
        PowerMockito.field(TotalHits.class, "value").set(totalHits, 2);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit1 = PowerMockito.mock(SearchHit.class);
        SearchHit hit2 = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit1, hit2};

        PowerMockito.when(hit1.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(hit2.getSourceAsString()).thenReturn(buildEs());

        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(hits.getTotalHits()).thenReturn(totalHits);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(response);
        List<UnofficialAlbum> similarAlbum = unofficialAlbumService.getSimilarAlbum(request);
        Assert.assertNotNull(similarAlbum);
        Assert.assertTrue(similarAlbum.size() > 0);
    }

    @Test
    public void getSimilarAlbum_throw() throws IOException {
        SearchUnofficialAlbumRequest request = new SearchUnofficialAlbumRequest();
        request.setTitle("末代皇帝");
        request.setSource("douban");

        when(restHighLevelClient.search(any(), any())).thenThrow(new RuntimeException());
        List<UnofficialAlbum> similarAlbum = unofficialAlbumService.getSimilarAlbum(request);
        Assert.assertEquals(0, similarAlbum.size());
    }

    @Test
    public void getSimilarAlbum_empty() throws IOException {
        SearchUnofficialAlbumRequest request = new SearchUnofficialAlbumRequest();
        request.setTitle("末代皇帝");
        request.setSource("douban");

        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit[] hitArray = {};
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(searchResponse.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenReturn(searchResponse);

        List<UnofficialAlbum> similarAlbum = unofficialAlbumService.getSimilarAlbum(request);
        Assert.assertEquals(0, similarAlbum.size());
    }

    @Test
    public void getAlbumQueryBuilderTest_1() {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setSourceScoreStart(10.0f);
        params.setSourceScoreEnd(10.0f);
        params.setUseHighLight("1");
        params.setManagerStatus("3");
        params.setStatus("0");
        params.setFeatureType("15");

        QueryBuilder result = unofficialAlbumService.getAlbumQueryBuilder(params);

        Assert.assertNotNull(result);
    }


    @Test
    public void getAlbumQueryBuilderTest_2() {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setSourceScoreStart(5.0f);
        params.setSourceScoreEnd(9.0f);
        params.setUseHighLight("0");
        params.setManagerStatus("3");

        QueryBuilder result = unofficialAlbumService.getAlbumQueryBuilder(params);

        Assert.assertNotNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void saveOrUpdateTest() throws IOException {
        UnofficialAlbumEs es = new UnofficialAlbumEs();
        UpdateResponse mockResponse = new UpdateResponse(null);
        Mockito.when(restHighLevelClient.update(Mockito.any(), Mockito.any())).thenReturn(mockResponse);
        verify(restHighLevelClient, times(1)).update(any(), any());
    }

    @Test
    public void batchInsertOrUpdateTest_hasFailures() throws IOException {
        unofficialAlbumService.batchInsertOrUpdate(Lists.emptyList());

        BulkResponse response = mock(BulkResponse.class);
        when(response.hasFailures()).thenReturn(true);
        when(restHighLevelClient.bulk(any(), any())).thenReturn(response);

        UnofficialAlbumEs es = new UnofficialAlbumEs();
        unofficialAlbumService.batchInsertOrUpdate(Lists.newArrayList(es));

        verify(restHighLevelClient, times(5)).bulk(any(), any());
    }


    @Test
    public void batchInsertOrUpdateTest_throw() throws IOException {
        unofficialAlbumService.batchInsertOrUpdate(Lists.emptyList());

        BulkResponse response = mock(BulkResponse.class);
        when(response.hasFailures()).thenReturn(false);
        when(restHighLevelClient.bulk(any(), any())).thenReturn(response);

        UnofficialAlbumEs es = new UnofficialAlbumEs();
        unofficialAlbumService.batchInsertOrUpdate(Lists.newArrayList(es));

        verify(restHighLevelClient, times(5)).bulk(any(), any());
    }

    @Test
    public void syncDataFromTVTest() throws IOException {
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};

        PowerMockito.when(hit.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(searchResponse.getHits()).thenReturn(hits);
        PowerMockito.when(searchResponse.getScrollId()).thenReturn("1");
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenReturn(searchResponse);

        SearchHits hits2 = PowerMockito.mock(SearchHits.class);
        SearchResponse scrollResponse = mock(SearchResponse.class);
        SearchHit[] hitArray2 = {};
        PowerMockito.when(hits2.getHits()).thenReturn(hitArray2);
        PowerMockito.when(scrollResponse.getHits()).thenReturn(hits2);
        PowerMockito.when(restHighLevelClient.scroll(any(SearchScrollRequest.class), any(RequestOptions.class)))
                .thenReturn(scrollResponse);

        BulkResponse response = mock(BulkResponse.class);
        when(response.hasFailures()).thenReturn(true);
        when(restHighLevelClient.bulk(any(), any())).thenReturn(response);

        UnofficialAlbumEs es = new UnofficialAlbumEs();
        unofficialAlbumService.batchInsertOrUpdate(Lists.newArrayList(es));

        when(restHighLevelClient.clearScroll(any(),any()))
                .thenThrow(new RuntimeException());

        unofficialAlbumService.syncDataFromTV();
    }

    private String buildEs() {
        return "    {\n" +
                        "            \"actor\": \"尊龙|陈冲|邬君梅|彼得·奥图尔|英若诚|吴涛|黄自强|丹尼斯·邓|坂本龙一|马吉·汉|里克·扬|田川洋行|苟杰德|理查德·吴|皱缇格|陈凯歌|卢燕|蒋锡礽|陈述|鲍皓昕|黄文捷|邵茹贞|亨利·基|张良斌|梁冬|康斯坦丁·格雷戈里|黄汉琪|王涛|宋怀桂|蔡鸿翔|程淑艳|张天民\",\n" +
                        "            \"area\": \"英国 / 意大利 / 中国大陆 / 法国\",\n" +
                        "            \"copyright\": 1,\n" +
                        "            \"copyrightCode\": \"tencent\",\n" +
                        "            \"createTime\": 1731552190236,\n" +
                        "            \"director\": \"贝纳尔多·贝托鲁奇\",\n" +
                        "            \"duration\": 163,\n" +
                        "            \"featureType\": 1,\n" +
                        "            \"horizontalIcon\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"horizontalImage\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"information\": \"溥仪（尊龙 饰）的一生在电影中娓娓道来。他从三岁起登基，年幼的眼光中只有大臣身上的一只蝈蝈，江山在他心中只是一个不明所以的名词。长大了，他以为可以变革，却被太监一把火烧了朝廷账本。他以为自己是大清江山的主人，却做了日本人的傀儡。解放后，他坐上了从苏联回来的火车，身边是押送监视他的解放军。他猜测自己难逃一死，便躲在狭小的卫生间里，割脉自杀。然而他没有死在火车上，命运的嘲笑还在等着他。文革的风风雨雨，在他身上留下了斑斑伤痕。\",\n" +
                        "            \"language\": \"英语 / 汉语普通话 / 日语\",\n" +
                        "            \"managerStatus\": 1,\n" +
                        "            \"programType\": \"movie\",\n" +
                        "            \"sid\": \"1074228711114592256\",\n" +
                        "            \"source\": \"douban\",\n" +
                        "            \"sourceAlbumId\": \"db_1293172\",\n" +
                        "            \"sourceScore\": \"9.3\",\n" +
                        "            \"sourceStatus\": 1,\n" +
                        "            \"sourceType\": 0,\n" +
                        "            \"sourceWebUrl\": \"https://v.qq.com/x/cover/29trop8s2ipr3es.html?ptag=newdouban.movie&subtype=1&type=online-video\",\n" +
                        "            \"status\": 1,\n" +
                        "            \"tags\": \"剧情|传记|历史\",\n" +
                        "            \"title\": \"末代皇帝\",\n" +
                        "            \"updateTime\": 1731552190236,\n" +
                        "            \"verticalIcon\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"verticalImage\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"year\": 1987\n" +
                        "        }";
    }

}
