package com.heytap.longvideo.search.service.standard;

import com.heytap.cpc.dfoob.goblin.core.Goblin;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.media.entity.AlbumSearchResponse;
import com.heytap.longvideo.client.media.query.AlbumSearchParameterModifier;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.MockServerContainer;
import util.ArrangeRpcMockerUtil;
import util.ElasticSearchUtil;
import util.MediaRpcMockerUtil;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 2024/9/30 11:27
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@UsingDataSet
class AdjustApiSearchAlbumServiceTest extends GoblinJunit4BaseTest {

    @Autowired
    private AdjustApiSearchAlbumService adjustApiSearchAlbumService;
    @Autowired
    private ElasticsearchRestTemplate restTemplate;
    @Autowired
    private ElasticSearchService elasticsearchService;
    @Autowired
    private StandardAlbumMapper standardAlbumMapper;
    @Autowired
    private InitService initService;
    private static final String POOL_CODE = "minorsPoolCode";
    private static final String SID = "sid";
    private static final String LAST_POOL_CODE = "cp_00000491";

    @Test
    void adjustSingleApiSearchAlbum() throws Exception {

        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        ArrangeRpcMockerUtil.getMinorsPoolCode();

        try {
            ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, null);

            String sid = "506315702588755968";

            getSearchHits(sid);

        } catch (Exception e) {
            log.error("adjustSingleApiSearchAlbum error", e);
        } finally {
            ElasticSearchUtil.deleteAlbumEs(elasticsearchService);
        }
    }

    @Test
    void adjustApiSearchAlbum() throws Exception {
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        ArrangeRpcMockerUtil.getMinorsPoolCode();
        try {
            ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, null);

            String sid = "506319464187383808";

            AlbumSearchParameterModifier param = new AlbumSearchParameterModifier();
            param.setSidList(Stream.of(sid).collect(Collectors.toList()));
            param.setPoolCode(LAST_POOL_CODE);
            // param.setSid(sid);

            List<AlbumSearchResponse> responses = this.adjustApiSearchAlbumService.adjustApiSearchAlbum(param);
            Assert.assertTrue(responses.get(0).getPoolCode().contains(LAST_POOL_CODE));

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery(SID, sid));

            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            NativeSearchQuery searchQuery = queryBuilder
                    .withQuery(boolQuery)
                    .withFields(SID, POOL_CODE)
                    .withPageable(PageRequest.of(0, 1))
                    .build();

            sleep();
            SearchHits<ProgramAlbumEs> searchHits = this.restTemplate.search(searchQuery, ProgramAlbumEs.class);
            List<SearchHit<ProgramAlbumEs>> searchHitList = searchHits.getSearchHits();

            Assert.assertFalse(searchHitList.isEmpty());

            ProgramAlbumEs programAlbumEs = searchHitList.get(0).getContent();

            Assert.assertTrue(programAlbumEs.getMinorsPoolCode().contains(LAST_POOL_CODE));

        } catch (Exception e) {
            log.error("adjustApiSearchAlbum error", e);
            Assert.assertNull(e);
        } finally {
            ElasticSearchUtil.deleteAlbumEs(elasticsearchService);
        }
    }

    @Test
    void delApiSearchAlbumPoolCode() throws Exception {
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        ArrangeRpcMockerUtil.getMinorsPoolCode();

        try {
            ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, true, null);

            String sid = "506315702588755968";

            AlbumSearchParameterModifier param = new AlbumSearchParameterModifier();
            param.setSidList(Stream.of(sid).collect(Collectors.toList()));
            param.setPoolCode(LAST_POOL_CODE);

            List<AlbumSearchResponse> responses = this.adjustApiSearchAlbumService.delApiSearchAlbumPoolCode(param);

            Assert.assertFalse(responses.get(0).getPoolCode().contains(LAST_POOL_CODE));

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery(SID, sid));

            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            NativeSearchQuery searchQuery = queryBuilder
                    .withQuery(boolQuery)
                    .withFields(SID, POOL_CODE)
                    .withPageable(PageRequest.of(0, 1))
                    .build();

            sleep();
            SearchHits<ProgramAlbumEs> searchHits = this.restTemplate.search(searchQuery, ProgramAlbumEs.class);
            List<SearchHit<ProgramAlbumEs>> searchHitList = searchHits.getSearchHits();

            Assert.assertFalse(searchHitList.isEmpty());

            ProgramAlbumEs programAlbumEs = searchHitList.get(0).getContent();

            Assert.assertFalse(programAlbumEs.getMinorsPoolCode().contains(LAST_POOL_CODE));

        } catch (Exception e) {
            log.error("delApiSearchAlbumPoolCode error", e);
        } finally {
            ElasticSearchUtil.deleteAlbumEs(elasticsearchService);
        }

    }

    @Test
    void collateSinglePoolCodeApiSearchAlbum() throws IOException {
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        List<String> sidList = Stream.of("625229331555176448", "654220125049921536").collect(Collectors.toList());

        String testDelSid = "506333325988810752";
        ArrangeRpcMockerUtil.getMinorsPoolCode();
        ArrangeRpcMockerUtil.getItemCount();
        ArrangeRpcMockerUtil.getSidListByPoolCode(sidList);

        try {
            ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, null);

            // 测试删除行为。添加数据  es数据已经不在内容池，但有内容池标记。
            getSearchHits(testDelSid);

            AlbumSearchParameterModifier param1 = new AlbumSearchParameterModifier();
            param1.setPoolCode(LAST_POOL_CODE);
            this.adjustApiSearchAlbumService.collateSinglePoolCodeApiSearchAlbum(param1);


            BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
            boolQuery1.filter(QueryBuilders.termsQuery(SID, sidList));

            NativeSearchQueryBuilder queryBuilder1 = new NativeSearchQueryBuilder();
            NativeSearchQuery searchQuery1 = queryBuilder1
                    .withQuery(boolQuery1)
                    .withFields(SID, POOL_CODE)
                    .build();
            sleep();
            SearchHits<ProgramAlbumEs> searchHits1 = this.restTemplate.search(searchQuery1, ProgramAlbumEs.class);
            List<SearchHit<ProgramAlbumEs>> searchHitList1 = searchHits1.getSearchHits();

            searchHitList1.stream().forEach(a -> {
                ProgramAlbumEs content = a.getContent();
                if (sidList.contains(content.getSid())) {
                    Assert.assertTrue(content.getMinorsPoolCode().contains(LAST_POOL_CODE));
                }
            });
        } catch (Exception e) {
            log.error("collateSinglePoolCodeApiSearchAlbum error", e);
        } finally {
            ElasticSearchUtil.deleteAlbumEs(elasticsearchService);
        }
    }

    private void getSearchHits(String sid) {
        AlbumSearchParameterModifier param = new AlbumSearchParameterModifier();
        param.setPoolCode(LAST_POOL_CODE);
        param.setSid(sid);

        Boolean result = this.adjustApiSearchAlbumService.adjustSingleApiSearchAlbum(param);
        Assert.assertTrue(result);

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery(SID, sid));

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery = queryBuilder
                .withQuery(boolQuery)
                .withFields(SID, POOL_CODE)
                .withPageable(PageRequest.of(0, 1))
                .build();
        sleep();
        SearchHits<ProgramAlbumEs> searchHits = this.restTemplate.search(searchQuery, ProgramAlbumEs.class);
        List<SearchHit<ProgramAlbumEs>> searchHitList = searchHits.getSearchHits();

        Assert.assertFalse(searchHitList.isEmpty());

        ProgramAlbumEs programAlbumEs = searchHitList.get(0).getContent();

        Assert.assertTrue(programAlbumEs.getMinorsPoolCode().contains(LAST_POOL_CODE));
    }

    /**
     * 单测使用到了 es .
     * 更新字段后立马查询 查询的字段还是未更新的状态。
     */
    void sleep() {
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
        }
    }


}