package com.heytap.longvideo.search.service.sync;

import com.heytap.longvideo.search.constants.HandleTypeEnum;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.properties.MqProperties;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;

import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class AlbumSyncToSearchServiceTest {
    @InjectMocks
    private AlbumSyncToSearchService albumSyncToSearchService;

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Mock
    private Producer<String, String> kafkaProducer;

    @Mock
    private MqProperties mqProperties;

    @Before
    public void setUp() {
//        MockitoAnnotations.openMocks(this);
        //ReflectionTestUtils.setField(albumSyncToSearchService, "FILTER_LIST", Collections.singletonList("keke"));
        when(mqProperties.getThirdPartyExternalSearchTopic()).thenReturn("test-topic");
        when(mqProperties.getThirdPartyExternalSearchKey()).thenReturn("test-key");
        when(mqProperties.getThirdPartyExternalSearchFilterList()).thenReturn(Collections.singletonList("douban"));
    }

    @Test
    public void testIncrementSyncWithStandardsAlbum_Insert() {
        StandardAlbumEs albumEs = new StandardAlbumEs();
        albumEs.setSid("1");
        albumEs.setTitle("Test");
        albumEs.setStatus(1);
        albumEs.setSource("valid");
        albumEs.setProgramType("movie");

        albumSyncToSearchService.incrementSyncWithStandardsAlbum(albumEs, HandleTypeEnum.INSERT.getValue());
        verify(kafkaProducer, times(1)).send(any(ProducerRecord.class));
    }

    @Test
    public void testIsFilterNotCooperative_Filtered() {
        assertTrue(albumSyncToSearchService.isFilterNotCooperative("keke"));
    }
}
