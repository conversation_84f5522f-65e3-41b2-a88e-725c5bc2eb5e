package com.heytap.longvideo.search.service.common;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.mapper.media.VirtualProgramMapper;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import redis.clients.jedis.JedisCluster;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class VirtualProgramServiceTest {
    @InjectMocks
    VirtualProgramService virtualProgramService;
    @Mock
    private JedisCluster jedisCluster;
    @Mock
    private SearchProperties searchConfig;
    @Mock
    private YoukuSourceFilterService youkuSourceFilterService;
    @Mock
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;
    @Mock
    private VersionFilterService versionFilterService;
    @Mock
    private VirtualProgramMapper virtualProgramMapper;
    @Mock
    private StandardAlbumMapper standardAlbumMapper;
    @Mock
    private InitService initService;
    @Mock
    private static int datebaseEnd = 0;
    @Mock
    private static int tableEnd = 0;

    @Test
    public void getProgramAlbumByVipTypeTest_sidEqualsVirtualSid() {
        ProgramAlbumEs originalData = new ProgramAlbumEs();
        originalData.setSid("123");
        originalData.setVirtualSid("123");
        originalData.setHasVirtualSid(0);
        ProgramAlbumEs result = virtualProgramService.getProgramAlbumByVipType(
                0, originalData, "", "", 1, 2, 0, "");
        assertEquals("123", result.getSid());
    }


    @Test
    public void getProgramAlbumByVipTypeTest_virtualValueBlank() {
        ProgramAlbumEs originalData = new ProgramAlbumEs();
        originalData.setSid("001");
        originalData.setVirtualSid("002");
        originalData.setHasVirtualSid(0);

        when(jedisCluster.hget(any(), anyString())).thenReturn("");

        ProgramAlbumEs result = virtualProgramService.getProgramAlbumByVipType(
                0, originalData, "", "", 1, 2, 0, "");
        assertEquals("001", result.getSid());
    }

    @Test
    public void getProgramAlbumByVipTypeTest_virtualMapEmpty() {
        ProgramAlbumEs originalData = new ProgramAlbumEs();
        originalData.setSid("001");
        originalData.setVirtualSid("002");
        originalData.setHasVirtualSid(0);

        when(jedisCluster.hget(any(), anyString())).thenReturn("{}");

        ProgramAlbumEs result = virtualProgramService.getProgramAlbumByVipType(
                0, originalData, "", "", 1, 2, 0, "");
        assertEquals("001", result.getSid());
    }

    @Test
    public void getProgramAlbumByVipTypeTest_priorityListEmpty() {
        ProgramAlbumEs originalData = new ProgramAlbumEs();
        originalData.setSid("001");
        originalData.setVirtualSid("002");
        originalData.setHasVirtualSid(0);

        when(jedisCluster.hget(any(), anyString())).thenReturn("{" +
                "\"key1\":{\"source\":\"s1\"}}");

        Map<String, List<String>> copyRightPriorityMap = new HashMap<>();
        when(searchConfig.getCopyRightPriorityMap()).thenReturn(copyRightPriorityMap);

        ProgramAlbumEs result = virtualProgramService.getProgramAlbumByVipType(
                0, originalData, "", "", 1, 2, 1, "");
        assertEquals("001", result.getSid());
    }

    @Test
    public void getProgramAlbumByVipTypeTest_priorityList() {
        ProgramAlbumEs originalData = new ProgramAlbumEs();
        originalData.setSid("001");
        originalData.setVirtualSid("002");
        originalData.setHasVirtualSid(0);

        when(jedisCluster.hget(any(), anyString())).thenReturn("{" +
                "\"letv\":{\"sid\":\"003\",\"source\":\"s1\", \"payStatus\":\"1\"}}");

        Map<String, List<String>> copyRightPriorityMap = new HashMap<>();
        copyRightPriorityMap.put("default", Lists.newArrayList("youkumobile","letv"));
        when(searchConfig.getCopyRightPriorityMap()).thenReturn(copyRightPriorityMap);

        when(youkuSourceFilterService.thirdPartyFilter(any(), any(), anyString())).thenReturn(true);
        when(funshionLongVideoAndWeidiouFilterService.filterItem(any())).thenReturn(true);
        when(versionFilterService.versionFilter(anyInt(), any(), anyString())).thenReturn(false);

        ProgramAlbumEs result = virtualProgramService.getProgramAlbumByVipType(
                0, originalData, "", "vip", 1, 2, 1, "");
        assertEquals("003", result.getSid());
    }


    @Test
    public void createCacheAndReturnAllAlbumTest() {
        when(virtualProgramMapper.selectAllVirtualProgramCount(any())).thenReturn(1);

        ProgramAlbumEs es = new ProgramAlbumEs();
        es.setSid("001");
        when(standardAlbumMapper.selectStandardAlbumListForApp(anyInt(), anyInt(), any()))
                .thenReturn(Lists.newArrayList(es));

        MisVirtualProgramRelation relation = new MisVirtualProgramRelation();
        relation.setSid("001");
        when(virtualProgramMapper.selectAllVirtualProgram(anyInt(), anyInt(), any()))
                .thenReturn(Lists.newArrayList(relation));

        virtualProgramService.createCacheAndReturnAllAlbum();
    }
}
