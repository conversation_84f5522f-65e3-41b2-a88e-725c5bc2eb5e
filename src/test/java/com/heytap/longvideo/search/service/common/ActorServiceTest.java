package com.heytap.longvideo.search.service.common;

import cn.hutool.core.lang.Assert;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SearchHits.class, SearchHit.class, TotalHits.class, RestHighLevelClient.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ActorServiceTest {
    @InjectMocks
    private ActorService actorService;

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Test
    public void selectAllActorAndDirectorTest() throws IOException {

        SearchResponse response1 = mock(SearchResponse.class);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};
        PowerMockito.when(hit.getSourceAsString()).thenReturn(buildEs());
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(response1.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class)))
                .thenReturn(response1);

        SearchResponse response2 = mock(SearchResponse.class);
        SearchHits hits2 = PowerMockito.mock(SearchHits.class);
        PowerMockito.when(hits2.getHits()).thenReturn(null);
        PowerMockito.when(response2.getHits()).thenReturn(hits2);
        PowerMockito.when(restHighLevelClient.scroll(any(SearchScrollRequest.class), any(RequestOptions.class)))
                .thenReturn(response2);

        Set<String> result = actorService.selectAllActorAndDirector();
        Assert.equals(1, result.size());
    }



    private String buildEs() {
        return "    {\n" +
                "            \"name\": \"末代皇帝\",\n" +
                "            \"isHot\": 0\n" +
                "        }";
    }
}
