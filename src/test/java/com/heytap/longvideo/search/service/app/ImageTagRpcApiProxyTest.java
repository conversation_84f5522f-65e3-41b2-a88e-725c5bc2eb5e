package com.heytap.longvideo.search.service.app;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.ImageTagRpcApi;
import com.heytap.longvideo.client.arrange.entity.MtvImageTag;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.common.lib.constants.ActivityTaskUnlockEpisodeConstant;
import com.heytap.longvideo.common.lib.rpc.ActivityRpcApi;
import com.heytap.longvideo.common.lib.rpc.resp.activity.UnlockEpisodeInfo;
import com.heytap.longvideo.common.lib.rpc.resp.activity.UnlockEpisodeInfoList;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.service.common.StrategyService;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import org.junit.Before;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ImageTagRpcApiProxyTest {
    @InjectMocks
    ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Mock
    private ImageTagRpcApi imageTagRpcApi;

    @Before
    public void build() throws IOException {
        String string = FileUtils.readFileToString(new ClassPathResource("/mock/imageTagRpcApiFindByStatus.json").getFile(), "UTF-8");
        List<MtvImageTag> mtvImageTags = JsonUtil.getListFormStr(string, MtvImageTag.class);
        RpcResult<List<MtvImageTag>> listRpcResult = new RpcResult<>(mtvImageTags);
        when(imageTagRpcApi.findByStatus(anyInt())).thenReturn(CompletableFuture.completedFuture(listRpcResult));
    }

    @Test
    public void imageTagRpcApiProxyTest_noVersion() {
        String code = "mgmobile";
        MtvImageTag byCodeV2 = imageTagRpcApiProxy.getByCodeV2(code);
        assertEquals(byCodeV2.getImageUrl(), "https://dhfs-test-cpc.wanyol.com/20210914152654805.png");

        code = "list_null";
        byCodeV2 = imageTagRpcApiProxy.getByCodeV2(code);
        assertNull(byCodeV2);

        code = "trailer_high";
        byCodeV2 = imageTagRpcApiProxy.getByCodeV2(code);
        assertNull(byCodeV2);
    }

    @Test
    public void imageTagRpcApiProxyTest_lowVersion() {
        String code = "trailer_high";
        MtvImageTag byCodeV2 = imageTagRpcApiProxy.getByCodeV2(code, "10.0.1");
        assertEquals(byCodeV2.getImageUrl(), "https://dhfs-test-cpc.wanyol.com/1615801573537_103x110.png");

        assertNull(imageTagRpcApiProxy.getByCodeV2(code, ""));

        when(imageTagRpcApi.findByStatus(anyInt())).thenReturn(CompletableFuture.completedFuture(null));
        assertNull(imageTagRpcApiProxy.getByCodeV2(code, ""));
    }

    @Test
    public void imageTagRpcApiProxyTest_errorRpcCode() {
        String code = "trailer_high";
        when(imageTagRpcApi.findByStatus(anyInt())).thenReturn(CompletableFuture.completedFuture(new RpcResult<>(-100001, "code error")));
        try {
            imageTagRpcApiProxy.getByCodeV2(code, "1.1.1");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "java.lang.RuntimeException: code error");
        }
    }

    @Test
    public void imageTagRpcApiProxyTest_rpcException() {
        String code = "trailer_high";
        when(imageTagRpcApi.findByStatus(anyInt())).thenThrow(new RuntimeException("mock exception"));
        try {
            imageTagRpcApiProxy.getByCodeV2(code, "1.1.1");
        } catch (Exception e) {
            System.out.println(e.getMessage());
            assertEquals(e.getMessage(), "mock exception");
        }
    }

    @Test
    public void imageTagRpcApiProxyTest_rpcReturnEmptyList() {
        String code = "trailer_high";
        when(imageTagRpcApi.findByStatus(anyInt())).thenReturn(CompletableFuture.completedFuture(new RpcResult<>(Collections.emptyList())));
        assertNull(imageTagRpcApiProxy.getByCodeV2(code, "1.1.1"));
    }
}
