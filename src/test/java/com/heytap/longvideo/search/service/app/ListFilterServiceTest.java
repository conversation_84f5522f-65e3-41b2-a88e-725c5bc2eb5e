package com.heytap.longvideo.search.service.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.model.param.app.Urlpack;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ListFilterServiceTest {
    @InjectMocks
    ListFilterService listFilterService;

    @Mock
    private ElasticsearchRestTemplate restTemplate;
    @Mock
    private SearchProperties searchConfig;
    @Mock
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;
    @Mock
    private YoukuSourceFilterService youkuSourceFilterService;

    @Before
    public void build() {
        when(searchConfig.getMgAppVersion()).thenReturn(4);
        when(searchConfig.getLetvAppVersion()).thenReturn(6);
        when(searchConfig.getYstAppVersion()).thenReturn(7);
        when(searchConfig.getMiguOlympicAppVersion()).thenReturn(8);
    }


    @Test
    public void listFilterTest() {
        ListFilterParam listFilterParam = new ListFilterParam();
        listFilterParam.setOffset(1);
        listFilterParam.setNumber(12);
        listFilterParam.setVersion(80200);
        listFilterParam.setCallType(2);

        //构建urlPack
        Urlpack urlpack = new Urlpack();
        urlpack.setActor("李小龙");
        //默认最热排序
        urlpack.setSort("hot");
        urlpack.setVersion_tag(CommonUtils.getVersionTag(80200));
        urlpack.setContentType("all");
        Map<String, Object> urlPackMap = new HashMap<>();
        urlPackMap.put("cmd_vod", urlpack);

        listFilterParam.setUrlpack(JacksonUtil.toJSONString(urlPackMap));

        SearchHit<ProgramAlbumEs> intentHit = buildSearchHit();

        SearchHits intentHits = mock(SearchHits.class);
        when(intentHits.getSearchHits()).thenReturn(Lists.newArrayList(intentHit));
        when(intentHits.getTotalHits()).thenReturn(0L);

        when(restTemplate.search(argThat((ArgumentMatcher<NativeSearchQuery>) query ->
                query!=null && query.getQuery().toString().contains("李小龙")), any()))
                .thenReturn(intentHits);

        when(funshionLongVideoAndWeidiouFilterService.filterItem(any())).thenReturn(false);
        when(youkuSourceFilterService.filterItem(any())).thenReturn(false);

        List<ProgramAlbumEs> result = listFilterService.listFilter(listFilterParam);

        Assert.assertEquals(0, result.size());
    }

    @Test
    public void buildSortTest() {
        Urlpack urlpack = new Urlpack();
        urlpack.setSort("free");
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        listFilterService.buildSort(urlpack, queryBuilder);
    }

    private SearchHit<ProgramAlbumEs> buildSearchHit() {
        return JSON.parseObject("    {\n" +
                        "        \"content\": {\n" +
                        "            \"actor\": \"尊龙|陈冲|邬君梅|彼得·奥图尔|英若诚|吴涛|黄自强|丹尼斯·邓|坂本龙一|马吉·汉|里克·扬|田川洋行|苟杰德|理查德·吴|皱缇格|陈凯歌|卢燕|蒋锡礽|陈述|鲍皓昕|黄文捷|邵茹贞|亨利·基|张良斌|梁冬|康斯坦丁·格雷戈里|黄汉琪|王涛|宋怀桂|蔡鸿翔|程淑艳|张天民\",\n" +
                        "            \"area\": \"英国 / 意大利 / 中国大陆 / 法国\",\n" +
                        "            \"copyright\": 1,\n" +
                        "            \"copyrightCode\": \"tencent\",\n" +
                        "            \"createTime\": 1731552190236,\n" +
                        "            \"director\": \"贝纳尔多·贝托鲁奇\",\n" +
                        "            \"duration\": 163,\n" +
                        "            \"featureType\": 1,\n" +
                        "            \"horizontalIcon\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"horizontalImage\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"information\": \"溥仪（尊龙 饰）的一生在电影中娓娓道来。他从三岁起登基，年幼的眼光中只有大臣身上的一只蝈蝈，江山在他心中只是一个不明所以的名词。长大了，他以为可以变革，却被太监一把火烧了朝廷账本。他以为自己是大清江山的主人，却做了日本人的傀儡。解放后，他坐上了从苏联回来的火车，身边是押送监视他的解放军。他猜测自己难逃一死，便躲在狭小的卫生间里，割脉自杀。然而他没有死在火车上，命运的嘲笑还在等着他。文革的风风雨雨，在他身上留下了斑斑伤痕。\",\n" +
                        "            \"language\": \"英语 / 汉语普通话 / 日语\",\n" +
                        "            \"managerStatus\": 1,\n" +
                        "            \"programType\": \"movie\",\n" +
                        "            \"sid\": \"1074228711114592256\",\n" +
                        "            \"source\": \"douban\",\n" +
                        "            \"sourceAlbumId\": \"db_1293172\",\n" +
                        "            \"sourceScore\": \"9.3\",\n" +
                        "            \"sourceStatus\": 1,\n" +
                        "            \"sourceType\": 0,\n" +
                        "            \"sourceWebUrl\": \"https://v.qq.com/x/cover/29trop8s2ipr3es.html?ptag=newdouban.movie&subtype=1&type=online-video\",\n" +
                        "            \"status\": 1,\n" +
                        "            \"tags\": \"剧情|传记|历史\",\n" +
                        "            \"title\": \"末代皇帝\",\n" +
                        "            \"updateTime\": 1731552190236,\n" +
                        "            \"verticalIcon\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"verticalImage\": \"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p452089833.jpg\",\n" +
                        "            \"year\": 1987\n" +
                        "        },\n" +
                        "        \"highlightFields\": {},\n" +
                        "        \"id\": \"1074228711114592256\",\n" +
                        "        \"score\": 46.82381,\n" +
                        "        \"sortValues\": []\n" +
                        "    }",
                new TypeReference<SearchHit<ProgramAlbumEs>>() {});
    }

}
