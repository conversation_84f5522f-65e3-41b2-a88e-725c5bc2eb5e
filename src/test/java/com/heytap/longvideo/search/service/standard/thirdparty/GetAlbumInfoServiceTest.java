package com.heytap.longvideo.search.service.standard.thirdparty;

import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.thirdparty.standard.GetAlbumInfoRequest;
import com.heytap.longvideo.search.model.response.thirdparty.GetAlbumInfoResponse;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GetAlbumInfoServiceTest {

    @Mock
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    @Mock
    private UnofficialAlbumService unofficialAlbumService;

    @Mock
    private DeepLinkUtils deepLinkUtils;

    @InjectMocks
    private GetAlbumInfoService service;

    private final String SID1 = "1148902712319070208";
    private final String SID2 = "1070473131786948608";
    private final String SID3 = "513569195770859520";

    // 测试1: 无效请求参数
    @Test
    public void whenInvalidRequest_thenReturnError() {
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();

        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);
        assertEquals(-1001, result.getCode());
        assertEquals("param is invalid", result.getMsg());
    }


    @Test
    public void testGetAlbumInfo_SingleSid_StandardAlbumNull() {
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();
        String sid = "1148902712319070208";
        request.setSid(sid);

        when(standardAlbumRpcApiProxy.getBySid(sid)).thenReturn(null);
        when(unofficialAlbumService.searchBySid(sid, UnofficialAlbumEs.class)).thenReturn(null);

        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);

        Assert.assertNotNull(result.getData());
        Assert.assertEquals(200, result.getCode());
    }

    // 查询自建节目验证
    @Test
    public void testGetAlbumInfo_SingleSid_StandardAlbumNotNull() {
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();
        String sid = "1148902712319070208";
        request.setSid(sid);

        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setSid(sid);
        standardAlbum.setTitle("萌夫木子李");
        standardAlbum.setSource("huashi");
        standardAlbum.setSourceWebUrl("http://example.com");

        when(standardAlbumRpcApiProxy.getBySid(sid)).thenReturn(standardAlbum);
        when(deepLinkUtils.getDeeplinkByType(any(), anyString())).thenReturn("yoli://yoli.com/yoli/longvideo/videodetail?linkValue=1148902712319070208");

        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);

        Assert.assertNotNull(result.getData());
        Assert.assertEquals("萌夫木子李", result.getData().getItemList().get(0).getTitle());
        Assert.assertEquals("yoli://yoli.com/yoli/longvideo/videodetail?linkValue=1148902712319070208&showSplashAd=0&openMainActivity=1&openFrom=screenoff_alg_CT_1148902712319070208_DP&showWhenLocked=1&swl=1", result.getData().getItemList().get(0).getDeepLink());
    }

    // 查询全网节目验证
    @Test
    public void testGetAlbumInfo_SingleSid_UnofficialAlbumNotNull() {
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();
        String sid = "1148902712319070208";
        request.setSid(sid);

        when(standardAlbumRpcApiProxy.getBySid(sid)).thenReturn(null);
        UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
        unofficialAlbumEs.setSid(sid);
        unofficialAlbumEs.setTitle("萌夫木子李");

        when(unofficialAlbumService.searchBySid(sid, UnofficialAlbumEs.class)).thenReturn(unofficialAlbumEs);

        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);

        Assert.assertNotNull(result.getData());
        assertEquals("萌夫木子李", result.getData().getItemList().get(0).getTitle());
        assertEquals("yoli://yoli.com/detail/longvideo/outofstockvideodetail?linkValue=1148902712319070208&title=萌夫木子李&showSplashAd=0&openMainActivity=1&openFrom=screenoff_alg_CT_1148902712319070208_DP&showWhenLocked=1&swl=1", result.getData().getItemList().get(0).getDeepLink());
    }

    // 测试2: 批量自建内容查询
    @Test
    public void whenBatchStandardSids_thenReturnAlbumList() {
        // Mock数据
        List<String> sids = Arrays.asList(SID1, SID2);

        Map<String, StandardAlbum> albumMap = new HashMap<>();
        StandardAlbum album1 = new StandardAlbum();
        album1.setSid(SID1);
        album1.setSource("huashi");
        albumMap.put(SID1, album1);

        StandardAlbum album2 = new StandardAlbum();
        album2.setSid(SID2);
        album2.setSource("youkumobile");
        album2.setSourceWebUrl("hap://applet/fc2345027171510597?path=%2Fpages%2Fplay%2Findex&query=showId%3D6c09efbfbd48efbfbd48");
        albumMap.put(SID2, album2);

        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(sids)).thenReturn(albumMap);
        when(deepLinkUtils.getDeeplinkByType(eq(TemplateLinkTypeEnum.WEB_FAST_APP.getCode()), anyString()))
                .thenReturn("yoli://yoli.com/yoli/bridge?linkValue=hap%3A%2F%2Fapplet%2Ffc2345027171510597%3Fpath%3D%252Fpages%252Fplay%252Findex%26query%3DshowId%253D6c09efbfbd48efbfbd48%26_SWL_%3D1");
        when(deepLinkUtils.getDeeplinkByType(eq(TemplateLinkTypeEnum.ALBUM.getCode()), anyString()))
                .thenReturn("yoli://default.link");

        // 构造请求
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();
        request.setSids(sids);

        // 执行测试
        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);

        // 验证结果
        assertEquals(2, result.getData().getItemListSize());
        assertTrue(result.getData().getItemList().stream()
                .allMatch(r -> "oppo_video".equals(r.getContentChannel())));

        // 验证优酷特殊链接
        GetAlbumInfoResponse youkuResponse = result.getData().getItemList().get(1);
        assertTrue(youkuResponse.getDeepLink().contains("yoli://yoli.com/yoli/bridge?linkValue=hap%3A%2F%2Fapplet%2Ffc2345027171510597%3Fpath%3D%252Fpages%252Fplay%252Findex%26query%3DshowId%253D6c09efbfbd48efbfbd48%26_SWL_%3D1"));
    }

    // 测试3: 批量全网内容查询
    @Test
    public void whenBatchUnofficialSids_thenReturnAlbumList() {
        // Mock数据
        List<String> sids = Collections.singletonList(SID3);
        List<UnofficialAlbumEs> esList = new ArrayList<>();

        UnofficialAlbumEs es = new UnofficialAlbumEs();
        es.setSid(SID3);
        es.setTitle("永不言败");
        esList.add(es);

        when(unofficialAlbumService.getAlbumList(sids)).thenReturn(esList);

        // 构造请求
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();
        request.setAllWebSids(sids);

        // 执行测试
        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);

        // 验证结果
        assertEquals(1, result.getData().getItemListSize());
        GetAlbumInfoResponse response = result.getData().getItemList().get(0);
        assertEquals(SID3, response.getSid());
        assertEquals("all_web", response.getContentChannel());
        assertTrue(response.getDeepLink().contains("outofstockvideodetail"));
    }

    // 测试4: 混合批量查询（自建+全网）
    @Test
    public void whenMixedBatchRequest_thenReturnCombinedResults() {
        // Mock自建内容
        List<String> standardSids = Collections.singletonList(SID1);
        Map<String, StandardAlbum> stdMap = new HashMap<>();
        StandardAlbum album = new StandardAlbum();
        album.setSid(SID1);
        album.setSource("huashi");
        stdMap.put(SID1, album);
        when(standardAlbumRpcApiProxy.getBySidsFilterInvalid(standardSids)).thenReturn(stdMap);
        when(deepLinkUtils.getDeeplinkByType(anyInt(), anyString())).thenReturn("standard_deeplink");

        // Mock全网内容
        List<String> unofficialSids = Collections.singletonList(SID3);
        List<UnofficialAlbumEs> esList = new ArrayList<>();
        UnofficialAlbumEs es = new UnofficialAlbumEs();
        es.setSid(SID3);
        esList.add(es);
        when(unofficialAlbumService.getAlbumList(unofficialSids)).thenReturn(esList);

        // 构造请求
        GetAlbumInfoRequest request = new GetAlbumInfoRequest();
        request.setSids(standardSids);
        request.setAllWebSids(unofficialSids);

        // 执行测试
        StandardResult<PageResponse<GetAlbumInfoResponse>> result = service.getAlbumInfo(request);

        // 验证结果
        PageResponse<GetAlbumInfoResponse> page = result.getData();
        assertEquals(2, page.getItemListSize());

        long oppoCount = page.getItemList().stream()
                .filter(r -> "oppo_video".equals(r.getContentChannel()))
                .count();
        long allWebCount = page.getItemList().stream()
                .filter(r -> "all_web".equals(r.getContentChannel()))
                .count();

        assertEquals(1, oppoCount);
        assertEquals(1, allWebCount);
    }
}