package com.heytap.longvideo.search.service.standard;

import com.heytap.longvideo.search.constants.SortStrategyConstant;
import com.heytap.longvideo.search.mapper.media.ProgramTypeMapper;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.mq.MediaJinsConsumerService;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SearchHits.class, SearchHit.class, TotalHits.class, RestHighLevelClient.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class StandardAlbumServiceTest {

    @Mock
    private RestHighLevelClient restHighLevelClient;
    @Mock
    private ElasticSearchService mockElasticsearchService;
    @Mock
    private ElasticsearchRestTemplate restTemplate;
    @Mock
    private HotVideoService mockHotVideoService;
    @Mock
    private SearchProperties mockSearchConfig;
    @Mock
    private ProgramTypeMapper mockProgramTypeMapper;
    @Mock
    private MediaJinsConsumerService mockConsumerService;
    @Mock
    private FunshionLongVideoAndWeidiouFilterService mockFunshionLongVideoAndWeidiouFilterService;

    @InjectMocks
    private StandardAlbumService standardAlbumServiceUnderTest;

    @Test
    public void testSearchAlbum() throws Exception {
        // Setup
        final SearchStandardAlbumParams request = new SearchStandardAlbumParams();
        request.setPageIndex(1);
        request.setPageSize(10);
        request.setTitle("title");
        request.setManagerStatus("managerStatus");
        request.setYearStart("yearStart");
        request.setYearEnd("yearEnd");
        request.setSourceScoreStart(0.0f);
        request.setSourceScoreEnd(0.0f);
        request.setSortStrategy(SortStrategyConstant.PLAY_PV);
        request.setSortType(0);
        request.setUsageScene("2*N");
        request.setAgeStart(0);
        request.setAgeEnd(0);
        request.setBlockYoukuMobile("blockYoukuMobile");
        request.setOppoScoreArray(new Integer[]{0});
        request.setUpdateTime(0L);

        TotalHits totalHits = PowerMockito.mock(TotalHits.class);
        // mock final对象的属性
        PowerMockito.field(TotalHits.class, "value").set(totalHits, 1);
        SearchHits hits = PowerMockito.mock(SearchHits.class);
        SearchHit hit = PowerMockito.mock(SearchHit.class);
        SearchHit[] hitArray = {hit};
        PowerMockito.when(hit.getSourceAsString()).thenReturn("{\"source\":\"miguolympic\",\"sourceStatus\":1,\"sourceType\":15}");
        PowerMockito.when(hit.getScore()).thenReturn(99.9f);
        SearchResponse response = mock(SearchResponse.class);
        PowerMockito.when(hits.getHits()).thenReturn(hitArray);
        PowerMockito.when(hits.getTotalHits()).thenReturn(totalHits);
        PowerMockito.when(response.getHits()).thenReturn(hits);
        PowerMockito.when(restHighLevelClient.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(response);


        // Run the test
        final PageResponse<StandardAlbumVo> result = standardAlbumServiceUnderTest.searchAlbum(request);

        // Verify the results
        assertEquals(99.9f, result.getItemList().get(0).getReleScore(), 0.001);
    }

    @Test(expected = Exception.class)
    public void testSearchAlbum_RestHighLevelClientThrowsIOException() throws Exception {
        // Setup
        final SearchStandardAlbumParams request = new SearchStandardAlbumParams();
        request.setPageIndex(0);
        request.setPageSize(0);
        request.setTitle("title");
        request.setManagerStatus("managerStatus");
        request.setYearStart("yearStart");
        request.setYearEnd("yearEnd");
        request.setSourceScoreStart(0.0f);
        request.setSourceScoreEnd(0.0f);
        request.setOrder("oppoHot");
        request.setSortStrategy(0);
        request.setSortType(0);
        request.setUsageScene("usageScene");
        request.setAgeStart(0);
        request.setAgeEnd(0);
        request.setBlockYoukuMobile("blockYoukuMobile");
        request.setOppoScoreArray(new Integer[]{0});
        request.setUpdateTime(0L);

        when(restHighLevelClient.search(new SearchRequest("standards_album"), RequestOptions.DEFAULT))
                .thenThrow(IOException.class);

        // Run the test
        standardAlbumServiceUnderTest.searchAlbum(request);
    }

    @Test
    public void testInitData() {
        // Setup
        final StandardAlbumEs standardAlbumEs = new StandardAlbumEs();
        standardAlbumEs.setTitle("title");
        standardAlbumEs.setSourceStatus(0);
        standardAlbumEs.setSource("source");
        standardAlbumEs.setSourceAlbumId("sourceAlbumId");
        standardAlbumEs.setSourceType(0);
        standardAlbumEs.setFeatureType(0);
        standardAlbumEs.setPreviewInfo("previewInfo");
        standardAlbumEs.setSid("sid");
        final List<StandardAlbumEs> list = Arrays.asList(standardAlbumEs);
        when(restTemplate.indexOps(StandardAlbumEs.class)).thenReturn(mock(IndexOperations.class));

        // Run the test
        standardAlbumServiceUnderTest.initData(null);
        standardAlbumServiceUnderTest.initData(list);
    }

    @Test
    public void testInsertOrUpdate() {
        // Setup
        final StandardAlbumEs es = new StandardAlbumEs();
        es.setTitle("title");
        es.setSourceStatus(0);
        es.setSource("source");
        es.setSourceAlbumId("sourceAlbumId");
        es.setSourceType(0);
        es.setFeatureType(0);
        es.setPreviewInfo("previewInfo");
        es.setSid("sid");

        when(restTemplate.save(any(StandardAlbumEs.class))).thenThrow(new RuntimeException());

        Assertions.assertThrows(RuntimeException.class, () -> standardAlbumServiceUnderTest.insertOrUpdate(es));
    }

    @Test
    public void testDelete() {
        final StandardAlbumEs es = new StandardAlbumEs();
        es.setTitle("title");
        es.setSourceStatus(0);
        es.setSource("source");
        es.setSourceAlbumId("sourceAlbumId");
        es.setSourceType(0);
        es.setFeatureType(0);
        es.setPreviewInfo("previewInfo");
        es.setSid("sid");

        when(restTemplate.delete(anyString(), any(IndexCoordinates.class))).thenThrow(new RuntimeException());

        Assertions.assertThrows(RuntimeException.class, () -> standardAlbumServiceUnderTest.delete(es));
    }

    @Test
    public void testSearchBySid() throws Exception {
        // Setup
        final StandardAlbumEs expectedResult = new StandardAlbumEs();
        expectedResult.setTitle("title");
        expectedResult.setSourceStatus(0);
        expectedResult.setSource("source");
        expectedResult.setSourceAlbumId("sourceAlbumId");
        expectedResult.setSourceType(0);
        expectedResult.setFeatureType(0);
        expectedResult.setPreviewInfo("previewInfo");
        expectedResult.setSid("sid");

        // Configure RestHighLevelClient.get(...).
        final GetResponse getResponse = mock(GetResponse.class);
        PowerMockito.when(getResponse.getSourceAsString()).thenReturn("{\"dayNo\":\"2\"}");

        when(restHighLevelClient.get(any(GetRequest.class), eq(RequestOptions.DEFAULT))).thenReturn(getResponse);

        // Run the test
        final StandardAlbumEs result = standardAlbumServiceUnderTest.searchBySid("keyWord");

        // Verify the results
        assertEquals(2, (int) result.getDayNo());
    }

    @Test
    public void testSearchBySid_throw_null() throws Exception {
        // Setup
        final StandardAlbumEs expectedResult = new StandardAlbumEs();
        expectedResult.setTitle("title");
        expectedResult.setSourceStatus(0);
        expectedResult.setSource("source");
        expectedResult.setSourceAlbumId("sourceAlbumId");
        expectedResult.setSourceType(0);
        expectedResult.setFeatureType(0);
        expectedResult.setPreviewInfo("previewInfo");
        expectedResult.setSid("sid");

        // Configure RestHighLevelClient.get(...).

        when(restHighLevelClient.get(any(GetRequest.class), eq(RequestOptions.DEFAULT))).thenThrow(new RuntimeException());

        // Run the test
        final StandardAlbumEs result = standardAlbumServiceUnderTest.searchBySid("keyWord");

        // Verify the results
        assertNull(result);
    }

}
