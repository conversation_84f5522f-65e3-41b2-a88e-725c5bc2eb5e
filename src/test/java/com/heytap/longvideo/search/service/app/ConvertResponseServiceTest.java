package com.heytap.longvideo.search.service.app;

import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.StandardEpisodeRpcApiProxy;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.video.client.enums.ButtonStatusEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ConvertResponseServiceTest {
    @InjectMocks
    ConvertResponseService convertResponseService;
    @Mock
    private SearchProperties searchProperties ;
    @Mock
    private StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy;

    @Before
    public void build(){
        Map<String, Map<String,Integer>> thirdMap = new HashMap<>();
        thirdMap.put("douban",new HashMap<>());
        when(searchProperties.getThirdSearchMap()).thenReturn(thirdMap);
        when(searchProperties.getSearchCardButtonNum()).thenReturn(2);
    }

    /**
     * 适配预约按钮
     */
    @Test
    public void buildNewResultCardButtons1(){
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponse();
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(1);
        convertResponseService.buildNewResultButtons(keyWordSearchResponse,new HashMap<>(),buildCanSubscribeSidList(),null);
        Assert.assertEquals(ButtonStatusEnum.VIEW_PREVIEW.getDesc(),keyWordSearchResponse.getButtons().get(0).getText());
        Assert.assertEquals(ButtonStatusEnum.RESERVATION.getDesc(),keyWordSearchResponse.getButtons().get(1).getText());
    }

    /**
     * 适配会员免费节目的按钮
     */
    @Test
    public void buildNewResultCardButton2(){
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponse();
        keyWordSearchResponse.setFeatureType(1);
        keyWordSearchResponse.setPayStatus(1);
        convertResponseService.buildNewResultButtons(keyWordSearchResponse,new HashMap<>(),buildCanSubscribeSidList(),null);
        Assert.assertEquals(ButtonStatusEnum.NOW_VIEW.getDesc(),keyWordSearchResponse.getButtons().get(0).getText());
        Assert.assertEquals(ButtonStatusEnum.COLLECT.getDesc(),keyWordSearchResponse.getButtons().get(1).getText());
    }

    /**
     * 适配按钮开关
     */
    @Test
    public void buildNewResultCardButton3(){
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponse();
        keyWordSearchResponse.setFeatureType(1);
        keyWordSearchResponse.setPayStatus(0);
        when(searchProperties.getSearchCardButtonNum()).thenReturn(1);
        convertResponseService.buildNewResultButtons(keyWordSearchResponse,new HashMap<>(),buildCanSubscribeSidList(),null);
        Assert.assertEquals(ButtonStatusEnum.FREE_VIEW.getDesc(),keyWordSearchResponse.getButtons().get(0).getText());
    }


    @Test
    public void buildNewResultHighLights(){
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponse();
        convertResponseService.buildNewResultHighLights(keyWordSearchResponse, new HashMap<>());
        Assert.assertEquals(2,keyWordSearchResponse.getHighlights().size());
    }

    @Test
    public void buildNewResultHighLights1(){
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponse();
        keyWordSearchResponse.setTags("美丽|是它");
        convertResponseService.buildNewResultHighLights(keyWordSearchResponse, new HashMap<>());
        Assert.assertEquals(3,keyWordSearchResponse.getHighlights().size());
    }

    @Test
    public void test_isNeedFilter(){
        KeyWordSearchResponse keyWordSearchResponse = buildKeyWordSearchResponse();
        List<KeyWordSearchResponse> keyWordSearchResponseList = new ArrayList<>();
        keyWordSearchResponseList.add(keyWordSearchResponse);
        when(standardEpisodeRpcApiProxy.countStandardEpisodeBySid(any())).thenReturn(0);
        keyWordSearchResponseList = keyWordSearchResponseList.stream().filter(keyWordSearchResponse1 -> convertResponseService.isNeedFilter(keyWordSearchResponse1, new ArrayList<>())).collect(Collectors.toList());
        Assert.assertEquals(0,keyWordSearchResponseList.size());
    }




    private List<String> buildCanSubscribeSidList(){
        List<String> resultList = new ArrayList<>();
        resultList.add("SID1");
        return resultList;
    }


    private KeyWordSearchResponse buildKeyWordSearchResponse(){
        return KeyWordSearchResponse.builder().
                sid("SID1").
                title("标题1").
                directors("导演A").
                stars("演员A,演员B").
                year(2025).
                sourceScore(9.7f).
                featureType(2).
                tags("美丽,其他,电影").
                build();
    }
}
