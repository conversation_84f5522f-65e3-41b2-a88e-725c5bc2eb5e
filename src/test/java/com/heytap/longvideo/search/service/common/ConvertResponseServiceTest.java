package com.heytap.longvideo.search.service.common;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.AlbumRankRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ConvertResponseServiceTest {
    @InjectMocks
    ConvertResponseService convertResponseService;

    @Mock
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;
    @Mock
    private YoukuSourceFilterService youkuSourceFilterService;
    @Mock
    private DeepLinkUtils deepLinkUtils;
    @Mock
    private String detailDeepLink;
    @Mock
    private ImageTagRpcApiProxy imageTagRpcApiProxy;
    @Mock
    private SearchProperties searchProperties;
    @Mock
    private AlbumRankRpcApiProxy albumRankRpcApiProxy;

    @Before
    public void build() {
        when(searchProperties.getSearchCardOptVersion()).thenReturn(80900);
    }

    @Test
    public void handleSingleSidTest_null() {
        KeyWordSearchResponse response = convertResponseService.handleSingleSid(null, null, null);
        assertNull(response);
    }

    @Test
    public void handleSingleSidTest_ztv() {
        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setSource("ztv");
        KeyWordSearchResponse response = convertResponseService.handleSingleSid(standardAlbum, null, null);
        assertNull(response);
    }


    @Test
    public void handleSingleSidTest_youkuSourceFilter() {
        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setFeatureType(1);
        standardAlbum.setPayStatus(1);
        standardAlbum.setActor("A|B");
        standardAlbum.setDirector("C|D");

        when(funshionLongVideoAndWeidiouFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.filterItemBySource(any(), any())).thenReturn(true);

        KeyWordSearchResponse response = convertResponseService.handleSingleSid(standardAlbum, new KeyWordSearchParamV2(), null);

        assertNull(response);
    }

    @Test
    public void handleSingleSidTest1() {
        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setFeatureType(1);
        standardAlbum.setPayStatus(1);
        standardAlbum.setActor("A|B");
        standardAlbum.setDirector("C|D");

        when(funshionLongVideoAndWeidiouFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.setDeepLinkFilter(any(), any(), any())).thenReturn(true);
        KeyWordSearchResponse response = convertResponseService.handleSingleSid(standardAlbum, new KeyWordSearchParamV2(), null);

        assertNotNull(response);
    }


    @Test
    public void handleSingleSidTest_noMarkCode() {
        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setFeatureType(1);
        standardAlbum.setPayStatus(1);
        standardAlbum.setActor("A|B");
        standardAlbum.setDirector("C|D");

        when(funshionLongVideoAndWeidiouFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.setDeepLinkFilter(any(), any(), any())).thenReturn(true);
        KeyWordSearchResponse response = convertResponseService.handleSingleSid(standardAlbum, new KeyWordSearchParamV2(), null);

        assertEquals("", response.getMarkCode());
    }



    @Test
    public void handleSingleSidTest_hasMarkCode() {
        StandardAlbum standardAlbum = new StandardAlbum();
        standardAlbum.setFeatureType(2);
        standardAlbum.setPayStatus(1);
        standardAlbum.setActor("A|B");
        standardAlbum.setDirector("C|D");

        when(funshionLongVideoAndWeidiouFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.filterItemBySource(any(), any())).thenReturn(false);
        when(youkuSourceFilterService.setDeepLinkFilter(any(), any(), any())).thenReturn(true);
        when(imageTagRpcApiProxy.getImageUrl(any())).thenReturn("url");

        KeyWordSearchResponse response = convertResponseService.handleSingleSid(standardAlbum, new KeyWordSearchParamV2(), 1);

        assertEquals("YG", response.getMarkCode());
        assertEquals("url", response.getMarkCodeUrl());
    }

    @Test
    public void sortTest_null() {
        convertResponseService.sort(null, null, null, 80700);
        convertResponseService.sort(null, null, null, 80900);
    }

    @Test
    public void sortTest_hot() {
        List<KeyWordSearchResponse> list = buildList();
        Map<String, StandardAlbum> map = buildMap();
        convertResponseService.sort(list, SortEnum.HOT.getType(), map, 80900);
        assertEquals("1001", list.get(0).getSid());
    }

    @Test
    public void sortTest_new() {
        List<KeyWordSearchResponse> list = buildList();
        Map<String, StandardAlbum> map = buildMap();
        convertResponseService.sort(list, SortEnum.NEW.getType(), map, 80900);
        assertEquals("1002", list.get(0).getSid());
    }

    @Test
    public void sortTest_free() {
        List<KeyWordSearchResponse> list = buildList();
        Map<String, StandardAlbum> map = buildMap();
        convertResponseService.sort(list, SortEnum.FREE.getType(), map, 80900);
        assertEquals("1002", list.get(0).getSid());
    }


    private List<KeyWordSearchResponse> buildList() {
        KeyWordSearchResponse content1 = new KeyWordSearchResponse();
        content1.setSid("1001");
        content1.setPayStatus(2);
        KeyWordSearchResponse content2 = new KeyWordSearchResponse();
        content2.setSid("1002");
        content2.setPayStatus(1);
        content2.setBrief("一句话简介");
        return Lists.newArrayList(content1, content2);
    }

    private Map<String, StandardAlbum> buildMap() {
        Map<String, StandardAlbum> map = new HashMap<>();
        StandardAlbum album1 = new StandardAlbum();
        album1.setSourceHot(99.9D);
        album1.setShowTime("20250520");
        map.put("1001", album1);
        StandardAlbum album2 = new StandardAlbum();
        album2.setSourceHot(88.8D);
        album2.setShowTime("20250530");
        map.put("1002", album2);
        return map;
    }


    @Test
    public void handleSubTitleTest() {
        convertResponseService.handleSubTitle(null, 80700);

        Map<String, AlbumRecommendInfo> recommendInfoMap = new HashMap<>();
        AlbumRecommendInfo recommendInfo1 = new AlbumRecommendInfo();
        recommendInfo1.setRecommendInfoDp("url");
        recommendInfoMap.put("1001", recommendInfo1);
        when(albumRankRpcApiProxy.getAlbumRecommendInfo(any(), any())).thenReturn(CompletableFuture.completedFuture(recommendInfoMap));

        List<KeyWordSearchResponse> list = buildList();
        convertResponseService.handleSubTitle(list, 80900);
        assertEquals("url", list.get(0).getRecommendInfoDp());
        assertEquals("一句话简介", list.get(1).getRecommendInfo());
    }


}
