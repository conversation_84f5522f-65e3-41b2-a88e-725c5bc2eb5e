package com.heytap.longvideo.search.service.app;


import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.search.api.LvSearchFeedbackRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchFeedback;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class SearchFeedbackServiceTest {
    @InjectMocks
    private SearchFeedbackService searchFeedbackService;

    @Mock
    private LvSearchFeedbackRpcApi lvSearchFeedbackRpcApi;

    @Mock
    private HttpDataChannel httpClient;

    @Mock
    private SearchProperties searchProperties;

    @Mock
    private UploadFileToOcsComponent fileToOcsComponent;

    @Mock
    private CsvExportConfig csvExportConfig;

    @Before
    public void build() {
        Map<String, String> options = JSON.parseObject(
                "{\"1\":\"搜不到我想要的\",\"2\":\"搜索的结果排序不合理\",\"3\":\"搜索的结果不够丰富\",\"4\":\"搜索的结果不能直接播放\",\"5\":\"希望增加筛选、排序等功能\",\"9\":\"其他问题\"}",
                Map.class);
        when(searchProperties.getFeedbackOption()).thenReturn(options);

        when(csvExportConfig.getBucketName()).thenReturn("bucketName");
        when(csvExportConfig.getEndPoint()).thenReturn("endPoint");
    }

    @Test
    public void collectTest1() {
        Map<String, Object> params = JSON.parseObject(
                "{\"startTime\":\"2024-11-11\", \"endTime\":\"xx\" }",
                Map.class);

        Assertions.assertThrows(RuntimeException.class, () -> searchFeedbackService.collect(params));
    }

    @Test
    public void collectTest2() {
        Map<String, Object> params = JSON.parseObject("{\"cycleDay\":7}", Map.class);

        when(lvSearchFeedbackRpcApi.listByCreateTime(any())).thenReturn(null);

        Assert.assertFalse(searchFeedbackService.collect(params));
    }


    @Test
    public void collectTest3() {
        Map<String, Object> params = JSON.parseObject("{\"cycleDay\":7}", Map.class);

        RpcResult rpcResult = new RpcResult(null);
        when(lvSearchFeedbackRpcApi.listByCreateTime(any())).thenReturn(CompletableFuture.completedFuture(rpcResult));

        Assert.assertTrue(searchFeedbackService.collect(params));
    }

    @Test
    public void collectTest4() {
        Map<String, Object> params = JSON.parseObject("{\"cycleDay\":7}", Map.class);

        List<LvSearchFeedback> list = JSON.parseArray(
                "[{\n" +
                        "    \"type\":9,\n" +
                        "    \"keyword\":\"cx1\",\n" +
                        "    \"note\":\"这里有问题\",\n" +
                        "    \"phone\":\"15010238088\",\n" +
                        "    \"image\":\"http://iot-long-video-original-hn2.s3v2.dg-access-test.wanyol.com/searchFeedback/20241112-111240_black.png\",\n" +
                        "    \"create_time\":\"2024-11-11 21:08:04\"\n" +
                        "}]",
                LvSearchFeedback.class);
        RpcResult<List<LvSearchFeedback>> rpcResult =new RpcResult(list);
        when(lvSearchFeedbackRpcApi.listByCreateTime(any())).thenReturn(CompletableFuture.completedFuture(rpcResult));

        // Workbook.write不兼容Mockito，只能校验抛Error
        try {
            searchFeedbackService.collect(params);
        } catch (Exception|Error e) {
            Assert.assertNotNull(e);
        }
    }


    @Test
    public void uploadOcsTest1() {
        when(fileToOcsComponent.uploadFileToOcs(any(),any(),any(),any(),any(),any(),any(),any())).thenReturn(false);

        Assertions.assertThrows(RuntimeException.class, () -> searchFeedbackService.uploadOcs("name"));

    }


    @Test
    public void uploadOcsTest2() {
        when(fileToOcsComponent.uploadFileToOcs(any(),any(),any(),any(),any(),any(),any(),any())).thenReturn(true);

        Assert.assertEquals("http://bucketName.endPoint/searchFeedback/name", searchFeedbackService.uploadOcs("name"));
    }
}