package com.heytap.longvideo.search.service.common;

import com.heytap.video.ad.common.entity.req.BuriedCommonReqProperty;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyRequest;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponse;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.cpc.video.framework.lib.strategy.StrategyMatchService;
import com.oppo.cpc.video.framework.lib.utils.VipInfoUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({VipInfoUtil.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class StrategyServiceTest {
    @InjectMocks
    StrategyService strategyService;

    @Mock
    private StrategyMatchService strategyMatchService;

    @Test
    public void matchSearchStrategyTest_throw() throws ExecutionException, InterruptedException {
        BuriedCommonReqProperty req = PowerMockito.mock(BuriedCommonReqProperty.class);
        when(req.getAttributeValues()).thenReturn(new AttributeValues());

        PowerMockito.mockStatic(VipInfoUtil.class);
        when(VipInfoUtil.buildVipInfoCf(any(), any(), any())).thenReturn(CompletableFuture.completedFuture(new MatchStrategyRequest()));

        when(strategyMatchService.matchStrategy(any())).thenThrow(new RuntimeException());

        Map<String, MatchStrategyResponseItem> result = strategyService.matchSearchStrategy(
                req, null).get();

        assertTrue(result.isEmpty());
    }

    @Test
    public void matchSearchStrategyTest_empty() throws ExecutionException, InterruptedException {
        BuriedCommonReqProperty req = PowerMockito.mock(BuriedCommonReqProperty.class);
        when(req.getAttributeValues()).thenReturn(new AttributeValues());

        PowerMockito.mockStatic(VipInfoUtil.class);
        when(VipInfoUtil.buildVipInfoCf(any(), any(), any())).thenReturn(CompletableFuture.completedFuture(new MatchStrategyRequest()));

        MatchStrategyResponse matchStrategyResponse = new MatchStrategyResponse();
        when(strategyMatchService.matchStrategy(any())).thenReturn(CompletableFuture.completedFuture(matchStrategyResponse));

        Map<String, MatchStrategyResponseItem> result = strategyService.matchSearchStrategy(
                req, null).get();

        assertTrue(result.isEmpty());
    }

    @Test
    public void matchSearchStrategyTest() throws ExecutionException, InterruptedException {
        BuriedCommonReqProperty req = PowerMockito.mock(BuriedCommonReqProperty.class);
        when(req.getAttributeValues()).thenReturn(new AttributeValues());
        String[] ids = {"1"};

        PowerMockito.mockStatic(VipInfoUtil.class);
        when(VipInfoUtil.buildVipInfoCf(any(), any(), any())).thenReturn(CompletableFuture.completedFuture(new MatchStrategyRequest()));

        MatchStrategyResponse matchStrategyResponse = new MatchStrategyResponse();
        Map<String, MatchStrategyResponseItem> map = new HashMap<>();
        map.put("1", new MatchStrategyResponseItem());
        matchStrategyResponse.setResult(map);
        when(strategyMatchService.matchStrategy(any())).thenReturn(CompletableFuture.completedFuture(matchStrategyResponse));

        Map<String, MatchStrategyResponseItem> result = strategyService.matchSearchStrategy(req, ids).get();

        assertEquals(1, result.size());
    }
}


