package com.heytap.longvideo.search.service.app;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class ProgramMatchServiceTest {
    @InjectMocks
    ProgramMatchService programMatchService;

    @Test
    public void calcActorOrDirectorScoreTest() {
        int result = programMatchService.calcActorOrDirectorScore("A|B", "B");
        assertEquals(10, result);

        result = programMatchService.calcActorOrDirectorScore("A", "A|B|C");
        assertEquals(6, result);
    }


}
