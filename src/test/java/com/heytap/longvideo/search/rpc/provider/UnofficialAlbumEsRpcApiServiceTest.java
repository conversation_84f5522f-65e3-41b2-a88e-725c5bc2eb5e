package com.heytap.longvideo.search.rpc.provider;

import com.heytap.longvideo.common.lib.rpc.RpcResult;
import com.heytap.longvideo.search.api.entity.UnofficialAlbumEsDTO;
import com.heytap.longvideo.search.infrastructure.cache.UnofficialAlbumEsCache;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


/**
 * <AUTHOR>
 * @date 2025/7/9
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class UnofficialAlbumEsRpcApiServiceTest {

    @Mock
    private UnofficialAlbumService unofficialAlbumService;

    @Mock
    private UnofficialAlbumEsCache unofficialAlbumEsCache;

    @InjectMocks
    private UnofficialAlbumEsRpcApiService unofficialAlbumEsRpcApiService;


    @Test
    public void testSearchBySidsFilterInvalid_Success() {
        // Mock data
        List<String> sids = Arrays.asList("550101160061652994", "513568368561836032", "895654893405253632");

        UnofficialAlbumEs album1 = new UnofficialAlbumEs();
        album1.setSid("550101160061652994");
        album1.setStatus(1);

        UnofficialAlbumEs album2 = new UnofficialAlbumEs();
        album2.setSid("513568368561836032");
        album2.setStatus(1);

        UnofficialAlbumEs album3 = new UnofficialAlbumEs();
        album3.setSid("895654893405253632");
        album3.setStatus(0); // Invalid status

        Map<String, UnofficialAlbumEs> albumMap = new HashMap<>();
        albumMap.put("550101160061652994", album1);
        albumMap.put("513568368561836032", album2);
        albumMap.put("895654893405253632", album3);

        when(unofficialAlbumEsCache.mget(any(), any(), any(), any())).thenReturn(albumMap);

        // Execute
        CompletableFuture<RpcResult<Map<String, UnofficialAlbumEsDTO>>> resultFuture = unofficialAlbumEsRpcApiService.searchBySidsFilterInvalid(sids);
        RpcResult<Map<String, UnofficialAlbumEsDTO>> result = resultFuture.join();

        // Verify
        assertTrue(result.isSuccess());
        assertEquals(2, result.getData().size());
        assertTrue(result.getData().containsKey("550101160061652994"));
        assertTrue(result.getData().containsKey("513568368561836032"));
        assertFalse(result.getData().containsKey("895654893405253632"));
    }

    @Test
    public void testSearchBySidsFilterInvalid_NoValidAlbums() {
        // Mock data
        List<String> sids = Arrays.asList("550101160061652994", "513568368561836032");

        UnofficialAlbumEs album1 = new UnofficialAlbumEs();
        album1.setSid("550101160061652994");
        album1.setStatus(0);

        UnofficialAlbumEs album2 = new UnofficialAlbumEs();
        album2.setSid("513568368561836032");
        album2.setStatus(0);

        Map<String, UnofficialAlbumEs> albumMap = new HashMap<>();
        albumMap.put("550101160061652994", album1);
        albumMap.put("513568368561836032", album2);

        when(unofficialAlbumEsCache.mget(any(), any(), any(), any())).thenReturn(albumMap);

        // Execute
        CompletableFuture<RpcResult<Map<String, UnofficialAlbumEsDTO>>> resultFuture = unofficialAlbumEsRpcApiService.searchBySidsFilterInvalid(sids);
        RpcResult<Map<String, UnofficialAlbumEsDTO>> result = resultFuture.join();

        // Verify
        assertTrue(result.isSuccess());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    public void testSearchBySids_Success() {
        // Mock data
        List<String> sids = Arrays.asList("550101160061652994", "513568368561836032", "895654893405253632");

        UnofficialAlbumEs album1 = new UnofficialAlbumEs();
        album1.setSid("550101160061652994");
        album1.setStatus(1);

        UnofficialAlbumEs album2 = new UnofficialAlbumEs();
        album2.setSid("513568368561836032");
        album2.setStatus(1);

        UnofficialAlbumEs album3 = new UnofficialAlbumEs();
        album3.setSid("895654893405253632");
        album3.setStatus(1);

        List<UnofficialAlbumEs> unofficialAlbumEsList = new ArrayList<>();
        unofficialAlbumEsList.add(album1);
        unofficialAlbumEsList.add(album2);
        unofficialAlbumEsList.add(album3);

        when(unofficialAlbumService.getAlbumList(any())).thenReturn(unofficialAlbumEsList);

        // Execute
        CompletableFuture<RpcResult<Map<String, UnofficialAlbumEsDTO>>> resultFuture = unofficialAlbumEsRpcApiService.searchBySids(sids);
        RpcResult<Map<String, UnofficialAlbumEsDTO>> result = resultFuture.join();

        // Verify
        assertTrue(result.isSuccess());
        assertEquals(3, result.getData().size());
    }
}
