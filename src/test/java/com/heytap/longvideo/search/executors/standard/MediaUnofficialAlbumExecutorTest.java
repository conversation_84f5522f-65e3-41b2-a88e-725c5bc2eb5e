package com.heytap.longvideo.search.executors.standard;

import com.heytap.longvideo.search.executors.unofficial.MediaUnofficialAlbumExecutor;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.concurrent.ExecutionException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class MediaUnofficialAlbumExecutorTest {
    @InjectMocks
    private MediaUnofficialAlbumExecutor mediaUnofficialAlbumExecutor;
    @Mock
    private UnofficialAlbumService unofficialAlbumService;

    @Test
    public void myExecuteTest1() throws ExecutionException, InterruptedException {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setSourceScoreStart(-1.0f);
        StandardResult<PageResponse<StandardAlbumVo>> result = mediaUnofficialAlbumExecutor.myExecute(params).get();
        Assert.assertEquals(400, result.getCode());
    }

    @Test
    public void myExecuteTest2() throws ExecutionException, InterruptedException {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setSourceScoreEnd(11.0f);
        StandardResult<PageResponse<StandardAlbumVo>> result = mediaUnofficialAlbumExecutor.myExecute(params).get();
        Assert.assertEquals(400, result.getCode());
    }

    @Test
    public void myExecuteTest3() throws ExecutionException, InterruptedException {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setProgramTitle("标题");
        params.setOriginStatus("1");
        StandardResult<PageResponse<StandardAlbumVo>> result = mediaUnofficialAlbumExecutor.myExecute(params).get();
        Assert.assertEquals(200, result.getCode());
    }

    @Test
    public void myExecuteTest4() throws Exception {
        SearchStandardAlbumParams params = new SearchStandardAlbumParams();
        params.setProgramTitle("标题");
        params.setOriginStatus("1");
        when(unofficialAlbumService.searchUnofficialAlbum(any())).thenThrow(new RuntimeException());

        StandardResult<PageResponse<StandardAlbumVo>> result = mediaUnofficialAlbumExecutor.myExecute(params).get();
        Assert.assertEquals(400, result.getCode());
    }
}
