package com.heytap.longvideo.search.executors.inside;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.liteflow.cmp.SearchTagCmp;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.inside.SearchByTagRequest;
import com.heytap.longvideo.search.model.param.inside.SearchByTagResponse;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/4/25
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchByTagTest {

    @InjectMocks
    private SearchByTagExecutor searchByTagExecutor;

    @Mock
    private SearchTagCmp searchTagCmp;

    @Test
    public void searchByTagTest() throws BizException, IOException, ExecutionException, InterruptedException {
        SearchByTagRequest request = new SearchByTagRequest();
        request.setKeyword("爱情");
        request.setVipType("videoVip");
        request.setVersion(80200);
        request.setPageIndex(1);
        request.setPageSize(20);
        when(searchTagCmp.getItemByTag(any(), any())).thenReturn(buildSearchTagCmpResult());
        SearchByTagResponse response = searchByTagExecutor.myExecute(request).get();
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getContents());
    }

    private CompletableFuture<SearchInterveneCardResponse> buildSearchTagCmpResult() throws IOException {
        String res = FileUtils.readFileToString(new ClassPathResource("mock/inside/searchTagCmpRes.json").getFile(), "UTF-8");
        SearchInterveneCardResponse searchInterveneCardResponse = JSON.parseObject(res, SearchInterveneCardResponse.class);
        return CompletableFuture.completedFuture(searchInterveneCardResponse);
    }

}
