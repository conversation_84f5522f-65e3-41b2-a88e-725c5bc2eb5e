package com.heytap.longvideo.search.executors.app;

import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.param.app.SearchFeedbackOption;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class SearchFeedbackOptionExecutorTest extends GoblinJunit4BaseTest {
    @Autowired
    private SearchFeedbackOptionExecutor searchFeedbackOptionExecutor;

    @Test
    public void searchFeedbackOptionTest() {
        try {
            List<SearchFeedbackOption> result = searchFeedbackOptionExecutor.myExecute(null).get();
            Assert.assertTrue(CollectionUtils.isNotEmpty(result));
        } catch (Exception e) {
            Assert.fail();
        }
    }
}
