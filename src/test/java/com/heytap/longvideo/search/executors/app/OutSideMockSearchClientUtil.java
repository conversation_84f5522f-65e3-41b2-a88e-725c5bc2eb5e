package com.heytap.longvideo.search.executors.app;

import com.alibaba.fastjson.JSON;
import com.heytap.cpc.dfoob.goblin.core.Goblin;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import esa.rpc.test.support.mock.Mocker;
import org.mockserver.client.MockServerClient;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.containers.MockServerContainer;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.commons.lang.CharEncoding.UTF_8;

public class OutSideMockSearchClientUtil {


    public static MockServerClient buildMockServerClient() {
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        return new MockServerClient(mockServer.getHost(), mockServer.getServerPort());
    }

    /**
     * 结果干预配置 mock
     * @throws IOException
     */
    public static void mockGetOutSearchIntervene() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvSearchInterveneRpcApi.class.getName())
                .methodName("getSearchInterveneList");
        String str = FileUtils.readFileToString(new ClassPathResource("mock/outside/outSideGetSearchIntervene.json").getFile(), UTF_8);
        List<LvSearchIntervene> list = JSON.parseArray(str, LvSearchIntervene.class);
        RpcResult<LvSearchIntervene> response = new RpcResult(0,"success",list);
        mocker.returnValue(response).configure();
    }

    public static void mockNullGetOutSearchIntervene() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvSearchInterveneRpcApi.class.getName())
                .methodName("getSearchInterveneList");

        RpcResult<LvSearchIntervene> response = new RpcResult(0,"success");
        mocker.returnValue(response).configure();
    }

    /**
     * 结果干预中，配置的剧
     * @throws IOException
     */
    public static void mockBySidsFilterInvalid() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(StandardAlbumRpcApi.class.getName())
                .methodName("getBySidsFilterInvalid");
        RpcResult<Map<String/* sid */, StandardAlbum>> resp = new RpcResult(0, "success");

        String str = FileUtils.readFileToString(new ClassPathResource("mock/outside/outSideMockBySidsFilterInvalid.json").getFile(), UTF_8);
        List<StandardAlbum> list = JSON.parseArray(str, StandardAlbum.class);
        Map<String, StandardAlbum> map = list.stream().collect(Collectors.toMap(StandardAlbum::getSid, StandardAlbum -> StandardAlbum));
        resp.setData(map);
        mocker.returnValue(resp).configure();
    }

}
