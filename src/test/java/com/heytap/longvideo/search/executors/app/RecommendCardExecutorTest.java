package com.heytap.longvideo.search.executors.app;

import com.heytap.cpc.dfoob.goblin.core.Goblin;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.LongVideoSearchCard;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import esa.rpc.test.support.mock.Mocker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockserver.client.MockServerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.testcontainers.containers.MockServerContainer;
import util.ArrangeRpcMockerUtil;
import util.HttpMockUtil;
import util.MediaRpcMockerUtil;

import java.io.IOException;

/**
 * <AUTHOR> Yanping
 * @date 2023/10/31
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class RecommendCardExecutorTest extends GoblinJunit4BaseTest {

    @Autowired
    private RecommendCardExecutor recommendCardExecutor;

    @Test
    public void testRecommendByAlgorithm() throws IOException {
        try {
            mockData();
            String code = "{\"sid\":\"625489226036695041\",\"keyword\":\"法医\"}";
            LongVideoSearchCard response = recommendCardExecutor.myExecute(buildRequest(code)).get();
            Assert.assertNotNull(response);
            Assert.assertNotNull(response.getContents());
        } catch (Exception e) {
            log.error("testRecommendByAlgorithm error:", e);
        }
    }

    @Test
    public void testRecommendByOperation() throws IOException {
        mockDataForOperation();
        try {
            String code = "3122";
            LongVideoSearchCard response = recommendCardExecutor.myExecute(buildRequest(code)).get();
            Assert.assertNotNull(response);
            Assert.assertEquals("悦视通节目-unit-test", response.getTitle());
            Assert.assertNotNull(response.getContents());
        } catch (Exception e) {
            log.error("testRecommendByOperation error:", e);
        }
    }

    @Test
    public void testRecommendByContentPool() throws IOException {
        mockDataForContentPool();
        try {
            String code = "3148";
            LongVideoSearchCard response = recommendCardExecutor.myExecute(buildRequest(code)).get();
            Assert.assertNotNull(response);
            Assert.assertEquals("yoli://yoli.com/YoliSearch/search/longRecommendDetail?code=3148&title=高能池", response.getDeepLink());
            Assert.assertNotNull(response.getContents());
        } catch (Exception e) {
            log.error("testRecommendByOperation error:", e);
        }
    }

    private void mockDataForOperation() throws IOException {
        //实时获取mock server的ip和端口
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        ArrangeRpcMockerUtil.getLvSearchInterveneById();
        ArrangeRpcMockerUtil.getSubjectItemForListCardWithMore();
        MediaRpcMockerUtil.getBySidsFilterInvalidV2();
        ArrangeRpcMockerUtil.imageTagRpcMock();

    }

    private void mockData() throws IOException {
        //实时获取mock server的ip和端口
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        HttpMockUtil.mockAlgorithmResponse(new MockServerClient(mockServer.getHost(), mockServer.getServerPort()));
        MediaRpcMockerUtil.getBySidsFilterInvalid();
        ArrangeRpcMockerUtil.imageTagRpcMock();
        ArrangeRpcMockerUtil.getMaterialsRpcMock();

    }

    private void mockDataForContentPool() throws IOException {
        //实时获取mock server的ip和端口
        MockServerContainer mockServer = (MockServerContainer) Goblin.getInstance().getContainerInstance("MOCKSERVER");
        ArrangeRpcMockerUtil.getLvSearchInterveneById();
        ArrangeRpcMockerUtil.getItemsWithCache();
        MediaRpcMockerUtil.getBySidsFilterInvalidV2();
        ArrangeRpcMockerUtil.imageTagRpcMock();
    }

    private InterveneCardParam buildRequest(String code) {
        InterveneCardParam request = new InterveneCardParam();
        request.setCode(code);
        return request;
    }

}
