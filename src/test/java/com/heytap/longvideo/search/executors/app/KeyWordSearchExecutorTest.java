package com.heytap.longvideo.search.executors.app;

import com.heytap.longvideo.search.constants.SearchTabEnum;
import com.heytap.longvideo.search.liteflow.cmp.SearchDuanjuCmp;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.concurrent.ExecutionException;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class KeyWordSearchExecutorTest {
    @InjectMocks
    KeyWordSearchExecutor keyWordSearchExecutor;
    @Mock
    private SearchDuanjuCmp searchDuanjuCmp;

    @Test
    public void myExecute_duanjuSearch() throws BizException, ExecutionException, InterruptedException {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword("短剧名称");
        param.setPageIndex(1);
        param.setSearchType(SearchTabEnum.DUANJU.getCode());
        param.setDuanjuHasMore(true);

        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        searchResponse.setTitle("短剧名称");
        when(searchDuanjuCmp.searchDuanju(any())).thenReturn(Lists.newArrayList(searchResponse));
        SearchResponse result = keyWordSearchExecutor.myExecute(param).get();

        assertEquals(SearchTabEnum.DUANJU.getCode(), result.getSearchTab());
        assertEquals("短剧名称", result.getLongVideoSearchResult().get(0).getTitle());
    }
}
