package com.heytap.longvideo.search.executors.inside;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.inside.SearchUnOfficialAlbumRequest;
import com.heytap.longvideo.search.model.param.inside.SearchUnOfficialAlbumResponse;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchHitsImpl;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> Yanping
 * @date 2025/4/25
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchUnOfficialAlbumExecutorTest {

    @InjectMocks
    private SearchUnOfficialAlbumExecutor searchUnOfficialAlbumExecutor;

    @Mock
    private ElasticsearchRestTemplate restTemplate;

    @Test
    public void searchBySidTest() throws BizException, IOException, ExecutionException, InterruptedException {
        SearchUnOfficialAlbumRequest request = new SearchUnOfficialAlbumRequest();
        request.setSid("1076442421124059136");
        request.setVipType("videoVip");
        request.setVersion(80200);
        when(restTemplate.search(any(NativeSearchQuery.class), eq(UnofficialAlbumEs.class))).thenReturn(buildEsTemplateSearchResult());
        SearchUnOfficialAlbumResponse response = searchUnOfficialAlbumExecutor.myExecute(request).get();
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getUnofficialAlbum());
    }

    private SearchHitsImpl<UnofficialAlbumEs> buildEsTemplateSearchResult() throws IOException {
        String res = FileUtils.readFileToString(new ClassPathResource("mock/inside/esTemplateSearchRes.json").getFile(), "UTF-8");
        return JSON.parseObject(res, new TypeReference<SearchHitsImpl<UnofficialAlbumEs>>(){});
    }
}
