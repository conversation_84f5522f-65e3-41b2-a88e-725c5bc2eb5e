package com.heytap.longvideo.search.executors.app;


import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.entity.es.HotActorEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockserver.client.MockServerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;
import util.HttpMockUtil;
import util.UTLogUtil;
import util.VipRpcMockerUtil;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class OutSideSearchExecutorTest extends GoblinJunit4BaseTest {


    private static MockServerClient mockServerClient;
    @Autowired
    private OutSideSearchExecutor outSideSearchExecutor;
    @Autowired
    private InitService initService;
    @Autowired
    private ElasticsearchRestTemplate restTemplate;
    @Autowired
    private StandardAlbumMapper standardAlbumMapper;
    @Autowired
    private ElasticSearchService elasticsearchService;

    /**
     * 前置mock创建
     *
     * @throws IOException
     */
    @BeforeClass
    public static void mockBeforeClass() throws IOException {
        mockServerClient = OutSideMockSearchClientUtil.buildMockServerClient();
        mockServerClient.reset();
    }

    @Test
    public void OutSideTest() throws IOException {
        mockServerClient.reset();
        mockData(mockServerClient);
        String desc = this.getClass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName();
        log.info(UTLogUtil.logStartInfo(desc));

        // 初始化es的数据
        ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, null);
        IndexOperations indexOperations = restTemplate.indexOps(HotActorEs.class);
        if (!indexOperations.exists()) {
            indexOperations.create();
        }
        try {

            // 中文校验
            CompletableFuture<SearchResponse> responseCf = outSideSearchExecutor.myExecute(buildRequest("法"));
            SearchResponse response = FutureUtil.getFutureIgnoreException(responseCf);
            checkResult(response);
            // pinyin校验
            CompletableFuture<SearchResponse> responseCf1 = outSideSearchExecutor.myExecute(buildRequest("dianyingmeng"));
            SearchResponse response1 = FutureUtil.getFutureIgnoreException(responseCf1);
            checkResult(response1);

            // deepLink校验
            KeyWordSearchParamV2 request = buildRequest("dianyingmeng");
            String versionName = "8.9.2";
            versionName = "40." + versionName;
            request.setVersionName(versionName);
            AttributeValues attributeValues = new AttributeValues();
            attributeValues.setClientFullBrowserVersion(versionName);
            request.setAttributeValues(attributeValues);
            CompletableFuture<SearchResponse> responseCf2 = outSideSearchExecutor.myExecute(request);
            SearchResponse response2 = FutureUtil.getFutureIgnoreException(responseCf2);
            checkDeepLink(response2);
            log.info("OutSideSearchExecutorTest execute success...");

            OutSideMockSearchClientUtil.mockNullGetOutSearchIntervene();
            // deepLink校验
            request = buildRequest("解密中国");
            versionName = "8.9.2";
            versionName = "40." + versionName;
            request.setVersionName(versionName);
            request.setQuickEngineVersion(90100);
            CompletableFuture<SearchResponse> responseCf3 = outSideSearchExecutor.myExecute(request);
            SearchResponse response3 = FutureUtil.getFutureIgnoreException(responseCf3);
            checkUpgradePageUrl(response3);
            log.info("outside_youkumobile_quickEngineVersion_mismatch_test execute success...");
        } catch (Exception e) {
            log.error(UTLogUtil.getExceptionPrefix(), desc, e);
            throw new RuntimeException(e);
        } finally {
            overHandler();
            log.info(UTLogUtil.logEndInfo(desc));
        }
    }

    /**
     * 视频版本满足，快应用不符合，下发升级页，并且拼接自动升级后跳转用的正常dp
     */
    // @Test
    // public void outside_youkumobile_quickEngineVersion_mismatch_test() throws IOException {
    //     mockServerClient.reset();
    //     String desc = this.getClass().getSimpleName() + "." + Thread.currentThread().getStackTrace()[1].getMethodName();
    //     log.info(UTLogUtil.logStartInfo(desc));
    //
    //     // rpc mock
    //     OutSideMockSearchClientUtil.mockBySidsFilterInvalid();
    //     VipRpcMockerUtil.mockUserVipInfo("mock/queryUserVipInfo.json");
    //     HttpMockUtil.mockMatchResourceStrategyResponse(mockServerClient);
    //
    //     // 初始化es的数据
    //     ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, null);
    //     IndexOperations indexOperations = restTemplate.indexOps(HotActorEs.class);
    //     if (!indexOperations.exists()) {
    //         indexOperations.create();
    //     }
    //     try {
    //         // deepLink校验
    //         KeyWordSearchParamV2 request = buildRequest("解密中国");
    //         String versionName = "8.9.2";
    //         versionName = "40." + versionName;
    //         request.setVersionName(versionName);
    //         request.setQuickEngineVersion(90100);
    //         AttributeValues attributeValues = new AttributeValues();
    //         attributeValues.setClientFullBrowserVersion(versionName);
    //         request.setAttributeValues(attributeValues);
    //         CompletableFuture<SearchResponse> responseCf2 = outSideSearchExecutor.myExecute(request);
    //         SearchResponse response = FutureUtil.getFutureIgnoreException(responseCf2);
    //         checkUpgradePageUrl(response);
    //         log.info("outside_youkumobile_quickEngineVersion_mismatch_test execute success...");
    //
    //     } catch (Exception e) {
    //         log.error(UTLogUtil.getExceptionPrefix(), desc, e);
    //         throw new RuntimeException(e);
    //     } finally {
    //         overHandler();
    //         log.info(UTLogUtil.logEndInfo(desc));
    //     }
    // }

    private void checkUpgradePageUrl(SearchResponse response) {
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getLongVideoSearchResult());
        List<KeyWordSearchResponse> searchResult = response.getLongVideoSearchResult();
        KeyWordSearchResponse searchResponse = searchResult.get(0);
        Assert.assertNotNull(searchResponse);
        Assert.assertEquals("youkumobile", searchResponse.getSource());
        Assert.assertTrue(searchResponse.getDeepLink().contains("yoli://yoli.com/yoli/h5?url=https%3A%2F%2Fdhfs-test-cpc.wanyol.com"));
        Assert.assertTrue(searchResponse.getDeepLink().contains("normalDp"));
    }


    private void checkDeepLink(SearchResponse response) {
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getLongVideoSearchResult());
        KeyWordSearchResponse keyWordSearchResponse = response.getLongVideoSearchResult().get(0);
        // 验证是否包含三方跳转新详情页参数
        Assert.assertTrue(keyWordSearchResponse.getDeepLink().contains("yoliDpDetailPageStyle=2&yoliMediaType=quansou"));
    }

    /**
     * 结束处理
     */
    private void overHandler() {
        elasticsearchService.deleteIndex(StandardAlbumEs.class);
        mockServerClient.reset();
//        mockServerClient.close();
    }

    /**
     * 结果校验
     *
     * @param response
     */
    private void checkResult(SearchResponse response) {
        Assert.assertNotNull("error OutSideSearchExecutorTest 端外搜索结果异常", response);
        Assert.assertNotNull("error OutSideSearchExecutorTest 端外搜索结果数据为空", response.getLongVideoSearchResult());
        List<KeyWordSearchResponse> searchResult = response.getLongVideoSearchResult();
        Assert.assertTrue("error OutSideSearchExecutorTest 端外搜索顺序异常", "936393417615912960".equals(searchResult.get(0).getSid()));
        Assert.assertTrue("error OutSideSearchExecutorTest 端外搜索顺序异常", "936393415476817920".equals(searchResult.get(1).getSid()));
    }

    /**
     * 构建请求
     *
     * @return
     */
    private KeyWordSearchParamV2 buildRequest(String keyWord) {
        KeyWordSearchParamV2 param = new KeyWordSearchParamV2();
        param.setKeyword(keyWord);
        param.setIsOut(1);
        param.setVersion(71400);
        param.setRequestId("123456789");
        param.setAppId("quansou_sousuozhida");
        param.setPageSize(3);
        return param;
    }


    /**
     * mock 初始化数据
     *
     * @throws IOException
     */
    public static void mockData(MockServerClient mockServerClient) throws IOException {
        OutSideMockSearchClientUtil.mockGetOutSearchIntervene();
        OutSideMockSearchClientUtil.mockBySidsFilterInvalid();
        VipRpcMockerUtil.mockUserVipInfo("mock/queryUserVipInfo.json");
        HttpMockUtil.mockMatchResourceStrategyResponse(mockServerClient);
    }


}
