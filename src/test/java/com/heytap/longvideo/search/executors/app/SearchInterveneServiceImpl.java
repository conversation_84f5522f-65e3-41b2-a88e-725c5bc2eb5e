package com.heytap.longvideo.search.executors.app;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import static org.apache.commons.lang.CharEncoding.UTF_8;

/**
 * <AUTHOR> Yanping
 * @date 2024/4/29
 */
@Slf4j
public class SearchInterveneServiceImpl {

    public RpcResult<LvSearchIntervene> getById(Integer id) {
        RpcResult<LvSearchIntervene> resp = new RpcResult(0, "success");
        try {
            String str;
            if (id == 3122) {
                str = FileUtils.readFileToString(new ClassPathResource("mock/lvSearchInterveneForRecommend.json").getFile(), UTF_8);
                LvSearchIntervene result = JSON.parseObject(str, LvSearchIntervene.class);
                resp.setData(result);
            } else {
                str = FileUtils.readFileToString(new ClassPathResource("mock/lvSearchInterveneForRecommendV2.json").getFile(), UTF_8);
                LvSearchIntervene result = JSON.parseObject(str, LvSearchIntervene.class);
                resp.setData(result);
            }
            return resp;
        } catch (Exception e) {
            log.error("LvSearchIntervene rpc getById mocker error ", e);
            return null;
        }
    }
}
