package com.heytap.longvideo.search.infrastructure.cache;

import com.heytap.longvideo.search.config.EsMediaCacheConfig;
import com.heytap.longvideo.search.infrastructure.constants.CacheKeyConstants;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import redis.clients.jedis.JedisCluster;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


/**
 * <AUTHOR>
 * @date 2025/7/9
 */
@RunWith(MockitoJUnitRunner.class)
public class BaseMediaCacheTest {
    @Mock
    private JedisCluster jedisCluster;

    @Mock
    private EsMediaCacheConfig esMediaCacheConfig;

    @Spy
    @InjectMocks
    private UnofficialAlbumEsCache unofficialAlbumEsCache;

    @BeforeEach
    public void setUp() {
        when(esMediaCacheConfig.getAlbumEsCacheTtl()).thenReturn(3600);
        when(esMediaCacheConfig.getMaxCacheHashTimes()).thenReturn(3600);
    }

    @Test
    public void testGet_HitCache() {
        // Mock data
        String id = "550101160061652994";
        String cacheKey = CacheKeyConstants.UNOFFICIAL_ALBUM_ES_KEY_PREFIX + id;
        UnofficialAlbumEs expectedData = new UnofficialAlbumEs();
        expectedData.setSid(id);
        String cacheData = JsonUtil.toJson(expectedData);

        when(jedisCluster.get(cacheKey)).thenReturn(cacheData);

        // Execute
        UnofficialAlbumEs result = unofficialAlbumEsCache.get(id, UnofficialAlbumEs.class, null);

        // Verify
        assertNotNull(result);
        assertEquals(id, result.getSid());
    }

    @Test
    public void testGet_MissCache_LoadFromCacheLoader() {
        // Mock data
        String id = "550101160061652994";
        String cacheKey = CacheKeyConstants.UNOFFICIAL_ALBUM_ES_KEY_PREFIX + id;
        UnofficialAlbumEs expectedData = new UnofficialAlbumEs();
        expectedData.setSid(id);
        String cacheData = JsonUtil.toJson(expectedData);

        when(jedisCluster.get(cacheKey)).thenReturn(null);
        when(esMediaCacheConfig.getAlbumEsCacheTtl()).thenReturn(3600);
        when(esMediaCacheConfig.getMaxCacheHashTimes()).thenReturn(3600);
        when(jedisCluster.setex(anyString(), anyInt(), anyString())).thenReturn("OK");

        // Execute
        UnofficialAlbumEs result = unofficialAlbumEsCache.get(id, UnofficialAlbumEs.class, sid -> expectedData);

        // Verify
        assertNotNull(result);
        assertEquals(id, result.getSid());
    }

    @Test
    public void testMget_HitCache() {
        // Mock data
        List<String> ids = Arrays.asList("550101160061652994", "513568368561836032");
        List<String> keys = ids.stream().map(id -> CacheKeyConstants.UNOFFICIAL_ALBUM_ES_KEY_PREFIX + id)
                .collect(Collectors.toList());
        List<String> cacheDataList = ids.stream()
                .map(id -> {
                    UnofficialAlbumEs data = new UnofficialAlbumEs();
                    data.setSid(id);
                    return JsonUtil.toJson(data);
                })
                .collect(Collectors.toList());

        // 使用 ArgumentCaptor 捕获实际传入的参数
        ArgumentCaptor<String[]> keysCaptor = ArgumentCaptor.forClass(String[].class);
        when(jedisCluster.mgetExt(keysCaptor.capture())).thenReturn(cacheDataList);

        // Execute
        List<UnofficialAlbumEs> result = unofficialAlbumEsCache.mget(ids, UnofficialAlbumEs.class);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(data -> ids.contains(data.getSid())));
    }

    @Test
    public void testMget_MissCache() {
        // Mock data
        List<String> ids = Arrays.asList("550101160061652994", "513568368561836032");
        List<String> keys = ids.stream().map(id -> CacheKeyConstants.UNOFFICIAL_ALBUM_ES_KEY_PREFIX + id)
                .collect(Collectors.toList());

        // 使用 ArgumentCaptor 捕获实际传入的参数
        ArgumentCaptor<String[]> keysCaptor = ArgumentCaptor.forClass(String[].class);
        when(jedisCluster.mgetExt(keysCaptor.capture())).thenReturn(Collections.emptyList());

        // Execute
        List<UnofficialAlbumEs> result = unofficialAlbumEsCache.mget(ids, UnofficialAlbumEs.class);

        // Verify
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    @Test
    public void testMgetWithCacheLoader_PartialCacheHit() {
        // Mock data
        List<String> ids = Arrays.asList("550101160061652994", "513568368561836032");
        List<String> keys = ids.stream().map(id -> CacheKeyConstants.UNOFFICIAL_ALBUM_ES_KEY_PREFIX + id)
                .collect(Collectors.toList());

        // Only the first ID is in the cache
        UnofficialAlbumEs cachedData = new UnofficialAlbumEs();
        cachedData.setSid("550101160061652994");
        String cacheData = JsonUtil.toJson(cachedData);

        // 使用 ArgumentCaptor 捕获实际传入的参数
        ArgumentCaptor<String[]> keysCaptor = ArgumentCaptor.forClass(String[].class);
        when(jedisCluster.mgetExt(keysCaptor.capture())).thenReturn(Arrays.asList(cacheData, null));

        // Mock cacheLoader to return data for the missing ID
        Map<String, UnofficialAlbumEs> loadedDataMap = new HashMap<>();
        UnofficialAlbumEs loadedData = new UnofficialAlbumEs();
        loadedData.setSid("513568368561836032");
        loadedDataMap.put("513568368561836032", loadedData);

        // Execute
        Map<String, UnofficialAlbumEs> result = unofficialAlbumEsCache.mget(ids, UnofficialAlbumEs.class, UnofficialAlbumEs::getSid, sidList -> loadedDataMap);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("550101160061652994"));
        assertTrue(result.containsKey("513568368561836032"));
        assertEquals("550101160061652994", result.get("550101160061652994").getSid());
        assertEquals("513568368561836032", result.get("513568368561836032").getSid());
    }
}
