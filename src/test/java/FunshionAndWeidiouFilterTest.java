import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.liteflow.cmp.SearchAlbumCmp;
import com.heytap.longvideo.search.liteflow.cmp.SearchRecommendCmp;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.app.SuggestService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import esa.rpc.test.support.mock.Mocker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;
import util.FunshionAndWeidiouFilterMockUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static esa.restlight.plugin.browser.utils.JacksonUtils.objectMapper;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2024/11/20 下午6:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class FunshionAndWeidiouFilterTest extends GoblinJunit4BaseTest {

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Autowired
    private SearchAlbumCmp searchAlbumCmp;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private StandardAlbumMapper standardAlbumMapper;

    @Autowired
    private InitService initService;

    @Autowired
    private SuggestService suggestService;

    @Autowired
    private SearchRecommendCmp searchRecommendCmp;

    @Autowired
    private ConvertResponseService convertResponseService;

    @Test
    public void testFunshionAndWeidiouFilterBySourceAndVersion() {
        /**
         * 低版本过滤
         */
        String source = "weidiou";
        Integer version = 71600;
        boolean res1 = funshionLongVideoAndWeidiouFilterService.filterItemBySource(source, version);
        Assert.assertTrue(res1);

        /**
         * 高版本下发
         */
        source = "weidiou";
        version = 80100;
        boolean res2 = funshionLongVideoAndWeidiouFilterService.filterItemBySource(source, version);
        Assert.assertFalse(res2);
        /**
         * 低版本过滤
         */
        source = "funshion_lv";
        version = 71600;
        boolean res3 = funshionLongVideoAndWeidiouFilterService.filterItemBySource(source, version);
        Assert.assertTrue(res3);
        /**
         * 高版本下发
         */
        source = "funshion_lv";
        version = 80100;
        boolean res4 = funshionLongVideoAndWeidiouFilterService.filterItemBySource(source, version);
        Assert.assertFalse(res4);
        /**
         * source不满足，不过滤
         */
        source = "weidiou1";
        version = 80100;
        boolean res5 = funshionLongVideoAndWeidiouFilterService.filterItemBySource(source, version);
        Assert.assertFalse(res5);
    }

    @Test
    public void testFunshionAndWeidiouFilterByVersion() {
        /**
         * 高版本下发
         */
        Integer version = 80100;
        boolean res1 = funshionLongVideoAndWeidiouFilterService.filterItem(version);
        Assert.assertFalse(res1);
        /**
         * 低版本过滤
         */
        version = 71600;
        boolean res2 = funshionLongVideoAndWeidiouFilterService.filterItem(version);
        Assert.assertTrue(res2);
        /**
         *  verion为空直接过滤
         */
        version = null;
        boolean res3 = funshionLongVideoAndWeidiouFilterService.filterItem(version);
        Assert.assertTrue(res3);
    }

    /**
     * /search/v2/searchByKeyword  搜索接口
     */
    @Test
    public void testSearchV2SearchByKeyWord() throws JsonProcessingException {
        String json = "{\"attachments\":{\"content-length\":\"0\",\"t-request-currentTimeMillis\":\"1732100819119\",\"t-CIRCUIT_TRACE_REQUEST_ID\":\"172916347033516683ede2181b133\",\"t-route-tag\":\"default\",\"REQUEST_RECEIVE_TIME\":\"1732100819118\",\"X-Proto\":\"HTTP\",\"t-CIRCUIT_TRACE_TRACE_ID\":\"f51adf485722118618682edb36606f0d\",\"t-CIRCUIT_TRACE_USER_IP\":\"**************\",\"X-Real-IP\":\"************\",\"t-gateway-internet-traffic\":\"true\",\"t-current-path\":\"video-youli-service\",\"t-req-ttl\":\"3000\",\"X-Forwarded-Proto\":\"http\",\"Connection\":\"keep-alive\",\"t-CIRCUIT_TRACE_TAG_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;\",\"Host\":\"vod-mobile-test.wanyol.com\",\"t-CIRCUIT_TRACE_BUUID\":\"0\",\"t-request-id\":\"560a748eeaddcacfa2096f5913706d3a\",\"x-user-ip\":\"************\",\"traceparent\":\"00-560a748eeaddcacfa2096f5913706d3a-9bb855f67191c4dd-00\",\"X-Forwarded-For\":\"************\",\"t_route_tag\":\"default\",\"t-level-id\":\"373311047.********.0.50\",\"t-CIRCUIT_TRACE_PATH_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;esa-rpc\",\"accept-encoding\":\"gzip\"},\"buuid\":\"0\",\"session\":\"eyJzIjoidG91dGlhbyIsIm9wcG8iOiJvZnM9ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFkSGx3SWpveExDSmlkV2xrSWpveE5USXlPVE14TnpneE9UTXpPREkyTXpBMExDSmhkV1FpT2lKMmFXUmxieUlzSW5abGNpSTZNaXdpY21GMElqb3hOekkzTkRJeE56TXdMQ0oxYm0waU9pSlBVRkJQWHpFeE1USTVNREEwTmpRaUxDSnBaQ0k2SWpFeE1USTVNREEwTmpRaUxDSmxlSEFpT2pFM01qa3hPVEl5Tmprc0ltUmpJam9pZEdWemRDSjkucnZIam9WTXQ0MmVUZkJ1QXZWWmxkVTBmcElfY05Gb25ZT2FBcnRQYzhJSSIsInRvdXRpYW8iOiJhX3Q9QUVwdVh0MjZUaFN0Y0tlU0VpVVNNdFY3ZTZTRHRKdDJIRWo2ZmdRTWdLcGlHSmtLWTdZUkhIbzJWZGdiYzFZNjNFOHlmVmZ3VjsxMTEyOTAwNDY0IiwiaW5mbyI6eyJydCI6IjE3MjkxNjM0NjkiLCJmdCI6IjE3Mjc0MjE3MzAiLCJ1biI6Ik9QUE9fMTExMjkwMDQ2NCIsInVpZCI6IjExMTI5MDA0NjQiLCJidXVpZCI6IjE1MjI5MzE3ODE5MzM4MjYzMDQiLCJzaWduIjoiOUM1RDNEMjExMUFBQURGNzZGRjA4MDhEQzNDNTUzRjAifX0=\",\"hasMore\":0,\"pageSize\":2,\"cookies\":{},\"feedssession\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3MjkxOTIyNjksImRjIjoidGVzdCJ9.rvHjoVMt42eTfBuAvVZldU0fpI_cNFonYOaArtPc8II\",\"searchTypeEnum\":\"DEFAULT\",\"dv\":\"\",\"quickEngineVersion\":90200,\"requestId\":\"172916347033516683ede2181b133\",\"appId\":\"video\",\"strategyResult\":{},\"keyword\":\"刘伯温\",\"deviceType\":0,\"method\":\"\",\"searchType\":\"1\",\"f\":\"json\",\"attributeValues\":{\"buuid\":0,\"channel\":\"OPPO\",\"pkg\":\"com.android.browser\",\"ouidStatus\":true,\"newsSource\":\"yidian\",\"nightModel\":false,\"deviceType\":0,\"systemLanguage\":\"zh-CN\",\"browserLanguage\":\"zh-CN\",\"ip\":\"************\",\"noImageMode\":false,\"defaultChannel\":true},\"vipType\":\"default\",\"scookieIgnoreException\":{\"s\":\"toutiao\",\"oppo\":\"ofs=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3MjkxOTIyNjksImRjIjoidGVzdCJ9.rvHjoVMt42eTfBuAvVZldU0fpI_cNFonYOaArtPc8II\",\"toutiao\":\"a_t=AEpuXt26ThStcKeSEiUSMtV7e6SDtJt2HEj6fgQMgKpiGJkKY7YRHHo2Vdgbc1Y63E8yfVfwV;1112900464\",\"info\":{\"uid\":\"1112900464\",\"rt\":\"1729163469\",\"buuid\":1522931781933826304,\"sign\":\"9C5D3D2111AAADF76FF0808DC3C553F0\",\"un\":\"OPPO_1112900464\",\"ft\":\"1727421730\"}},\"versionTag\":5,\"version\":80100,\"pageIndex\":1,\"isOut\":0,\"scookie\":{\"s\":\"toutiao\",\"oppo\":\"ofs=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3MjkxOTIyNjksImRjIjoidGVzdCJ9.rvHjoVMt42eTfBuAvVZldU0fpI_cNFonYOaArtPc8II\",\"toutiao\":\"a_t=AEpuXt26ThStcKeSEiUSMtV7e6SDtJt2HEj6fgQMgKpiGJkKY7YRHHo2Vdgbc1Y63E8yfVfwV;1112900464\",\"info\":{\"uid\":\"1112900464\",\"rt\":\"1729163469\",\"buuid\":1522931781933826304,\"sign\":\"9C5D3D2111AAADF76FF0808DC3C553F0\",\"un\":\"OPPO_1112900464\",\"ft\":\"1727421730\"}}}";
        KeyWordSearchParamV2 param = objectMapper.readValue(json, KeyWordSearchParamV2.class);
        param.setVersionTag(9);
        param.setKeyword("刘伯温");
        List<ProgramAlbumEs> initData = new ArrayList<>();
        String esData = "{\"actor\":\"高强|鲍国安\",\"actorPinyin\":\"高强|鲍国安\",\"area\":\"中国内地\",\"brief\":\"神机妙算刘伯温精彩演绎\",\"contentType\":\"tv\",\"copyrightCode\":\"weidiou\",\"director\":\"朱建新\",\"directorPinyin\":\"朱建新\",\"epstitle\":\"神机妙算刘伯温精彩演绎\",\"featureType\":1,\"functionScore\":1.08,\"hasVirtualSid\":0,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=2f60f3e3035a99277806c174a56115ca&w=0&h=0\",\"isHot\":0,\"isSeries\":0,\"language\":\"普通话\",\"markCode\":\"funshion_vip_1\",\"payStatus\":1,\"programInfo\":\"全 18 集\",\"releScore\":120.30802,\"seriesTitle\":{\"input\":[\"刘伯温\"],\"weight\":108},\"seriesTitlePinyin\":{\"input\":[\"刘伯温\"],\"weight\":108},\"showTime\":\"199206\",\"sid\":\"1074120979346804736\",\"sortDefine\":0.0,\"source\":\"weidiou\",\"sourceScore\":7.6,\"status\":1,\"suggestTitlePinyin\":{\"input\":[\"刘伯温\"],\"weight\":108},\"tags\":\"历史\",\"thirdDate\":0,\"title\":\"刘伯温\",\"titlePinyin\":\"刘伯温\",\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=30e095186c97c5f4e8fad9ddbcf98ca1&w=0&h=0\",\"vipType\":1,\"virtualSid\":\"1074120979346804736\",\"year\":1992}";
        ProgramAlbumEs programAlbumEs = JSONObject.parseObject(esData, ProgramAlbumEs.class);
        initData.add(programAlbumEs);
        ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, initData);
        List<ProgramAlbumEs> search1 = searchAlbumCmp.search(param);
        Assert.assertTrue(search1.isEmpty());
        param.setVersionTag(10);
        param.setKeyword("刘伯温");
        List<ProgramAlbumEs> search2 = searchAlbumCmp.search(param);
        Assert.assertEquals("weidiou", search2.get(0).getSource());
    }

    /**
     * /search/suggest  搜索提示词接口
     */
    @Test
    public void testSearchSuggest() throws JsonProcessingException {
        String paramJson = "{\"appId\":\"video\",\"appVersion\":50000,\"attachments\":{\"content-length\":\"0\",\"t-request-currentTimeMillis\":\"1732106389397\",\"t-CIRCUIT_TRACE_REQUEST_ID\":\"*****************************\",\"t-route-tag\":\"default\",\"REQUEST_RECEIVE_TIME\":\"1732106389395\",\"X-Proto\":\"HTTP\",\"t-CIRCUIT_TRACE_TRACE_ID\":\"1734f726440fa28011826a178384d83b\",\"t-CIRCUIT_TRACE_USER_IP\":\"**************\",\"X-Real-IP\":\"************\",\"t-gateway-internet-traffic\":\"true\",\"t-current-path\":\"video-youli-service\",\"t-req-ttl\":\"3000\",\"X-Forwarded-Proto\":\"http\",\"Connection\":\"keep-alive\",\"t-CIRCUIT_TRACE_TAG_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;\",\"Host\":\"vod-mobile-test.wanyol.com\",\"t-CIRCUIT_TRACE_BUUID\":\"0\",\"t-request-id\":\"f2215d7a32d932fb9907a0869a3f9571\",\"x-user-ip\":\"************\",\"traceparent\":\"00-f2215d7a32d932fb9907a0869a3f9571-6a2812ad75872319-00\",\"X-Forwarded-For\":\"************\",\"t_route_tag\":\"default\",\"t-level-id\":\"674056260.********.0.50\",\"t-CIRCUIT_TRACE_PATH_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;esa-rpc\",\"accept-encoding\":\"gzip\"},\"attributeValues\":{\"browserLanguage\":\"zh-CN\",\"buuid\":0,\"channel\":\"OPPO\",\"defaultChannel\":true,\"deviceType\":0,\"ip\":\"************\",\"newsSource\":\"yidian\",\"nightModel\":false,\"noImageMode\":false,\"ouidStatus\":true,\"pkg\":\"com.android.browser\",\"systemLanguage\":\"zh-CN\"},\"f\":\"json\",\"feedssession\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3MjkxNjA4NTAsImRjIjoidGVzdCJ9.difNitwZlpK77rXJmVNOLxcfc6eDcyF1XK7RUWh3Zgs\",\"hasMore\":0,\"isOut\":0,\"keyword\":\"如果历史\",\"method\":\"\",\"offset\":0,\"pageNo\":0,\"pageSize\":3,\"quickEngineVersion\":90200,\"requestId\":\"*****************************\",\"scookie\":{\"info\":{\"buuid\":1522931781933826304,\"ft\":\"1727421730\",\"rt\":\"1729132051\",\"sign\":\"6064E07D32A77E5EEC8A88285FAD63D1\",\"uid\":\"1112900464\",\"un\":\"OPPO_1112900464\"},\"oppo\":\"ofs=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3MjkxNjA4NTAsImRjIjoidGVzdCJ9.difNitwZlpK77rXJmVNOLxcfc6eDcyF1XK7RUWh3Zgs\",\"s\":\"toutiao\",\"toutiao\":\"a_t=A6c3b143SfNXab9H2YYXbMKHaQSw44kpBtK3hAXBHJYyd1d1izPTeLU2HoXsMz3J7nNtkxkDX;1112900464\"},\"scookieIgnoreException\":{\"$ref\":\"$.scookie\"},\"searchTypeEnum\":\"DEFAULT\",\"session\":\"eyJzIjoidG91dGlhbyIsIm9wcG8iOiJvZnM9ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFkSGx3SWpveExDSmlkV2xrSWpveE5USXlPVE14TnpneE9UTXpPREkyTXpBMExDSmhkV1FpT2lKMmFXUmxieUlzSW5abGNpSTZNaXdpY21GMElqb3hOekkzTkRJeE56TXdMQ0oxYm0waU9pSlBVRkJQWHpFeE1USTVNREEwTmpRaUxDSnBaQ0k2SWpFeE1USTVNREEwTmpRaUxDSmxlSEFpT2pFM01qa3hOakE0TlRBc0ltUmpJam9pZEdWemRDSjkuZGlmTml0d1pscEs3N3JYSm1WTk9MeGNmYzZlRGN5RjFYSzdSVVdoM1pncyIsInRvdXRpYW8iOiJhX3Q9QTZjM2IxNDNTZk5YYWI5SDJZWVhiTUtIYVFTdzQ0a3BCdEszaEFYQkhKWXlkMWQxaXpQVGVMVTJIb1hzTXozSjduTnRreGtEWDsxMTEyOTAwNDY0IiwiaW5mbyI6eyJydCI6IjE3MjkxMzIwNTEiLCJmdCI6IjE3Mjc0MjE3MzAiLCJ1biI6Ik9QUE9fMTExMjkwMDQ2NCIsInVpZCI6IjExMTI5MDA0NjQiLCJidXVpZCI6IjE1MjI5MzE3ODE5MzM4MjYzMDQiLCJzaWduIjoiNjA2NEUwN0QzMkE3N0U1RUVDOEE4ODI4NUZBRDYzRDEifX0=\",\"strategyResult\":{},\"version\":80100,\"versionTag\":5}";
        KeyWordSearchParam param = objectMapper.readValue(paramJson, KeyWordSearchParam.class);
        param.setKeyword("如果历史");
        param.setVersion(80100);
        List<ProgramAlbumEs> initData = new ArrayList<>();
        String esData = "{\"actor\":\"\",\"area\":\"内地\",\"brief\":\"中华历史 源远流长\",\"contentType\":\"kids\",\"copyrightCode\":\"funshion_lv\",\"director\":\"\",\"epstitle\":\"中华历史 源远流长\",\"featureType\":1,\"functionScore\":1.17,\"hasVirtualSid\":0,\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=90c2349d1e781d34b0228daf274dab2d&w=0&h=0\",\"isHot\":0,\"isSeries\":0,\"language\":\"普通话\",\"markCode\":\"\",\"payStatus\":0,\"programInfo\":\"全 12 集\",\"releScore\":38.261353,\"seriesTitle\":{\"input\":[\"如果历史是一群喵 第八季\"],\"weight\":117},\"seriesTitlePinyin\":{\"input\":[\"如果历史是一群喵 第八季\"],\"weight\":117},\"showTime\":\"202204\",\"sid\":\"1056435171550744576\",\"sortDefine\":0.0,\"source\":\"funshion_lv\",\"sourceScore\":8.1,\"status\":1,\"suggestTitlePinyin\":{\"input\":[\"如果历史是一群喵 第八季\"],\"weight\":117},\"tags\":\"早教\",\"thirdDate\":0,\"title\":\"如果历史是一群喵 第八季\",\"titlePinyin\":\"如果历史是一群喵 第八季\",\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=63785eff53e27f35a4f5bfdc32c3d355&w=0&h=0\",\"vipType\":0,\"virtualSid\":\"1056435171550744576\",\"year\":2022}";
        ProgramAlbumEs programAlbumEs = JSONObject.parseObject(esData, ProgramAlbumEs.class);
        initData.add(programAlbumEs);
        ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, false, initData);
        List<String> suggest1 = suggestService.searchForSuggest("如果历史", new ArrayList<>(), param);
        Assert.assertTrue(suggest1.contains("如果历史是一群喵 第八季"));

        param.setKeyword("如果历史");
        param.setVersion(80000);
        List<String> suggest2 = suggestService.searchForSuggest("如果历史", new ArrayList<>(), param);
        Assert.assertEquals(0, suggest2.size());
    }

    //    推荐卡二级页：/search/v2/recommendCardDetail
    @Test
    public void testRecommendCardDetail() throws Exception {
        ArrayList<Mocker> mockers = new ArrayList<>();
        FunshionAndWeidiouFilterMockUtil.lvContentItemRpcApiMockForRecommendCard(mockers);
        FunshionAndWeidiouFilterMockUtil.standardAlbumRpcApiMockForRecommendCard(mockers);
        String context = "{\"bannerList\":[],\"baseSearchResult\":[{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其他\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"sports\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fmzc00200bxjvzrb.html\",\"directors\":\"\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/mzc00200bxjvzrb1619012000059/0\",\"languages\":\"其他\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"\",\"sid\":\"613545255035293696\",\"sortDefine\":48.06119918823242,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"\",\"tags\":\"NBA|腾讯视频体育|体育\",\"thirdDate\":1,\"title\":\"NBA热点推荐\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/mzc00200bxjvzrb1619011978557/450\",\"year\":2021},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"sports\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fzbik3c38m7tqoty.html\",\"directors\":\"\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/zbik3c38m7tqoty1578633292/0\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"\",\"sid\":\"659261289893404672\",\"sortDefine\":43.319061279296875,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"\",\"tags\":\"NBA|精彩篮球|体育\",\"thirdDate\":1,\"title\":\"NBA最热视频推荐\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/zbik3c38m7tqoty1540180353/450\",\"year\":2020},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"日本\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F8298-0-0.html\",\"directors\":\"及川博則\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20230331-1/655c95aef708b642c43e1ba47e3b674d.jpg\",\"languages\":\"日语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076446061717016577\",\"sortDefine\":37.837799072265625,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"江口德子|小林树奈子|涩谷谦人|佐々木春香|大塚明夫|宇梶刚士|大塚宁宁|川原和久|佐伯大地|佐野岳|释由美子|前原滉|松金米子|小手伸也|小野塚勇人|松尾贵史|山之内铃|铃木优华|津田宽治|八岛智人|鹫尾真知子\",\"tags\":\"日韩剧\",\"thirdDate\":1,\"title\":\"独活女子的推荐第三季\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20230331-1/655c95aef708b642c43e1ba47e3b674d.jpg\",\"year\":2023},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其他\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fmzc002005c3sgt5.html\",\"directors\":\"\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/mzc002005c3sgt51589876284094/0\",\"languages\":\"普通话\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"更新至1集\",\"sid\":\"480604774073454592\",\"sortDefine\":37.837799072265625,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"\",\"tags\":\"\",\"thirdDate\":1,\"title\":\"福利官推荐\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/mzc002005c3sgt51589875904562/350\",\"year\":2020},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"内地\",\"brief\":\"斗智斗勇缉拿罪犯\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"copyrightCode\":\"iqiyi\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fm.iqiyi.com%2Fsearch.html%3Fsource%3Dinput%26key%3D%E6%96%A9%E9%A3%8E%E8%A1%8C%E5%8A%A8\",\"directors\":\"\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://pic5.iqiyipic.com/image/20220822/d4/93/v_134343682_m_601_m6_480_270.jpg\",\"languages\":\"普通话\",\"linkType\":1,\"payStatus\":0,\"sid\":\"513569015378038784\",\"sortDefine\":33.480464935302734,\"source\":\"iqiyi\",\"sourceScore\":0.0,\"stars\":\"张宗煌|廖顺劲|寒月\",\"tags\":\"动作|悬疑|犯罪|警匪|法律|网络电影|独播|高智商\",\"thirdDate\":1,\"title\":\"斩风行动\",\"verticalIcon\":\"http://pic5.iqiyipic.com/image/20220822/d4/93/v_134343682_m_601_m6_260_360.jpg\",\"year\":2019},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"内地\",\"brief\":\"双A搭档逐梦蓝天\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"iqiyi\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fm.iqiyi.com%2Fsearch.html%3Fsource%3Dinput%26key%3D%E5%90%91%E9%A3%8E%E8%80%8C%E8%A1%8C\",\"directors\":\"王之\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://pic6.iqiyipic.com/image/20231109/c7/cc/a_100493610_m_601_m22_480_270.jpg\",\"languages\":\"其他\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"全39集\",\"sid\":\"713770698014924800\",\"sortDefine\":33.480464935302734,\"source\":\"iqiyi\",\"sourceScore\":0.0,\"stars\":\"王凯|谭松韵|刘畅|晏紫东|邵羽柒|周奇奇|刘钧|成泰燊|波子橙\",\"tags\":\"爱情|励志|都市\",\"thirdDate\":1,\"title\":\"向风而行\",\"verticalIcon\":\"http://pic6.iqiyipic.com/image/20231109/c7/cc/a_100493610_m_601_m22_260_360.jpg\",\"year\":2022},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其他\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fmzc00200f29ejuo.html\",\"directors\":\"吕小品\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/mzc00200f29ejuo1603072844/0\",\"languages\":\"其他\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"更新至220集\",\"sid\":\"544508860916912128\",\"sortDefine\":33.480464935302734,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"刘天池|朱家三|李乃文\",\"tags\":\"\",\"thirdDate\":1,\"title\":\"风行四季\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/mzc00200f29ejuo1603072837/450\",\"year\":2004},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"中国大陆\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"show\",\"copyrightCode\":\"iqiyi\",\"deepLink\":\"heytapbrowser://webpage/view?url=http%3A%2F%2Fwww.iqiyi.com%2Fv_19rrjeo938.html%3Fvfm%3Dm_331_dbdy%26fv%3D4904d94982104144a1548dd9040df241%26amp%3Bsubtype%3D9%26amp%3Btype%3Donline-video%26amp%3Blink2key%3Dc7dfbe2179\",\"directors\":\"耿小强\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p2321648043.jpg\",\"languages\":\"汉语普通话\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1074568450623590400\",\"sortDefine\":33.480464935302734,\"source\":\"douban\",\"sourceScore\":7.4,\"stars\":\"周群|梁永斌|徐立|李晓峰|余声|李彬|亚群|刘刚|马滢|郭德纲|谢楠|张杨果而|欧弟|喻恩泰|裴蓓|安琥|张善为|潘奕\",\"tags\":\"脱口秀\",\"thirdDate\":1,\"title\":\"剧风行动\",\"verticalIcon\":\"https://img3.doubanio.com/view/photo/s_ratio_poster/public/p2321648043.jpg\",\"year\":2005},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"加拿大\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.kkys02.com%2Fplay%2F30648-4-279604.html\",\"directors\":\"克里斯托弗·格斯特\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"https://vres.jxlfl.cn/vod1/vod/cover/20230509/11/43/21/a3c0e90d3145f87e4debd282147be8a6.jpg\",\"languages\":\"\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1074571642262441984\",\"sortDefine\":33.480464935302734,\"source\":\"keke\",\"sourceScore\":0.0,\"stars\":\"Jim|Moret|Stuart|Luce|玛丽·格罗斯|马蒂·贝拉夫斯基\",\"tags\":\"美国|乐队|乔纳森|剧情片|喜剧|喜剧片|歌手|民谣|纪念|音乐会\",\"thirdDate\":1,\"title\":\"风载歌行\",\"verticalIcon\":\"https://vres.jxlfl.cn/vod1/vod/cover/20230509/11/43/21/a3c0e90d3145f87e4debd282147be8a6.jpg\",\"year\":2003},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"内地\",\"brief\":\"破坏敌军“风影”计划\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"iqiyi\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fm.iqiyi.com%2Fsearch.html%3Fsource%3Dinput%26key%3D%E8%BF%BD%E9%A3%8E%E8%A1%8C%E5%8A%A8\",\"directors\":\"张东东\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://pic3.iqiyipic.com/image/20230404/9c/b8/a_100175106_m_601_m7_480_270.jpg\",\"languages\":\"其他\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"全45集\",\"sid\":\"550099967662968832\",\"sortDefine\":33.480464935302734,\"source\":\"iqiyi\",\"sourceScore\":0.0,\"stars\":\"李泰|陈维涵|\",\"tags\":\"剧情|战争|谍战\",\"thirdDate\":1,\"title\":\"追风行动\",\"verticalIcon\":\"http://pic3.iqiyipic.com/image/20230404/9c/b8/a_100175106_m_601_m7_260_360.jpg\",\"year\":2018},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"英国\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F27362-0-0.html\",\"directors\":\"戴维·布莱尔\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20220802-1/790e30d88956a2f9c412f11164e62f9c.jpg\",\"languages\":\"英语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076302256267251712\",\"sortDefine\":31.695247650146484,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"伊万·瑞恩|米洛·吉布森|斯蒂芬妮·马蒂尼|马辛·多洛辛斯基|克里斯托弗·哈德克|丹·柏曼|拉裴尔·德普雷|米歇尔·迪耶克斯|尼古拉斯·法瑞尔|萨姆·霍尔|拉多斯劳·凯姆|迈尔斯·基奥治|斯图尔特·帕克|罗伯特·波特尔|阿德里安·扎仁巴\",\"tags\":\"战争片\",\"thirdDate\":1,\"title\":\"飓风行动2018\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20220802-1/790e30d88956a2f9c412f11164e62f9c.jpg\",\"year\":2018},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"韩国\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F46589-0-0.html\",\"directors\":\"郭安贞\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20220417-1/0c872b43a0d844228eccb6fcb2632338.jpg\",\"languages\":\"韩语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076445522606346241\",\"sortDefine\":29.455747604370117,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"张赫|吴智昊|李多海|李钟赫|金基石\",\"tags\":\"日韩剧\",\"thirdDate\":1,\"title\":\"推奴\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20220417-1/0c872b43a0d844228eccb6fcb2632338.jpg\",\"year\":2010},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"中国大陆\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Ftqtrg3pro880glm%2Fy0035440jiv.html%3Fptag%3Dnewdouban.tv%26amp%3Bsubtype%3D1%26amp%3Btype%3Donline-video\",\"directors\":\"康洪雷\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p1999648179.jpg\",\"languages\":\"汉语普通话\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1074569032419692544\",\"sortDefine\":29.455747604370117,\"source\":\"douban\",\"sourceScore\":8.4,\"stars\":\"濮存昕|张国强|李菁菁|高亚麟|刘威葳|赵倩|李博|李泓良|缪俊杰|李依伊\",\"tags\":\"剧情\",\"thirdDate\":1,\"title\":\"推拿\",\"verticalIcon\":\"https://img1.doubanio.com/view/photo/s_ratio_poster/public/p1999648179.jpg\",\"year\":2013},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"内地\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2F3t92c193tprxnqw.html\",\"directors\":\"文杰\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/3t92c193tprxnqw1553482630/0\",\"languages\":\"普通话\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"全47集\",\"sid\":\"544461654335770624\",\"sortDefine\":29.455747604370117,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"贾乃亮|王鸥|边潇潇\",\"tags\":\"都市|言情|言情\",\"thirdDate\":1,\"title\":\"推手\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/3t92c193tprxnqw1553493853/450\",\"year\":2019},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"台湾\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F35616-0-0.html\",\"directors\":\"李安\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20220528-1/baa7d7a876649ef0a81c2055d52de6e6.jpg\",\"languages\":\"国语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076303719399870465\",\"sortDefine\":29.455747604370117,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"郎雄|王莱|王伯昭|戴布·斯内德|李涵\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推手\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20220528-1/baa7d7a876649ef0a81c2055d52de6e6.jpg\",\"year\":1991},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"内地\",\"brief\":\"金马奖最佳剧情片\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"copyrightCode\":\"iqiyi\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fm.iqiyi.com%2Fsearch.html%3Fsource%3Dinput%26key%3D%E6%8E%A8%E6%8B%BF\",\"directors\":\"娄烨\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://pic8.iqiyipic.com/image/20210708/7f/2b/v_108789019_m_601_m8_480_270.jpg\",\"languages\":\"普通话\",\"linkType\":1,\"payStatus\":0,\"sid\":\"513568129578782721\",\"sortDefine\":29.455747604370117,\"source\":\"iqiyi\",\"sourceScore\":0.0,\"stars\":\"郭晓东|秦昊|张磊|梅婷|黄轩|张垒\",\"tags\":\"内地影史经典|友谊|社会问题|成长|伤感|温暖|文艺|乡村|口碑佳片|人性\",\"thirdDate\":1,\"title\":\"推拿\",\"verticalIcon\":\"http://pic8.iqiyipic.com/image/20210708/7f/2b/v_108789019_m_601_m8_260_360.jpg\",\"year\":2014},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"美国\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F9558-0-0.html\",\"directors\":\"拉敏·巴哈尼\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20230520-1/535762e1dda91355f3b1cde8e0570433.jpg\",\"languages\":\"英语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076300484735848448\",\"sortDefine\":29.196456909179688,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"阿迈德·拉兹维|拉蒂西亚·多瑞拉|CharlesDanielSandoval|AliReza|Farooq&#039;Duke&#039;Muhammad|PanickerUpendran|ArunLal|RaziaMujahid|HassanRazvi|MustafaRazvi|AltafHoussein|BillLewis|AbdelrahmaAbde\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推手推车的男人\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20230520-1/535762e1dda91355f3b1cde8e0570433.jpg\",\"year\":2005},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"英国\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F21876-0-0.html\",\"directors\":\"内详\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20220802-1/4db482b5a2701d5bdafe10dfcd870a48.jpg\",\"languages\":\"英语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076446236858568704\",\"sortDefine\":28.694751739501953,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"内详\",\"tags\":\"欧美剧\",\"thirdDate\":1,\"title\":\"投行风云第二季\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20220802-1/4db482b5a2701d5bdafe10dfcd870a48.jpg\",\"year\":2022},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"美国\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F64810-0-0.html\",\"directors\":\"莉娜·邓纳姆|汀治·克里希南|玛丽·奈姬|EdLilly\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.ffzy888.com/upload/vod/20230314-1/60d2e44875b50bda9c6c52d4a5df934f.jpg\",\"languages\":\"英语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076446226255368192\",\"sortDefine\":28.694751739501953,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"纳巴汉·里兹万|弗雷娅·梅弗|威尔·图德|康纳·麦克尼尔|梁振邦|普莉安卡·伯福德|德里克·里德尔|本·劳埃德-休斯|埃米尔·艾尔-马斯里|安德鲁·巴肯|科勒·康拉迪|乔·赫斯特|柯林·麦克法兰|詹姆斯·梅尔维尔|克里斯·雷利|乔治·韦伯斯特|特雷弗·怀特|HeleneMaksoud|\",\"tags\":\"欧美剧\",\"thirdDate\":1,\"title\":\"投行风云第一季\",\"verticalIcon\":\"http://img.ffzy888.com/upload/vod/20230314-1/60d2e44875b50bda9c6c52d4a5df934f.jpg\",\"year\":2020},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"大陆\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F44646-0-0.html\",\"directors\":\"黄佐临\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20220423-1/074ae94ba93a1a257cbbb88aa7eb618e.jpg\",\"languages\":\"其它\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076432895540908032\",\"sortDefine\":27.701236724853516,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"王兰英|费兴生\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"双推磨\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20220423-1/074ae94ba93a1a257cbbb88aa7eb618e.jpg\",\"year\":1954},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"台湾\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F64204-0-0.html\",\"directors\":\"李安\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.ffzy888.com/upload/vod/20230326-1/f4a518bcc7e7093ee1d72e84f8aa5bac.jpg\",\"languages\":\"国语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076434677050560512\",\"sortDefine\":27.701236724853516,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"郎雄|王莱|王伯昭|戴布·斯内德|李涵\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推手1991\",\"verticalIcon\":\"http://img.ffzy888.com/upload/vod/20230326-1/f4a518bcc7e7093ee1d72e84f8aa5bac.jpg\",\"year\":1991},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"大陆\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F15791-0-0.html\",\"directors\":\"娄烨\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20230106-1/4281768b38aea3787e006ce629cfb37e.jpg\",\"languages\":\"国语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076301793736183808\",\"sortDefine\":27.701236724853516,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"郭晓东|秦昊|张磊|梅婷|黄轩|黄璐|黄军军|姜丹|穆怀鹏\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推拿2014\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20230106-1/4281768b38aea3787e006ce629cfb37e.jpg\",\"year\":2014},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其它\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F65201-0-0.html\",\"directors\":\"阿斯加·法哈蒂\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.ffzy888.com/upload/vod/20230314-1/55b00b50ec09db81f008edef0971fbee.jpg\",\"languages\":\"其它\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076434861931286528\",\"sortDefine\":27.701236724853516,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"谢哈布·侯赛尼|塔拉内·阿里多斯蒂|巴巴克·卡里米|法里德·萨贾蒂·侯赛尼|米娜·沙达蒂\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推销员\",\"verticalIcon\":\"http://img.ffzy888.com/upload/vod/20230314-1/55b00b50ec09db81f008edef0971fbee.jpg\",\"year\":2016},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"大陆\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F63251-0-0.html\",\"directors\":\"邓衍成\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.ffzy888.com/upload/vod/20230410-1/e043f974c7f5c295f6c68833c11672f9.jpg\",\"languages\":\"国语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076444300189028352\",\"sortDefine\":26.271448135375977,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"周群达|吴晓敏|姜大卫|张智尧|计春华\",\"tags\":\"动作片\",\"thirdDate\":1,\"title\":\"镖行天下之风云际会\",\"verticalIcon\":\"http://img.ffzy888.com/upload/vod/20230410-1/e043f974c7f5c295f6c68833c11672f9.jpg\",\"year\":2007},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其他\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"news\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fmzc00200arkd7o0.html\",\"directors\":\"\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/mzc00200arkd7o01587977527692/0\",\"languages\":\"其他\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"更新至19集\",\"sid\":\"478057429469319168\",\"sortDefine\":26.168903350830078,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"\",\"tags\":\"精品栏目|新闻\",\"thirdDate\":1,\"title\":\"推本好书\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/mzc00200arkd7o01587977508093/350\",\"year\":2020},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其他\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"tv\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fmzc00200qfsxwx1.html\",\"directors\":\"林清振\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/mzc00200qfsxwx11598976461/0\",\"languages\":\"其他\",\"linkType\":1,\"payStatus\":0,\"programInfo\":\"全20集\",\"sid\":\"544499867536412672\",\"sortDefine\":26.168903350830078,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"陈都灵\",\"tags\":\"\",\"thirdDate\":1,\"title\":\"推理笔记\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/mzc00200qfsxwx11598976453/450\",\"year\":2017},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"其它\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F23037-0-0.html\",\"directors\":\"阿斯哈·法哈蒂\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.lzzyimg.com/upload/vod/20220908-1/273d41014dd64e17fffe5c8593a21f27.jpg\",\"languages\":\"其它\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076301922471956480\",\"sortDefine\":26.168903350830078,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"沙哈布·侯赛尼|塔拉内·阿里多斯蒂|巴巴克·卡里米|法里德·萨贾蒂·侯赛尼|米娜·沙达蒂\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推销员2016\",\"verticalIcon\":\"http://img.lzzyimg.com/upload/vod/20220908-1/273d41014dd64e17fffe5c8593a21f27.jpg\",\"year\":2016},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"法国\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fwww.cdqwhg.com%2Fplay%2F55070-0-0.html\",\"directors\":\"Vincent|Garenq\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://img.ffzy888.com/upload/vod/20231111-1/a88d8c02b9b5460f6c2e43805a397855.jpg\",\"languages\":\"法语\",\"linkType\":1,\"payStatus\":0,\"sid\":\"1076304982766178304\",\"sortDefine\":26.168903350830078,\"source\":\"cupfox\",\"sourceScore\":0.0,\"stars\":\"菲利普·托雷顿|WladimirYordanoff|诺埃米·洛夫斯基\",\"tags\":\"剧情片\",\"thirdDate\":1,\"title\":\"推定有罪\",\"verticalIcon\":\"http://img.ffzy888.com/upload/vod/20231111-1/a88d8c02b9b5460f6c2e43805a397855.jpg\",\"year\":2011},{\"aiSource\":0,\"albumFeatureType\":1,\"allowChaseAlbum\":0,\"area\":\"内地\",\"brief\":\"\",\"buttonStatus\":2,\"buttonText\":\"全网搜\",\"chaseAlbumStatus\":0,\"contentType\":\"movie\",\"copyrightCode\":\"tencent\",\"deepLink\":\"heytapbrowser://webpage/view?url=https%3A%2F%2Fv.qq.com%2Fx%2Fcover%2Fbv0lljpgk6l8dvp.html\",\"directors\":\"\",\"featureType\":1,\"functionScore\":0.0,\"horizontalIcon\":\"http://puui.qpic.cn/vcover_hz_pic/0/bv0lljpgk6l8dvp1501144627/0\",\"languages\":\"普通话\",\"linkType\":1,\"payStatus\":0,\"sid\":\"544470860275208192\",\"sortDefine\":26.168903350830078,\"source\":\"tencent\",\"sourceScore\":0.0,\"stars\":\"陈都灵|林柏宏|汪铎\",\"tags\":\"剧情|悬疑|院线\",\"thirdDate\":1,\"title\":\"推理笔记\",\"verticalIcon\":\"http://puui.qpic.cn/vcover_vt_pic/0/bv0lljpgk6l8dvp1517970458/450\",\"year\":2017}],\"interveneConfigMap\":{\"RECOMMEND\":{\"id\":8421,\"interveneType\":4,\"isShowMarkCode\":0,\"linkType\":10003,\"linkValue\":\"cp_00000564\",\"matchType\":1,\"name\":\"风行推荐\",\"queryKeyword\":\"风行推荐\",\"status\":1,\"title\":\"风行标签\"}},\"requestParam\":{\"appId\":\"video\",\"attachments\":{\"content-length\":\"0\",\"t-request-currentTimeMillis\":\"1732187762373\",\"t-CIRCUIT_TRACE_REQUEST_ID\":\"172975098306916683ede2181b133\",\"t-route-tag\":\"default\",\"REQUEST_RECEIVE_TIME\":\"1732187762373\",\"X-Proto\":\"HTTP\",\"t-CIRCUIT_TRACE_TRACE_ID\":\"8b75e91dbe96813a8171c3cc46e0cc27\",\"t-CIRCUIT_TRACE_USER_IP\":\"**************\",\"X-Real-IP\":\"************\",\"t-gateway-internet-traffic\":\"true\",\"t-current-path\":\"video-youli-service\",\"t-req-ttl\":\"3000\",\"X-Forwarded-Proto\":\"http\",\"Connection\":\"keep-alive\",\"t-CIRCUIT_TRACE_TAG_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;\",\"Host\":\"vod-mobile-test.wanyol.com\",\"t-CIRCUIT_TRACE_BUUID\":\"0\",\"t-request-id\":\"d6a9c4d524268a7fa2cd52c7e17505c6\",\"x-user-ip\":\"************\",\"traceparent\":\"00-d6a9c4d524268a7fa2cd52c7e17505c6-1186f20879a5c9f6-00\",\"X-Forwarded-For\":\"************\",\"t_route_tag\":\"default\",\"t-level-id\":\"610673826.*********.0.50\",\"t-CIRCUIT_TRACE_PATH_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;esa-rpc\",\"accept-encoding\":\"gzip\"},\"attributeValues\":{\"browserLanguage\":\"zh-CN\",\"buuid\":0,\"channel\":\"OPPO\",\"defaultChannel\":true,\"deviceType\":0,\"ip\":\"************\",\"newsSource\":\"yidian\",\"nightModel\":false,\"noImageMode\":false,\"ouidStatus\":true,\"pkg\":\"com.android.browser\",\"systemLanguage\":\"zh-CN\"},\"buuid\":\"0\",\"deviceType\":0,\"dv\":\"\",\"f\":\"json\",\"feedssession\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3Mjk3NzkzMzcsImRjIjoidGVzdCJ9.0yquZuUFxNkTrqVlf1EClnZ5jQLk7PqSEvQf29MeNw4\",\"hasMore\":0,\"isOut\":0,\"keyword\":\"风行推荐\",\"method\":\"\",\"pageIndex\":1,\"pageSize\":2,\"quickEngineVersion\":90200,\"requestId\":\"172975098306916683ede2181b133\",\"scookie\":{\"info\":{\"buuid\":1522931781933826304,\"ft\":\"1727421730\",\"rt\":\"1729750538\",\"sign\":\"787EC65A23CEBB356C46602B7C9CD9D9\",\"uid\":\"1112900464\",\"un\":\"OPPO_1112900464\"},\"oppo\":\"ofs=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3Mjk3NzkzMzcsImRjIjoidGVzdCJ9.0yquZuUFxNkTrqVlf1EClnZ5jQLk7PqSEvQf29MeNw4\",\"s\":\"toutiao\",\"toutiao\":\"a_t=APcbl0F7rRCo6M6n4ykbGa142wNna1sxL3kAuy3kkftu7s4vBptpqJbxAz3dTWw3Le7XJyuHc;1112900464\"},\"scookieIgnoreException\":{\"$ref\":\"$.requestParam.scookie\"},\"searchType\":\"1\",\"searchTypeEnum\":\"DEFAULT\",\"session\":\"eyJzIjoidG91dGlhbyIsIm9wcG8iOiJvZnM9ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFkSGx3SWpveExDSmlkV2xrSWpveE5USXlPVE14TnpneE9UTXpPREkyTXpBMExDSmhkV1FpT2lKMmFXUmxieUlzSW5abGNpSTZNaXdpY21GMElqb3hOekkzTkRJeE56TXdMQ0oxYm0waU9pSlBVRkJQWHpFeE1USTVNREEwTmpRaUxDSnBaQ0k2SWpFeE1USTVNREEwTmpRaUxDSmxlSEFpT2pFM01qazNOemt6TXpjc0ltUmpJam9pZEdWemRDSjkuMHlxdVp1VUZ4TmtUcnFWbGYxRUNsblo1alFMazdQcVNFdlFmMjlNZU53NCIsInRvdXRpYW8iOiJhX3Q9QVBjYmwwRjdyUkNvNk02bjR5a2JHYTE0MndObmExc3hMM2tBdXkza2tmdHU3czR2QnB0cHFKYnhBejNkVFd3M0xlN1hKeXVIYzsxMTEyOTAwNDY0IiwiaW5mbyI6eyJydCI6IjE3Mjk3NTA1MzgiLCJmdCI6IjE3Mjc0MjE3MzAiLCJ1biI6Ik9QUE9fMTExMjkwMDQ2NCIsInVpZCI6IjExMTI5MDA0NjQiLCJidXVpZCI6IjE1MjI5MzE3ODE5MzM4MjYzMDQiLCJzaWduIjoiNzg3RUM2NUEyM0NFQkIzNTZDNDY2MDJCN0M5Q0Q5RDkifX0=\",\"strategyResult\":{},\"version\":80100,\"versionTag\":10,\"vipType\":\"default\"},\"searchAllWebResult\":[],\"searchInterveneResult\":[],\"searchResponse\":{\"hasMore\":0,\"longVideoBannerList\":[],\"longVideoSearchResult\":[{\"$ref\":\"$.baseSearchResult[0]\"},{\"$ref\":\"$.baseSearchResult[1]\"}],\"pageIndex\":1,\"pageSize\":2}}";
        SearchByKeyWordContext searchByKeyWordContext = objectMapper.readValue(context, SearchByKeyWordContext.class);
        searchByKeyWordContext.getRequestParam().setVersion(80100);
        CompletableFuture<SearchInterveneCardResponse> itemByOperation = searchRecommendCmp.getItemByOperation(searchByKeyWordContext, 1);
        List<KeyWordSearchResponse> contents = itemByOperation.get().getContents();
        List<KeyWordSearchResponse> list = contents.stream().filter(item -> "weidiou".equals(item.getSource())).collect(Collectors.toList());
        Assert.assertTrue(list.size() > 0);

        searchByKeyWordContext.getRequestParam().setVersion(71600);
        CompletableFuture<SearchInterveneCardResponse> itemByOperation2 = searchRecommendCmp.getItemByOperation(searchByKeyWordContext, 1);
        List<KeyWordSearchResponse> contents2 = itemByOperation2.get().getContents();
        List<KeyWordSearchResponse> list2 = contents2.stream().filter(item -> "weidiou".equals(item.getSource())).collect(Collectors.toList());
        Assert.assertFalse(list2.size() > 0);
    }

    //    标签卡二级页：/search/v2/tagCardDetail
    @Test
    public void testTagCardDetail() throws JsonProcessingException {
        ArrayList<Mocker> mockers = new ArrayList<>();
        FunshionAndWeidiouFilterMockUtil.standardAlbumRpcApiMockForRecommendCard(mockers);
        String sidListJson = "[\"1074120971360849920\",\"1074120972560420864\",\"1074120969095925760\",\"1073837889705660416\",\"1074120979346804736\",\"1071268038126522368\",\"1071268072834387968\",\"1071268087111798784\",\"1071268105440907264\",\"1071268147182620672\",\"1071268160340152320\",\"1071268199938576384\"]";
        List<String> sidList = objectMapper.readValue(sidListJson, new TypeReference<List<String>>() {
        });
        String paramJson = "{\"appId\":\"video\",\"attachments\":{\"content-length\":\"0\",\"t-request-currentTimeMillis\":\"1732193093440\",\"t-CIRCUIT_TRACE_REQUEST_ID\":\"172975098306916683ede2181b133\",\"t-route-tag\":\"default\",\"REQUEST_RECEIVE_TIME\":\"1732193093441\",\"X-Proto\":\"HTTP\",\"t-CIRCUIT_TRACE_TRACE_ID\":\"eb546e1cced2ac3757490580039e644d\",\"t-CIRCUIT_TRACE_USER_IP\":\"**************\",\"X-Real-IP\":\"************\",\"t-gateway-internet-traffic\":\"true\",\"t-current-path\":\"video-youli-service\",\"t-req-ttl\":\"3000\",\"X-Forwarded-Proto\":\"http\",\"Connection\":\"keep-alive\",\"t-CIRCUIT_TRACE_TAG_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;\",\"Host\":\"vod-mobile-test.wanyol.com\",\"t-CIRCUIT_TRACE_BUUID\":\"0\",\"t-request-id\":\"d6a9c4d524268a7fa2cd52c7e17505c6\",\"x-user-ip\":\"************\",\"traceparent\":\"00-d6a9c4d524268a7fa2cd52c7e17505c6-b7eb5a23c3bd7547-00\",\"X-Forwarded-For\":\"************\",\"t_route_tag\":\"default\",\"t-level-id\":\"355980225.********.0.50\",\"t-CIRCUIT_TRACE_PATH_LIST\":\"null;;null;;,FEEDS;;video-youli-service;;esa-rpc\",\"accept-encoding\":\"gzip\"},\"attributeValues\":{\"browserLanguage\":\"zh-CN\",\"buuid\":0,\"channel\":\"OPPO\",\"defaultChannel\":true,\"deviceType\":0,\"ip\":\"************\",\"newsSource\":\"yidian\",\"nightModel\":false,\"noImageMode\":false,\"ouidStatus\":true,\"pkg\":\"com.android.browser\",\"systemLanguage\":\"zh-CN\"},\"buuid\":\"0\",\"deviceType\":0,\"dv\":\"\",\"f\":\"json\",\"feedssession\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3Mjk3NzkzMzcsImRjIjoidGVzdCJ9.0yquZuUFxNkTrqVlf1EClnZ5jQLk7PqSEvQf29MeNw4\",\"hasMore\":0,\"isOut\":0,\"keyword\":\"风行标签\",\"method\":\"\",\"pageIndex\":1,\"pageSize\":2,\"quickEngineVersion\":90200,\"requestId\":\"172975098306916683ede2181b133\",\"scookie\":{\"info\":{\"buuid\":1522931781933826304,\"ft\":\"1727421730\",\"rt\":\"1729750538\",\"sign\":\"787EC65A23CEBB356C46602B7C9CD9D9\",\"uid\":\"1112900464\",\"un\":\"OPPO_1112900464\"},\"oppo\":\"ofs=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoxNTIyOTMxNzgxOTMzODI2MzA0LCJhdWQiOiJ2aWRlbyIsInZlciI6MiwicmF0IjoxNzI3NDIxNzMwLCJ1bm0iOiJPUFBPXzExMTI5MDA0NjQiLCJpZCI6IjExMTI5MDA0NjQiLCJleHAiOjE3Mjk3NzkzMzcsImRjIjoidGVzdCJ9.0yquZuUFxNkTrqVlf1EClnZ5jQLk7PqSEvQf29MeNw4\",\"s\":\"toutiao\",\"toutiao\":\"a_t=APcbl0F7rRCo6M6n4ykbGa142wNna1sxL3kAuy3kkftu7s4vBptpqJbxAz3dTWw3Le7XJyuHc;1112900464\"},\"scookieIgnoreException\":{\"$ref\":\"$.scookie\"},\"searchType\":\"1\",\"searchTypeEnum\":\"DEFAULT\",\"session\":\"eyJzIjoidG91dGlhbyIsIm9wcG8iOiJvZnM9ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFkSGx3SWpveExDSmlkV2xrSWpveE5USXlPVE14TnpneE9UTXpPREkyTXpBMExDSmhkV1FpT2lKMmFXUmxieUlzSW5abGNpSTZNaXdpY21GMElqb3hOekkzTkRJeE56TXdMQ0oxYm0waU9pSlBVRkJQWHpFeE1USTVNREEwTmpRaUxDSnBaQ0k2SWpFeE1USTVNREEwTmpRaUxDSmxlSEFpT2pFM01qazNOemt6TXpjc0ltUmpJam9pZEdWemRDSjkuMHlxdVp1VUZ4TmtUcnFWbGYxRUNsblo1alFMazdQcVNFdlFmMjlNZU53NCIsInRvdXRpYW8iOiJhX3Q9QVBjYmwwRjdyUkNvNk02bjR5a2JHYTE0MndObmExc3hMM2tBdXkza2tmdHU3czR2QnB0cHFKYnhBejNkVFd3M0xlN1hKeXVIYzsxMTEyOTAwNDY0IiwiaW5mbyI6eyJydCI6IjE3Mjk3NTA1MzgiLCJmdCI6IjE3Mjc0MjE3MzAiLCJ1biI6Ik9QUE9fMTExMjkwMDQ2NCIsInVpZCI6IjExMTI5MDA0NjQiLCJidXVpZCI6IjE1MjI5MzE3ODE5MzM4MjYzMDQiLCJzaWduIjoiNzg3RUM2NUEyM0NFQkIzNTZDNDY2MDJCN0M5Q0Q5RDkifX0=\",\"strategyResult\":{},\"version\":71600,\"versionTag\":8,\"vipType\":\"default\"}";
        KeyWordSearchParamV2 param = objectMapper.readValue(paramJson, KeyWordSearchParamV2.class);
        param.setVersion(80100);
        List<KeyWordSearchResponse> responseBySidList = convertResponseService.getResponseBySidList(sidList, 0, param, null);
        List<KeyWordSearchResponse> funshionLongvideo = responseBySidList.stream().filter(item -> "funshion_lv".equals(item.getSource())).collect(Collectors.toList());
        Assert.assertTrue(funshionLongvideo.size() > 0);

        param.setVersion(71600);
        List<KeyWordSearchResponse> responseBySidList2 = convertResponseService.getResponseBySidList(sidList, 0, param, null);
        List<KeyWordSearchResponse> funshionLongvideo2 = responseBySidList2.stream().filter(item -> "funshion_lv".equals(item.getSource())).collect(Collectors.toList());
        Assert.assertEquals(0, funshionLongvideo2.size());

        param.setVersion(null);
        List<KeyWordSearchResponse> responseBySidList3 = convertResponseService.getResponseBySidList(sidList, 0, param, null);
        List<KeyWordSearchResponse> funshionLongvideo3 = responseBySidList3.stream().filter(item -> "funshion_lv".equals(item.getSource())).collect(Collectors.toList());
        Assert.assertEquals(0, funshionLongvideo2.size());
    }

}
