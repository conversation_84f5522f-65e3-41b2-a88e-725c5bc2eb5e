import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.mq.ThirdPartyMediaSyncListener;
import com.heytap.longvideo.search.mq.TvMediaSyncConsumerService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.service.MultiParamsRpcService;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageClientExt;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.message.MessageQueue;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/4/28 下午11:25
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class TvMediaSyncConsumerServiceTest extends GoblinJunit4BaseTest {

    @Autowired
    private TvMediaSyncConsumerService tvMediaSyncConsumerService;

    @Test
    public void consumeMessageTest() {
        String body ="{\"episodes\":[],\"trailers\":[],\"stagePhotos\":[],\"videos\":[],\"programRoles\":[{\"id\":269379,\"sid\":\"1049927464949829632\",\"eid\":null,\"personSid\":\"483616681315487744\",\"roleType\":1,\"doubanId\":null,\"mtimeId\":null,\"tencentId\":\"483616681315487744\",\"roleName\":\"\",\"programTitle\":\"夏阳的轮回\",\"orderNum\":1,\"castName\":\"张珏\",\"source\":\"iqiyi\",\"headImg\":\"https://longvideo.heytapimage.com/20210416000050862\",\"roleImg\":\"\",\"squareImg\":\"https://longvideo.heytapimage.com/20210416000049401\",\"status\":1,\"relateType\":0,\"createTime\":1726215464000,\"updateTime\":1726215464000},{\"id\":269380,\"sid\":\"1049927464949829632\",\"eid\":null,\"personSid\":\"483731454497542144\",\"roleType\":2,\"doubanId\":null,\"mtimeId\":null,\"tencentId\":\"483731454497542144\",\"roleName\":\"\",\"programTitle\":\"夏阳的轮回\",\"orderNum\":1,\"castName\":\"许梦圆\",\"source\":\"iqiyi\",\"headImg\":\"https://longvideo.heytapimage.com/20210529021844647\",\"roleImg\":\"\",\"squareImg\":\"https://longvideo.heytapimage.com/20210529021843010\",\"status\":1,\"relateType\":0,\"createTime\":1726215464000,\"updateTime\":1726215464000}],\"sid\":\"1049927464949829632\",\"title\":\"夏阳的轮回\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":6,\"originStatus\":0,\"source\":\"iqiyi\",\"sourceAlbumId\":\"qNN76l.7.0e1f9c548492c269\",\"sourceWebUrl\":\"https://m.mgtv.com/h/688054.html\",\"sourceScore\":\"8\",\"programType\":\"tv\",\"subProgramType\":\"tv\",\"sourceType\":13,\"price\":0,\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"category\":\"都市|悬疑\",\"episodeStyle\":null,\"payStatus\":1,\"duration\":7984,\"unit\":1,\"year\":2024,\"area\":\"内地\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"被夏家收养的白氏集团千金夏阳，在被亲生爷爷邀请回到白氏的那一天被人枪杀，陷入无限流循环，不断自救。\",\"mtimeId\":null,\"mtimeScore\":null,\"doubanId\":null,\"doubanTags\":null,\"doubanScore\":null,\"validEpisode\":0,\"totalEpisode\":85,\"quarterId\":null,\"quarterDetail\":0,\"completed\":0,\"brief\":\"白氏千金陷无限循环自救\",\"period\":\"\",\"verticalIcon\":\"http://2img.hitv.com/preview/sp_images/2025/03/05/202503051139492422495.jpg\",\"horizontalIcon\":\"http://0img.hitv.com/preview/sp_images/2025/03/05/202503051139575167780.jpg\",\"verticalImage\":\"http://2img.hitv.com/preview/sp_images/2025/03/05/202503051139492422495.jpg\",\"horizontalImage\":\"http://0img.hitv.com/preview/sp_images/2025/03/05/202503051139575167780.jpg\",\"verifyStatus\":0,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"iqiyi\",\"showTime\":\"202409\",\"featureType\":0,\"vipType\":0,\"chargeType\":0,\"director\":\"张珏\",\"actor\":\"许梦圆|徐尧|武潇\",\"keyword\":\"\",\"tags\":\"悬疑|都市\",\"showKind\":null,\"programInfo\":\"\",\"createTime\":1726215464000,\"updateTime\":null,\"publishTime\":1745848213000,\"vipStatus\":0,\"playCount\":0,\"programBackgroundImage\":null,\"programNameImage\":null,\"experienceType\":null,\"extraInfo\":\"\",\"supply\":null,\"notifySyncDone\":null,\"startTime\":0,\"endTime\":0}";
        MessageExt messageExt = new MessageClientExt();
        messageExt.setBody(body.getBytes());
        List<MessageExt> msgList = new ArrayList<>();
        msgList.add(messageExt);
        MessageQueue messageQueue = JsonUtil.fromStr("{\"topic\":\"meiziUniverseProgramAlbumDeliveryTopic\",\"brokerName\":\"broker-Cluster-QUkrNePubbb@CN-S01-DGTEST01@-2\",\"queueId\":7}", MessageQueue.class);
        ConsumeConcurrentlyContext context = new ConsumeConcurrentlyContext(messageQueue);
        ConsumeConcurrentlyStatus consumeConcurrentlyStatus = tvMediaSyncConsumerService.consumeMessage(msgList, context);
        Assert.assertEquals(consumeConcurrentlyStatus, ConsumeConcurrentlyStatus.CONSUME_SUCCESS);
    }

}
