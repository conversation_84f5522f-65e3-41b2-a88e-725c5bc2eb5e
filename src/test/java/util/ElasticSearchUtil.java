package util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.UgcStandardVideoEs;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.completion.Completion;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;

import java.util.*;

/**
 * <AUTHOR> Yanping
 * @date 2023/10/31
 */
public class ElasticSearchUtil {

    public static void createIndexAndAddData(StandardAlbumMapper standardAlbumMapper, InitService initService,
                                             ElasticSearchService elasticsearchService, ElasticsearchRestTemplate restTemplate, boolean addPoolCode, List<ProgramAlbumEs> initData) {
        List<ProgramAlbumEs> programAlbumEsList = standardAlbumMapper.selectStandardAlbumListForApp(0, 0, CommonConstant.allowSource);
        if (initData != null) {
            programAlbumEsList.addAll(initData);
        }
        int nowYear = Calendar.getInstance().get(Calendar.YEAR);
        for (ProgramAlbumEs programAlbumEs : programAlbumEsList) {
            if (programAlbumEs == null) {
                continue;
            }
            programAlbumEs.setVirtualSid(programAlbumEs.getSid());
            programAlbumEs.setHasVirtualSid(1);
            if (addPoolCode) {
                programAlbumEs.setMinorsPoolCode("cp_00000486,cp_00000487,cp_00000488,cp_00000489,cp_00000490,cp_00000491");
            }
            initService.setProgramAlbumEs(programAlbumEs, nowYear);
        }
        Map<String, Integer> actorMap = new HashMap<>(programAlbumEsList.size());
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (ProgramAlbumEs programAlbum : programAlbumEsList) {
            programAlbum.setOppoHot(0L);
            programAlbum.setDayNo(0);
            programAlbum.setTitlePinyin(StringUtil.toPinyin(programAlbum.getTitle(), PinyinFormat.WITHOUT_TONE));
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(programAlbum);
            indexQueries.add(indexQuery);
            initService.setActorEs(programAlbum, actorMap);
        }

        // 创建索引之前先删除索引（如果存在）
        ifExistThenDeleteIndex(restTemplate);
        elasticsearchService.createIndexAndMapping(AlbumEs.class);

        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(ProgramAlbumEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(ProgramAlbumEs.class);
        indexOperations.refresh();
    }

    public static void searchAlbumCmpMock(ElasticSearchService elasticsearchService, ElasticsearchRestTemplate restTemplate) {
        List<ProgramAlbumEs> programAlbumEsList = JSONObject.parseObject("[{\"actor\":\"梁冠华|董璇|张永强|刘蕾|赵毅|淳于珊珊|沙玉华|李绪良\",\"actorPinyin\":\"梁冠华|董璇|张永强|刘蕾|赵毅|淳于珊珊|沙玉华|李绪良\",\"area\":\"内地\",\"brief\":\"狸猫换太子传奇\",\"contentType\":\"tv\",\"copyrightCode\":\"sohu\",\"dayNo\":0,\"director\":\"钱雁秋\",\"directorPinyin\":\"钱雁秋\",\"epstitle\":\"\",\"featureType\":1,\"functionScore\":1.04,\"hasVirtualSid\":1,\"horizontalIcon\":\"http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20150829/vrsa_hor9049645.jpg\",\"isHot\":0,\"isSeries\":0,\"language\":\"普通话\",\"last15DaysClickPv\":0,\"last15DaysPlayPv\":0,\"last30DaysClickPv\":0,\"last30DaysPlayPv\":0,\"last7DaysClickPv\":0,\"last7DaysPlayPv\":0,\"markCode\":\"\",\"oppoHot\":0,\"payStatus\":0,\"programInfo\":\"全 30 集\",\"releScore\":189.74629,\"seriesTitle\":{\"input\":[\"大宋奇案\"],\"weight\":104},\"seriesTitlePinyin\":{\"input\":[\"大宋奇案\"],\"weight\":104},\"showTime\":\"201509\",\"sid\":\"586129156434120704\",\"sortDefine\":0.0,\"source\":\"sohu\",\"sourceScore\":6.4,\"status\":1,\"suggestTitlePinyin\":{\"input\":[\"大宋奇案\"],\"weight\":104},\"tags\":\"悬疑,爱情,古装\",\"thirdDate\":0,\"title\":\"大宋奇案\",\"titlePinyin\":\"大宋奇案\",\"verticalIcon\":\"http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20150829/vrsa_ver9049645.jpg\",\"vipType\":0,\"virtualSid\":\"617199834608193536\",\"year\":2005},{\"actor\":\"张新成|周雨彤|王佑硕|苏晓彤|郑伟\",\"actorPinyin\":\"张新成|周雨彤|王佑硕|苏晓彤|郑伟\",\"area\":\"内地\",\"brief\":\"七斋六子一起探案友情\",\"contentType\":\"tv\",\"copyrightCode\":\"mgmobile\",\"dayNo\":0,\"director\":\"\",\"epstitle\":\"\",\"featureType\":1,\"functionScore\":1.51,\"hasVirtualSid\":0,\"horizontalIcon\":\"http://4img.hitv.com/preview/sp_images/2023/08/18/202308181624527024324.jpg\",\"isHot\":1,\"isSeries\":0,\"language\":\"普通话\",\"last15DaysClickPv\":0,\"last15DaysPlayPv\":0,\"last30DaysClickPv\":0,\"last30DaysPlayPv\":0,\"last7DaysClickPv\":0,\"last7DaysPlayPv\":0,\"markCode\":\"mgmobile_vip_1\",\"oppoHot\":0,\"payStatus\":1,\"programInfo\":\"全 13 集\",\"releScore\":105.12355,\"seriesTitle\":{\"input\":[\"大宋探案局\"],\"weight\":151},\"seriesTitlePinyin\":{\"input\":[\"大宋探案局\"],\"weight\":151},\"showTime\":\"202308\",\"sid\":\"910400883228061696\",\"sortDefine\":0.0,\"source\":\"mgmobile\",\"sourceScore\":9.0,\"status\":1,\"suggestTitlePinyin\":{\"input\":[\"大宋探案局\"],\"weight\":151},\"tags\":\"芒果出品\",\"thirdDate\":0,\"title\":\"大宋探案局\",\"titlePinyin\":\"大宋探案局\",\"verticalIcon\":\"http://2img.hitv.com/preview/sp_images/2023/08/18/202308181625000371851.jpg\",\"vipType\":1,\"virtualSid\":\"910400883228061696\",\"year\":2023}]", new TypeReference<ArrayList<ProgramAlbumEs>>() {
        });

        List<IndexQuery> indexQueries = new ArrayList<>();
        for (ProgramAlbumEs programAlbum : programAlbumEsList) {
            programAlbum.setOppoHot(0L);
            programAlbum.setDayNo(0);
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(programAlbum);
            indexQueries.add(indexQuery);
        }

        // 创建索引之前先删除索引（如果存在）
        ifExistThenDeleteIndex(restTemplate);
        elasticsearchService.createIndexAndMapping(AlbumEs.class);

        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(ProgramAlbumEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(ProgramAlbumEs.class);
        indexOperations.refresh();
    }

    public static void searchUgcStandardVideoMock(ElasticSearchService elasticsearchService, ElasticsearchRestTemplate restTemplate) {
        List<UgcStandardVideoEs> ugcStandardVideoEsList = JSONObject.parseObject("[{\"authorName\":\"原子拆条视频\",\"authorPhoto\":\"\",\"category\":\"\",\"channel\":\"爽点拆条\",\"channelId\":\"100000410\",\"createTime\":1723795917000,\"duration\":161,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=c4d0207fa47b7d13678b9cee5745a95e&w=0&h=0\",\"linkSid\":\"680628508279644160\",\"linkTitle\":\"我们最美好的十年\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=53A5074F6A8DF393D3B19167B572C6887C3650BE&type=play&cp=fbmrw02&ctime=1723897201&codec=&sign=caf6570cd82314a250ca743c4bfa9338&funtransid=d3092d1972d23733f1ba83a73f903834\",\"programType\":\"\",\"publishTime\":1723795917000,\"screenMode\":1,\"sid\":\"1041122212087779328\",\"source\":\"funshion\",\"sourceAuthorId\":\"501495\",\"sourceMediaId\":\"314517\",\"sourceStatus\":1,\"sourceVideoId\":\"391035787\",\"status\":1,\"subTag\":\"\",\"tag\":\"小姐\",\"title\":\"刘小姐听了范洋的故事后被感动，不惜花一万块买下他的手镯\",\"uniKey\":\"1041122212087779328:1041144463638056960\",\"updateTime\":1723897201000,\"verticalIcon\":\"http://img.funshion.com/sdw?oid=e2a30e17fa4b28ad6c946eca43f4e7e5&w=0&h=0\",\"vid\":\"1041144463638056960\"},{\"authorName\":\"南乔热聊影视\",\"authorPhoto\":\"https://img3.funshion.com/sdw?oid=ce4f91129aaba1e3ebce496192ab26f6&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796480000,\"duration\":31,\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=4af0d1aeed36e7af677c14813483b3c8&w=0&h=0\",\"linkSid\":\"625489099087695875\",\"linkTitle\":\"巴啦啦小魔仙之彩虹心石\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=1B3596E9D8AF43C742B31115A56AF3C316C730C9&type=play&cp=fbmrw02&ctime=1723796480&codec=&sign=8811a9aa94b8386cfc54e03c8ee0d19d&funtransid=5f42c560a968494a57a6405242ecb5b1\",\"programType\":\"\",\"publishTime\":1723796480000,\"screenMode\":1,\"sid\":\"1040950701611732992\",\"source\":\"funshion\",\"sourceAuthorId\":\"530189\",\"sourceMediaId\":\"96172\",\"sourceStatus\":1,\"sourceVideoId\":\"410151695\",\"status\":1,\"subTag\":\"\",\"tag\":\"巴啦啦小魔仙之彩虹心石\",\"title\":\"【巴啦啦小魔仙之彩虹心石】这要看女王的智慧了\",\"uniKey\":\"1040950701611732992:1040950717822717952\",\"updateTime\":1723817212000,\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=452207f524f048567b4b518bd12e7c4a&w=0&h=0\",\"vid\":\"1040950717822717952\"},{\"authorName\":\"原子拆条视频\",\"authorPhoto\":\"\",\"category\":\"\",\"channel\":\"爽点拆条\",\"channelId\":\"100000410\",\"createTime\":1723796414000,\"duration\":270,\"horizontalIcon\":\"http://img.funshion.com/sdw?oid=e06433156febcac7117f8cc298efb1f6&w=0&h=0\",\"linkSid\":\"625489006368411648\",\"linkTitle\":\"李米的猜想\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=9F7DD863A9103C37098B5F3636FA81A48C0A5D79&type=play&cp=fbmrw02&ctime=1723796414&codec=&sign=5fe4c9ef129d727af7f54ebc660aaa53&funtransid=41c766face83a0ef61aaeb60774b68be\",\"programType\":\"\",\"publishTime\":1723796414000,\"screenMode\":1,\"sid\":\"1041122212087779328\",\"source\":\"funshion\",\"sourceAuthorId\":\"501495\",\"sourceMediaId\":\"86780\",\"sourceStatus\":1,\"sourceVideoId\":\"402496651\",\"status\":1,\"subTag\":\"\",\"tag\":\"爽点拆条\",\"title\":\"李米没想到自己载的客人是坏人，被两人绑架要求拿出两千块（下）\",\"uniKey\":\"1041122212087779328:1041144556424450048\",\"updateTime\":1723815959000,\"verticalIcon\":\"http://img.funshion.com/sdw?oid=d23aae56afa66a4a6f6b8fe2c51f1e39&w=0&h=0\",\"vid\":\"1041144556424450048\"},{\"authorName\":\"老友热聊影视\",\"authorPhoto\":\"https://img2.funshion.com/sdw?oid=ef263cc047500e1341b93b0b993dc1c9&w=0&h=0\",\"category\":\"\",\"channel\":\"电影片花\",\"channelId\":\"1015\",\"createTime\":1723796490000,\"duration\":208,\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=e485b2fa6ebfb3a4ad30b63e0adcdc5f&w=0&h=0\",\"linkSid\":\"882520877680472064\",\"linkTitle\":\"小志和玩具 第二季\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=21DA5D70F7692CA14FD3C2DEB7BBDEC2E679242E&type=play&cp=fbmrw02&ctime=1723796490&codec=&sign=8c8238cf3db76d9fea6a7793af0df318&funtransid=09d59df6577be73630c7be1e77c97cf1\",\"programType\":\"\",\"publishTime\":1723796490000,\"screenMode\":1,\"sid\":\"1040950736701280256\",\"source\":\"funshion\",\"sourceAuthorId\":\"530198\",\"sourceMediaId\":\"1033619\",\"sourceStatus\":1,\"sourceVideoId\":\"410151811\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具 第二季\",\"title\":\"变身卡猜谜游戏小志输了所有贴了一脸卡片\",\"uniKey\":\"1040950736701280256:1040950736801943552\",\"updateTime\":1723796491000,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=ea5ed1d6dad74bcf874007f9fab8ca9e&w=0&h=0\",\"vid\":\"1040950736801943552\"},{\"authorName\":\"老邪说电影\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=fbf35c46c709b89519ead757630ca400&w=0&h=0\",\"category\":\"\",\"channel\":\"电视剧解说\",\"channelId\":\"100000393\",\"createTime\":1723796489000,\"duration\":546,\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=bf2de1f1fb216dcf09bff99b9b85f300&w=0&h=0\",\"linkSid\":\"629978522957586437\",\"linkTitle\":\"兰桐花开\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=B24EA307623518046744D96BF73BC4EB088E291D&type=play&cp=fbmrw02&ctime=1723796489&codec=&sign=45d933e160bb85f62ada69f3bc76dccb&funtransid=a1a11a7f74a166a7d956f2d556731b74\",\"programType\":\"\",\"publishTime\":1723796489000,\"screenMode\":1,\"sid\":\"1005309796779470848\",\"source\":\"funshion\",\"sourceAuthorId\":\"769\",\"sourceMediaId\":\"331021\",\"sourceStatus\":1,\"sourceVideoId\":\"342654623\",\"status\":1,\"subTag\":\"\",\"tag\":\"兰桐花开\",\"title\":\"看完笑出猪叫！女主带头创业失败导致全村去要饭的《兰桐花开》\",\"uniKey\":\"1005309796779470848:1038965357332844544\",\"updateTime\":1723796490000,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=ef6e1c2579d0e7d4e60e2c97c8fa1825&w=0&h=0\",\"vid\":\"1038965357332844544\"},{\"authorName\":\"老友热聊影视\",\"authorPhoto\":\"https://img2.funshion.com/sdw?oid=ef263cc047500e1341b93b0b993dc1c9&w=0&h=0\",\"category\":\"\",\"channel\":\"电影片花\",\"channelId\":\"1015\",\"createTime\":1723796490000,\"duration\":165,\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=55532d6516e66e78c3b868639d2c15e2&w=0&h=0\",\"linkSid\":\"882520842611896320\",\"linkTitle\":\"小志和玩具 第九季\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=B2594F6184DDD07F1E3FC0C73CD2CAAB392841AE&type=play&cp=fbmrw02&ctime=1723796490&codec=&sign=86ec5a34c76d3cb62d1c184a2a485956&funtransid=3303085025cbda7b05a8ddd3ca1c93ef\",\"programType\":\"\",\"publishTime\":1723796490000,\"screenMode\":1,\"sid\":\"1040950736701280256\",\"source\":\"funshion\",\"sourceAuthorId\":\"530198\",\"sourceMediaId\":\"1033633\",\"sourceStatus\":1,\"sourceVideoId\":\"410151817\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具 第九季\",\"title\":\"冰河僵尸博士的獠牙征服者把植物庄园踏平烈焰霸王龙变身\",\"uniKey\":\"1040950736701280256:1040950734100811776\",\"updateTime\":1723796490000,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=8a48d0fc1054dd78b6447c9d0b36549b&w=0&h=0\",\"vid\":\"1040950734100811776\"},{\"authorName\":\"韩国媳妇大林妹妹\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=e75852071599e149c3bfcf7fd554dbf5&w=0&h=0\",\"category\":\"\",\"channel\":\"电视剧解说\",\"channelId\":\"100000393\",\"createTime\":1723431914000,\"duration\":259,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=d17211277d1abd9fed952ef786c8988d&w=0&h=0\",\"linkSid\":\"629978522957586437\",\"linkTitle\":\"兰桐花开\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=A738F430492C52B85CE75316A058E5B487AFF7FA&type=play&cp=fbmrw02&ctime=1723796488&codec=&sign=1cb5d56a1b00c90cee3ea03020b24296&funtransid=6a7e843ba7841d5f98f8b1f70723b12f\",\"programType\":\"\",\"publishTime\":1723431914000,\"screenMode\":1,\"sid\":\"1039044873048739840\",\"source\":\"funshion\",\"sourceAuthorId\":\"432288\",\"sourceMediaId\":\"331021\",\"sourceStatus\":1,\"sourceVideoId\":\"401551663\",\"status\":1,\"subTag\":\"\",\"tag\":\"兰桐花开\",\"title\":\"《兰桐花开》：没人要的红薯边角料，却被他们当成发财的宝贝\",\"uniKey\":\"1039044873048739840:1038965354191310848\",\"updateTime\":1723796489000,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=9e133037dbc27ebb77c6b8ffdc0c4e5d&w=0&h=0\",\"vid\":\"1038965354191310848\"},{\"authorName\":\"艾斯热聊影视\",\"authorPhoto\":\"https://img1.funshion.com/sdw?oid=d7183f89f38fe51fd73d2ef4170cfe81&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796488000,\"duration\":155,\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=bfbcea50d5ebb53f37f206d1dc85d0a3&w=0&h=0\",\"linkSid\":\"882520887298011136\",\"linkTitle\":\"小志和玩具 第三季\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=26FC1DD40B1ECE54735ED9FC9493183FEB5E16C7&type=play&cp=fbmrw02&ctime=1723796488&codec=&sign=19b59245857f488ecc0334274e0636eb&funtransid=3aafdb563c83aa6a208361050bec268b\",\"programType\":\"\",\"publishTime\":1723796488000,\"screenMode\":1,\"sid\":\"1040950649031938050\",\"source\":\"funshion\",\"sourceAuthorId\":\"530190\",\"sourceMediaId\":\"1033621\",\"sourceStatus\":1,\"sourceVideoId\":\"410151711\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具 第三季\",\"title\":\"僵尸博士抓捕独角兽小志和霸王龙联手报仇心奇爆龙战车X\",\"uniKey\":\"1040950649031938050:1040950729474494464\",\"updateTime\":1723796489000,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=5ffe5bf2aac94a5cbf28d7103f6fd6ac&w=0&h=0\",\"vid\":\"1040950729474494464\"},{\"authorName\":\"百万剪辑狮\",\"authorPhoto\":\"https://img1.funshion.com/sdw?oid=00baf5b1ae59760a79f294f1b0b8eb46&w=0&h=0\",\"category\":\"\",\"channel\":\"电视剧解说\",\"channelId\":\"100000393\",\"createTime\":1723796489000,\"duration\":157,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=fbce30ccd6866fa5c7979639de04cebf&w=0&h=0\",\"linkSid\":\"629978522957586437\",\"linkTitle\":\"兰桐花开\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=3FDA38D5B5FECB7916DCE25FFC5CA91983BD4E24&type=play&cp=fbmrw02&ctime=1723796489&codec=&sign=724ce66da460422174e89f000fcdfda8&funtransid=8a99039b2bdd22ca2afe3dc2144c6ad8\",\"programType\":\"\",\"publishTime\":1723796489000,\"screenMode\":1,\"sid\":\"1039044873044545536\",\"source\":\"funshion\",\"sourceAuthorId\":\"451637\",\"sourceMediaId\":\"331021\",\"sourceStatus\":1,\"sourceVideoId\":\"374743213\",\"status\":1,\"subTag\":\"\",\"tag\":\"兰桐花开\",\"title\":\"男人只是尝了口咸盐，就断定天会下暴雨！《兰桐花开》\",\"uniKey\":\"1039044873044545536:1038965338974375936\",\"updateTime\":1723796489000,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=8fda13764deb8fa0772161ed4cf5d54c&w=0&h=0\",\"vid\":\"1038965338974375936\"},{\"authorName\":\"艾斯热聊影视\",\"authorPhoto\":\"https://img1.funshion.com/sdw?oid=d7183f89f38fe51fd73d2ef4170cfe81&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796488000,\"duration\":184,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=200698d4b4a6fa86113342542422d80a&w=0&h=0\",\"linkSid\":\"882520805043515392\",\"linkTitle\":\"小志和玩具 第五季\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=6F59992FEE72304A2A49445212562EADA4A92052&type=play&cp=fbmrw02&ctime=1723796488&codec=&sign=49581222999da19533d23f5ad244a45e&funtransid=e2cf2356a16fc8e6849421dd61c7b262\",\"programType\":\"\",\"publishTime\":1723796488000,\"screenMode\":1,\"sid\":\"1040950649031938050\",\"source\":\"funshion\",\"sourceAuthorId\":\"530190\",\"sourceMediaId\":\"1033625\",\"sourceStatus\":1,\"sourceVideoId\":\"410151715\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具 第五季\",\"title\":\"僵尸博士抓住豌豆射手小志打败邪恶恐龙心奇爆龙战车X\",\"uniKey\":\"1040950649031938050:1040950717864660993\",\"updateTime\":1723796488000,\"verticalIcon\":\"http://img.funshion.com/sdw?oid=44b88a088d29c6139e9f5ff04298b58e&w=0&h=0\",\"vid\":\"1040950717864660993\"},{\"authorName\":\"艾斯热聊影视\",\"authorPhoto\":\"https://img1.funshion.com/sdw?oid=d7183f89f38fe51fd73d2ef4170cfe81&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796487000,\"duration\":190,\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=cb5e2bcea314ea0d13237f898603bee1&w=0&h=0\",\"linkSid\":\"882520887298011136\",\"linkTitle\":\"小志和玩具 第三季\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=4B93553BD3ACF7D17C42967A5EEBC96719C9CC9C&type=play&cp=fbmrw02&ctime=1723796487&codec=&sign=36c276fe732de63f0b30bb29f381c10b&funtransid=2495ae9b6c175c02e3c5354c5a8350c0\",\"programType\":\"\",\"publishTime\":1723796487000,\"screenMode\":1,\"sid\":\"1040950649031938050\",\"source\":\"funshion\",\"sourceAuthorId\":\"530190\",\"sourceMediaId\":\"1033621\",\"sourceStatus\":1,\"sourceVideoId\":\"410151709\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具 第三季\",\"title\":\"僵尸博士抓走窝瓜小志爬天空之城成功营救窝瓜\",\"uniKey\":\"1040950649031938050:1040950649354899456\",\"updateTime\":1723796488000,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=b0af16e63624720c691c425372813e27&w=0&h=0\",\"vid\":\"1040950649354899456\"},{\"authorName\":\"艾斯热聊影视\",\"authorPhoto\":\"https://img1.funshion.com/sdw?oid=d7183f89f38fe51fd73d2ef4170cfe81&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796487000,\"duration\":300,\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=c40ba592212c54aac5f67bd0fc697207&w=0&h=0\",\"linkSid\":\"905962005641474048\",\"linkTitle\":\"小志和玩具之心奇爆龙战车6 全集\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=DC5B992FF3397F2DA89E7C40E382471E62741A75&type=play&cp=fbmrw02&ctime=1723796487&codec=&sign=89674601cbd39e75475e3dbc0d49f8f2&funtransid=80635fec798e5eedfd64f82fa7f952c6\",\"programType\":\"\",\"publishTime\":1723796487000,\"screenMode\":1,\"sid\":\"1040950649031938050\",\"source\":\"funshion\",\"sourceAuthorId\":\"530190\",\"sourceMediaId\":\"1050357\",\"sourceStatus\":1,\"sourceVideoId\":\"410151713\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具之心奇爆龙战车6 全集\",\"title\":\"僵尸分裂爆龙小队成功了罗马巨人僵尸趁机偷袭心奇爆龙战车6\",\"uniKey\":\"1040950649031938050:1040950649170350080\",\"updateTime\":1723796487000,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=57bc73a5808f06f99e280e00a282c915&w=0&h=0\",\"vid\":\"1040950649170350080\"},{\"authorName\":\"艾斯热聊影视\",\"authorPhoto\":\"https://img1.funshion.com/sdw?oid=d7183f89f38fe51fd73d2ef4170cfe81&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796487000,\"duration\":147,\"horizontalIcon\":\"http://img.funshion.com/sdw?oid=78a7093a44c1e879646f9ec6c452fe29&w=0&h=0\",\"linkSid\":\"882520823397789696\",\"linkTitle\":\"小志和玩具 第七季\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=9416E1A6525E4F46BB59F73C6185C3946DA938CC&type=play&cp=fbmrw02&ctime=1723796487&codec=&sign=51e00a2ae424011cf6cf07ea777bdd0c&funtransid=a7f977beb661e025ac2624e0163eec8c\",\"programType\":\"\",\"publishTime\":1723796487000,\"screenMode\":1,\"sid\":\"1040950649031938050\",\"source\":\"funshion\",\"sourceAuthorId\":\"530190\",\"sourceMediaId\":\"1033629\",\"sourceStatus\":1,\"sourceVideoId\":\"410151717\",\"status\":1,\"subTag\":\"\",\"tag\":\"小志和玩具 第七季\",\"title\":\"僵尸博士制作的陀螺斗士vs霸王龙陀螺战车\",\"uniKey\":\"1040950649031938050:1040950648876748803\",\"updateTime\":1723796487000,\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=62ba33fa4e3a1732a6c44820eab0acca&w=0&h=0\",\"vid\":\"1040950648876748803\"},{\"authorName\":\"兔兔热聊影视\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=47abae4ca92d5b9d82f96ffc59d2fcf9&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796486000,\"duration\":227,\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=577b3b2292cbd599a98384341f911504&w=0&h=0\",\"linkSid\":\"839336392655052800\",\"linkTitle\":\"熊妹玩具2023\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=EF4FBEED6C2EEF565D48D2D3A559921BE7A62316&type=play&cp=fbmrw02&ctime=1723796486&codec=&sign=3bcb92e8c952b4e6649ab0399186c0de&funtransid=1da4d0f99a1a84e9a0027790bf3c77bf\",\"programType\":\"\",\"publishTime\":1723796486000,\"screenMode\":1,\"sid\":\"1040950649031938049\",\"source\":\"funshion\",\"sourceAuthorId\":\"530191\",\"sourceMediaId\":\"1038623\",\"sourceStatus\":1,\"sourceVideoId\":\"410151719\",\"status\":1,\"subTag\":\"\",\"tag\":\"熊妹玩具2023\",\"title\":\"白雪公主有一张魔法地图\",\"uniKey\":\"1040950649031938049:1040950733983371264\",\"updateTime\":1723796487000,\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=241d65af383fa699c3db42e340a15177&w=0&h=0\",\"vid\":\"1040950733983371264\"},{\"authorName\":\"兔兔热聊影视\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=47abae4ca92d5b9d82f96ffc59d2fcf9&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796486000,\"duration\":97,\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=4e90edaa930dd5f6d9feae3135054c32&w=0&h=0\",\"linkSid\":\"821150267377655808\",\"linkTitle\":\"托宝战士之潮玩大战\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=8E3C927543A66B7DB0EC1794676A6B25023352FB&type=play&cp=fbmrw02&ctime=1723796486&codec=&sign=90d223a1f5f5ad92363aabe6b814b893&funtransid=3d1b3677d2ed90b88f0b3540f74e96c5\",\"programType\":\"\",\"publishTime\":1723796486000,\"screenMode\":1,\"sid\":\"1040950649031938049\",\"source\":\"funshion\",\"sourceAuthorId\":\"530191\",\"sourceMediaId\":\"1034913\",\"sourceStatus\":1,\"sourceVideoId\":\"410151725\",\"status\":1,\"subTag\":\"\",\"tag\":\"托宝战士之潮玩大战\",\"title\":\"托宝战士全新变形玩具，音速雷霆和巨岩先锋\",\"uniKey\":\"1040950649031938049:1040950729520631808\",\"updateTime\":1723796486000,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=0a5d1535a84c3877899037331cec6c5f&w=0&h=0\",\"vid\":\"1040950729520631808\"},{\"authorName\":\"兔兔热聊影视\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=47abae4ca92d5b9d82f96ffc59d2fcf9&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796485000,\"duration\":38,\"horizontalIcon\":\"http://img.funshion.com/sdw?oid=42283164c88aa8f6d6b1d33942e3e445&w=0&h=0\",\"linkSid\":\"785762117960802304\",\"linkTitle\":\"小猪佩奇 第九季 普通话版\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=9EB3E2C296ACDEEE8964B38A0B9DC85F745BF033&type=play&cp=fbmrw02&ctime=1723796485&codec=&sign=4567e1710717d3a6dbdc70d035d301f3&funtransid=7061443486761c9164c3cb45142379ab\",\"programType\":\"\",\"publishTime\":1723796485000,\"screenMode\":1,\"sid\":\"1040950649031938049\",\"source\":\"funshion\",\"sourceAuthorId\":\"530191\",\"sourceMediaId\":\"1030125\",\"sourceStatus\":1,\"sourceVideoId\":\"410151721\",\"status\":1,\"subTag\":\"\",\"tag\":\"小猪佩奇 第九季 普通话版\",\"title\":\"小猪佩奇之大山（55）\",\"uniKey\":\"1040950649031938049:1040950649178738688\",\"updateTime\":1723796486000,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=db6d4ace664c7d7ad3402084ae147ba3&w=0&h=0\",\"vid\":\"1040950649178738688\"},{\"authorName\":\"兔兔热聊影视\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=47abae4ca92d5b9d82f96ffc59d2fcf9&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796486000,\"duration\":178,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=0e170bb786435d86ab342d05caaf4159&w=0&h=0\",\"linkSid\":\"824146291318792192\",\"linkTitle\":\"奇妙萌可之闪亮宝石\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=31E6CF20AE4591BB8CB9E130936872CC8FD659E1&type=play&cp=fbmrw02&ctime=1723796485&codec=&sign=93d4ac693add03a22d5e9e8503a6ecb1&funtransid=3739085bee34517aeeff3201b5361fc0\",\"programType\":\"\",\"publishTime\":1723796486000,\"screenMode\":1,\"sid\":\"1040950649031938049\",\"source\":\"funshion\",\"sourceAuthorId\":\"530191\",\"sourceMediaId\":\"1035179\",\"sourceStatus\":1,\"sourceVideoId\":\"410151731\",\"status\":1,\"subTag\":\"\",\"tag\":\"奇妙萌可之闪亮宝石\",\"title\":\"《奇妙萌可之闪亮宝石》萌可学校开学 中\",\"uniKey\":\"1040950649031938049:1040950722524532736\",\"updateTime\":1723796486000,\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=a070fc2b3949a2a268d9c90e2c817a34&w=0&h=0\",\"vid\":\"1040950722524532736\"},{\"authorName\":\"烟雨热聊影视\",\"authorPhoto\":\"https://img3.funshion.com/sdw?oid=a0a929c8394416eadc9c1a42e7123ae8&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796484000,\"duration\":78,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=244c56a1893a37c2fda5afb236aa6ba5&w=0&h=0\",\"linkSid\":\"930383228618059776\",\"linkTitle\":\"愤怒的摄影师\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=7D7003DB810104FB41C4C6AD585093222D89BF6D&type=play&cp=fbmrw02&ctime=1723796484&codec=&sign=efe6691261921a09d5fb1fdefe28bdb0&funtransid=e4dd54ad82d3ccdf5bfea655f0dd3546\",\"programType\":\"\",\"publishTime\":1723796484000,\"screenMode\":1,\"sid\":\"1040950649031938048\",\"source\":\"funshion\",\"sourceAuthorId\":\"530196\",\"sourceMediaId\":\"1054055\",\"sourceStatus\":1,\"sourceVideoId\":\"410151789\",\"status\":1,\"subTag\":\"\",\"tag\":\"愤怒的摄影师\",\"title\":\"【愤怒的摄影师】第35集预告\",\"uniKey\":\"1040950649031938048:1040950662151720960\",\"updateTime\":1723796485000,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=40a3017f5cd62535fa2ee666b0c4a3b4&w=0&h=0\",\"vid\":\"1040950662151720960\"},{\"authorName\":\"烟雨热聊影视\",\"authorPhoto\":\"https://img3.funshion.com/sdw?oid=a0a929c8394416eadc9c1a42e7123ae8&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796484000,\"duration\":253,\"horizontalIcon\":\"http://img.funshion.com/sdw?oid=b562600f259da8c7f0cd0dd94571a271&w=0&h=0\",\"linkSid\":\"625489239781429252\",\"linkTitle\":\"生命中的好日子\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=86CF8F18D5A71F73B28A1D511A501085C797CA43&type=play&cp=fbmrw02&ctime=1723796484&codec=&sign=284dfa91c546d832d5712e7da72de3d4&funtransid=f89edc1e72fe0715f0cf746a9af25552\",\"programType\":\"\",\"publishTime\":1723796484000,\"screenMode\":1,\"sid\":\"1040950649031938048\",\"source\":\"funshion\",\"sourceAuthorId\":\"530196\",\"sourceMediaId\":\"300239\",\"sourceStatus\":1,\"sourceVideoId\":\"410151795\",\"status\":1,\"subTag\":\"\",\"tag\":\"生命中的好日子\",\"title\":\"生命中的好日子：晕！女子好心给朋友安慰，谁知朋友不领情\",\"uniKey\":\"1040950649031938048:1040950794456846336\",\"updateTime\":1723796485000,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=01d944ce3b1dfd1d27484994666fbc72&w=0&h=0\",\"vid\":\"1040950794456846336\"},{\"authorName\":\"兔兔热聊影视\",\"authorPhoto\":\"https://img.funshion.com/sdw?oid=47abae4ca92d5b9d82f96ffc59d2fcf9&w=0&h=0\",\"category\":\"\",\"channel\":\"少儿片花\",\"channelId\":\"100000059\",\"createTime\":1723796485000,\"duration\":120,\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=1fb0271832bbe14fdb83dad8e392302a&w=0&h=0\",\"linkSid\":\"684586611496919040\",\"linkTitle\":\"奇拉小队字母儿歌\",\"playUrl\":\"http://papi.funshion.com/api/cpcdnurl?infohash=7E52B1C3D901703268521D0D07AEBDFFA72E8939&type=play&cp=fbmrw02&ctime=1723796485&codec=&sign=26348982f04d405aa776b123a37e13ae&funtransid=b3df5058fea57b780707ca4e0214014c\",\"programType\":\"\",\"publishTime\":1723796485000,\"screenMode\":1,\"sid\":\"1040950649031938049\",\"source\":\"funshion\",\"sourceAuthorId\":\"530191\",\"sourceMediaId\":\"1015221\",\"sourceStatus\":1,\"sourceVideoId\":\"410151729\",\"status\":1,\"subTag\":\"\",\"tag\":\"奇拉小队字母儿歌\",\"title\":\"奇拉小队字母儿歌-一起来唱ABC\",\"uniKey\":\"1040950649031938049:1040950648880943104\",\"updateTime\":1723796485000,\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=2c9f774f4c8fe25c7503c13dbd409624&w=0&h=0\",\"vid\":\"1040950648880943104\"}]", new TypeReference<List<UgcStandardVideoEs>>() {
        });
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (UgcStandardVideoEs ugcStandardVideoEs : ugcStandardVideoEsList) {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(ugcStandardVideoEs);
            indexQueries.add(indexQuery);
        }

        // 创建索引之前先删除索引（如果存在）
        IndexOperations indexOperations = restTemplate.indexOps(UgcStandardVideoEs.class);
        if (indexOperations.exists()) {
            indexOperations.delete();
        }

        elasticsearchService.createIndexAndMapping(UgcStandardVideoEs.class);

        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(UgcStandardVideoEs.class.getAnnotation(Document.class).indexName()));
        indexOperations.refresh();
    }

    public static void deleteAlbumEs(ElasticSearchService elasticsearchService) {
        elasticsearchService.deleteIndex(AlbumEs.class);
    }

    public static void ifExistThenDeleteIndex(ElasticsearchRestTemplate restTemplate) {
        IndexOperations indexOperations = restTemplate.indexOps(AlbumEs.class);
        if (indexOperations.exists()) {
            indexOperations.delete();
        }
    }

    @Data
    @Document(indexName = "api_search_album_v2")
    @Setting(settingPath = "/esConfig/setting.json")
    public static class AlbumEs {

        //节目SID
        @Field(type = FieldType.Keyword)
        private String sid;

        //节目标题
        @Field(type = FieldType.Text)
        private String title;

        //副标题
        @Field(type = FieldType.Keyword)
        private String epstitle;

        //会员角标CODE
        @Field(type = FieldType.Keyword)
        private String markCode;

        //导演（或者主持人）
        @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
        private String director;

        //演员（或者嘉宾）
        @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
        private String actor;

        //标签列表
        @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
        private String tags;

        @Field(type = FieldType.Keyword)
        private String source;

        @Field(type = FieldType.Keyword)
        private int status;

        @Field(type = FieldType.Keyword)
        private String verticalIcon;

        @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
        private String area;

        @Field(type = FieldType.Float, index = false)
        private float sourceScore;

        @Field(type = FieldType.Integer)
        private Integer year;


        @Field(type = FieldType.Integer)
        private int featureType;

        @Field(type = FieldType.Integer)
        private int payStatus;

        @Field(type = FieldType.Keyword)
        private String copyrightCode;

        @Field(type = FieldType.Keyword)
        private String contentType;

        @Field(type = FieldType.Keyword)
        private String horizontalIcon;

        @Field(type = FieldType.Keyword)
        private String programInfo;

        @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
        private String language;

        @Field(type = FieldType.Integer)
        private Integer vipType;

        @Field(type = FieldType.Keyword)
        private String showTime;

        private String brief;


        @Id
        @Field(type = FieldType.Keyword)
        private String virtualSid;

        //节目标题（用于拼音搜索，网上有一种方法是直接让分词即支持拼音又支持中文，但是测试效果不了，例如输入哥哥，会搜索出格格，因此还是采用单独字段的方式）
        private String titlePinyin;

        //导演（或者主持人）
        private String directorPinyin;

        //演员（或者嘉宾）
        private String actorPinyin;

        @Field(type = FieldType.Float, index = false)
        private float functionScore = 0;

        @Field(type = FieldType.Float, index = false)
        private Float releScore;

        @Field(type = FieldType.Float, index = false)
        private double sortDefine;

        private List<String> multipleSourceCode;

        @Field(type = FieldType.Integer, index = false)
        private Integer isSeries = 0;

        @CompletionField(maxInputLength = 16)
        private Completion seriesTitle;

        private Completion suggestTitlePinyin;

        private Completion seriesTitlePinyin;

        @Field(type = FieldType.Integer, index = false)
        private Integer isHot = 0;

        @Field(type = FieldType.Integer)
        private Integer dayNo;

        @Field(type = FieldType.Long)
        private Long oppoHot;

        @Field(type = FieldType.Integer)
        private Integer hasVirtualSid = 0;

        private String webUrl;

        private String deepLink;

    }
}
