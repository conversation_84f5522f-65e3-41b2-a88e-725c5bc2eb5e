package util;

import org.mockserver.client.MockServerClient;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;


public class HttpMockUtil {



    /**
     * 算法接口
     * @param client
     */
    public static void mockAlgorithmResponse(MockServerClient client) throws IOException {
        String vipInnerAddResponse = FileUtils.readFileToString(new ClassPathResource("\\mock\\algorithm.json").getFile(), "UTF-8");
        client.when(HttpRequest.request().withPath("/v2/recommend/lvAlgorithm/forward")
                .withMethod("GET")).respond(HttpResponse.response().withBody(vipInnerAddResponse));
    }

    /**
     * 电视媒资库腾讯节目查询接口
     * @param client
     * @throws IOException
     */
    public static void mockTencentSearchResponse(MockServerClient client) throws IOException {
        String vipInnerAddResponse = FileUtils.readFileToString(new ClassPathResource("\\mock\\misTencentProgram.json").getFile(), "UTF-8");
        client.when(HttpRequest.request().withPath("/meizi/misTencentProgram/pageList")
                .withMethod("POST")).respond(HttpResponse.response().withBody(vipInnerAddResponse));
    }
    /**
     * 电视媒资库爱奇艺节目查询接口
     * @param client
     * @throws IOException
     */
    public static void mockIqiyiSearchResponse(MockServerClient client) throws IOException {
        String vipInnerAddResponse = FileUtils.readFileToString(new ClassPathResource("\\mock\\misIqiyiProgram.json").getFile(), "UTF-8");
        client.when(HttpRequest.request().withPath("/meizi/misIqiyiProgram/pageList")
                .withMethod("POST")).respond(HttpResponse.response().withBody(vipInnerAddResponse));
    }


    public static void mockHistoryResponse(MockServerClient client) throws IOException {
        String vipInnerAddResponse = FileUtils.readFileToString(new ClassPathResource("\\mock\\history.json").getFile(), "UTF-8");
        client.when(HttpRequest.request().withPath("/v1/relation/relationList")
                .withMethod("POST")).respond(HttpResponse.response().withBody(vipInnerAddResponse));
    }

    /**
     * 资源位策略匹配
     * @param client
     */
    public static void mockMatchResourceStrategyResponse(MockServerClient client) throws IOException {
        String response = FileUtils.readFileToString(new ClassPathResource("mock/outside/strategyMatchResult.json").getFile(), "UTF-8");

        client.when(HttpRequest.request()
                .withPath("/v1/rule/match")
                .withMethod("POST")).withId("/v1/rule/match").respond(HttpResponse.response().withBody(response));
    }

}
