package util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.heytap.longvideo.client.arrange.*;
import com.heytap.longvideo.client.arrange.entity.*;
import com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO;
import com.heytap.longvideo.client.arrange.model.response.ItemForListCardWithMoreVO;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchKeyword;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchRecommend;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.search.executors.app.SearchInterveneServiceImpl;
import esa.rpc.test.support.mock.Mocker;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang.CharEncoding.UTF_8;
import com.heytap.longvideo.client.arrange.search.api.LvSearchKeywordRpcApi;


public class ArrangeRpcMockerUtil {

    /*
        这两个方法没有用上,但是有类依赖报错,因此注释掉
     */

//    public static Mocker getSearchRecommendMockWithNUll() {
//        //RPC接口mock
//        Mocker mocker = Mocker.newBuilder()
//                .interfaceName(LvSearchRecommendRpcApi.class.getName())
//                .methodName("getSearchRecommend");
//        RpcResult<LvSearchRecommend> resp =new RpcResult(0,"success");
//
//        LvSearchRecommend lvSearchRecommend=null;
//        resp.setData(lvSearchRecommend);
//        mocker.returnValue(resp).configure();
//        return mocker;
//    }
//
//    public static Mocker getSearchRecommendMock() {
//        //RPC接口mock
//        Mocker mocker = Mocker.newBuilder()
//                .interfaceName(LvSearchRecommendRpcApi.class.getName())
//                .methodName("getSearchRecommend");
//        RpcResult<LvSearchRecommend> resp =new RpcResult(0,"success");
//
//        LvSearchRecommend lvSearchRecommend= JSON.parseObject("{\"name\":\"内容池测试\",\"title\":\"内容池测试\",\"queryKeyword\":\"pre_小猪\",\"linkType\":\"4\",\"linkValue\":\"subject_20230526154930\",\"isShowMarkCode\":\"1\"}",LvSearchRecommend.class);
//        resp.setData(lvSearchRecommend);
//        mocker.returnValue(resp).configure();
//        return mocker;
//    }

    public static Mocker getItemForListCardMock() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(SubjectRpcApi.class.getName())
                .methodName("getItemForListCard");
        RpcResult<List<MtvSubjectitem>> resp =new RpcResult(0,"success");

        List<MtvSubjectitem> subjectitemList= JSON.parseArray("[{\"linkType\":\"1\",\"linkValue\":\"625489226036695041\"},{\"linkType\":\"1\",\"linkValue\":\"841235307864203264\"},{\"linkType\":\"1\",\"linkValue\":\"853499825038209024\"},{\"linkType\":\"1\",\"linkValue\":\"631002331600855040\"}]",MtvSubjectitem.class);
        resp.setData(subjectitemList);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker selectThirdSearch() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvSearchKeywordRpcApi.class.getName())
                .methodName("selectThirdSearchKeyWordList");
        RpcResult<List<LvSearchKeyword>> resp =new RpcResult(0,"success");

        List<LvSearchKeyword> subjectitemList= JSON.parseArray("[{\"sid\": \"821148321858134016\",\"title\": \"爱的二八定律\",\"query_keyword\": \"pre_爱的二八定律\",\"source\": \"tencent\",\"orderIndex\": 1}]",LvSearchKeyword.class);
        resp.setData(subjectitemList);
        mocker.returnValue(resp).configure();
        return mocker;
    }


    public static Mocker getMaterialsRpcMock() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvContentMaterialRpcApi.class.getName())
                .methodName("getMaterials");
        RpcResult<Map<String, List<LvContentMaterialVO>>> resp =new  RpcResult(0,"success");
        Map<String, List<LvContentMaterialVO>> cons=new HashMap<>();
        cons= JSONObject.parseObject("{\"625489226036695041\":[{\"brief\":\"素材A\",\"imgUrl\":\"https://dhfs-test-cpc.wanyol.com/20230310101829473.jpg\",\"imgStyle\":1,\"size\":\"1200x1920\",\"class\":\"com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO\",\"sid\":\"625229331139940355\",\"imgType\":\"jpg\"},{\"brief\":\"素材B\",\"imgUrl\":\"https://dhfs-test-cpc.wanyol.com/20230310101829473.jpg\",\"imgStyle\":2,\"size\":\"608x1080\",\"class\":\"com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO\",\"sid\":\"625229331139940355\",\"imgType\":\"jpg\"}],\"841235307864203264\":[{\"brief\":\"横图横图横图\",\"imgUrl\":\"https://dhfs-test-cpc.wanyol.com/20230406161455680.gif\",\"imgStyle\":2,\"size\":\"273x486\",\"class\":\"com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO\",\"sid\":\"625229331139940355\",\"imgType\":\"gif\"},{\"brief\":\"横图横图横图\",\"imgUrl\":\"https://dhfs-test-cpc.wanyol.com/20230406161455680.gif\",\"imgStyle\":1,\"size\":\"273x486\",\"class\":\"com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO\",\"sid\":\"625229331139940355\",\"imgType\":\"gif\"}],\"631002331600855040\":[{\"brief\":\"素材B\",\"imgUrl\":\"https://dhfs-test-cpc.wanyol.com/20230310101829473.jpg\",\"imgStyle\":1,\"size\":\"608x1080\",\"class\":\"com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO\",\"sid\":\"625229331139940355\",\"imgType\":\"jpg\"}]}",new TypeReference<Map<String,List<LvContentMaterialVO>>>(){});
        resp.setData(cons);

        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker imageTagRpcMock() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(ImageTagRpcApi.class.getName())
                .methodName("findByStatus");
        RpcResult<List<MtvImageTag>> resp =new  RpcResult(0,"success");

        List<MtvImageTag> results=JSONArray.parseArray("[{\"publishTime\":\"2022-06-08 15:55:01\",\"code\":\"trailer\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/1615801573537_103x110.png\",\"name\":\"trailer\",\"endVersion\":\"9.99.9\",\"id\":2,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-06-08 15:55:01\",\"code\":\"vip\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/1615801573538_98x108.png\",\"name\":\"改VIP\",\"endVersion\":\"9.99.9\",\"id\":3,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"brand\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230329104810667.png\",\"name\":\"品牌方\",\"endVersion\":\"9.99.9\",\"id\":4,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-06-08 15:55:01\",\"code\":\"test\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://longvideo.heytapimage.com/20200602165654689.png\",\"name\":\"test\",\"endVersion\":\"9.99.9\",\"id\":5,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-06-08 15:55:01\",\"code\":\"sunyaotest\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20210508170244611.png\",\"name\":\"sunyaotest\",\"endVersion\":\"9.99.9\",\"id\":6,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"sohu\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230113173055786.png\",\"name\":\"搜狐\",\"endVersion\":\"9.99.9\",\"id\":7,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"qq\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20210519095617460.png\",\"name\":\"腾讯\",\"endVersion\":\"9.99.9\",\"id\":8,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"funshion\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20210519095600305.png\",\"name\":\"funshion\",\"endVersion\":\"9.99.9\",\"id\":9,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"huashi\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230113173002872.png\",\"name\":\"欢太视频\",\"endVersion\":\"9.99.9\",\"id\":10,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"senyu\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230113173013687.png\",\"name\":\"欢太视频\",\"endVersion\":\"9.99.9\",\"id\":13,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:13\",\"code\":\" funshion_vip_1\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135531772.png\",\"name\":\"专辑VIP角标\",\"endVersion\":\"9.99.9\",\"id\":14,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-02 17:37:35\",\"code\":\" funshion_vip_1_e\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135541929.png\",\"name\":\"剧集VIP角标\",\"endVersion\":\"9.99.9\",\"id\":15,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-06 15:58:45\",\"code\":\"funshion_single_2\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135554601.png\",\"name\":\"专辑付费角标\",\"endVersion\":\"9.99.9\",\"id\":16,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:13\",\"code\":\"funshion_vip_1\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135606489.png\",\"name\":\"VIP节目海报角标\",\"endVersion\":\"9.99.9\",\"id\":17,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:13\",\"code\":\"funshion_vip_1_e\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135616198.png\",\"name\":\"VIP剧集角标\",\"endVersion\":\"9.99.9\",\"id\":18,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-06 15:58:45\",\"code\":\"funshion_single_2_e\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135624812.png\",\"name\":\"剧集付费角标\",\"endVersion\":\"9.99.9\",\"id\":19,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-01-13 17:31:02\",\"code\":\"mgmobile\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230113172936308.png\",\"name\":\"芒果TV\",\"endVersion\":\"9.99.9\",\"id\":20,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-02 17:32:07\",\"code\":\"mgmobile_vip_1\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135638086.png\",\"name\":\"芒果VIP专辑角标\",\"endVersion\":\"9.99.9\",\"id\":21,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-02 17:32:07\",\"code\":\"mgmobile_vip_1_e\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135649234.png\",\"name\":\"芒果VIP剧集角标\",\"endVersion\":\"9.99.9\",\"id\":22,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:05\",\"code\":\"mongo_single_2\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135700676.png\",\"name\":\"芒果单片付费剧头角标\",\"endVersion\":\"9.99.9\",\"id\":23,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:05\",\"code\":\"mgmobile_single_2\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135709764.png\",\"name\":\"芒果专辑单片付费角标\",\"endVersion\":\"9.99.9\",\"id\":24,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:05\",\"code\":\"mgmobile_single_2_e\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135718110.png\",\"name\":\"芒果剧集单片付费角标\",\"endVersion\":\"9.99.9\",\"id\":25,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-03-29 10:34:33\",\"code\":\"YG\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135730443.png\",\"name\":\"预告角标\",\"endVersion\":\"9.99.9\",\"id\":26,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-29 16:49:07\",\"code\":\"gengxin1\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135741524.png\",\"name\":\"更新\",\"endVersion\":\"9.99.9\",\"id\":27,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-02 19:27:27\",\"code\":\"tuijian\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135754486.png\",\"name\":\"推荐\",\"endVersion\":\"9.99.9\",\"id\":28,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-08-26 17:51:40\",\"code\":\"测试\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220815161902210.jpg\",\"name\":\"测试\",\"endVersion\":\"9.99.9\",\"id\":29,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:05\",\"code\":\"shoubo\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135808436.png\",\"name\":\"首播\",\"endVersion\":\"9.99.9\",\"id\":30,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:05\",\"code\":\"mianfei\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135821015.png\",\"name\":\"免费\",\"endVersion\":\"9.99.9\",\"id\":31,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:02:05\",\"code\":\"zhuanti\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135841694.png\",\"name\":\"专题\",\"endVersion\":\"9.99.9\",\"id\":32,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"YY_mysb\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135857317.png\",\"name\":\"免费首播\",\"endVersion\":\"9.99.9\",\"id\":33,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"rebo_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135908663.png\",\"name\":\"热播\",\"endVersion\":\"9.99.9\",\"id\":34,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"redian_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135919796.png\",\"name\":\"热点\",\"endVersion\":\"9.99.9\",\"id\":35,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"xianmian_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135932501.png\",\"name\":\"限免\",\"endVersion\":\"9.99.9\",\"id\":36,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"dajieju_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135942462.png\",\"name\":\"大结局\",\"endVersion\":\"9.99.9\",\"id\":37,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"dubo_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135952905.png\",\"name\":\"独播\",\"endVersion\":\"9.99.9\",\"id\":38,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"duanju_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140005474.png\",\"name\":\"短剧\",\"endVersion\":\"9.99.9\",\"id\":39,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"gaoqing_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140016986.png\",\"name\":\"高清\",\"endVersion\":\"9.99.9\",\"id\":40,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"jifeng_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140029576.png\",\"name\":\"季风\",\"endVersion\":\"9.99.9\",\"id\":41,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:54\",\"code\":\"jingdian_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140042910.png\",\"name\":\"经典\",\"endVersion\":\"9.99.9\",\"id\":42,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:48\",\"code\":\"languang_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140054045.png\",\"name\":\"蓝光\",\"endVersion\":\"9.99.9\",\"id\":43,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:48\",\"code\":\"pianhua_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140106215.png\",\"name\":\"片花\",\"endVersion\":\"9.99.9\",\"id\":44,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:48\",\"code\":\"zizhi_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140118675.png\",\"name\":\"自制\",\"endVersion\":\"9.99.9\",\"id\":45,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-08 14:01:48\",\"code\":\"wanjie_\",\"beginVersion\":\"1.0.1\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908140132641.png\",\"name\":\"完结\",\"endVersion\":\"9.99.9\",\"id\":46,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-09-17 09:58:08\",\"code\":\"new\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908163353489.png\",\"name\":\"new2\",\"endVersion\":\"5.26.1\",\"id\":48,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-29 17:03:47\",\"code\":\"funshion_vip_1\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20221229170019888.png\",\"name\":\"专辑VIP角标2\",\"endVersion\":\"9.99.9\",\"id\":49,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"code\":\"22\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220917095908499.png\",\"name\":\"11333\",\"endVersion\":\"5.27.0\",\"id\":51,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"code\":\"66\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220917102118082.png\",\"name\":\"55\",\"endVersion\":\"9.99.9\",\"id\":53,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"code\":\"sad2\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"\",\"name\":\"we22312\",\"endVersion\":\"9.99.9\",\"id\":54,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"code\":\"dsf34\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"\",\"name\":\"de432s\",\"endVersion\":\"9.99.9\",\"id\":55,\"type\":1,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"code\":\"11\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20221031100253075.jpg\",\"name\":\"111\",\"endVersion\":\"9.99.9\",\"id\":57,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-19 18:35:17\",\"code\":\"feature\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20221219183510933.png\",\"name\":\"正片\",\"endVersion\":\"9.99.9\",\"id\":58,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-02-16 20:25:22\",\"code\":\"letv\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230113173002872.png\",\"name\":\"乐视\",\"endVersion\":\"9.99.9\",\"id\":59,\"type\":2,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"code\":\"test\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20221222161824719.png\",\"name\":\"swxx\",\"endVersion\":\"9.99.9\",\"id\":60,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2022-12-29 16:49:07\",\"code\":\"gengxin1\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20220908135741524.png\",\"name\":\"更新\",\"endVersion\":\"9.99.9\",\"id\":62,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1},{\"publishTime\":\"2023-03-29 10:34:33\",\"code\":\"xjyg\",\"beginVersion\":\"5.26.0\",\"imageUrl\":\"https://dhfs-test-cpc.wanyol.com/20230329094716521.png\",\"name\":\"选集预告\",\"endVersion\":\"9.99.9\",\"id\":65,\"type\":0,\"class\":\"com.heytap.longvideo.client.arrange.entity.MtvImageTag\",\"status\":1}]",MtvImageTag.class);
        resp.setData(results);

        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker getAlbumRecommendInfoMock() {
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(AlbumRankRpcApi.class.getName())
                .methodName("getAlbumRecommendInfo");
        RpcResult<Map<String, AlbumRecommendInfo>> rpcResult = new RpcResult(0, "success");

        Map<String, AlbumRecommendInfo> albumRecommendInfoMap = new HashMap<>();
        albumRecommendInfoMap = JSONObject.parseObject("{\"testSid\":{\"recommendInfo\":\"RecommendInfo\",\"recommendInfoDp\":\"RecommendInfoDp\"}}", new TypeReference<Map<String, AlbumRecommendInfo>>() {
        });

        rpcResult.setData(albumRecommendInfoMap);

        mocker.returnValue(rpcResult).configure();
        return mocker;
    }

    public static Mocker getLvSearchInterveneById() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvSearchInterveneRpcApi.class.getName())
                .methodName("getById");
        RpcResult<LvSearchIntervene> resp = new RpcResult(0, "success");
        mocker.returnValue(resp).mockImpl(new SearchInterveneServiceImpl()).configure();
        return mocker;
    }

    public static Mocker getSubjectItemForListCardWithMore() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(SubjectRpcApi.class.getName())
                .methodName("getItemForListCardWithMore");
        RpcResult<ItemForListCardWithMoreVO> resp = new RpcResult(0, "success");
        String str = FileUtils.readFileToString(new ClassPathResource("mock/itemForListCardWithMoreVO.json").getFile(), UTF_8);
        ItemForListCardWithMoreVO result = JSON.parseObject(str, ItemForListCardWithMoreVO.class);
        resp.setData(result);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker getItemsWithCache() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvContentItemRpcApi.class.getName())
                .methodName("getItemsWithCache");
        RpcResult<Page<LvContentItem>> resp = new RpcResult(0, "success");
        String str = FileUtils.readFileToString(new ClassPathResource("mock/getItemsWithCacheRpc.json").getFile(), UTF_8);
        Page<LvContentItem> result = JSON.parseObject(str,  new TypeReference<Page<LvContentItem>>(){});
        resp.setData(result);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker getItemCount() throws IOException {
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvContentItemRpcApi.class.getName())
                .methodName("getItemCountByContentPoolCode");

        RpcResult<Integer> resp = new RpcResult(0, "success");
        resp.setData(2);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker getSidListByPoolCode(List<String> sidList) throws IOException {
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvContentItemRpcApi.class.getName())
                .methodName("getSidListByPoolCode");

        RpcResult<List<String>> resp = new RpcResult(0, "success");
        resp.setData(sidList);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker getMinorsPoolCode() throws IOException {
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvContentPoolRpcApi.class.getName())
                .methodName("getMinorsPoolCode");

        RpcResult<List<MinorPoolCodeRecord>> resp = new RpcResult(0, "success");

        List<MinorPoolCodeRecord> results = new ArrayList<>();
        MinorPoolCodeRecord minorPoolCodeRecord = new MinorPoolCodeRecord("last", "cp_00000491");
        results.add(minorPoolCodeRecord);
        resp.setData(results);
        mocker.returnValue(resp).configure();
        return mocker;
    }

}