package util;

import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.github.stuxuhai.jpinyin.PinyinHelper;

/*
 * Description string工具类
 * Date 10:45 2021/12/6
 * Author songjiajia 80350688
*/
public class StringUtil extends org.apache.commons.lang3.StringUtils {

    public static String toPinyin(String str,PinyinFormat format){
        StringBuilder pinyinBuild = new StringBuilder();
        char[] hanYuArr = str.toCharArray();
        for (int i = 0, len = hanYuArr.length; i < len; i++) {
            if (Character.toString(hanYuArr[i]).matches("[\\u4E00-\\u9FA5]+")) {
                String[] pys = PinyinHelper.convertToPinyinArray(hanYuArr[i],format);
                pinyinBuild.append(pys[0]);
            } else {
                pinyinBuild.append(hanYuArr[i]);
            }
        }
        return pinyinBuild.toString();
    }
}
