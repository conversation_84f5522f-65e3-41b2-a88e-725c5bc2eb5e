package util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import esa.rpc.test.support.mock.Mocker;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.apache.commons.lang.CharEncoding.UTF_8;


public class MediaRpcMockerUtil {

    public static Mocker getBySidsFilterInvalid() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(StandardAlbumRpcApi.class.getName())
                .methodName("getBySidsFilterInvalid");
        RpcResult<Map<String/* sid */, StandardAlbum>> resp =new RpcResult(0,"success");

        Map<String/* sid */, StandardAlbum> results=new HashMap<>();
        StandardAlbum standardAlbum1= JSON.parseObject("{\"sid\":\"625489226036695041\",\"highLightVid\":\"759402922462355456\",\"title\":\"东北抗日联军\",\"subTitle\":\"共产国际战士找出卧底\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"huashi\",\"sourceAlbumId\":\"M05CJF6KK3\",\"sourceWebUrl\":\"\",\"sourceType\":2,\"sourceScore\":\"8.1\",\"programType\":\"tv\",\"subProgramType\":\"tv\",\"unit\":1,\"category\":\"战争\",\"duration\":0,\"director\":\"李文岐\",\"actor\":\"王洛勇|刘威葳|成泰燊|张秋歌\",\"year\":2015,\"area\":\"中国内地\",\"tags\":\"战争\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《东北抗日联军》：东北抗日联军长达十四年的抗日战争史同红军长征史、南方八省红军游击队三年游击战争史一样，是我党我军历史上最艰苦卓绝的篇章。在漫长的岁月里，中国共产党创建的抗日联军，身处没有后方支援、粮弹两无，又与中共中央联络中断的情况下，以草根树皮为食，以林海雪原为家，以日寇之衣为衣、以日寇之枪为枪抗战不已，纵横驰骋，大起大落，大悲大喜，用鲜血和生命谱写了一段可歌可泣、波澜壮阔、地域特色浓郁独特的故事。\",\"validEpisode\":46,\"totalEpisode\":46,\"completed\":1,\"brief\":\"共产国际战士找出卧底\",\"period\":\"\",\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=615e0b2140695304be9eec96c52a97c5&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/625489226036695041_vImg.jpeg\",\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=21ce143424116135008355aa0f701a59&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/625489226036695041_hImg.png\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=615e0b2140695304be9eec96c52a97c5&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/625489226036695041_vImg.jpeg\",\"horizontalImage\":\"http://img3.funshion.com/sdw?oid=21ce143424116135008355aa0f701a59&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/625489226036695041_hImg.png\",\"backGroundColor\":\"#D6BFB0\",\"backGroundColorTwo\":\"#805A40\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"huashi\",\"programInfo\":\"全 46 集\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":3,\"showTime\":\"201507\",\"publishTime\":1681235238000,\"processStatus\":0,\"createTime\":1663383165000,\"updateTime\":1681235238000,\"sourceHot\":0.0,\"startTime\":-28800000,\"endTime\":-28800000,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"previewInfo\":\"[]\",\"mappingTags\":\"\"}",StandardAlbum.class);
        results.put("625489226036695041",standardAlbum1);
        StandardAlbum standardAlbum2=JSON.parseObject("{\"sid\":\"841235307864203264\",\"highLightVid\":\"858006067697856512\",\"title\":\"东海客栈\",\"subTitle\":\"浙东人民英勇无畏的抗战史\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"huashi\",\"sourceAlbumId\":\"M05CB5RYNC\",\"sourceWebUrl\":\"\",\"sourceType\":2,\"sourceScore\":\"8.5\",\"programType\":\"tv\",\"subProgramType\":\"tv\",\"unit\":1,\"category\":\"战争\",\"duration\":0,\"director\":\"许敏勇|冯玉玺\",\"actor\":\"高昌昊|皇羽軒|王一剑|冯玉玺|闫鹿杨\",\"year\":2023,\"area\":\"中国内地\",\"tags\":\"战争\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《东海客栈》：1942年抗日战争进入关键阶段，浙东地区的日军利用占据的港口源源不断地向日本本土输送物资。而活跃在这一带的新四军浙东纵队为了破坏日军的交通线，打击日军。决定在宁波和舟山之间的嘉海县建立一个秘密联络站。经过精心挑选...\",\"validEpisode\":24,\"totalEpisode\":24,\"completed\":1,\"brief\":\"浙东人民英勇无畏的抗战史\",\"period\":\"\",\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=4068d053d172137ec54837a2d1029646&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/841235307864203264_vIcon.jpeg\",\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=47192357dbf9f40be61f41cad6cc9454&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/841235307864203264_hIcon.jpeg\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=4068d053d172137ec54837a2d1029646&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/841235307864203264_vImg.jpeg\",\"horizontalImage\":\"http://img1.funshion.com/sdw?oid=47192357dbf9f40be61f41cad6cc9454&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/841235307864203264_hImg.jpeg\",\"backGroundColor\":\"#D6CBB0\",\"backGroundColorTwo\":\"#806E40\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"huashi\",\"programInfo\":\"全 24 集\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":8.00,\"nowPrice\":4.00,\"vipPrice\":2.00,\"payEffectDays\":10000,\"showTime\":\"202302\",\"publishTime\":1680870382000,\"processStatus\":0,\"createTime\":1675857660000,\"updateTime\":1680870382000,\"sourceHot\":0.0,\"startTime\":1676980946000,\"endTime\":-28800000,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"previewInfo\":\"[]\",\"mappingTags\":\"\"}",StandardAlbum.class);
        results.put("841235307864203264",standardAlbum2);
        StandardAlbum standardAlbum3=JSON.parseObject("{\"sid\":\"853499825038209024\",\"highLightVid\":\"854584327957295104\",\"title\":\"中国乒乓之绝地反击\",\"subTitle\":\"球不落地，永不放弃\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"huashi\",\"sourceAlbumId\":\"M05CDHVNB3\",\"sourceWebUrl\":\"\",\"sourceType\":2,\"sourceScore\":\"8.1\",\"programType\":\"movie\",\"subProgramType\":\"movie\",\"unit\":1,\"category\":\"动作|剧情\",\"duration\":8178,\"director\":\"俞白眉|邓超\",\"actor\":\"邓超|孙俪|段博文\",\"year\":2023,\"area\":\"内地\",\"tags\":\"运动|剧情\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《中国乒乓之绝地反击》：影片讲述在男乒被“欧洲列强”碾压的 90年代初，留洋教练戴敏佳请缨回国，带领新兵老将在天津向“列强”发起反击，拯救男乒的故事。男乒的寂寞无敌，在那传奇一夜奏响序曲。\",\"validEpisode\":1,\"totalEpisode\":1,\"completed\":1,\"brief\":\"球不落地，永不放弃\",\"period\":\"\",\"verticalIcon\":\"http://img.funshion.com/sdw?oid=d0e6b3cc24e21a84b1156a1006f1e8e9&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/853499825038209024_vIcon.jpeg\",\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=4839aaabab3524f4935fbd14c6b89c49&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/853499825038209024_hIcon.jpeg\",\"verticalImage\":\"http://img.funshion.com/sdw?oid=d0e6b3cc24e21a84b1156a1006f1e8e9&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/853499825038209024_vImg.jpeg\",\"horizontalImage\":\"http://img1.funshion.com/sdw?oid=4839aaabab3524f4935fbd14c6b89c49&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/853499825038209024_hImg.jpeg\",\"backGroundColor\":\"#D6B7B0\",\"backGroundColorTwo\":\"#804D40\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"huashi\",\"programInfo\":\"\",\"medium\":2,\"payStatus\":1,\"vipType\":1,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"funshion_vip_1\",\"formerPrice\":10.00,\"nowPrice\":10.00,\"vipPrice\":6.00,\"payEffectDays\":30,\"showTime\":\"202301\",\"publishTime\":1681244989000,\"processStatus\":0,\"createTime\":1678781749000,\"updateTime\":1681280286000,\"sourceHot\":0.0,\"startTime\":1679040314000,\"endTime\":-28800000,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"preOnlineTime\":1680192000000,\"previewInfo\":\"[]\",\"mappingTags\":\"\"}",StandardAlbum.class);
        results.put("853499825038209024",standardAlbum3);
        StandardAlbum standardAlbum4=JSON.parseObject("{\"sid\":\"631002331600855040\",\"title\":\"中国之光福建智造\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"sohu\",\"sourceAlbumId\":\"9716137\",\"sourceWebUrl\":\"http://tv.sohu.com/s2021/zyzgzgfzjz/\",\"sourceType\":2,\"sourceScore\":\"6.2\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"纪实\",\"duration\":0,\"director\":\"\",\"actor\":\"\",\"year\":2021,\"area\":\"内地\",\"tags\":\"科技|社会|人文|纪录片|纪实\",\"language\":\"\",\"honor\":\"\",\"information\":\"《中国之光 福建智造》是一部讲述福建明星企业科技创新的系列专题片。这些企业或在行业内走在全国或世界前列，或在产业链供应链处于关键核心地位，用他们鲜活生动的故事展示福建企业的自主科创成就。\",\"validEpisode\":11,\"totalEpisode\":0,\"completed\":0,\"brief\":\"福建企业的自主科创成就\",\"period\":\"\",\"verticalIcon\":\"http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20210705/vrsa_ver9716137.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/631002331600855040_vIcon.jpeg\",\"horizontalIcon\":\"http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20210705/vrsa_hor9716137.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/631002331600855040_hIcon.jpeg\",\"verticalImage\":\"http://photocdn.tv.sohu.com/img/20210705/vrsa_ver9716137.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/631002331600855040_vImg.jpeg\",\"horizontalImage\":\"http://photocdn.tv.sohu.com/img/20210705/vrsa_hor9716137.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/631002331600855040_hImg.jpeg\",\"backGroundColor\":\"#B0C5D6\",\"backGroundColorTwo\":\"#406380\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"sohu\",\"programInfo\":\"08-05 期\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":10043,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":0},{\\\"level\\\":2,\\\"payStatus\\\":0},{\\\"level\\\":3,\\\"payStatus\\\":0},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1667811804000,\"processStatus\":0,\"createTime\":1654679129000,\"updateTime\":1680151146000,\"sourceHot\":0.0,\"startTime\":-28800000,\"endTime\":-28800000,\"sourceSeriesId\":\"\",\"prePush\":0,\"mappingTags\":\"\"}",StandardAlbum.class);
        results.put("631002331600855040",standardAlbum4);
        StandardAlbum standardAlbum5=JSON.parseObject("{\"sid\":\"654209460109254656\",\"title\":\"披荆斩棘的哥哥\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yqaa67.7.dfa0e81816e49778\",\"sourceWebUrl\":\"https://m.mgtv.com/h/367750.html\",\"sourceType\":2,\"sourceScore\":\"9.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"黄贯中|林志炫|陈小春|谢天华|林晓峰|张智霖|梁汉文|赵文卓|陈辉|黄征|张晋|胡海泉|言承旭|姚中仁|张淇|欧阳靖|庄濠全|李承铉|刘端端|尹正|周延|张云龙|李铢衔|刘聪|瑞奇|高瀚宇|麦亨利|刘迦|李响|白举纲|布瑞吉\",\"year\":2021,\"area\":\"内地\",\"tags\":\"明星|真人秀|音乐|芒果出品\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《披荆斩棘的哥哥》为芒果tv全景音乐竞演综艺，嘉宾包括歌手、舞者、演员、音乐制作人等等，嘉宾们彼此挑战，披荆斩棘，通过男人之间的彼此探索、家族建立的进程，诠释“滚烫的人生永远发光”，见证永不陨落的精神力。\",\"validEpisode\":37,\"totalEpisode\":37,\"completed\":1,\"brief\":\"芒果TV首档男性音乐竞演类节目\",\"period\":\"\",\"verticalIcon\":\"http://2img.hitv.com/preview/sp_images/2021/09/09/202109091042465199884.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654209460109254656_vImg.jpeg\",\"horizontalIcon\":\"http://4img.hitv.com/preview/sp_images/2021/08/09/202108091038330384461.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654209460109254656_hImg.jpeg\",\"verticalImage\":\"http://2img.hitv.com/preview/sp_images/2021/09/09/202109091042465199884.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654209460109254656_vImg.jpeg\",\"horizontalImage\":\"http://4img.hitv.com/preview/sp_images/2021/08/09/202108091038330384461.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654209460109254656_hImg.jpeg\",\"backGroundColor\":\"#B0D6BF\",\"backGroundColorTwo\":\"#40805A\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"全 37 期\",\"medium\":2,\"payStatus\":1,\"vipType\":1,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"mgmobile_vip_1\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681291564000,\"processStatus\":0,\"createTime\":1649828810000,\"updateTime\":1681291564000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[\\\"106760\\\"]\",\"prePush\":0,\"mappingTags\":\"播报|真人秀|音乐|其它\"}",StandardAlbum.class);
        results.put("654209460109254656",standardAlbum5);
        StandardAlbum standardAlbum6=JSON.parseObject("{\"sid\":\"654209461333991424\",\"title\":\"披荆斩棘的哥哥 舞台纯享版\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yN6NEY.7.4bd4b1c284766a53\",\"sourceWebUrl\":\"https://m.mgtv.com/h/385819.html\",\"sourceType\":2,\"sourceScore\":\"9.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"黄贯中|林志炫|陈小春|谢天华|林晓峰|张智霖|梁汉文|赵文卓|陈辉|黄征|张晋|胡海泉|言承旭|姚中仁|张淇|欧阳靖|庄濠全|李承铉|刘端端|尹正|周延|张云龙|李铢衔|刘聪|瑞奇|高瀚宇|麦亨利|刘迦|李响|白举纲|布瑞吉\",\"year\":2021,\"area\":\"内地\",\"tags\":\"明星|真人秀|音乐|芒果出品\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《披荆斩棘的哥哥 舞台纯享版》是《披荆斩棘的哥哥》官方衍生节目，会员尊享最燃舞台连连看，见证永不陨落的时代印记！\",\"validEpisode\":9,\"totalEpisode\":9,\"completed\":1,\"brief\":\"会员尊享高能舞台一次看够！\",\"period\":\"\",\"verticalIcon\":\"http://3img.hitv.com/preview/sp_images/2021/08/10/202108101421445474205.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654209461333991424_vImg.jpeg\",\"horizontalIcon\":\"http://2img.hitv.com/preview/sp_images/2021/08/10/202108101421509202579.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654209461333991424_hImg.jpeg\",\"verticalImage\":\"http://3img.hitv.com/preview/sp_images/2021/08/10/202108101421445474205.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654209461333991424_vImg.jpeg\",\"horizontalImage\":\"http://2img.hitv.com/preview/sp_images/2021/08/10/202108101421509202579.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654209461333991424_hImg.jpeg\",\"backGroundColor\":\"#D6BCB0\",\"backGroundColorTwo\":\"#805540\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"全 9 期\",\"medium\":2,\"payStatus\":1,\"vipType\":1,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"mgmobile_vip_1\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681295660000,\"processStatus\":0,\"createTime\":1649828405000,\"updateTime\":1681295660000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"mappingTags\":\"播报|真人秀|音乐|其它\"}",StandardAlbum.class);
        results.put("654209461333991424",standardAlbum6);
        StandardAlbum standardAlbum7=JSON.parseObject("{\"sid\":\"654252864709009408\",\"title\":\"哥哥的下午茶\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yNaOay.7.02c007f47e8fda31\",\"sourceWebUrl\":\"https://m.mgtv.com/h/387273.html\",\"sourceType\":2,\"sourceScore\":\"8.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"高瀚宇|李承铉|白举纲\",\"year\":2021,\"area\":\"内地\",\"tags\":\"访谈|真人秀|明星|芒果出品\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《哥哥的下午茶》“哥哥”特别企划！在“哥哥”披荆棘斩过后，一起来杯馥郁芬芳的下午茶，复盘舞台、追溯成长、回应话题、展现隐藏技能！哥哥请放松，披荆斩棘一起冲！\",\"validEpisode\":17,\"totalEpisode\":17,\"completed\":1,\"brief\":\"《披荆斩棘的哥哥》特别企划\",\"period\":\"\",\"verticalIcon\":\"http://4img.hitv.com/preview/internettv/sp_images/ott/2021/8/21/zongyi/387273/20210821141622732-new.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252864709009408_vImg.jpeg\",\"horizontalIcon\":\"http://2img.hitv.com/preview/internettv/sp_images/ott/2021/8/21/zongyi/387273/20210821141626213-new.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252864709009408_hImg.jpeg\",\"verticalImage\":\"http://4img.hitv.com/preview/internettv/sp_images/ott/2021/8/21/zongyi/387273/20210821141622732-new.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252864709009408_vImg.jpeg\",\"horizontalImage\":\"http://2img.hitv.com/preview/internettv/sp_images/ott/2021/8/21/zongyi/387273/20210821141626213-new.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252864709009408_hImg.jpeg\",\"backGroundColor\":\"#B0D0D6\",\"backGroundColorTwo\":\"#407680\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"全 17 期\",\"medium\":2,\"payStatus\":1,\"vipType\":1,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"mgmobile_vip_1\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681291585000,\"processStatus\":0,\"createTime\":1649828401000,\"updateTime\":1681291585000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"mappingTags\":\"访谈|真人秀|播报|其它\"}",StandardAlbum.class);
        results.put("654252864709009408",standardAlbum7);
        StandardAlbum standardAlbum8=JSON.parseObject("{\"sid\":\"654252863433940992\",\"title\":\"蒙面舞王 第二季\",\"subTitle\":\"蒙面舞王2\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yNaY6q.7.06aba74baa429a1f\",\"sourceWebUrl\":\"https://m.mgtv.com/h/387956.html\",\"sourceType\":2,\"sourceScore\":\"8.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"陈伟霆|容祖儿|韩庚|方俊\",\"year\":2021,\"area\":\"内地\",\"tags\":\"真人秀|竞技\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《蒙面舞王》是一档明星和素人共同搭档争夺擂主的舞蹈比赛，首档所有艺人戴上面具，大秀舞技的节目。超过20位艺人分组对抗角逐擂主，全凭舞艺一争高下。\",\"validEpisode\":10,\"totalEpisode\":10,\"completed\":1,\"brief\":\"蒙面类舞蹈竞演节目\",\"period\":\"\",\"verticalIcon\":\"http://3img.hitv.com/preview/sp_images/2021/08/27/202108271932061234692.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252863433940992_vImg.jpeg\",\"horizontalIcon\":\"http://3img.hitv.com/preview/sp_images/2021/08/27/202108271931567513879.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252863433940992_hImg.jpeg\",\"verticalImage\":\"http://3img.hitv.com/preview/sp_images/2021/08/27/202108271932061234692.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252863433940992_vImg.jpeg\",\"horizontalImage\":\"http://3img.hitv.com/preview/sp_images/2021/08/27/202108271931567513879.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252863433940992_hImg.jpeg\",\"backGroundColor\":\"#D6CDB0\",\"backGroundColorTwo\":\"#807040\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"全 10 期\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681292870000,\"processStatus\":0,\"createTime\":1649828396000,\"updateTime\":1681292870000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[\\\"106045\\\"]\",\"prePush\":0,\"mappingTags\":\"真人秀|竞技\"}",StandardAlbum.class);
        results.put("654252863433940992",standardAlbum8);
        StandardAlbum standardAlbum9=JSON.parseObject("{\"sid\":\"654252862674771968\",\"title\":\"抖音新潮好物夜\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yNq7Na.7.f12979fe563a0070\",\"sourceWebUrl\":\"https://m.mgtv.com/h/386087.html\",\"sourceType\":2,\"sourceScore\":\"8.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"古力娜扎|张萌|张亮|大张伟|李斯丹妮|张欣尧|黄晓明|THE NINE|尚雯婕|唐汉霄|杨倩|蔡国庆|黄明昊|容祖儿|许靖韵|张继科|腾格尔|硬糖少女303|丁真珍珠|戚薇|张沫凡|陈楚生|吴岱林|陈梦|刘宇宁|何雯娜|TF家族|周深|萧敬腾|王明娟|杨丞琳|施逸凡|盛宇|林述巍|杨千嬅|苏炳添|陈伟霆\",\"year\":2021,\"area\":\"内地\",\"tags\":\"晚会|明星\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"本次晚会以“国潮”为出发点，通过内容创新和价值引领，打造一场集舞台唱演和双屏直播互动于一体，全面对话年轻消费人群的购物晚会。湖南卫视也将通过这次与抖音平台的强强联合，打破常规纯演唱会式的晚会形式，解锁更多新潮玩法。\",\"validEpisode\":1,\"totalEpisode\":1,\"completed\":1,\"brief\":\"解锁潮流新玩法\",\"period\":\"\",\"verticalIcon\":\"http://4img.hitv.com/preview/sp_images/2021/08/10/202108101739420871488.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252862674771968_vImg.jpeg\",\"horizontalIcon\":\"http://4img.hitv.com/preview/sp_images/2021/08/10/202108101739298255176.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252862674771968_hImg.jpeg\",\"verticalImage\":\"http://4img.hitv.com/preview/sp_images/2021/08/10/202108101739420871488.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252862674771968_vImg.jpeg\",\"horizontalImage\":\"http://4img.hitv.com/preview/sp_images/2021/08/10/202108101739298255176.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252862674771968_hImg.jpeg\",\"backGroundColor\":\"#D6BBB0\",\"backGroundColorTwo\":\"#805340\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"全 1 期\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681299616000,\"processStatus\":0,\"createTime\":1649828404000,\"updateTime\":1681299616000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"mappingTags\":\"晚会|播报\"}",StandardAlbum.class);
        results.put("654252862674771968",standardAlbum9);
        StandardAlbum standardAlbum10=JSON.parseObject("{\"sid\":\"654252861244514304\",\"title\":\"定义 2021\",\"subTitle\":\"定义2|定义第二季\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yN6aaa.7.b12de1b71128acc8\",\"sourceWebUrl\":\"https://m.mgtv.com/h/385777.html\",\"sourceType\":2,\"sourceScore\":\"8.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"黄贯中|陈小春|谢天华|林晓峰|赵文卓|胡海泉|欧阳靖|李承铉|李铢衔|刘聪|高瀚宇|白举纲|布瑞吉\",\"year\":2021,\"area\":\"内地\",\"tags\":\"明星|访谈|芒果出品\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《定义 2021》是一档聚焦混龄男性艺人的情境式互动交流节目，通过沉浸式漫谈结合真人秀的方式，探讨舞台之外男性艺人（哥哥们）披荆斩棘的态度，呈现一场关于艺品艺德的碰撞。\",\"validEpisode\":15,\"totalEpisode\":15,\"completed\":1,\"brief\":\"艺品艺德的硬核碰撞\",\"period\":\"\",\"verticalIcon\":\"http://1img.hitv.com/preview/internettv/sp_images/ott/2021/8/11/zongyi/385777/20210811101929804-new.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252861244514304_vImg.jpeg\",\"horizontalIcon\":\"http://0img.hitv.com/preview/internettv/sp_images/ott/2021/8/11/zongyi/385777/20210811101936483-new.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654252861244514304_hImg.jpeg\",\"verticalImage\":\"http://1img.hitv.com/preview/internettv/sp_images/ott/2021/8/11/zongyi/385777/20210811101929804-new.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252861244514304_vImg.jpeg\",\"horizontalImage\":\"http://0img.hitv.com/preview/internettv/sp_images/ott/2021/8/11/zongyi/385777/20210811101936483-new.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654252861244514304_hImg.jpeg\",\"backGroundColor\":\"#D6D2B0\",\"backGroundColorTwo\":\"#807940\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"全 15 期\",\"medium\":2,\"payStatus\":1,\"vipType\":1,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[]\",\"downloadMarkcode\":\"\",\"downloadAble\":0,\"markCode\":\"mgmobile_vip_1\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681288016000,\"processStatus\":0,\"createTime\":1649828409000,\"updateTime\":1681288016000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[\\\"106003\\\"]\",\"prePush\":0,\"mappingTags\":\"播报|访谈|其它\"}",StandardAlbum.class);
        results.put("654252861244514304",standardAlbum10);
        StandardAlbum standardAlbum11=JSON.parseObject("{\"sid\":\"654209462080577536\",\"title\":\"拿来吧！小芒\",\"subTitle\":\"\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"mgmobile\",\"sourceAlbumId\":\"yNO6aE.7.cfe6d243f3af6c06\",\"sourceWebUrl\":\"https://m.mgtv.com/h/382571.html\",\"sourceType\":2,\"sourceScore\":\"8.0\",\"programType\":\"show\",\"subProgramType\":\"show\",\"unit\":2,\"category\":\"综艺\",\"duration\":0,\"director\":\"未知\",\"actor\":\"沈凌\",\"year\":2021,\"area\":\"内地\",\"tags\":\"生活\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《拿来吧！小芒》是一档三人组队全民竞答生活服务科普节目，节目开启全新答题玩法，每期由3位嘉宾搭档合作，共同作答7道题，为观众与自己赢取小芒积分兑换豪礼！\",\"validEpisode\":29,\"totalEpisode\":48,\"completed\":0,\"brief\":\"开启全新答题玩法\",\"period\":\"\",\"verticalIcon\":\"http://4img.hitv.com/preview/sp_images/2021/08/03/202108031518473972010.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654209462080577536_vImg.jpeg\",\"horizontalIcon\":\"http://2img.hitv.com/preview/sp_images/2021/07/30/202107301213164903135.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654209462080577536_hImg.jpeg\",\"verticalImage\":\"http://4img.hitv.com/preview/sp_images/2021/08/03/202108031518473972010.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654209462080577536_vImg.jpeg\",\"horizontalImage\":\"http://2img.hitv.com/preview/sp_images/2021/07/30/202107301213164903135.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654209462080577536_hImg.jpeg\",\"backGroundColor\":\"#B0B0D6\",\"backGroundColorTwo\":\"#404080\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"programInfo\":\"10-07 期\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":0},{\\\"level\\\":2,\\\"payStatus\\\":0},{\\\"level\\\":3,\\\"payStatus\\\":0},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":0.00,\"nowPrice\":0.00,\"vipPrice\":0.00,\"payEffectDays\":0,\"showTime\":\"202108\",\"publishTime\":1681296039000,\"processStatus\":0,\"createTime\":1649828415000,\"updateTime\":1681296039000,\"sourceHot\":0.0,\"startTime\":0,\"endTime\":0,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"mappingTags\":\"生活\"}",StandardAlbum.class);
        results.put("654209462080577536",standardAlbum11);
        StandardAlbum standardAlbum12=JSON.parseObject("{\"sid\":\"856305744616796160\",\"highLightVid\":\"857122308089884672\",\"title\":\"虫生 第一季\",\"subTitle\":\"顶级雇佣兵穿越异世界\",\"managerStatus\":0,\"status\":1,\"sourceStatus\":1,\"verifyStatus\":1,\"source\":\"senyu\",\"sourceAlbumId\":\"M001RTH4XB\",\"sourceWebUrl\":\"\",\"sourceType\":2,\"sourceScore\":\"7.5\",\"programType\":\"comic\",\"subProgramType\":\"comic\",\"unit\":1,\"category\":\"科幻\",\"duration\":0,\"director\":\"\",\"actor\":\"\",\"year\":2022,\"area\":\"内地\",\"tags\":\"科幻\",\"language\":\"普通话\",\"honor\":\"\",\"information\":\"《虫生 第一季》：\\\"顶级雇佣兵王焕在一次刺杀任务中被害身亡，醒来后却到了一个波诡云谲的地方。在这里都是已经死去的人，而复活之后都具备了一种“虫”的能力，这种变化莫测的能力叫做“虫生”。\\n来到这个奇异的世界后，为了生存下去，王焕加入到11队。在这里有可爱乖巧的划蝽苏可，有性感清冷的伊蚊尹紫，还有结实可靠的蜣螂大奎......身边的队友有各种虫生的状态，那么雇佣兵出身的王焕，会虫生成什么形态呢？\\\"\",\"validEpisode\":26,\"totalEpisode\":26,\"completed\":1,\"brief\":\"顶级雇佣兵穿越异世界\",\"period\":\"\",\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=6a08643bb7f2259b138e4c2383b01fc6&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/856305744616796160_vIcon.jpeg\",\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=6305181bf4894cc3075282af1b01dae0&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/856305744616796160_hIcon.jpeg\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=6a08643bb7f2259b138e4c2383b01fc6&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/856305744616796160_vImg.jpeg\",\"horizontalImage\":\"http://img1.funshion.com/sdw?oid=6305181bf4894cc3075282af1b01dae0&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/856305744616796160_hImg.jpeg\",\"backGroundColor\":\"#B0D6D6\",\"backGroundColorTwo\":\"#408080\",\"featureType\":1,\"riskFlag\":0,\"copyright\":1,\"copyrightCode\":\"senyu\",\"programInfo\":\"全 26 集\",\"medium\":2,\"payStatus\":0,\"vipType\":0,\"chargeType\":0,\"price\":0,\"keyword\":\"\",\"extraInfo\":\"\",\"sourcePlayCount\":0,\"supplyType\":\"normal\",\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"downloadMarkcode\":\"\",\"downloadAble\":1,\"markCode\":\"\",\"formerPrice\":6.00,\"nowPrice\":6.00,\"vipPrice\":0.00,\"payEffectDays\":30,\"showTime\":\"202206\",\"publishTime\":1681419473000,\"processStatus\":0,\"createTime\":1679450732000,\"updateTime\":1681419473000,\"sourceHot\":0.0,\"startTime\":1680400850000,\"endTime\":-28800000,\"sourceSeriesId\":\"[]\",\"prePush\":0,\"previewInfo\":\"[]\",\"mappingTags\":\"科幻\"}",StandardAlbum.class);
        results.put("856305744616796160",standardAlbum12);
        StandardAlbum standardAlbum13=JSON.parseObject("{\"copyright\":1,\"processStatus\":0,\"year\":2022,\"honor\":\"\",\"chargeType\":0,\"sourceAlbumId\":\"E77EE7l.7.96df2a3dc5e1dcf1\",\"language\":\"zh_CN\",\"source\":\"mgmobile\",\"downloadAble\":0,\"totalEpisode\":0,\"horizontalIcon\":\"http://0img.hitv.com/preview/channel_images/20220121111907271.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/702426915134447616_vImg.jpeg\",\"formerPrice\":0,\"subTitle\":\"2022湖南卫视春节联欢晚会\",\"backGroundColor\":\"#B0C1D6\",\"price\":0,\"mappingTags\":\"\",\"payEffectDays\":0,\"sourceSeriesId\":\"\",\"keyword\":\"\",\"horizontalImage\":\"http://0img.hitv.com/preview/channel_images/20220121111907271.jpg\",\"area\":\"CN\",\"publishTime\":\"2022-09-09 00:00:01\",\"period\":\"\",\"director\":\"\",\"showTime\":\"202201\",\"prePush\":0,\"completed\":0,\"riskFlag\":0,\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/702426915134447616_hImg.jpeg\",\"tags\":\"\",\"unit\":1,\"backGroundColorTwo\":\"#405D80\",\"sourceType\":2,\"managerStatus\":0,\"copyrightCode\":\"mgmobile\",\"status\":1,\"extraInfo\":\"\",\"sourcePlayCount\":0,\"medium\":2,\"title\":\"2022湖南卫视春节联欢晚会\",\"sid\":\"702426915134447616\",\"duration\":0,\"nowPrice\":0,\"verifyStatus\":1,\"subProgramType\":\"2\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/702426915134447616_vImg.jpeg\",\"supplyType\":\"normal\",\"vipPrice\":0,\"featureType\":1,\"startTime\":\"2022-01-26 19:15:00\",\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"class\":\"com.heytap.longvideo.client.media.entity.StandardAlbum\",\"downloadMarkcode\":\"\",\"verticalIcon\":\"http://0img.hitv.com/preview/channel_images/20220121102818201.jpg\",\"brief\":\"福虎耀青春，温暖中国年！\",\"programType\":\"live\",\"programInfo\":\"\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/702426915134447616_hImg.jpeg\",\"validEpisode\":1,\"vipType\":0,\"updateTime\":\"2023-03-30 12:31:03\",\"sourceWebUrl\":\"\",\"actor\":\"2022湖南卫视春晚\",\"sourceStatus\":1,\"sourceHot\":0,\"markCode\":\"mgmobile_vip_1\",\"sourceScore\":\"0.0\",\"createTime\":\"2022-04-15 16:57:00\",\"information\":\"晚会以 “福虎耀青春，温暖中国年”为主题，以“乡村振兴”、“祝福冬奥”、“湖南三高四新”为创意原点构思节目，站在2022的起点展望未来、描绘愿景。\",\"verticalImage\":\"http://0img.hitv.com/preview/channel_images/20220121102818201.jpg\",\"endTime\":\"2022-01-26 23:16:00\",\"category\":\"\",\"payStatus\":1}",StandardAlbum.class);
        results.put("702426915134447616",standardAlbum13);
        StandardAlbum standardAlbum14=JSON.parseObject("{\"copyright\":1,\"processStatus\":0,\"year\":2016,\"honor\":\"\",\"chargeType\":0,\"sourceAlbumId\":\"M0010UNK26\",\"language\":\"普通话\",\"source\":\"senyu\",\"downloadAble\":1,\"totalEpisode\":33,\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=e65068bde4dbe5e83505463cac54bfe0&w=0&h=0\",\"highLightVid\":\"859081998956933120\",\"previewInfo\":\"[]\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/629978519111409665_vImg.jpeg\",\"formerPrice\":0,\"subTitle\":\"中国棚改第一剧\",\"backGroundColor\":\"#D6C7B0\",\"price\":0,\"mappingTags\":\"剧情|爱情|家庭\",\"payEffectDays\":3,\"sourceSeriesId\":\"[]\",\"keyword\":\"\",\"horizontalImage\":\"http://img2.funshion.com/sdw?oid=e65068bde4dbe5e83505463cac54bfe0&w=0&h=0\",\"area\":\"中国内地\",\"publishTime\":\"2023-04-18 06:30:16\",\"period\":\"\",\"director\":\"张永新\",\"showTime\":\"201609\",\"prePush\":0,\"completed\":1,\"riskFlag\":0,\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/629978519111409665_hImg.jpeg\",\"tags\":\"剧情|情感|家庭|电视剧\",\"unit\":1,\"backGroundColorTwo\":\"#806640\",\"sourceType\":2,\"managerStatus\":0,\"copyrightCode\":\"senyu\",\"status\":1,\"extraInfo\":\"\",\"sourcePlayCount\":0,\"medium\":2,\"title\":\"安居\",\"sid\":\"629978519111409665\",\"duration\":0,\"nowPrice\":0,\"verifyStatus\":1,\"subProgramType\":\"tv\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/629978519111409665_vImg.jpeg\",\"supplyType\":\"normal\",\"vipPrice\":0,\"featureType\":1,\"startTime\":\"2022-07-21 12:54:16\",\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1}]\",\"class\":\"com.heytap.longvideo.client.media.entity.StandardAlbum\",\"downloadMarkcode\":\"\",\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=7b422563e3fb8a84e4f675a25ce77a38&w=0&h=0\",\"brief\":\"中国棚改第一剧\",\"programType\":\"tv\",\"programInfo\":\"全 33 集\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/629978519111409665_hImg.jpeg\",\"validEpisode\":33,\"vipType\":0,\"updateTime\":\"2023-04-18 06:30:16\",\"sourceWebUrl\":\"\",\"actor\":\"王志飞|程煜|来喜|李晓峰|傅晶|刘敏涛\",\"sourceStatus\":1,\"sourceHot\":0,\"markCode\":\"\",\"sourceScore\":\"7.7\",\"createTime\":\"2022-07-21 12:53:07\",\"information\":\"鹿城市的北梁棚户区改造正如火如荼地进行，组长张家旗率领北梁当地基层干部马倩、没脾气的城管队长黄明、熟知北梁当地情况的中学教师杨天旺以及富二代女孩凌姗，组成了一个由背景各异的动迁小组，在艰难中开展起动迁工作。经历过一系列的波折磨难，小组的成员们见证了悲欢离合、人情冷暖。与女儿相依为命的养鸡户秦翠莲、与兄弟争遗产的周贵、梦想四世同堂的赵大爷，每一个家庭的背后，都是醇厚的烟火温情。在动迁的过程中，每一个成员也在经历着变化和成长：马倩终于跳出感情的桎梏，找到了自己的存在价值；凌珊摆脱富二代的包袱，成为独当一面的主心骨；黄明让长久瞧不起他的妻子发现了丈夫身上的血性和气概，而张家旗也没有想到，这次心不甘情不愿的动迁任务，让他重新认识了北梁，重新认识了生活在这里的人民。\",\"verticalImage\":\"http://img3.funshion.com/sdw?oid=7b422563e3fb8a84e4f675a25ce77a38&w=0&h=0\",\"endTime\":\"1970-01-01 08:00:00\",\"category\":\"剧情|情感\",\"payStatus\":0}",StandardAlbum.class);
        results.put("629978519111409665",standardAlbum14);
        StandardAlbum standardAlbum15=JSON.parseObject("{\"copyright\": 1,\"processStatus\": 0,\"year\": 2023,\"honor\": \"\",\"chargeType\": 0,\"sourceAlbumId\": \"M05CE4ABH9\",\"language\": \"普通话\",\"source\": \"huashi\",\"horizontalIcon\": \"http://img2.funshion.com/sdw?oid=ab28ef65782ebd9e1a66ee013b1d8e4e&w=0&h=0\",\"totalEpisode\": 1,\"downloadAble\": 1,\"highLightVid\": \"889780298647252992\",\"verticalIconOcs\": \"https://dhfs-test-cpc.wanyol.com/889735023148838912_0622vIcon.png\",\"previewInfo\": \"[]\",\"formerPrice\": 6,\"backGroundColor\": \"#CCD6B0\",\"subTitle\": \"命运不同却彼此深爱\",\"price\": 0,\"mappingTags\": \"剧情|家庭\",\"payEffectDays\": 30,\"sourceSeriesId\": \"[]\",\"keyword\": \"\",\"horizontalImage\": \"http://img2.funshion.com/sdw?oid=ab28ef65782ebd9e1a66ee013b1d8e4e&w=0&h=0\",\"area\": \"中国内地\",\"publishTime\": \"2023-06-23 00:11:11\",\"period\": \"\",\"director\": \"唐明智\",\"showTime\": \"202305\",\"prePush\": 0,\"completed\": 1,\"riskFlag\": 0,\"horizontalImageOcs\": \"https://dhfs-test-cpc.wanyol.com/889735023148838912_0622hImg.png\",\"tags\": \"剧情|家庭\",\"backGroundColorTwo\": \"#6E8040\",\"unit\": 1,\"sourceType\": 2,\"managerStatus\": 0,\"copyrightCode\": \"huashi\",\"status\": 1,\"extraInfo\": \"1047529\",\"sourcePlayCount\": 0,\"medium\": 2,\"title\": \"大桥小乔\",\"sid\": \"889735023148838912\",\"tuputag\": \"\",\"duration\": 5561,\"nowPrice\": 6,\"verticalImageOcs\": \"https://dhfs-test-cpc.wanyol.com/889735023148838912_0622vImg.png\",\"verifyStatus\": 1,\"subProgramType\": \"movie\",\"supplyType\": \"normal\",\"featureType\": 1,\"vipPrice\": 0,\"startTime\": \"1970-01-01 00:00:00\",\"definition\": \"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"class\": \"com.heytap.longvideo.client.media.entity.StandardAlbum\",\"verticalIcon\": \"http://img.funshion.com/sdw?oid=f67d683667eef8d33c06b891899dbe21&w=0&h=0\",\"downloadMarkcode\": \"\",\"brief\": \"命运不同却彼此深爱\",\"programType\": \"movie\",\"programInfo\": \"\",\"horizontalIconOcs\": \"https://dhfs-test-cpc.wanyol.com/889735023148838912_0622hIcon.png\",\"validEpisode\": 1,\"vipType\": 1,\"updateTime\": \"2023-06-23 00:11:11\",\"sourceWebUrl\": \"\",\"sourceStatus\": 1,\"actor\": \"龙斌|郑敏\",\"sourceHot\": 0,\"markCode\": \"funshion_vip_1\",\"sourceScore\": \"8.1\",\"createTime\": \"2023-06-22 16:01:33\",\"verticalImage\": \"http://img.funshion.com/sdw?oid=f67d683667eef8d33c06b891899dbe21&w=0&h=0\",\"information\": \"《大桥小乔》：讲述一对80后出生的姐妹不同命运却彼此深爱的故事。姐姐乔琳从小受到父母呵护，在父母出现困难时，懂事地为了缓解家庭经济困难。妹妹许妍为了出人头地，拼命三娘的个性让她事业与爱情双丰收。\",\"endTime\": \"1970-01-01 00:00:00\",\"category\": \"剧情|家庭\",\"payStatus\": 1}",StandardAlbum.class);
        results.put("889735023148838912",standardAlbum15);
        StandardAlbum standardAlbum16=JSON.parseObject("{\"actor\":\"小猪佩奇\",\"area\":\"英国\",\"backGroundColor\":\"#B0C1D6\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#346299\\\"}\",\"backGroundColorTwo\":\"#405C80\",\"brief\":\"小猪佩奇愉快的日常生活\",\"category\":\"动漫\",\"chargeType\":0,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"createTime\":1649836110000,\"definition\":\"[{\\\"level\\\":1,\\\"payStatus\\\":1},{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"阿斯特利\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":8065,\"endTime\":0,\"extraInfo\":\"\",\"featureType\":1,\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://4img.hitv.com/preview/internettv/sp_images/ott/2018/12/29/dongman/327936/20181229140736257-new.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654244148668473344_hImg.jpeg\",\"horizontalImage\":\"http://4img.hitv.com/preview/internettv/sp_images/ott/2018/12/29/dongman/327936/20181229140736257-new.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654244148668473344_hImg.jpeg\",\"information\":\"小猪佩奇是一个可爱的但是有些小专横的小猪。她已经五岁了，与她的猪妈妈，猪爸爸，和弟弟乔治生活在一起。故事内容多数环绕日常生活，比如小孩子们参加学前游戏小组、探访祖父母和表亲、在游乐场游玩、踏单车等等。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"益智|早教\",\"markCode\":\"mgmobile_vip_1\",\"medium\":2,\"nowPrice\":0.00,\"payEffectDays\":0,\"payStatus\":1,\"period\":\"\",\"prePush\":0,\"price\":0,\"processStatus\":0,\"programInfo\":\"全 26 集\",\"programType\":\"kids\",\"publishTime\":1697788514000,\"riskFlag\":0,\"showTime\":\"201801\",\"sid\":\"654244148668473344\",\"source\":\"mgmobile\",\"sourceAlbumId\":\"yOaYyq.7.7f8bfbd53ada76a9\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"9.0\",\"sourceSeriesId\":\"[\\\"71146\\\"]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"https://m.mgtv.com/h/327936.html\",\"startTime\":0,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小猪佩奇 第5季\",\"supplyType\":\"normal\",\"tags\":\"幼教|益智|亲子\",\"title\":\"小猪佩奇 第五季\",\"totalEpisode\":26,\"tuputag\":\"童年|粉红猪小妹|英国动画|正片|动漫|小猪佩奇|PeppaPig|6岁|儿童|乔治|3岁|动画片|0|4|幼教|亲子|佩奇|动画|粉红猪小妹第五季|小猪佩奇第五季|英国|早教|益智\",\"unit\":1,\"updateTime\":1697788514000,\"validEpisode\":26,\"verifyStatus\":1,\"verticalIcon\":\"http://4img.hitv.com/preview/internettv/sp_images/ott/2018/12/29/dongman/327936/20181229140512081-new.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/654244148668473344_vImg.jpeg\",\"verticalImage\":\"http://4img.hitv.com/preview/internettv/sp_images/ott/2018/12/29/dongman/327936/20181229140512081-new.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/654244148668473344_vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":1,\"year\":2018}",StandardAlbum.class);
        results.put("654244148668473344",standardAlbum16);

        results.putAll(JSONObject.parseObject("{\"586129156434120704\":{\"actor\":\"梁冠华|董璇|张永强|刘蕾|赵毅|淳于珊珊|沙玉华|李绪良\",\"area\":\"内地\",\"backGroundColor\":\"#B0BDD6\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#345799\\\",\\\"fanZaiColor\\\":\\\"#334D80\\\"}\",\"backGroundColorTwo\":\"#405680\",\"brief\":\"狸猫换太子传奇\",\"category\":\"悬疑|爱情|古装\",\"chargeType\":0,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"sohu\",\"createTime\":1665735121000,\"definition\":\"\",\"director\":\"钱雁秋\",\"downloadAble\":0,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":0,\"extraInfo\":\"\",\"featureType\":1,\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://photocdn.tv.sohu.com/img/c_lfill,w_540,h_304,g_faces/20150829/vrsa_hor9049645.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/586129156434120704_hIcon.jpeg\",\"horizontalImage\":\"http://photocdn.tv.sohu.com/img/20150829/vrsa_hor9049645.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/586129156434120704_hImg.jpeg\",\"information\":\"北宋真宗年间，辽军大举攻宋，要塞澶州危急。真宗皇帝率禁军主力增援，然而，大军到了黄河岸边，真宗却因畏怕危险，不敢过河，以寇准为首的主战派与以丁谓为首的主和派发生了激烈第争执，以致于寇准情急之下收拉皇帝的衣袖，造成局势失控，形成僵持。在这危急时刻，以一名叫李玉的王府诰命挺身而出，用自己的聪明智慧，打破僵局，促成皇帝渡河，保住了宋室江山。李玉的美貌与智慧深深打动了真宗皇帝，而李玉对真宗也是情根深种，二人在寇准的撮合之下相爱了。然而，后宫的德妃刘娥，却是个阴险狡诈，妒心深重的人，当真宗决定将李玉迎纳入宫以后，她妒发入狂，用尽奸计欲置李玉于死地，李玉在真宗、寇准、陈琳等人的保护下数次死力逃生，但她宽容大度并不计较。然而，刘娥却变本加厉，从只对李玉以人，最后发展到了弑君的地步。终于，刘妃的奸谋暴露在众人面前，然而，由于皇帝的软弱，和李妃的善良，他们再一次宽恕了刘妃。后来在国家危难之际，包拯临危受命，他能否保真太子登基，能否昭雪沉冤铲除刘娥，一场惊心动魄的生死较量展开了。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"payEffectDays\":0,\"payStatus\":0,\"period\":\"30集全\",\"prePush\":0,\"price\":0,\"processStatus\":1,\"programInfo\":\"全 30 集\",\"programType\":\"tv\",\"publishTime\":1665767521000,\"riskFlag\":0,\"showTime\":\"201509\",\"sid\":\"586129156434120704\",\"source\":\"sohu\",\"sourceAlbumId\":\"9049645\",\"sourceHot\":0.0,\"sourcePlayCount\":4643402,\"sourceScore\":\"6.4\",\"sourceSeriesId\":\"\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"http://tv.sohu.com/s2015/dsqalmhtz/\",\"startTime\":0,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"\",\"supplyType\":\"normal\",\"tags\":\"悬疑|爱情|古装\",\"title\":\"大宋奇案\",\"totalEpisode\":30,\"tuputag\":\"悬疑|爱情|古装\",\"unit\":1,\"updateTime\":1719197012000,\"validEpisode\":29,\"verifyStatus\":1,\"verticalIcon\":\"http://photocdn.tv.sohu.com/img/c_lfill,w_353,h_530,g_faces/20150829/vrsa_ver9049645.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/586129156434120704_vIcon.jpeg\",\"verticalImage\":\"http://photocdn.tv.sohu.com/img/20150829/vrsa_ver9049645.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/586129156434120704_vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2005},\"910400883228061696\":{\"actor\":\"张新成|周雨彤|王佑硕|苏晓彤|郑伟|付伟伦\",\"area\":\"内地\",\"backGroundColor\":\"#D6BBB0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#995234\\\",\\\"fanZaiColor\\\":\\\"#804A33\\\"}\",\"backGroundColorTwo\":\"#805340\",\"brief\":\"七斋六子一起探案友情\",\"category\":\"电视剧\",\"chargeType\":0,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"mgmobile\",\"createTime\":1692348018000,\"definition\":\"[]\",\"director\":\"未知\",\"downloadAble\":0,\"downloadMarkcode\":\"\",\"duration\":49273,\"endTime\":-28800000,\"extraInfo\":\"\",\"featureType\":1,\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://4img.hitv.com/preview/sp_images/2023/08/18/202308181624527024324.jpg\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/910400883228061696_0819hIcon.jpeg\",\"horizontalImage\":\"http://4img.hitv.com/preview/sp_images/2023/08/18/202308181624527024324.jpg\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/910400883228061696_0819hImg.jpeg\",\"information\":\"节目以电视剧《大宋少年志2》主演们为拍摄主体，七斋六子共同合宿，以真实原生关系组成友情局，一起探案友情，挥洒少年青春热血。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"其它\",\"markCode\":\"mgmobile_vip_1\",\"medium\":2,\"nowPrice\":0.00,\"payEffectDays\":0,\"payStatus\":1,\"period\":\"\",\"prePush\":0,\"price\":0,\"processStatus\":0,\"programInfo\":\"全 13 集\",\"programType\":\"tv\",\"publishTime\":1719870629000,\"riskFlag\":0,\"showTime\":\"202308\",\"sid\":\"910400883228061696\",\"source\":\"mgmobile\",\"sourceAlbumId\":\"6ay6la.7.cdad224bf512232a\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"9.0\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"https://m.mgtv.com/h/573547.html\",\"startTime\":1696391435000,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"\",\"supplyType\":\"normal\",\"tags\":\"芒果出品\",\"title\":\"大宋探案局\",\"totalEpisode\":13,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1719870629000,\"validEpisode\":13,\"verifyStatus\":1,\"verticalIcon\":\"http://2img.hitv.com/preview/sp_images/2023/08/18/202308181625000371851.jpg\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/910400883228061696_0819vIcon.jpeg\",\"verticalImage\":\"http://2img.hitv.com/preview/sp_images/2023/08/18/202308181625000371851.jpg\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/910400883228061696_0819vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":1,\"year\":2023}}", new TypeReference<HashMap<String, StandardAlbum>>() {}));
        resp.setData(results);

        mocker.returnValue(resp).configure();
        return mocker;
    }


    public static Mocker getBySidsFilterInvalidV2() throws IOException {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(StandardAlbumRpcApi.class.getName())
                .methodName("getBySidsFilterInvalid");
        RpcResult<Map<String/* sid */, StandardAlbum>> resp = new RpcResult(0, "success");

        String str = FileUtils.readFileToString(new ClassPathResource("mock/getBySidsFilterInvalidV2Response.json").getFile(), UTF_8);
        Map<String/* sid */, StandardAlbum> results = JSON.parseObject(str, new TypeReference<Map<String, StandardAlbum>>(){});
        resp.setData(results);
        mocker.returnValue(resp).configure();
        return mocker;
    }


}
