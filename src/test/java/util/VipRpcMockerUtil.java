package util;

import com.alibaba.fastjson.JSON;
import com.oppo.cpc.video.framework.lib.vip.RpcResult;
import com.oppo.cpc.video.framework.lib.vip.UserVipInfoRpcApi;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import esa.rpc.test.support.mock.Mocker;
import org.springframework.core.io.ClassPathResource;
import org.testcontainers.shaded.org.apache.commons.io.FileUtils;

import java.io.IOException;

/**
 * <AUTHOR> Yanping
 * @date 2025/6/11
 */
public class VipRpcMockerUtil {
    /**
     * 用户vip信息
     *
     * @return
     * @throws IOException
     */
    public static Mocker mockUserVipInfo(String mockFileName){
        String readFileToString = null;
        try {
            readFileToString = FileUtils.readFileToString(new ClassPathResource(mockFileName).getFile(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(UserVipInfoRpcApi.class.getName())
                .methodName("queryUserVipInfo");
        RpcResult<VideoVipInfo> rpcResult = new RpcResult(0, "success");
        VideoVipInfo res = JSON.parseObject(readFileToString, VideoVipInfo.class);
        rpcResult.setData(res);
        mocker.returnValue(rpcResult).configure();
        return mocker;
    }
}
