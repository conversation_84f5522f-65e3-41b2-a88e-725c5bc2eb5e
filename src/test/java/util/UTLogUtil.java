package util;

/**
 * @Description: 单测日志工具类
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/7/2
 */
public class UTLogUtil {
    public static String logStartInfo(String desc) {
        return "------------------- " + desc + " -> start.";
    }

    public static String logEndInfo(String desc) {
        return "------------------- " + desc + " -> end.";
    }

    public static String getExceptionPrefix() {
        return "[{}] unexpected error: ";
    }
}
