package util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heytap.longvideo.client.arrange.LvContentItemRpcApi;
import com.heytap.longvideo.client.arrange.entity.LvContentItem;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import esa.rpc.test.support.mock.Mocker;

import java.util.ArrayList;
import java.util.Map;

import static esa.restlight.plugin.browser.utils.JacksonUtils.objectMapper;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2024/11/20 下午7:08
 */
public class FunshionAndWeidiouFilterMockUtil {

    public static void lvContentItemRpcApiMockForRecommendCard(ArrayList<Mocker> mockers) throws JsonProcessingException {
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvContentItemRpcApi.class.getName())
                .methodName("getItemsWithCache");
        String json ="{\"current\":1,\"optimizeCountSql\":true,\"orders\":[],\"pages\":3,\"records\":[{\"contentItemCode\":\"ci_19478428\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731854941000,\"createUser\":\"80408770\",\"id\":12596209,\"linkSid\":\"\",\"linkValue\":\"1074120971360849920\",\"orderIndex\":1,\"status\":1,\"title\":\"太平私盐案\",\"trailerVid\":\"\",\"updateTime\":1731854941000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19478427\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731854941000,\"createUser\":\"80408770\",\"id\":12596208,\"linkSid\":\"\",\"linkValue\":\"1074120972560420864\",\"orderIndex\":2,\"status\":1,\"title\":\"世纪之约\",\"trailerVid\":\"\",\"updateTime\":1731854941000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19478426\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731854941000,\"createUser\":\"80408770\",\"id\":12596207,\"linkSid\":\"\",\"linkValue\":\"1074120969095925760\",\"orderIndex\":3,\"status\":1,\"title\":\"新疆姑娘\",\"trailerVid\":\"\",\"updateTime\":1731854941000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19478425\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731854941000,\"createUser\":\"80408770\",\"id\":12596206,\"linkSid\":\"\",\"linkValue\":\"1073837889705660416\",\"orderIndex\":4,\"status\":1,\"title\":\"越王勾践\",\"trailerVid\":\"\",\"updateTime\":1731854941000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19478424\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731854941000,\"createUser\":\"80408770\",\"id\":12596205,\"linkSid\":\"\",\"linkValue\":\"1074120979346804736\",\"orderIndex\":5,\"status\":1,\"title\":\"刘伯温\",\"trailerVid\":\"\",\"updateTime\":1731854941000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463369\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586661,\"linkSid\":\"\",\"linkValue\":\"1071268038126522368\",\"orderIndex\":6,\"status\":1,\"title\":\"小志和玩具第十一季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463368\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586660,\"linkSid\":\"\",\"linkValue\":\"1071268072834387968\",\"orderIndex\":7,\"status\":1,\"title\":\"小志和玩具第十三季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463367\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586659,\"linkSid\":\"\",\"linkValue\":\"1071268087111798784\",\"orderIndex\":8,\"status\":1,\"title\":\"小志和玩具第十四季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463366\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586658,\"linkSid\":\"\",\"linkValue\":\"1071268105440907264\",\"orderIndex\":9,\"status\":1,\"title\":\"小志和玩具第十五季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463365\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586657,\"linkSid\":\"\",\"linkValue\":\"1071268147182620672\",\"orderIndex\":10,\"status\":1,\"title\":\"小志和玩具第十七季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463364\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586656,\"linkSid\":\"\",\"linkValue\":\"1071268160340152320\",\"orderIndex\":11,\"status\":1,\"title\":\"小志和玩具第十八季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"},{\"contentItemCode\":\"ci_19463363\",\"contentPoolCode\":\"cp_00000564\",\"contentType\":1,\"createTime\":1731744789000,\"createUser\":\"80408770\",\"id\":12586655,\"linkSid\":\"\",\"linkValue\":\"1071268199938576384\",\"orderIndex\":12,\"status\":1,\"title\":\"小志和玩具第十二季\",\"trailerVid\":\"\",\"updateTime\":1731744789000,\"updateUser\":\"\"}],\"searchCount\":true,\"size\":12,\"total\":26}";
        Page<LvContentItem> res = objectMapper.readValue(json, new TypeReference<Page<LvContentItem>>() {});
        RpcResult<Page<LvContentItem>> pageRpcResult = new RpcResult<>(res);
        mocker.returnValue(pageRpcResult).configure();
        mockers.add(mocker);
    }

    public static void standardAlbumRpcApiMockForRecommendCard(ArrayList<Mocker> mockers) throws JsonProcessingException {
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(StandardAlbumRpcApi.class.getName())
                .methodName("getBySidsFilterInvalid");
        String json = "{\"1071268105440907264\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#B0C9D6\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#347799\\\",\\\"fanZaiColor\\\":\\\"#336680\\\"}\",\"backGroundColorTwo\":\"#406A80\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704330000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076821\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=228e1ed12bd2624efaa5745658e98909&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268105440907264_1104hImg.jpeg\",\"horizontalImage\":\"http://img1.funshion.com/sdw?oid=228e1ed12bd2624efaa5745658e98909&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268105440907264_1104hImg.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158879000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268105440907264\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M297Q16AWU\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.1\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十五季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158879000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img.funshion.com/sdw?oid=2da86b41206ef039ff92f1d9d7fc9ec6&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268105440907264_1104vIcon.jpeg\",\"verticalImage\":\"http://img.funshion.com/sdw?oid=2da86b41206ef039ff92f1d9d7fc9ec6&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268105440907264_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1071268038126522368\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#C0B0D6\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#603499\\\",\\\"fanZaiColor\\\":\\\"#543380\\\"}\",\"backGroundColorTwo\":\"#5B4080\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704203000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076801\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=21c2c4e163b0f6511ff09a93aec85cef&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268038126522368_1104hImg.jpeg\",\"horizontalImage\":\"http://img3.funshion.com/sdw?oid=21c2c4e163b0f6511ff09a93aec85cef&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268038126522368_1104hImg.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158868000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268038126522368\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M2972UZU58\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.4\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十一季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158868000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=4d9a2aaed41e406db85256e68eeb0dd3&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268038126522368_1104vIcon.jpeg\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=4d9a2aaed41e406db85256e68eeb0dd3&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268038126522368_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1074120979346804736\":{\"actor\":\"高强|鲍国安\",\"area\":\"中国内地\",\"backGroundColor\":\"#D6C7B0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#997034\\\",\\\"fanZaiColor\\\":\\\"#806033\\\"}\",\"backGroundColorTwo\":\"#806640\",\"brief\":\"神机妙算刘伯温精彩演绎\",\"category\":\"历史\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"weidiou\",\"createTime\":1731381931000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1}]\",\"director\":\"朱建新\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1072045\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":10.00,\"honor\":\"\",\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=2f60f3e3035a99277806c174a56115ca&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120979346804736_1112hIcon.jpeg\",\"horizontalImage\":\"http://img2.funshion.com/sdw?oid=2f60f3e3035a99277806c174a56115ca&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120979346804736_1112hImg.jpeg\",\"information\":\"《刘伯温》：元朝末年，天下大乱，群雄逐鹿，民不聊生。龙典寺小和尚朱元璋揭竿起事，率兵攻入南京应天府。后得知民间有“三分天下诸葛亮，一统江山刘伯温”的说法后，亲临文成南田请出了隐居乡间的刘伯温。沧海横流，方显英雄本色。刘伯温出山后。以“帝师”、“王佐”的身份，为朱元璋定了西平陈友谅，东征灭张士诚而后一统大业的战略。一统江山后，刘伯温辅助朱元璋开始了政治改革。应付自如，指点江山，笑傲群雄，最后引身而退，奸相胡惟庸仍不放过退隐山林的刘伯温，几次阴谋加害。刘伯温之死成为千古迷案，云遮雾隐。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"历史\",\"markCode\":\"funshion_vip_1\",\"medium\":2,\"nowPrice\":5.00,\"participantType\":-1,\"payEffectDays\":10000,\"payStatus\":1,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 18 集\",\"programType\":\"tv\",\"publishTime\":1732166203000,\"riskFlag\":0,\"showTime\":\"199206\",\"sid\":\"1074120979346804736\",\"source\":\"weidiou\",\"sourceAlbumId\":\"M279FBPS1X\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"7.6\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"神机妙算刘伯温精彩演绎\",\"supplyType\":\"normal\",\"tags\":\"历史\",\"title\":\"刘伯温\",\"totalEpisode\":18,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732166203000,\"validEpisode\":18,\"verifyStatus\":1,\"verticalIcon\":\"http://img1.funshion.com/sdw?oid=30e095186c97c5f4e8fad9ddbcf98ca1&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120979346804736_1112vIcon.jpeg\",\"verticalImage\":\"http://img1.funshion.com/sdw?oid=30e095186c97c5f4e8fad9ddbcf98ca1&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120979346804736_1112vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":1,\"year\":1992},\"1071268147182620672\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#B0C9D6\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#347899\\\",\\\"fanZaiColor\\\":\\\"#336680\\\"}\",\"backGroundColorTwo\":\"#406A80\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704359000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076825\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=bd9ba2cb59189084ea70496fcf65ed35&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268147182620672_1104hIcon.jpeg\",\"horizontalImage\":\"http://img1.funshion.com/sdw?oid=bd9ba2cb59189084ea70496fcf65ed35&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268147182620672_1104hImg.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158882000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268147182620672\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M29756C6JP\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.0\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十七季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158882000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=f9dd7d2aa1de3b3ca5e6dc28ea41ed76&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268147182620672_1104vIcon.jpeg\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=f9dd7d2aa1de3b3ca5e6dc28ea41ed76&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268147182620672_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1074120969095925760\":{\"actor\":\"佟凡\",\"area\":\"中国内地\",\"backGroundColor\":\"#D6D1B0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#998B34\\\",\\\"fanZaiColor\\\":\\\"#807533\\\"}\",\"backGroundColorTwo\":\"#807740\",\"brief\":\"新疆四姐妹独特魅力的生动诠释\",\"category\":\"都市\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"weidiou\",\"createTime\":1731381928000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1}]\",\"director\":\"邬丽娅·司马义诺娃\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1071815\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=90ad38ad0339eb291741816a7ee01f0d&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120969095925760_1112hIcon.png\",\"horizontalImage\":\"http://img2.funshion.com/sdw?oid=90ad38ad0339eb291741816a7ee01f0d&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120969095925760_1112hImg.png\",\"information\":\"《新疆姑娘》：主要讲述了北京某大学教授阿不都拉夫妇四个女儿各自在观念、情感、生活和事业上一波三折、悲欢离合的心路历程，融入丰富多彩的现代都市生活场景，反映生活在北京的维吾尔族人在精神和生活层面经受传统和现代生活的双重锤炼。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"都市\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 20 集\",\"programType\":\"tv\",\"publishTime\":1732166196000,\"riskFlag\":0,\"showTime\":\"200406\",\"sid\":\"1074120969095925760\",\"source\":\"weidiou\",\"sourceAlbumId\":\"M279DHPE3N\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.5\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"新疆四姐妹独特魅力的生动诠释\",\"supplyType\":\"normal\",\"tags\":\"都市\",\"title\":\"新疆姑娘\",\"totalEpisode\":20,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732166195000,\"validEpisode\":20,\"verifyStatus\":1,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=01678e1d18c4e8bc579123e69a121035&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120969095925760_1112vIcon.png\",\"verticalImage\":\"http://img3.funshion.com/sdw?oid=01678e1d18c4e8bc579123e69a121035&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120969095925760_1112vImg.png\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2004},\"1071268072834387968\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#D6C5B0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#996B34\\\",\\\"fanZaiColor\\\":\\\"#805C33\\\"}\",\"backGroundColorTwo\":\"#806240\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704261000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076805\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=7fc9c887dcfbb17b979d2ce545cf5fc4&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268072834387968_1104hIcon.jpeg\",\"horizontalImage\":\"http://img3.funshion.com/sdw?oid=7fc9c887dcfbb17b979d2ce545cf5fc4&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268072834387968_1104hIcon.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158872000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268072834387968\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M2970YAGDR\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.0\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十三季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158872000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=ef52b70f61590d7ea54bb6d948c36c00&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268072834387968_1104vIcon.jpeg\",\"verticalImage\":\"http://img3.funshion.com/sdw?oid=ef52b70f61590d7ea54bb6d948c36c00&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268072834387968_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1071268199938576384\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#D6C5B0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#996B34\\\",\\\"fanZaiColor\\\":\\\"#805D33\\\"}\",\"backGroundColorTwo\":\"#806340\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704635000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076803\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img.funshion.com/sdw?oid=ace1d5aafe2e371811f803dc9d0e3556&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268199938576384_1104hIcon.jpeg\",\"horizontalImage\":\"http://img.funshion.com/sdw?oid=ace1d5aafe2e371811f803dc9d0e3556&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268199938576384_1104hIcon.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158953000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268199938576384\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M297YWCAF9\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"7.5\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十二季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158953000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=39636f9cabb5dc2abd91f24af2452af7&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268199938576384_1104vIcon.jpeg\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=39636f9cabb5dc2abd91f24af2452af7&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268199938576384_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1071268160340152320\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#BFB0D6\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#5B3499\\\",\\\"fanZaiColor\\\":\\\"#513380\\\"}\",\"backGroundColorTwo\":\"#594080\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704389000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076827\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=5fad7ffe611b5d8468afa46863108fa2&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268160340152320_1104hIcon.jpeg\",\"horizontalImage\":\"http://img2.funshion.com/sdw?oid=5fad7ffe611b5d8468afa46863108fa2&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268160340152320_1104hIcon.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158887000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268160340152320\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M297VU7CTX\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.2\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十八季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158887000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=abfaef83f24f8b88c1ed9ed3128cb1c9&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268160340152320_1104vImg.jpeg\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=abfaef83f24f8b88c1ed9ed3128cb1c9&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268160340152320_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1074120972560420864\":{\"actor\":\"周野芒|赵丽娟\",\"area\":\"中国内地\",\"backGroundColor\":\"#D6B1B0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#993834\\\",\\\"fanZaiColor\\\":\\\"#803633\\\"}\",\"backGroundColorTwo\":\"#804240\",\"brief\":\"感受中国核工业人不屈不挠精神\",\"category\":\"都市\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"weidiou\",\"createTime\":1731381929000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1}]\",\"director\":\"王文杰\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1071825\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"highLightVid\":\"1076653544800702464\",\"honor\":\"\",\"horizontalIcon\":\"http://img.funshion.com/sdw?oid=91a78d95c720d01445095d0dabe47b2a&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120972560420864_1112hIcon.png\",\"horizontalImage\":\"http://img.funshion.com/sdw?oid=91a78d95c720d01445095d0dabe47b2a&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120972560420864_1112hImg.png\",\"information\":\"《世纪之约》：这是一部反映曾参加过\\\"两弹一艇\\\"建造的核工业战线的功臣们，冷战年月结束以后走出深山峻岭来到改革开放的最前沿大特区，以雷默为代表的一大批核工业人离开了大西北核潜艇基地，告别了过去那种长期处于保密状态、半军事化的生活...\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"都市\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 19 集\",\"programType\":\"tv\",\"publishTime\":1732166198000,\"riskFlag\":0,\"showTime\":\"200206\",\"sid\":\"1074120972560420864\",\"source\":\"weidiou\",\"sourceAlbumId\":\"M279M0AZ7Q\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.2\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"感受中国核工业人不屈不挠精神\",\"supplyType\":\"normal\",\"tags\":\"都市\",\"title\":\"世纪之约\",\"totalEpisode\":19,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732166197000,\"validEpisode\":19,\"verifyStatus\":1,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=2d6d2360b15cc3b80d354f10c5a0faae&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120972560420864_1112vIcon.png\",\"verticalImage\":\"http://img3.funshion.com/sdw?oid=2d6d2360b15cc3b80d354f10c5a0faae&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120972560420864_1112vImg.png\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2002},\"1074120971360849920\":{\"actor\":\"张弓\",\"area\":\"中国内地\",\"backGroundColor\":\"#D6B0C9\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#993477\\\",\\\"fanZaiColor\\\":\\\"#803366\\\"}\",\"backGroundColorTwo\":\"#80406A\",\"brief\":\"古装探案惊心动魄的历史风云\",\"category\":\"古装\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"weidiou\",\"createTime\":1731381929000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1}]\",\"director\":\"张志明\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1071917\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img2.funshion.com/sdw?oid=92dace8a8abd41dc4c3e5c8952223271&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120971360849920_1112hIcon.jpeg\",\"horizontalImage\":\"http://img2.funshion.com/sdw?oid=92dace8a8abd41dc4c3e5c8952223271&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120971360849920_1112hImg.jpeg\",\"information\":\"《太平私盐案》：故事发生在古代的太平县，讲述了李璞不畏强权，不因亲情而徇私枉法，公正查处孙继宗等人贩卖私盐一案的故事。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"古装\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 5 集\",\"programType\":\"tv\",\"publishTime\":1732166194000,\"riskFlag\":0,\"showTime\":\"199906\",\"sid\":\"1074120971360849920\",\"source\":\"weidiou\",\"sourceAlbumId\":\"M279JN2UGY\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.4\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"古装探案惊心动魄的历史风云\",\"supplyType\":\"normal\",\"tags\":\"古装\",\"title\":\"太平私盐案\",\"totalEpisode\":5,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732166194000,\"validEpisode\":5,\"verifyStatus\":1,\"verticalIcon\":\"http://img.funshion.com/sdw?oid=8e6959c87e3893fb4f2040855a0815a3&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120971360849920_1112vIcon.jpeg\",\"verticalImage\":\"http://img.funshion.com/sdw?oid=8e6959c87e3893fb4f2040855a0815a3&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1074120971360849920_1112vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":1999},\"1071268087111798784\":{\"actor\":\"\",\"area\":\"内地\",\"backGroundColor\":\"#D6B0C1\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#993463\\\",\\\"fanZaiColor\\\":\\\"#803356\\\"}\",\"backGroundColorTwo\":\"#80405D\",\"brief\":\"小志带你去冒险哦\",\"category\":\"早教\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"funshion_lv\",\"createTime\":1730704296000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1},{\\\"level\\\":4,\\\"payStatus\\\":1}]\",\"director\":\"\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1076819\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":0.00,\"honor\":\"\",\"horizontalIcon\":\"http://img3.funshion.com/sdw?oid=2695a4fa4e465cfd62e9c8a7b95d2577&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268087111798784_1104hIcon.jpeg\",\"horizontalImage\":\"http://img3.funshion.com/sdw?oid=2695a4fa4e465cfd62e9c8a7b95d2577&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268087111798784_1104hImg.jpeg\",\"information\":\"小志和玩具，主要讲述小志哥哥开箱很多玩具，并且解说的视频。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"早教\",\"markCode\":\"\",\"medium\":2,\"nowPrice\":0.00,\"participantType\":-1,\"payEffectDays\":3,\"payStatus\":0,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 100 集\",\"programType\":\"kids\",\"publishTime\":1732158876000,\"riskFlag\":0,\"showTime\":\"202410\",\"sid\":\"1071268087111798784\",\"source\":\"funshion_lv\",\"sourceAlbumId\":\"M297EE0CEZ\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.1\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"kids\",\"subTitle\":\"小志带你去冒险哦\",\"supplyType\":\"normal\",\"tags\":\"早教\",\"title\":\"小志和玩具第十四季\",\"totalEpisode\":100,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732158876000,\"validEpisode\":100,\"verifyStatus\":1,\"verticalIcon\":\"http://img3.funshion.com/sdw?oid=27438103ef6af582f554609ccf7d04de&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268087111798784_1104vIcon.jpeg\",\"verticalImage\":\"http://img3.funshion.com/sdw?oid=27438103ef6af582f554609ccf7d04de&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1071268087111798784_1104vImg.jpeg\",\"vipPrice\":0.00,\"vipType\":0,\"year\":2024},\"1073837889705660416\":{\"actor\":\"陈宝国|周扬|鲍国安|尤勇智\",\"area\":\"中国内地\",\"backGroundColor\":\"#D6C1B0\",\"backGroundColorJson\":\"{\\\"v_color_s66_b60\\\":\\\"#996134\\\",\\\"fanZaiColor\\\":\\\"#805533\\\"}\",\"backGroundColorTwo\":\"#805C40\",\"brief\":\"带你重回春秋乱世\",\"category\":\"历史|战争\",\"chargeType\":0,\"competitionType\":-1,\"completed\":1,\"copyright\":1,\"copyrightCode\":\"weidiou\",\"createTime\":1731314437000,\"definition\":\"[{\\\"level\\\":2,\\\"payStatus\\\":1},{\\\"level\\\":3,\\\"payStatus\\\":1}]\",\"director\":\"黄健中|元彬\",\"downloadAble\":1,\"downloadMarkcode\":\"\",\"duration\":0,\"endTime\":-28800000,\"extraInfo\":\"1072243\",\"featureType\":1,\"fitAge\":\"\",\"formerPrice\":10.00,\"honor\":\"\",\"horizontalIcon\":\"http://img1.funshion.com/sdw?oid=4c8522dca20ff41a8f0c01a76e09f078&w=0&h=0\",\"horizontalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1073837889705660416_1111hIcon.png\",\"horizontalImage\":\"http://img1.funshion.com/sdw?oid=4c8522dca20ff41a8f0c01a76e09f078&w=0&h=0\",\"horizontalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1073837889705660416_1111hImg.png\",\"information\":\"《越王勾践》：在吴越两国因战争结下仇怨的背景下，吴王夫差（尤勇智 饰）在伍子胥（鲍国安 饰）的辅佐下日夜勤兵，准备报父仇。与此同时，越国得意之时，勾践（陈宝国 饰）铸成“王者之剑”，欲灭吴国。然而，当夫差攻进“剑庐”时，伍子胥已领兵渡过大江，大败越军。越国危在旦夕，范蠡提出降吴的主张，勾践接受并成为吴国的奴隶，为保留一线复国的机会。勾践在吴王府中默默等待，为了复国的大志而受尽屈辱。夫差对勾践不杀之说不以为然，认为一个亡国的奴隶不会再成为威胁。后来，太宰伯噽向夫差荐上越女西施。夫差被西施的美貌所倾倒，开始放松了警惕。伍子胥劝说吴王不可相信勾践，但被夫差视而不见。勾践和范蠡在暗中积蓄力量，最终得到机会回越国。勾践回国后，继续谨慎从事，积蓄力量。夫差一再北进欲称霸，最终被伍子胥所阻。夫差赐死伍子胥后，吴国实力削弱。勾践终于得到机会，举兵复国。在姑苏破城之日，勾践率大军向伍子胥自刎之地致敬。夫差败于勾践之手，也自刎而死。勾践历经十数年的卧薪尝胆、韬光养晦、励精图治，最终实现了复国的宏愿。\",\"keyword\":\"\",\"language\":\"普通话\",\"managerStatus\":0,\"mappingTags\":\"历史|战争\",\"markCode\":\"funshion_vip_1\",\"medium\":2,\"nowPrice\":5.00,\"participantType\":-1,\"payEffectDays\":10000,\"payStatus\":1,\"period\":\"\",\"prePush\":0,\"previewInfo\":\"[]\",\"price\":0,\"processStatus\":0,\"programInfo\":\"全 41 集\",\"programType\":\"tv\",\"publishTime\":1732166207000,\"riskFlag\":0,\"showTime\":\"200606\",\"sid\":\"1073837889705660416\",\"source\":\"weidiou\",\"sourceAlbumId\":\"M27908D9HD\",\"sourceHot\":0.0,\"sourcePlayCount\":0,\"sourceScore\":\"8.5\",\"sourceSeriesId\":\"[]\",\"sourceStatus\":1,\"sourceType\":2,\"sourceWebUrl\":\"\",\"startTime\":-28800000,\"status\":1,\"subProgramType\":\"tv\",\"subTitle\":\"带你重回春秋乱世\",\"supplyType\":\"normal\",\"tags\":\"历史|战争\",\"title\":\"越王勾践\",\"totalEpisode\":41,\"tuputag\":\"\",\"unit\":1,\"updateTime\":1732166207000,\"validEpisode\":41,\"verifyStatus\":1,\"verticalIcon\":\"http://img2.funshion.com/sdw?oid=d754b0ba51e34db22a393c0cb1a15856&w=0&h=0\",\"verticalIconOcs\":\"https://dhfs-test-cpc.wanyol.com/1073837889705660416_1111vIcon.png\",\"verticalImage\":\"http://img2.funshion.com/sdw?oid=d754b0ba51e34db22a393c0cb1a15856&w=0&h=0\",\"verticalImageOcs\":\"https://dhfs-test-cpc.wanyol.com/1073837889705660416_1111vImg.png\",\"vipPrice\":0.00,\"vipType\":1,\"year\":2006}}";
        Map<String, StandardAlbum> res = objectMapper.readValue(json, new TypeReference<Map<String, StandardAlbum>>() {});
        RpcResult<Map<String, StandardAlbum>> mapRpcResult = new RpcResult<>(res);
        mocker.returnValue(mapRpcResult).configure();
        mockers.add(mocker);
    }
}
