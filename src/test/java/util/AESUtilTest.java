package util;

import com.heytap.longvideo.search.utils.AESUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

public class AESUtilTest {
    // 加密密钥，长度为32字节
    public static final String KEY = "6162636465666768696a6b6c6d6e6f70";

    @Test
    public void test1() {
        // 原始数据
        String originalData = "13553588581";

        // 前端传的BASE64编码
        String encryptedData = "HVgo4lEaCyRarGKnMvOqrA==";
        String iv = "TqFTGJTtpWOtnBclNtGsqA==";

        // 解密
        String decryptedData = AESUtil.decrypt(encryptedData, KEY, iv);

        Assert.assertEquals(originalData, decryptedData);
    }

    @Test
    public void test2() {
        // 原始数据
        String originalData = "15010238088";

        // 加密
        Pair<String, String> pair = AESUtil.encrypt(originalData, KEY);
        String iv = pair.getLeft();
        String encryptedData = pair.getRight();
        System.out.println("iv: " + iv);
        System.out.println("encrypted Data: " + encryptedData);

        // 解密
        String decryptedData = AESUtil.decrypt(encryptedData, KEY, iv);
        System.out.println("Decrypted Data: " + decryptedData);

        Assert.assertEquals(originalData, decryptedData);
    }
}
