import com.alibaba.fastjson.JSON;
import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.PageRpcApi;
import com.heytap.longvideo.client.arrange.entity.LvPage;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.LvMediaSeriesItemRpcApi;
import com.heytap.longvideo.client.media.LvMediaSeriesRpcApi;
import com.heytap.longvideo.client.media.entity.oppomedia.LvMediaSeries;
import com.heytap.longvideo.client.media.entity.oppomedia.LvMediaSeriesItem;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.liteflow.cmp.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import esa.rpc.test.support.mock.Mocker;
import guava.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import util.MediaRpcMockerUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class SeriesServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private SearchSeriesCmp searchSeriesCmp;

    @Autowired
    private GetSearchIntentCmp getSearchIntentCmp;

    @Autowired
    private ResultAggregationCmp resultAggregationCmp;

    @Autowired
    private SearchInterveneCmp searchInterveneCmp;

    @Test
    public void testSearchSeriesService() {
        try {
            mockData();
            KeyWordSearchParamV2 keyWordSearchParamV2 = new KeyWordSearchParamV2();
            keyWordSearchParamV2.setVersion(71100);
            keyWordSearchParamV2.setPageIndex(1);
            keyWordSearchParamV2.setSearchType("1");
            keyWordSearchParamV2.setDeviceType(0);
            AttributeValues attributeValues = new AttributeValues();
            attributeValues.setBuuid(123L);
            keyWordSearchParamV2.setAttributeValues(attributeValues);
            keyWordSearchParamV2.setKeyword("小猪佩奇");
            Map<InterveneTypeEnum, LvSearchIntervene> map = FutureUtil.getFutureIgnoreException(getSearchIntentCmp.getSearchIntervene(keyWordSearchParamV2));
            Assert.assertTrue(map.size() > 0);
            KeyWordSearchParamV2 paramV2 = new KeyWordSearchParamV2();
            paramV2.setVersion(71600);
            paramV2.setQuickEngineVersion(90200);
            SearchInterveneCardResponse seriesResopnse = FutureUtil.getFutureIgnoreException(
                    searchSeriesCmp.getSeries(map.get(InterveneTypeEnum.SERIES), 1, 12, "", paramV2, null, 1));
            Assert.assertTrue(seriesResopnse.getContents().size() > 0);

            SearchByKeyWordContext context = new SearchByKeyWordContext();
            context.setRequestParam(keyWordSearchParamV2);
            context.setInterveneConfigMap(map);
            context.getSearchResponse().setLongVideoSeries(seriesResopnse);
            List<KeyWordSearchResponse> baseSearchResult = new ArrayList<>();
            String str = "{\n" +
                    "\t\"sid\": \"650715036733612032\",\n" +
                    "\t\"title\": \"小猪佩奇 第二季\",\n" +
                    "\t\"markCode\": \"\",\n" +
                    "\t\"markCodeUrl\": \"\",\n" +
                    "\t\"directors\": \"阿斯特利\",\n" +
                    "\t\"source\": \"mgmobile\",\n" +
                    "\t\"verticalIcon\": \"http://4img.hitv.com/preview/sp_images/2023/01/04/202301041827275675681.jpg\",\n" +
                    "\t\"area\": \"英国\",\n" +
                    "\t\"sourceScore\": 9.0,\n" +
                    "\t\"year\": 2006,\n" +
                    "\t\"payStatus\": 0,\n" +
                    "\t\"copyrightCode\": \"mgmobile\",\n" +
                    "\t\"contentType\": \"kids\",\n" +
                    "\t\"horizontalIcon\": \"http://0img.hitv.com/preview/internettv/sp_images/ott/2017/dongman/130489/20170410180235603-new.jpg\",\n" +
                    "\t\"programInfo\": \"全 52 集\",\n" +
                    "\t\"languages\": \"普通话\",\n" +
                    "\t\"releScore\": 10.0,\n" +
                    "\t\"stars\": \"小猪佩奇\",\n" +
                    "\t\"functionScore\": 0.0,\n" +
                    "\t\"tags\": \"亲子|益智\",\n" +
                    "\t\"linkType\": 1,\n" +
                    "\t\"featureType\": 2,\n" +
                    "\t\"multipleSourceCode\": [\n" +
                    "\t\t\"mgmobile\"\n" +
                    "\t],\n" +
                    "\t\"recommendInfo\": \"粉红猪小妹\",\n" +
                    "\t\"deepLink\": \"yoli://yoli.com/yoli/longvideo/videodetail?linkValue=650715036733612032\",\n" +
                    "\t\"brief\": \"粉红猪小妹\"\n" +
                    "}";
            KeyWordSearchResponse keyWordSearchResponse = JSON.parseObject(str, KeyWordSearchResponse.class);
            baseSearchResult.add(keyWordSearchResponse);
            context.setBaseSearchResult(baseSearchResult);

            KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
            searchResponse.setSortIndex(10);
            context.setSearchInterveneResult(Lists.newArrayList(searchResponse));
            resultAggregationCmp.resultAggregation(context);
        } catch (Throwable e) {
            log.error("testSearchSeriesService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("testSearchSeriesService pass");
    }

    private void mockData() throws Exception {
        mockGetSearchIntervene();
        mockGetValidSeriesBySeriesId();
        mockGetValidSeriesItemBySeriesId();
        MediaRpcMockerUtil.getBySidsFilterInvalid();
        mockGetLvPage();
    }


    public static Mocker mockGetSearchIntervene() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvSearchInterveneRpcApi.class.getName())
                .methodName("getSearchInterveneList");
        RpcResult<List<LvSearchIntervene>> resp = new RpcResult(0, "success");
        String str = "[{\"id\":56,\"interveneDetail\":\"\",\"interveneType\":3,\"linkType\":10040,\"linkValue\":\"1309\",\"matchType\":2,\"name\":\"小猪佩奇\",\"queryKeyword\":\"小猪佩奇\",\"status\":1,\"title\":\"title\"},{\"id\":57,\"interveneDetail\":\"\",\"interveneType\":1,\"linkType\":102,\"linkValue\":\"pa_000675\",\"subInterveneType\":1,\"matchType\":2,\"name\":\"小猪佩奇\",\"queryKeyword\":\"小猪佩奇\",\"status\":1,\"title\":\"title\"}]";
        List<LvSearchIntervene> list = JSON.parseArray(str, LvSearchIntervene.class);
        resp.setData(list);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker mockGetLvPage() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(PageRpcApi.class.getName())
                .methodName("findByCodeWithCache");
        RpcResult<LvPage> resp = new RpcResult(0, "success");
        String str = "{\"auditStatus\":1,\"auditors\":\"\",\"autoMatch\":0,\"channelCode\":\"lv_dandandandandan\",\"clientVersion\":\"\",\"code\":\"pa_000675\",\"createTime\":1693362751000,\"hasContentMaterialRefresh\":1,\"homePage\":1,\"id\":577,\"invalidTime\":1782748800000,\"name\":\"丹丹丹丹丹\",\"operatorId\":\"W9069949(黄丹丹)\",\"operatorName\":\"\",\"pageEffectInvalidTime\":1874643378000,\"pageEffectSwitch\":1,\"pageEffectType\":1,\"pageEffectV2\":\"{\\\"PageNavigationData\\\":{\\\"immersiveBackColor\\\":\\\"\\\",\\\"focusTabTitleColor\\\":\\\"\\\",\\\"unfocusTabTitleColor\\\":\\\"\\\",\\\"iconColor\\\":\\\"\\\",\\\"underscoreColor\\\":\\\"\\\",\\\"unChooseChannelFigUrl\\\":\\\"\\\",\\\"blackNavigateImage\\\":\\\"\\\",\\\"chooseChannelFigUrl\\\":\\\"\\\",\\\"blackNavigateFocusImage\\\":\\\"\\\",\\\"immersionFigUrl\\\":\\\"\\\",\\\"blackNavigateImmerseImage\\\":\\\"\\\",\\\"dataSwitch\\\":true,\\\"headFigUrl\\\":\\\"\\\",\\\"pageNavigationStartTime\\\":\\\"\\\",\\\"pageNavigationEndTime\\\":\\\"\\\"},\\\"PageListData\\\":{\\\"dataSwitch\\\":true,\\\"pageListBackColor\\\":\\\"\\\",\\\"pageListIconColor\\\":\\\"\\\",\\\"moduleTitleColor\\\":\\\"\\\",\\\"showTitleColor\\\":\\\"\\\",\\\"showSubtitleColor\\\":\\\"\\\",\\\"pageListStartTime\\\":\\\"\\\",\\\"pageListEndTime\\\":\\\"\\\"},\\\"PageTabData\\\":{\\\"dataSwitch\\\":true,\\\"pageTabBackColor\\\":\\\"\\\",\\\"pageTabIconColor\\\":\\\"\\\",\\\"pageTabStartTime\\\":\\\"\\\",\\\"pageTabEndTime\\\":\\\"\\\"},\\\"navigationFontEffect\\\":2,\\\"PageImmersionData\\\":{\\\"headFigUrl\\\":\\\"\\\"}}\",\"pageEffectValidTime\":1716876978000,\"pageOperateInfo\":\"{\\\"type\\\":1,\\\"operateInfo\\\":[{\\\"id\\\":125,\\\"linkType\\\":10000,\\\"linkValue\\\":\\\"yoli://yoli.com/TaskForVip/fullScreen?vipType=video_vip&sourceEntry=free_icon\\\",\\\"onlineTitle\\\":\\\"做任务送会员\\\",\\\"image\\\":\\\"https://dhfs-test-cpc.wanyol.com/20240322165235247.png\\\",\\\"darkImage\\\":\\\"https://dhfs-test-cpc.wanyol.com/20240401153559799.png\\\",\\\"title\\\":\\\"做任务送会员\\\"},{\\\"id\\\":115,\\\"linkType\\\":120,\\\"linkValue\\\":\\\"\\\",\\\"onlineTitle\\\":\\\"观看历史\\\",\\\"image\\\":\\\"https://dhfs-test-cpc.wanyol.com/20230922101610775.png\\\",\\\"darkImage\\\":\\\"https://dhfs-test-cpc.wanyol.com/20230922101617759.png\\\",\\\"title\\\":\\\"观看历史\\\"},{\\\"id\\\":116,\\\"linkType\\\":109,\\\"linkValue\\\":\\\"contentType=tv&sort=最新排序&tag=谍战&area=中国香港&payment=会员\\\",\\\"onlineTitle\\\":\\\"筛选\\\",\\\"image\\\":\\\"https://dhfs-test-cpc.wanyol.com/20230922101536780.png\\\",\\\"darkImage\\\":\\\"https://dhfs-test-cpc.wanyol.com/20230922101544100.png\\\",\\\"title\\\":\\\"筛选\\\"}]}\",\"pageType\":102,\"pageValue\":\"pa_000675\",\"publishStatus\":1,\"publishTime\":1717556592000,\"relationOperator\":-1,\"secondFloorPageDeepLink\":\"yoli://yoli.com/detail_feed_video_yoli/mix_video?fromId=youli_highlights&channelSource=youli&scene=1&poolCode=cp_00000263&pageCode=pa_000675&lvChannelName=丹丹丹丹\",\"secondFloorPageTitle\":\"进入二楼\",\"selectJump\":\"contentType=all\",\"status\":1,\"title\":\"丹丹丹丹\",\"updateTime\":1717556578000,\"validTime\":1716825600000}";
        LvPage lvPage = JSON.parseObject(str, LvPage.class);
        resp.setData(lvPage);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker mockGetValidSeriesBySeriesId() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvMediaSeriesRpcApi.class.getName())
                .methodName("getValidSeriesBySeriesId");
        RpcResult<LvMediaSeries> resp = new RpcResult(0, "success");
        String str = "{\"count\":32,\"createTime\":1672763604000,\"moreEpisode\":1,\"name\":\"小猪佩奇\",\"operatorName\":\"吴嘉乐(W90010035)\",\"programType\":\"kids\",\"refreshStatus\":1,\"refreshed\":0,\"seriesId\":1309,\"sourceSeriesId\":\"{\\\"mgmobile\\\":[\\\"103537\\\",\\\"71146\\\"],\\\"senyu\\\":[\\\"541\\\"],\\\"sohu\\\":[\\\"7232\\\",\\\"5177\\\",\\\"6674\\\",\\\"6412\\\",\\\"6345\\\",\\\"5937\\\",\\\"10417\\\",\\\"6700\\\",\\\"6414\\\",\\\"6413\\\",\\\"7953\\\",\\\"7813\\\",\\\"6415\\\"]}\",\"status\":1,\"title\":\"小猪佩奇\",\"updateTime\":1673941028000}";
        LvMediaSeries lvMediaSeries = JSON.parseObject(str, LvMediaSeries.class);
        resp.setData(lvMediaSeries);
        mocker.returnValue(resp).configure();
        return mocker;
    }

    public static Mocker mockGetValidSeriesItemBySeriesId() {
        //RPC接口mock
        Mocker mocker = Mocker.newBuilder()
                .interfaceName(LvMediaSeriesItemRpcApi.class.getName())
                .methodName("getValidSeriesItemBySeriesId");
        RpcResult<List<LvMediaSeriesItem>> resp = new RpcResult(0, "success");
        String str = "[{\"chapter\":1,\"createTime\":1673518717000,\"horizontalIcon\":\"http://2img.hitv.com/preview/internettv/sp_images/ott/2016/dongman/130491/20161005110248605-new.jpg\",\"id\":7427,\"language\":\"普通话\",\"operatorName\":\"盛惟馨(W9005448)\",\"orderIndex\":1,\"programType\":\"kids\",\"seriesId\":1309,\"showName\":\"第一季\",\"showTime\":0,\"sid\":\"654244148668473344\",\"source\":\"mgmobile\",\"sourceStatus\":1,\"status\":1,\"title\":\"小猪佩奇 第一季\",\"updateTime\":1674975003000,\"verticalIcon\":\"http://0img.hitv.com/preview/sp_images/2023/01/04/202301041826149413915.jpg\"}]";
        List<LvMediaSeriesItem> list = JSON.parseArray(str, LvMediaSeriesItem.class);
        resp.setData(list);
        mocker.returnValue(resp).configure();
        return mocker;
    }


}