import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.UnofficialAlbumOriginalImageMappingOcs;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.rpc.consumer.UnofficialAlbumImageMappingRpcApiProxy;
import com.heytap.longvideo.search.service.common.ImageUploadHelperComponet;
import com.heytap.longvideo.search.service.spider.UnofficialAlbumImageUrlTransformService;
import com.heytap.longvideo.search.utils.FutureUtil;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.internal.util.reflection.Whitebox;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/7/9 下午5:34
 */


@RunWith(PowerMockRunner.class)
@PrepareForTest({SearchHits.class, SearchHit.class, TotalHits.class, RestHighLevelClient.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class UnofficialAlbumImageUrlTransformServiceTest {
    @InjectMocks
    private UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService;
    @Mock
    private UnofficialAlbumImageMappingRpcApiProxy unofficialAlbumImageMappingRpcApiProxy;
    @Mock
    private ImageUploadHelperComponet imageUploadHelperComponet;

    @Test
    public void handleVIconTest() throws IOException {
        UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
        PowerMockito.doReturn("111").when(imageUploadHelperComponet).uploadPic(any(), any(), any(), any());
        List<UnofficialAlbumOriginalImageMappingOcs> list = new ArrayList<>();
        RpcResult rpcResult = new RpcResult(list);
        PowerMockito.doReturn(CompletableFuture.completedFuture(rpcResult)).when(unofficialAlbumImageMappingRpcApiProxy).findByOriginalUrl(any());
        ThreadFactory threadFactory = new CustomizableThreadFactory("all-web-image-ocs-pool-test-");
        Whitebox.setInternalState(unofficialAlbumImageUrlTransformService, "threadPoolExecutor", new ThreadPoolExecutor(5, 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue(100), threadFactory));
        Boolean res = unofficialAlbumImageUrlTransformService.generateImageAndSyncToEs(unofficialAlbumEs, true, false);
        Assert.assertTrue(res);
    }
}