import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.VideoOperationLogEs;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.common.VideoOperationLogService;
import com.heytap.video.client.search.model.VideoOperationLog;
import com.heytap.video.client.search.model.request.VideoOperationLogQueryRequest;
import com.heytap.video.client.search.model.response.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.test.context.junit4.SpringRunner;



@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class ViseoOperationLogServiceTest extends GoblinJunit4BaseTest {

    @Autowired
    private VideoOperationLogService videoOperationLogService;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    /**
     * 搜索
     */
    @Test
    public void testAppSearchService() {
        try {
            elasticsearchService.createIndexAndMapping(VideoOperationLogEs.class);
            VideoOperationLogEs es =new VideoOperationLogEs();
            es.setBusinessType("a");
            es.setCreateTime(System.currentTimeMillis());
            es.setBusinessMudule("a");
            es.setCreateUser("aaa");
            es.setBusinessCode("a");
            es.setRequestInfo("{12313}");
            es.setResponseInfo("asda");

            videoOperationLogService.insert(es);
            IndexOperations indexOperations = restTemplate.indexOps(VideoOperationLogEs.class);
            indexOperations.refresh();
            VideoOperationLogQueryRequest request =new VideoOperationLogQueryRequest();
            request.setBusinessMudule("a");
            request.setCreateUser("aaa");
            request.setBusinessCode("a");
            request.setPageIndex(1);
            request.setPageSize(10);
            PageResponse<VideoOperationLog> response= videoOperationLogService.queryPage(request);
            Assert.assertTrue(response.getItemList().size()>0);

        } catch (Throwable e) {
            log.error("testStandAlbumService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }

}