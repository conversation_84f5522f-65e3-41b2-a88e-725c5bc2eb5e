import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.executors.standard.CmsStandardAlbumExecutor;
import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class StandVideoServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private CmsStandardAlbumExecutor cmsStandardAlbumExecutor;

    @Autowired
    private InitStandardDataService initStandardDataService;

    @Autowired
    private StandardVideoService standardVideoService;

    @Autowired
    private ElasticSearchService elasticsearchService;

    /**
     * 新增和查询标准化的视频
     */
    @Test
    public void testStandVideoService1() {
        try {
            elasticsearchService.deleteIndex(StandardVideoEs.class);
            elasticsearchService.createIndexAndMapping(StandardVideoEs.class);
            InitDataParam initDataParam = new InitDataParam();
            initDataParam.setType("video");
            initDataParam.setDataBaseEnd(0);
            initDataParam.setTableEnd(0);
            initDataParam.setUpdateTime("2021-01-01 00:00:00");
            initStandardDataService.initData(initDataParam);
            SearchStandardAlbumParams request = new SearchStandardAlbumParams();
            SearchStandardVideoParams standardAlbumParams = new SearchStandardVideoParams();
            standardAlbumParams.setVid("749030003991322624");
            standardAlbumParams.setPageIndex(1);
            PageResponse<StandardVideoVo> result = standardVideoService.searchVideo(standardAlbumParams);
            Assert.assertEquals("749030003991322624", result.getItemList().get(0).getVid());

            List<VideoTypeListVo> videoTypeListVoList = standardVideoService.getVideoType("sohu");
            Assert.assertEquals("0", videoTypeListVoList.get(0).getValue());

        } catch (Throwable e) {
            log.error("testStandVideoService error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }


    /**
     * 视频导出测试
     */
    @Test
    public void testStandVideoService2() {
        try {
            SearchStandardVideoParams searchStandardVideoParams = new SearchStandardVideoParams();
            searchStandardVideoParams.setSource("funshion");
            searchStandardVideoParams.setOrder("updateTime");
            searchStandardVideoParams.setTitle("11");
            SearchExportVo searchExportVo = standardVideoService.searchAndExport(searchStandardVideoParams);
            Assert.assertNotNull(searchExportVo.getSearchExportId());
            Assert.assertNotNull(searchExportVo.getUrl());
        } catch (Exception e) {
            log.error("testMediaSearchAndExport error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }

    /**
     * 视频导出测试
     */
    @Test
    public void testStandVideoService3() {
        try {
            SearchStandardVideoParams searchStandardVideoParams = new SearchStandardVideoParams();
            searchStandardVideoParams.setSource("mgmobile");
            searchStandardVideoParams.setOrder("updateTime");
            searchStandardVideoParams.setTitle("111");
            SearchExportVo searchExportVo = standardVideoService.searchAndExport(searchStandardVideoParams);
            Assert.assertNotNull(searchExportVo.getSearchExportId());
            Assert.assertNotNull(searchExportVo.getUrl());
        } catch (Exception e) {
            log.error("testMediaSearchAndExport error", e);
            //校验有无异常，有异常则测试不通过，说明代码有问题
            Assert.assertNull(e);
        }
        log.info("test pass");
    }

}