import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.liteflow.cmp.SearchAlbumCmp;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.completion.Completion;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;

import java.util.*;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class AppSearchServiceTest extends GoblinJunit4BaseTest {
    @Autowired
    private InitService initService;


    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private SearchAlbumCmp searchAlbumCmp;

    @Autowired
    private ListFilterService listFilterService;


    @Autowired
    private StandardAlbumMapper standardAlbumMapper;

    @Autowired
    private ElasticSearchService elasticsearchService;

    /**
     * 搜索
     */
    @Test
    public void testAppSearchService() {
        try {
            ElasticSearchUtil.createIndexAndAddData(standardAlbumMapper, initService, elasticsearchService, restTemplate, true, null);

            KeyWordSearchParamV2 keyWordSearchParam = new KeyWordSearchParamV2();
            keyWordSearchParam.setKeyword("流浪");
            keyWordSearchParam.setPageIndex(1);
            List<ProgramAlbumEs> programAlbumEs = searchAlbumCmp.search(keyWordSearchParam);
            Assert.assertEquals("625229331555176448", programAlbumEs.get(0).getSid());

            ListFilterParam listFilterParam = new ListFilterParam();
            listFilterParam.setVersion(60300);
            listFilterParam.setNumber(27);
            listFilterParam.setOffset(0);
            listFilterParam.setUrlpack("{\"cmd_vod\":{\"contentType\":\"all\",\"version_tag\":\"4\",\"sort\":\"score\",\"age\":\"0-18\"}}");
            List<ProgramAlbumEs> listfilterResult = listFilterService.listFilter(listFilterParam);
            Assert.assertTrue(listfilterResult.size() > 0);

            KeyWordSearchParamV2 searchParamV2 = new KeyWordSearchParamV2();
            searchParamV2.setKeyword("神探四豆星");
            searchParamV2.setPageIndex(1);
            List<ProgramAlbumEs> programAlbumEs2 = searchAlbumCmp.search(searchParamV2);
            Assert.assertEquals("630983657569591296", programAlbumEs2.get(0).getSid());
        } catch (Throwable e) {
            log.error("testStandAlbumService error", e);
        } finally {
            ElasticSearchUtil.deleteAlbumEs(elasticsearchService);
        }
        log.info("test pass");
    }

}