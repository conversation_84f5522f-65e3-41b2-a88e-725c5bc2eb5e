import com.heytap.cpc.dfoob.goblin.core.GoblinJunit4BaseTest;
import com.heytap.cpc.dfoob.goblin.core.xunit.core.UsingDataSet;
import com.heytap.longvideo.search.bootstrap.Main;
import com.heytap.longvideo.search.model.param.standard.UgcSearchVideoParams;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.UgcStandardVideoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import util.ElasticSearchUtil;



@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Main.class})
@Slf4j
@UsingDataSet
public class UgcStandVideoServiceTest extends GoblinJunit4BaseTest {

    @Autowired
    private UgcStandardVideoService ugcStandardVideoService;

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 新增和查询标准化的视频
     */
    @Test
    public void testSearchVideo() {

        // ES数据mock
        ElasticSearchUtil.searchUgcStandardVideoMock(elasticSearchService, elasticsearchRestTemplate);

        UgcSearchVideoParams request = new UgcSearchVideoParams();
        request.setSource("funshion");
        try {
            // case 1
            request.setLinkSid("680628508279644160");
            ugcStandardVideoService.searchVideo(request);

            // case 2
            request.setLinkSid(null);
            request.setIsRelatedAlbum(0);
            ugcStandardVideoService.searchVideo(request);

            // case 3
            request.setIsRelatedAlbum(1);
            ugcStandardVideoService.searchVideo(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}