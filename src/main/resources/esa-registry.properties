# 该配置文件仅供本地开发使用，实际测试环境、线上环境发布平台会注入这些到环境变量，不再需要此文件。
# 本地开发需要自己配置paas_app_id和instance_id，esa_registry_server、esa_push_connect_server和paas_idc_code不用修改
# 注册中心server地址（开发环境）
esa_registry_server=registry-testzx.esa.wanyol.com
#esa_registry_server=registry-dev.esa.wanyol.com

# push地址
esa_push_connect_server=10.177.38.161:6001,10.177.38.162:6001
#esa_push_connect_server=10.176.21.197:6001,10.176.21.198:6001
# 机房编号(根据你的服务需要注册到哪里去云平台自己查找，注意消费方和提供方要配置一样 这里用的开发环境的'志享开发'举例)
paas_idc_code=CN-S01-DGTEST01
#paas_idc_code=CN-S01-DGDEV01

# 应用appId，与consumer端@Reference注解中对应的providerAppId值相同
paas_app_id=longvideo-search-rest


# 实例编号, 不同实例需保证此编号唯一，本地开发时自己随机配置
instance_id=53453464363-22bc-4e59-bbaa-6464363-searchrest