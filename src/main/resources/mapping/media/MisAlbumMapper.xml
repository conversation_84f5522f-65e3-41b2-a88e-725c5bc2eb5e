<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.heytap.longvideo.search.mapper.media.MisAlbumOrgMapper">

    <select id="getOrgAlbumByAlbumId" resultType="com.heytap.longvideo.client.media.entity.MisAlbumOrg">
        select *
        from longvideo_media${databaseIndex}.mis_album_org${tableIndex}
        where source_album_id = #{sourceAlbumId}
    </select>

    <select id="getOrgAlbumBatch" resultType="com.heytap.longvideo.client.media.entity.MisAlbumOrg">
        select *
        from longvideo_media${databaseIndex}.mis_album_org${tableIndex}
        where source = #{source} and id > #{id}
        order by id asc limit #{size}
    </select>

    <update id="updateStatus">
        update longvideo_media${databaseIndex}.mis_album_org${tableIndex}
        set source_status = #{sourceStatus}, update_time = now()
        where source_album_id = #{sourceAlbumId}
    </update>
</mapper>