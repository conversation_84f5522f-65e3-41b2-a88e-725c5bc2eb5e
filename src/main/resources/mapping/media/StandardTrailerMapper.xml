<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.StandardTrailerMapper">


    <select id="selectStandardTrailerList" resultType="com.heytap.longvideo.search.model.entity.es.StandardTrailerEs">
       select tid,
        sid,
        eid,
        vid,
        source,
        url,
        tail_time,
        head_time,
        source_site,
        source_video_id,
        title,
        vertical_icon,
        horizontal_icon,
        status,
        source_status,
        trailer_type,
        order_num,
        risk_flag,
        duration,
        source_play_count,
        create_time,
        update_time,
        pay_status,
        mark_code,
        definition,
        is_project,
        relate_start,
        relate_end,
        source_episode_id,
        clip_duration,
        play_url_expire_time,
        sports_video_type,
        fit_age_min,
        fit_age_max,
        fit_age
       from longvideo_media${databaseIndex}.standard_trailer${tableIndex}
            where update_time &gt;= #{updateTime} AND tid > #{start} order by tid asc limit #{size}
    </select>

    <select id="selectStandardTrailerCount" resultType="int">
       select count(1) as count
       from longvideo_media${databaseIndex}.standard_trailer${tableIndex}
            where update_time &gt;= #{updateTime}
    </select>


</mapper>
