<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.SeriesAlbumMapper">
    <select id="selectSeriesAlbumList" resultType="com.heytap.longvideo.search.model.entity.db.SeriesAlbum">
        select i.title as albumTitle,s.name as seriesName from oppo_media.media_series_item i left join oppo_media.media_series s
         on i.series_id =s.series_id  order by i.id asc limit #{start},#{size}
    </select>

    <select id="selectAllSeriesProgramCount" resultType="int">
         select count(1) from oppo_media.media_series_item i left join oppo_media.media_series s
         on i.series_id =s.series_id
    </select>


</mapper>
