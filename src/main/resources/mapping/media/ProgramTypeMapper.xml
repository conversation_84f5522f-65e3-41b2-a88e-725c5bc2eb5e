<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.ProgramTypeMapper">
    <select id="selectNameByCode" resultType="com.heytap.longvideo.search.model.entity.db.MisProgramType">
        select `code`, `name` from oppo_media.mis_program_type where `code` = #{code}
    </select>

</mapper>
