<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.StandardEpisodeMapper">


    <select id="selectStandardEpisodeList" resultType="com.heytap.longvideo.search.model.entity.es.StandardEpisodeEs">
       select eid,
        sid,
        vid,
        source,
        url,
        source_video_id,
        source_site,
        tail_time,
        head_time,
        duration,
        title,
        feature_type,
        order_num,
        episode,
        episode_term,
        vertical_icon,
        horizontal_icon,
        copyright,
        copyright_code,
        status,
        source_status,
        pay_status,
        vip_type,
        charge_type,
        subscript_code,
        verify_status,
        risk_flag,
        source_play_count,
        create_time,
        update_time,
        mark_code,
        definition,
        is_project,
        source_episode_id,
        sourcePvId,
        clip_duration,
        play_url_expire_time,
        sports_video_type
       from longvideo_media${databaseIndex}.standard_episode${tableIndex}
            where update_time &gt;= #{updateTime} AND eid > #{start} order by eid asc limit #{size}
    </select>

    <select id="selectStandardEpisodeCount" resultType="int">
       select count(1) as count
       from longvideo_media${databaseIndex}.standard_episode${tableIndex}
            where update_time &gt;= #{updateTime}
    </select>


</mapper>
