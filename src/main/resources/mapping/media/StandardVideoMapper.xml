<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.StandardVideoMapper">
    <select id="selectStandardVideoList" resultType="com.heytap.longvideo.search.model.entity.es.StandardVideoEs">
       select v.id as idOffSet,concat(v.sid,':',v.vid) as uniKey,v.*
       from longvideo_media${databaseIndex}.standard_video${tableIndex} v
            where id &gt; #{id,jdbcType=BIGINT} and update_time &gt;= #{updateTime} order by id asc limit #{size}
    </select>

    <select id="selectStandardVideoCount" resultType="int">
       select count(1) as count
       from longvideo_media${databaseIndex}.standard_video${tableIndex}
            where update_time &gt;= #{updateTime}
    </select>


</mapper>
