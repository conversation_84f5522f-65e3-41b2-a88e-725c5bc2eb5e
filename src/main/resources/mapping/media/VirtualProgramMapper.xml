<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.VirtualProgramMapper">
    <select id="selectAllVirtualProgram" resultType="com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation">
         select virtual_sid,sid
          from  oppo_media.mis_virtual_program_relation where status =1 and source in
            <foreach collection="sourceList" separator="," open="(" close=")" item="source">
                #{source}
            </foreach>
        order by id asc limit #{start},#{size}
    </select>


    <select id="selectAllVirtualProgramCount" resultType="int">
         select count(1) as count from  oppo_media.mis_virtual_program_relation where status =1 and source in
        <foreach collection="sourceList" separator="," open="(" close=")" item="source">
            #{source}
        </foreach>
    </select>

    <select id="selectVirtualProgramBySid" resultType="com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation">
         select virtual_sid,sid,status
          from  oppo_media.mis_virtual_program_relation where sid =#{sid} limit 1
    </select>

    <select id="selectVirtualProgramByVirtualSid" resultType="com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation">
         select virtual_sid,sid
          from  oppo_media.mis_virtual_program_relation where status =1 and virtual_sid =#{virtualSid} and source in
          <foreach collection="sourceList" separator="," open="(" close=")" item="source">
            #{source}
          </foreach>
    </select>

</mapper>
