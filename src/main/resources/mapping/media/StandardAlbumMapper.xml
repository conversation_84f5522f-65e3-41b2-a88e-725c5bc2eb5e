<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.media.StandardAlbumMapper">
    <select id="selectStandardAlbumList" resultType="com.heytap.longvideo.search.model.entity.es.StandardAlbumEs">
       select sid,
        high_light_vid,
        title,
        sub_title,
        manager_status,
        status,
        source_status,
        verify_status,
        source,
        source_album_id,
        source_web_url,
        source_type,
        source_score,
        program_type,
        sub_program_type,
        unit,
        category,
        duration,
        director,
        actor,
        year,
        area,
        tags,
        language,
        honor,
        information,
        valid_episode,
        total_episode,
        completed,
        brief,
        period,
        vertical_icon,
        vertical_icon_ocs,
        horizontal_icon,
        horizontal_icon_ocs,
        vertical_image,
        vertical_image_ocs,
        horizontal_image,
        horizontal_image_ocs,
        back_ground_color,
        back_ground_color_two,
        back_ground_color_json,
        feature_type,
        risk_flag,
        copyright,
        copyright_code,
        program_info,
        medium,
        pay_status,
        vip_type,
        charge_type,
        price,
        keyword,
        extra_info,
        source_play_count,
        source_hot,
        supply_type,
        definition,
        download_markcode,
        download_able,
        mark_code,
        free_start_time,
        free_end_time,
        end_time,
        start_time,
        pay_effect_days,
        vip_price,
        now_price,
        former_price,
        show_time,
        process_status,
        publish_time,
        create_time,
        update_time,
        source_series_id,
        prePush,
        preOnlineTime,
        online_time,
        previewInfo,
        mapping_tags,
        tuputag,
        competition_id,
        competition_name,
        season_id,
        season_name,
        participant_type,
        competition_type,
        phase,
        match_group,
        match_start_time,
        round,
        participant_countrys,
        sport_name,
        event_name,
        presenter,
        fit_age_min,
        fit_age_max,
        fit_age,
        oppo_score,
        douban_score,
        cp_score
       from longvideo_media${databaseIndex}.standard_album${tableIndex}
            where update_time &gt;= #{updateTime} order by sid asc limit #{start},#{size}
    </select>

    <select id="selectStandardAlbumCount" resultType="int">
       select count(1) as count
       from longvideo_media${databaseIndex}.standard_album${tableIndex}
            where update_time &gt;= #{updateTime}
    </select>

    <select id="selectStandardAlbumListForApp" resultType="com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs">
       select a.sid,
        a.high_light_vid,
        a.title,
        a.manager_status,
        a.status,
        a.source_status,
        a.verify_status,
        a.source,
        a.source_album_id,
        a.source_web_url,
        a.source_type,
        a.source_score,
        a.sub_program_type,
        a.unit,
        a.category,
        a.duration,
        a.director,
        a.actor,
        a.year,
        a.area,
        a.tags,
        a.language,
        a.honor,
        a.information,
        a.valid_episode,
        a.total_episode,
        a.completed,
        a.brief,
        a.period,
        a.vertical_icon,
        a.vertical_icon_ocs,
        a.horizontal_icon,
        a.horizontal_icon_ocs,
        a.vertical_image,
        a.vertical_image_ocs,
        a.horizontal_image,
        a.horizontal_image_ocs,
        a.back_ground_color,
        a.back_ground_color_two,
        a.back_ground_color_json,
        a.feature_type,
        a.risk_flag,
        a.copyright,
        a.copyright_code,
        a.program_info,
        a.medium,
        a.pay_status,
        a.vip_type,
        a.charge_type,
        a.price,
        a.keyword,
        a.extra_info,
        a.source_play_count,
        a.source_hot,
        a.supply_type,
        a.definition,
        a.download_markcode,
        a.download_able,
        a.mark_code,
        a.free_start_time,
        a.free_end_time,
        a.end_time,
        a.start_time,
        a.pay_effect_days,
        a.vip_price,
        a.now_price,
        a.former_price,
        a.show_time,
        a.process_status,
        a.publish_time,
        a.create_time,
        a.update_time,
        a.source_series_id,
        a.prePush,
        a.preOnlineTime,
        a.online_time,
        a.previewInfo,
        a.mapping_tags,
        a.tuputag,
        a.competition_id,
        a.competition_name,
        a.season_id,
        a.season_name,
        a.participant_type,
        a.competition_type,
        a.phase,
        a.match_group,
        a.match_start_time,
        a.round,
        a.participant_countrys,
        a.sport_name,
        a.event_name,
        a.presenter,
        a.fit_age_min,
        a.fit_age_max,
        a.fit_age,
        a.oppo_score,
        a.douban_score,
        a.cp_score,
        a.sub_title as epstitle,a.program_type as content_type
       from longvideo_media${databaseIndex}.standard_album${tableIndex} a
            where status =1 and  feature_type in (1,2) and source in
            <foreach collection="sourceList" separator="," open="(" close=")" item="source">
                #{source}
            </foreach>
             and program_type != 'live'
            limit 30000;
    </select>

     <select id="selectStandardAlbumBySid" resultType="com.heytap.longvideo.search.model.entity.es.StandardAlbumEs">
       select sid,
         high_light_vid,
         title,
         sub_title,
         manager_status,
         status,
         source_status,
         verify_status,
         source,
         source_album_id,
         source_web_url,
         source_type,
         source_score,
         program_type,
         sub_program_type,
         unit,
         category,
         duration,
         director,
         actor,
         year,
         area,
         tags,
         language,
         honor,
         information,
         valid_episode,
         total_episode,
         completed,
         brief,
         period,
         vertical_icon,
         vertical_icon_ocs,
         horizontal_icon,
         horizontal_icon_ocs,
         vertical_image,
         vertical_image_ocs,
         horizontal_image,
         horizontal_image_ocs,
         back_ground_color,
         back_ground_color_two,
         back_ground_color_json,
         feature_type,
         risk_flag,
         copyright,
         copyright_code,
         program_info,
         medium,
         pay_status,
         vip_type,
         charge_type,
         price,
         keyword,
         extra_info,
         source_play_count,
         source_hot,
         supply_type,
         definition,
         download_markcode,
         download_able,
         mark_code,
         free_start_time,
         free_end_time,
         end_time,
         start_time,
         pay_effect_days,
         vip_price,
         now_price,
         former_price,
         show_time,
         process_status,
         publish_time,
         create_time,
         update_time,
         source_series_id,
         prePush,
         preOnlineTime,
         online_time,
         previewInfo,
         mapping_tags,
         tuputag,
         competition_id,
         competition_name,
         season_id,
         season_name,
         participant_type,
         competition_type,
         phase,
         match_group,
         match_start_time,
         round,
         participant_countrys,
         sport_name,
         event_name,
         presenter,
         fit_age_min,
         fit_age_max,
         fit_age,
         oppo_score,
         douban_score,
         cp_score
       from longvideo_media${databaseIndex}.standard_album${tableIndex}
            where sid =#{sid} and status = #{status}
    </select>

    <select id="getStandardAlbumBySid" resultType="com.heytap.longvideo.client.media.entity.StandardAlbum">
       select sid,
        high_light_vid,
        title,
        sub_title,
        manager_status,
        status,
        source_status,
        verify_status,
        source,
        source_album_id,
        source_web_url,
        source_type,
        source_score,
        program_type,
        sub_program_type,
        unit,
        category,
        duration,
        director,
        actor,
        year,
        area,
        tags,
        language,
        honor,
        information,
        valid_episode,
        total_episode,
        completed,
        brief,
        period,
        vertical_icon,
        vertical_icon_ocs,
        horizontal_icon,
        horizontal_icon_ocs,
        vertical_image,
        vertical_image_ocs,
        horizontal_image,
        horizontal_image_ocs,
        back_ground_color,
        back_ground_color_two,
        back_ground_color_json,
        feature_type,
        risk_flag,
        copyright,
        copyright_code,
        program_info,
        medium,
        pay_status,
        vip_type,
        charge_type,
        price,
        keyword,
        extra_info,
        source_play_count,
        source_hot,
        supply_type,
        definition,
        download_markcode,
        download_able,
        mark_code,
        free_start_time,
        free_end_time,
        end_time,
        start_time,
        pay_effect_days,
        vip_price,
        now_price,
        former_price,
        show_time,
        process_status,
        publish_time,
        create_time,
        update_time,
        source_series_id,
        prePush,
        preOnlineTime,
        online_time,
        previewInfo,
        mapping_tags,
        tuputag,
        competition_id,
        competition_name,
        season_id,
        season_name,
        participant_type,
        competition_type,
        phase,
        match_group,
        match_start_time,
        round,
        participant_countrys,
        sport_name,
        event_name,
        presenter,
        fit_age_min,
        fit_age_max,
        fit_age,
        oppo_score,
        douban_score,
        cp_score from longvideo_media${databaseIndex}.standard_album${tableIndex}
            where sid =#{sid}
    </select>

</mapper>
