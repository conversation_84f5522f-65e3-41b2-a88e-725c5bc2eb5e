<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.heytap.longvideo.search.mapper.media.MisVideoOrgMapper" >

    <select id="getOrgVideoBatchByAlbumId" resultType="com.heytap.longvideo.client.media.entity.MisVideoOrg">
        select id,
        source,
        source_video_id,
        source_album_id,
        sid,
        vid,
        feature_type,
        time_lap,
        source_status,
        origin_info,
        process_times,
        version,
        create_time,
        update_time
        from longvideo_media${databaseIndex}.mis_video_org${tableIndex}
        where source_album_id = #{sourceAlbumId} and id > #{id}
        order by id asc limit #{size}
    </select>

    <select id="getOrgVideoBatch" resultType="com.heytap.longvideo.client.media.entity.MisVideoOrg">
        select id,
        source,
        source_video_id,
        source_album_id,
        sid,
        vid,
        feature_type,
        time_lap,
        source_status,
        origin_info,
        process_times,
        version,
        create_time,
        update_time
        from longvideo_media${databaseIndex}.mis_video_org${tableIndex}
        where source = #{source} and id > #{id}
        order by id asc limit #{size}
    </select>

    <update id="updateStatus">
        update longvideo_media${databaseIndex}.mis_video_org${tableIndex}
        set source_status = #{sourceStatus}, update_time = now()
        where source_album_id = #{sourceAlbumId} and source_video_id = #{sourceVideoId}
    </update>
</mapper>