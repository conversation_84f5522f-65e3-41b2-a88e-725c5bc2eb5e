<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heytap.longvideo.search.mapper.tvservice.RankCollectMapper">
    <select id="selectRankCollect" resultType="com.heytap.longvideo.search.model.entity.es.HotVideoEs">
       select rankType as contentType,name as title
       from rank_collect where playCount &gt; 5000 and rankType !='overall' limit 100
    </select>

</mapper>
