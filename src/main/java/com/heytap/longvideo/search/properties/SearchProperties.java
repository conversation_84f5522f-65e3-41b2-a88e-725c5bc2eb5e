package com.heytap.longvideo.search.properties;

import com.google.common.collect.Lists;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/*
 * Description 参数配置
 * Date 11:12 2021/12/2
 * Author songjiajia 80350688
 */
@Component
@Getter
public class SearchProperties {

    /**
     * 芒果上线版本
     */
    @HeraclesDynamicConfig(key = "mg.appVersion", fileName = "search_config.properties")
    private int mgAppVersion = 4;

    /**
     * 风行单点付费上线版本
     */
    @HeraclesDynamicConfig(key = "funshion.singleBuy.appVersion", fileName = "search_config.properties")
    private int funshionSingleBuyAppVersion = 42300;

    /**
     * 8.9 搜索结果卡优化版本
     */
    @HeraclesDynamicConfig(key = "searchCard.opt.version", fileName = "search_config.properties")
    private int searchCardOptVersion = 80900;
    /**
     * 7.13 做任务解锁剧集 展示角标
     */
    @HeraclesDynamicConfig(key = "taskUnlockEpisodeExp.version", fileName = "search_config.properties")
    private Integer taskUnlockEpisodeExpVersion = 71300;

    @HeraclesDynamicConfig(key = "copyRight.priority", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, List<String>> copyRightPriorityMap;

    @HeraclesDynamicConfig(key = "filter.priority", fileName = "search_config.properties", textType = TextType.JSON)
    private List<String> filterPriority;

    @HeraclesDynamicConfig(key = "letv.appVersion", fileName = "search_config.properties")
    private int letvAppVersion = 6;

    @HeraclesDynamicConfig(key = "yst.appVersion", fileName = "search_config.properties")
    private int ystAppVersion = 7;

    @HeraclesDynamicConfig(key = "miguOlympic.appVersion", fileName = "search_config.properties")
    private int miguOlympicAppVersion = 8;


    @HeraclesDynamicConfig(key = "funshionlongvideoAndWeidiou.appVersion", fileName = "search_config.properties")
    private int funshionlongvideoAndWeidiouAppVersion = 10;


    @HeraclesDynamicConfig(key = "third.search", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, Map<String, Integer>> thirdSearchMap;

    @HeraclesDynamicConfig(key = "outside.third.search", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, Map<String, Integer>> outSideThirdSearchMap;

    @HeraclesDynamicConfig(key = "recommend.algorithm.url", fileName = "search_config.properties")
    private String recommendAlgorithmUrl;

    @HeraclesDynamicConfig(key = "recommend.algorithm.timeout", fileName = "search_config.properties")
    private int recommendAlgorithmTimeout;

    @HeraclesDynamicConfig(key = "recommend.algorithm.cid", fileName = "search_config.properties")
    private String recommendAlgorithmCid;

    @HeraclesDynamicConfig(key = "recommend.algorithm.route", fileName = "search_config.properties")
    private String recommendAlgorithmRoute;

    @HeraclesDynamicConfig(key = "recommend.algorithm.bidlst", fileName = "search_config.properties")
    private String recommendAlgorithmBidlst;

    @HeraclesDynamicConfig(key = "default.recommend.algorithm.docid", fileName = "search_config.properties")
    private String defaultRecommendAlgorithmDocid;

    @HeraclesDynamicConfig(key = "default.recommend.algorithm.num", fileName = "search_config.properties")
    private Integer defaultRecommendAlgorithmNum;

    @HeraclesDynamicConfig(key = "tvAlbum.search.url", fileName = "search_config.properties")
    private String youkuSearchUrl;

    @HeraclesDynamicConfig(key = "tvAlbum.aqiyi.search.url", fileName = "search_config.properties")
    private String aqiyiSearchUrl;

    @HeraclesDynamicConfig(key = "tvAlbum.tencent.search.url", fileName = "search_config.properties")
    private String tencentSearchUrl;

    @HeraclesDynamicConfig(key = "browser.search.deepLink", fileName = "search_config.properties")
    private String browserDeepLink;

    @HeraclesDynamicConfig(key = "browser.url.deepLink", fileName = "search_config.properties")
    private String browserUrlLink;

    @HeraclesDynamicConfig(key = "third.search.switch", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, Integer> searchSwitchConfig;

    @HeraclesDynamicConfig(key = "third.search.range", fileName = "search_config.properties", textType = TextType.JSON)
    private List<String> contentTypeRange;

    @HeraclesDynamicConfig(key = "third.search.douban", fileName = "search_config.properties", textType = TextType.JSON)
    private List<String> doubanPriority;

    @HeraclesDynamicConfig(key = "iqiyi.search.url", fileName = "search_config.properties")
    private String iqiyiSearchUrl;

    @HeraclesDynamicConfig(key = "baidu.search.url", fileName = "search_config.properties")
    private String baiduSearchUrl;

    @HeraclesDynamicConfig(key = "search.feedback.option", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, String> feedbackOption;

    @HeraclesDynamicConfig(key = "album.detail.deepLink", fileName = "search_config.properties")
    private String detailDeepLink;

    // 聚合卡排序项（8.9版本之前）
    @HeraclesDynamicConfig(key = "search.tag.sortType.list", fileName = "search_config.properties", textType = TextType.JSON)
    private List<SearchInterveneCardResponse.SortType> sortTypeList;

    // 聚合卡排序项（8.9版本之后）
    @HeraclesDynamicConfig(key = "search.sort.list", fileName = "search_config.properties", textType = TextType.JSON)
    private List<SearchInterveneCardResponse.SortType> sortList;

    /**
     * 搜索热词逻辑开关
     * 0：关闭
     * 1：打开
     */
    @HeraclesDynamicConfig(key = "search.hot.keyword.switch", fileName = "search_config.properties")
    private Integer hotKeyWordSwitch = 0;

    @HeraclesDynamicConfig(key = "third.search.score", fileName = "search_config.properties")
    private Float thirdSearchScore = 0.7f;

    @HeraclesDynamicConfig(key = "yunhe.url", fileName = "search_config.properties")
    private String yunheUrl;

    @HeraclesDynamicConfig(key = "yunhe.name", fileName = "search_config.properties")
    private String yunheName;

    @HeraclesDynamicConfig(key = "yunhe.pwd", fileName = "search_config.properties")
    private String yunhePwd;

    @HeraclesDynamicConfig(key = "duanju.search.version", fileName = "search_config.properties")
    private Integer duanjuSearchVersion = 80700;

    @HeraclesDynamicConfig(key = "duanju.search.url", fileName = "search_config.properties")
    private String duanjuSearchUrl;

    @HeraclesDynamicConfig(key = "duanju.play.url", fileName = "search_config.properties")
    private String duanjuPlayUrl;

    @HeraclesDynamicConfig(key = "duanju.sourceId.blacklist", fileName = "search_config.properties")
    private List<String> duanjuSourceIdBlacklist;

    @HeraclesDynamicConfig(key = "douban.task.commit.path", fileName = "search_config.properties")
    private String doubanTaskCommitPath;

    @HeraclesDynamicConfig(key = "yunhe.douban.task.commit.num", fileName = "search_config.properties")
    private Integer yunheDoubanTaskCommitNum = 100;

    @HeraclesDynamicConfig(key = "search.filter.list", fileName = "search_config.properties")
    private List<String> searchFilterList = Lists.newArrayList("tv","movie","show","kids","comic","doc","music");

    @HeraclesDynamicConfig(key="relation.getstatus.url",fileName = "search_config.properties")
    private String relationGetListUrl ;

    @HeraclesDynamicConfig(key="user.middle.relation.secretKey",fileName = "search_config.properties")
    private String relationSecretKey;

    @HeraclesDynamicConfig(key="relation.timeout",fileName = "search_config.properties")
    private int relationTimeOut = 800;

    @HeraclesDynamicConfig(key = "search.card.button.num",fileName = "search_config.properties")
    private int searchCardButtonNum;

    @HeraclesDynamicConfig(key = "subscribe.timeLimit", fileName = "search_config.properties")
    private Long subscribeTimeLimit;

    @HeraclesDynamicConfig(key = "default.vertical.icon",fileName = "search_config.properties")
    private String defaultVerticalIcon;

    /**
     * 标签deepLink的前缀
     */
    @HeraclesDynamicConfig(key = "tag.deepLink.prefix",fileName = "search_config.properties")
    private String tagDeepLinkPrefix;

    @HeraclesDynamicConfig(key = "source.icon", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, String> sourceIconMap;

    @HeraclesDynamicConfig(key = "app.download.page", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, String> appDownloadPageMap;

    @HeraclesDynamicConfig(key = "app.upgrade.page", fileName = "search_config.properties")
    private String appUpgradePage;

    @HeraclesDynamicConfig(key = "browser.play.episode.startNum",fileName = "search_config.properties")
    private Integer browserPlayEpisodeStartNum;

    @HeraclesDynamicConfig(key = "browser.play.episode.endNum",fileName = "search_config.properties")
    private Integer browserPlayEpisodeEndNum;

    @HeraclesDynamicConfig(key = "browser.play.episode.descNum",fileName = "search_config.properties")
    private Integer browserPlayEpisodeDescNum;

    @HeraclesDynamicConfig(key = "filter.douBan.top.apiKey", fileName = "search_config.properties", textType = TextType.JSON)
    private List<String> filterApiKey;

    /**
     * 跳爱奇艺主端的dp地址
     */
    @HeraclesDynamicConfig(key = "outside.search.iqiyiMmobile.deepLink",fileName = "search_config.properties")
    private String iqiyiMmobileDeepLink;

    /**
     * 对外 小布全网搜出移动端爱奇艺的开关
     */
    @HeraclesDynamicConfig(key = "outside.breeno.search.iqiyiMmobile.enable", fileName = "search_config.properties")
    private boolean breenoSearchIqiyiMmobileSwitch = false;
}
