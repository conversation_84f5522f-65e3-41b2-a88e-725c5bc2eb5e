package com.heytap.longvideo.search.properties;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Set;


/*
 * Description 参数配置
 * Date 11:12 2021/12/2
 * Author song<PERSON>ajia 80350688
*/
@Component
@Data
public class HotVideoProperties {

    @HeraclesDynamicConfig(key = "movie.hot.title", fileName = "hot_video_config.properties", textType = TextType.JSON)
    private Set<String> movieHotTitle;

    @HeraclesDynamicConfig(key = "tv.hot.title", fileName = "hot_video_config.properties", textType = TextType.JSON)
    private Set<String> tvHotTitle;

    @HeraclesDynamicConfig(key = "kids.hot.title", fileName = "hot_video_config.properties", textType = TextType.JSON)
    private Set<String> kidsHotTitle;

    @HeraclesDynamicConfig(key = "show.hot.title", fileName = "hot_video_config.properties", textType = TextType.JSON)
    private Set<String> showHotTitle;

    @HeraclesDynamicConfig(key = "comic.hot.title", fileName = "hot_video_config.properties", textType = TextType.JSON)
    private Set<String> comicHotTitle;

    @HeraclesDynamicConfig(key = "hot.actor", fileName = "hot_video_config.properties", textType = TextType.JSON)
    private Set<String> hotActor;

}
