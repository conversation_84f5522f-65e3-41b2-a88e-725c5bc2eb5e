package com.heytap.longvideo.search.properties;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/*
 * Description mq参数配置
 * Date 11:12 2021/12/2
 * Author song<PERSON>ajia 80350688
 */
@Data
@Component
public class MqProperties {

    @HeraclesDynamicConfig(key = "rocketmq.name-server.mediaJins", fileName = "mq.properties")
    private String mediaJinsNameServer;

    @HeraclesDynamicConfig(key = "rocketmq.topic.mediaJins", fileName = "mq.properties")
    private String mediaJinsTopic;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.name-server", fileName = "mq.properties")
    private String thirdPartyMediaNameServer;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.topic", fileName = "mq.properties")
    private String thirdPartyMediaTopic;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.group", fileName = "mq.properties")
    private String thirdPartyMediaConsumerGroup;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.instance-name", fileName = "mq.properties")
    private String thirdPartyMediaInstanceName;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.maxReconsumeTimes", fileName = "mq.properties")
    private Integer thirdPartyMediaMaxReconsumeTimes;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.threadMax", fileName = "mq.properties")
    private Integer thirdPartyMediaThreadMax;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.threadMin", fileName = "mq.properties")
    private Integer thirdPartyMediaThreadMin;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.deadLetter.name-server", fileName = "mq.properties")
    private String thirdPartyMediaDeadLetterNameServer;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.deadLetter.topic", fileName = "mq.properties")
    private String thirdPartyMediaDeadLetterTopic;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.deadLetter.group", fileName = "mq.properties")
    private String thirdPartyMediaDeadLetterConsumerGroup;

    @HeraclesDynamicConfig(key = "rocketmq.third-party-media.deadLetter.instance-name", fileName = "mq.properties")
    private String thirdPartyMediaDeadLetterInstanceName;

    @HeraclesDynamicConfig(key = "kafka.yunheRank.servers", fileName = "mq.properties")
    private String yunheRankServers;

    @HeraclesDynamicConfig(key = "kafka.yunheRank.topic", fileName = "mq.properties")
    private String yunheRankTopic;

    @HeraclesDynamicConfig(key = "kafka.yunheRank.key", fileName = "mq.properties")
    private String yunheRankKey;

    @HeraclesDynamicConfig(key = "third.party.external.search.servers", fileName = "mq.properties")
    private String thirdPartyExternalSearchServers;

    @HeraclesDynamicConfig(key = "third.party.external.search.topic", fileName = "mq.properties")
    private String thirdPartyExternalSearchTopic;

    @HeraclesDynamicConfig(key = "third.party.external.search.client.id", fileName = "mq.properties")
    private String thirdPartyExternalSearchClientId;

    @HeraclesDynamicConfig(key = "third.party.external.search.key", fileName = "mq.properties")
    private String thirdPartyExternalSearchKey;

    @HeraclesDynamicConfig(key = "third.party.external.search.filter.list", fileName = "mq.properties",textType = TextType.JSON)
    private List<String> thirdPartyExternalSearchFilterList;

    @HeraclesDynamicConfig(key = "third.party.external.search.standard.filter.list", fileName = "mq.properties",textType = TextType.JSON)
    private List<String> thirdParyExternalSearchStandardFilterList;

    @HeraclesDynamicConfig(key = "rocketmq.iqiyi.media.sync.name-server", fileName = "mq.properties")
    private String iqiyiMediaNameServer;

    @HeraclesDynamicConfig(key = "rocketmq.iqiyi.media.sync.topic", fileName = "mq.properties")
    private String iqiyiMediaTopic;

    @HeraclesDynamicConfig(key = "rocketmq.iqiyi.media.sync.consumer.group", fileName = "mq.properties")
    private String iqiyiMediaConsumerGroup;

    @HeraclesDynamicConfig(key = "rocketmq.iqiyi.media.sync.consumer.instanceName", fileName = "mq.properties")
    private String iqiyiMediaInstanceName;
}
