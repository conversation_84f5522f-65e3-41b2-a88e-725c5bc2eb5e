package com.heytap.longvideo.search.liteflow.cmp;


import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.RecommendAlgorithmData;
import com.heytap.longvideo.search.model.RecommendAlgorithmResponse;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/*
 * Description 长视频兜底展示运营配置的内容
 * Date 11:11 2024/11/12
 * Author 80409577
 */
@Slf4j
@LiteflowComponent("defaultRecommendCmp")
public class DefaultRecommendCmp extends NodeComponent {

    @Autowired
    HttpDataChannel httpDataChannel;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private YoukuSourceFilterService youkuSourceFilterService;

    @Autowired
    DeepLinkUtils deepLinkUtils;

    @Autowired
    private CommonService commonService;

    @Autowired
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    @HeraclesDynamicConfig(key = "album.detail.deepLink", fileName = "search_config.properties")
    private String detailDeepLink;

    @HeraclesDynamicConfig(key = "default.recommend.switch", fileName = "search_config.properties")
    private Integer  defaultRecommendSwitch;

    // 内容池正片推荐
    public static final String BIDLST = "B1655276899008";
    // 卡片标题
    public static final String TITLE = "精选影视作品";

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        getDefaultRecommend(context.getRequestParam(), context.getSearchResponse());
    }


    public void getDefaultRecommend(KeyWordSearchParamV2 param, SearchResponse searchResponse) {
        // 综合tab是否展示取决于开关值
        if (("1".equals(param.getSearchType()) && defaultRecommendSwitch == 0)
                || param.getVersion() < 80100 || param.getPageIndex() != 1 || param.getDeviceType() != 0) {
            return;
        }
        // banner+结果卡+标签卡+推荐卡+系列卡+影人卡均为空时，才展示兜底推荐内容
        if (CollectionUtils.isNotEmpty(searchResponse.getLongVideoBannerList())
                || CollectionUtils.isNotEmpty(searchResponse.getLongVideoSearchResult())
                || Objects.nonNull(searchResponse.getLongVideoRecommend())
                || Objects.nonNull(searchResponse.getLongVideoSeries())
                || Objects.nonNull(searchResponse.getLongVideoTag())
                || Objects.nonNull(searchResponse.getLongVideoActor())) {
            return;
        }

        SearchInterveneCardResponse defaultRecommendResponse = queryFromAlgorithm(param);
        if (defaultRecommendResponse == null) {
            return;
        }

        // 处理搜狐VIP内容
        commonService.handleSohuVipContents(defaultRecommendResponse);
        searchResponse.setLongVideoDefaultRecommend(defaultRecommendResponse);
        log.info("getDefaultRecommend defaultRecommendResponse={}", JacksonUtil.toJSONString(defaultRecommendResponse));
    }

    /**
     * 查算法接口，获取运营配置的内容池
     * @param request
     * @return
     */
    public SearchInterveneCardResponse queryFromAlgorithm(KeyWordSearchParamV2 request) {
        Map<String, String> params = new HashMap<>();
        params.put("r_dv", request.getDv());
        params.put("route", searchProperties.getRecommendAlgorithmRoute());
        params.put("bidlst", BIDLST);
        params.put("cid", searchProperties.getRecommendAlgorithmCid());
        params.put("r_predict_id", request.getRequestId());
        params.put("num", String.valueOf(searchProperties.getDefaultRecommendAlgorithmNum()));
        if (StringUtils.isNotBlank(request.getBuuid())) {
            params.put("r_buuid", String.valueOf(request.getBuuid()));
        }
        params.put("r_page", String.valueOf(request.getPageIndex() - 1));
        params.put("docId", searchProperties.getDefaultRecommendAlgorithmDocid());
        try {
            RecommendAlgorithmResponse response = httpDataChannel.getForObject(
                    searchProperties.getRecommendAlgorithmUrl(), RecommendAlgorithmResponse.class,
                    params, searchProperties.getRecommendAlgorithmTimeout());
            if (response == null || response.getStatus() == null || response.getStatus() != 0 || CollectionUtils.isEmpty(response.getData())) {
                log.warn("queryFromAlgorithm no data, params={}, response={}", params, response);
                return null;
            }

            List<String> sidList = response.getData().stream()
                    .map(RecommendAlgorithmData::getId)
                    .distinct()
                    .collect(Collectors.toList());

            List<KeyWordSearchResponse> contents = buildResponseBySid(sidList, request);
            if (CollectionUtils.isEmpty(contents)) {
                return null;
            }
            SearchInterveneCardResponse defaultRecommendResponse = new SearchInterveneCardResponse();
            defaultRecommendResponse.setTitle(TITLE);
            defaultRecommendResponse.setContents(contents);
            defaultRecommendResponse.setHasMore(false);
            defaultRecommendResponse.setAiSource(1);
            // 用于上报时标识每次查询
            defaultRecommendResponse.setCode(request.getKeyword());

            // 算法场景 优酷内容拼接算法透传字段
            commonService.addAlgorithmTransparentToDeepLink(defaultRecommendResponse);
            return defaultRecommendResponse;
        } catch (Exception e) {
            log.error("getDefaultRecommendByAlgorithm error, params=" + params, e);
            return null;
        }
    }


    public List<KeyWordSearchResponse> buildResponseBySid(List<String> sidList, KeyWordSearchParamV2 request) {
        List<KeyWordSearchResponse> result = new ArrayList<>();
        Map<String, StandardAlbum> stringStandardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);
        for (Map.Entry<String, StandardAlbum> entry : stringStandardAlbumMap.entrySet()) {
            String sid = entry.getKey();
            StandardAlbum standardAlbum = entry.getValue();
            KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
            keyWordSearchResponse.setSid(sid);
            // 设置deepLink
            setDeepLink(standardAlbum, request, keyWordSearchResponse);

            if (StringUtils.isBlank(keyWordSearchResponse.getTitle())) {
                keyWordSearchResponse.setTitle(standardAlbum.getTitle());
            }
            if (StringUtils.isBlank(keyWordSearchResponse.getHorizontalIcon())) {
                keyWordSearchResponse.setHorizontalIcon(standardAlbum.getHorizontalIcon());
            }
            if (StringUtils.isBlank(keyWordSearchResponse.getVerticalIcon())) {
                keyWordSearchResponse.setVerticalIcon(standardAlbum.getVerticalImage());
            }
            if (StringUtils.isBlank(keyWordSearchResponse.getRecommendInfo())) {
                keyWordSearchResponse.setRecommendInfo(standardAlbum.getBrief());
            }
            if (StringUtils.isBlank(keyWordSearchResponse.getProgramInfo())) {
                keyWordSearchResponse.setProgramInfo(standardAlbum.getProgramInfo());
            }
            keyWordSearchResponse.setSourceScore(StringUtils.isBlank(standardAlbum.getSourceScore()) ? 0.0F : Float.parseFloat(standardAlbum.getSourceScore()));
            keyWordSearchResponse.setFeatureType(standardAlbum.getFeatureType());
            keyWordSearchResponse.setContentType(standardAlbum.getProgramType());
            keyWordSearchResponse.setSource(standardAlbum.getSource());
            keyWordSearchResponse.setCopyrightCode(standardAlbum.getSource());
            keyWordSearchResponse.setPayStatus(standardAlbum.getPayStatus());
            keyWordSearchResponse.setFeatureType(standardAlbum.getFeatureType());
            keyWordSearchResponse.setAlbumFeatureType(standardAlbum.getFeatureType());

            result.add(keyWordSearchResponse);
        }
        return result;
    }

    private void setDeepLink(StandardAlbum standardAlbum, KeyWordSearchParamV2 request, KeyWordSearchResponse keyWordSearchResponse) {
        if (youkuSourceFilterService.setDeepLinkFilter(standardAlbum.getSource(), request.getQuickEngineVersion(), request.getVersion())) {
            keyWordSearchResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), standardAlbum.getSourceWebUrl()));
        } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(standardAlbum.getSource())) {
            keyWordSearchResponse.setDeepLink(youkuSourceFilterService.getUpGradeUrl(standardAlbum.getSid(), standardAlbum.getSource(), standardAlbum.getTitle()));
        } else {
            keyWordSearchResponse.setDeepLink(String.format(detailDeepLink, standardAlbum.getSid()));
        }
    }
}
