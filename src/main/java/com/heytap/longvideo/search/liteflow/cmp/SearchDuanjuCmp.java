package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.search.constants.SearchTabEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.duanju.DuanjuSearchContent;
import com.heytap.longvideo.search.model.duanju.DuanjuSearchResponse;
import com.heytap.longvideo.search.model.duanju.DuanjuSearchVo;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@LiteflowComponent("searchDuanjuCmp")
public class SearchDuanjuCmp extends NodeComponent {
    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private HttpDataChannel httpDataChannel;

    // H5短剧(全网内容，本期需屏蔽)
    public static final Integer DUANJU_TYPE_H5 = 3;

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        context.setDuanjuSearchResult(searchDuanju(context.getRequestParam()));
    }

    public List<KeyWordSearchResponse> searchDuanju(KeyWordSearchParamV2 param) {
        // 8.7之前的版本，不查询
        if (param.getVersion() < searchProperties.getDuanjuSearchVersion()) {
            return Collections.emptyList();
        }

        // 其他tab跳综合tab时，不查询
        if (SearchTabEnum.SUMMARY.getCode().equals(param.getSearchType()) &&
                StringUtils.isNotBlank(param.getLastSearchTab()) &&
                !SearchTabEnum.SUMMARY.getCode().equals(param.getLastSearchTab())) {
            return Collections.emptyList();
        }

        // 仅综合tab与短剧tab时查询
        if (!SearchTabEnum.SUMMARY.getCode().equals(param.getSearchType())
                && !SearchTabEnum.DUANJU.getCode().equals(param.getSearchType())) {
            return Collections.emptyList();
        }

        // 异形屏，不查询
        if (param.getDeviceType() != 0 && param.getDeviceType() != 5) {
            return  Collections.emptyList();
        }

        Map<String, String> params = new HashMap<>();
        params.put("keyword", param.getKeyword());
        params.put("pageIndex", String.valueOf(param.getPageIndex()));
        try {
            DuanjuSearchResponse response = httpDataChannel.getForObject(
                    searchProperties.getDuanjuSearchUrl(), DuanjuSearchResponse.class, params, 1000);
            if (response == null || response.getRet() != 0 || response.getResult() == null || CollectionUtils.isEmpty(response.getResult().getElements())) {
                log.info("searchDuanju no data, params={}, response={}", params, response);
                return Collections.emptyList();
            }
            param.setDuanjuHasMore(response.getResult().getHasMore());
            return Optional.ofNullable(response.getResult().getElements().get(0).getContents())
                    .map(contents -> contents.stream()
                            .map(DuanjuSearchContent::getDuanjuVo)
                            .filter(vo -> !DUANJU_TYPE_H5.equals(vo.getDuanjuType()))
                            .filter(vo -> !searchProperties.getDuanjuSourceIdBlacklist().contains(vo.getSource() + "_" + vo.getDuanjuId()))
                            .map(this::convert)
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public KeyWordSearchResponse convert(DuanjuSearchVo duanjuSearchVo) {
        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        // 短剧sid，仅用于埋点上报
        searchResponse.setSid(duanjuSearchVo.getDuanjuId());
        // 短剧cid，仅用于埋点上报
        searchResponse.setVirtualSid(duanjuSearchVo.getId().toString());
        searchResponse.setTitle(duanjuSearchVo.getTitle());
        searchResponse.setSource(duanjuSearchVo.getSource());
        searchResponse.setHorizontalIcon(duanjuSearchVo.getCoverImageUrl());
        searchResponse.setVerticalIcon(duanjuSearchVo.getCoverImageUrl());
        searchResponse.setDeepLink(String.format(searchProperties.getDuanjuPlayUrl(), duanjuSearchVo.getSource(), duanjuSearchVo.getDuanjuId()));

        return searchResponse;
    }
}

