package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/*
 * Description 获取搜索人工干预意图
 * Date 14:26 2023/10/9
 * Author songjiajia 80350688
 */
@Slf4j
@LiteflowComponent("getSearchIntentCmp")
public class GetSearchIntentCmp extends NodeComponent {

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 0, timeout = 1000)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @HeraclesDynamicConfig(key = "recommend.minVersion", fileName = "search_config.properties")
    private Integer recommendMinVersion = 61100;

    @HeraclesDynamicConfig(key = "series.minVersion", fileName = "search_config.properties")
    private Integer seriesMinVersion = 70200;

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (context.getRequestParam().getPageIndex() != 1) {
            context.setInterveneConfigMap(new HashMap<>());
            return;
        }
        Map<InterveneTypeEnum, LvSearchIntervene> map = FutureUtil.getFutureIgnoreException(getSearchIntervene(context.getRequestParam()));
        context.setInterveneConfigMap(map);
    }


    public CompletableFuture<Map<InterveneTypeEnum, LvSearchIntervene>> getSearchIntervene(KeyWordSearchParamV2 request) {
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getSearchInterveneList(request.getKeyword()).handleAsync((rpcResult, e) -> {
            if (e != null || rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getSearchIntervene error, the request:{}, the response:{}", request.getKeyword(), rpcResult, e);
                return Collections.emptyMap();
            }
            List<LvSearchIntervene> interveneList = rpcResult.getData();
            if (CollectionUtils.isEmpty(interveneList)) {
                return Collections.emptyMap();
            }
            return fetchInterveneMap(interveneList, request);
        });

    }


    private Map<InterveneTypeEnum, LvSearchIntervene> fetchInterveneMap(
            List<LvSearchIntervene> interveneList, KeyWordSearchParamV2 request) {
        // 同类型干预项，保留ID更小的
        EnumMap<InterveneTypeEnum, LvSearchIntervene> map = interveneList.stream()
                .map(intervene -> new AbstractMap.SimpleEntry<>(
                        InterveneTypeEnum.getByCode(intervene.getInterveneType()),
                        intervene
                ))
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, current) -> existing.getId() < current.getId() ? existing : current,
                        () -> new EnumMap<>(InterveneTypeEnum.class)
                ));

        if (request.getVersion() < recommendMinVersion || request.getPageIndex() != 1 ||
                (request.getDeviceType() != 0 && request.getDeviceType() != 5) || !"1".equals(request.getSearchType())) {
            map.remove(InterveneTypeEnum.RECOMMEND);
        }
        if (request.getVersion() < seriesMinVersion || request.getPageIndex() != 1 ||
                (request.getDeviceType() != 0 && request.getDeviceType() != 5) || !"1".equals(request.getSearchType())) {
            map.remove(InterveneTypeEnum.SERIES);
            map.remove(InterveneTypeEnum.TAG);
        }
        // 干预优先级：标签卡>系列卡>相关推荐卡
        if (map.containsKey(InterveneTypeEnum.TAG)) {
            map.remove(InterveneTypeEnum.SERIES);
            map.remove(InterveneTypeEnum.RECOMMEND);
        } else if (map.containsKey(InterveneTypeEnum.SERIES)) {
            map.remove(InterveneTypeEnum.RECOMMEND);
        }
        return map;
    }
}