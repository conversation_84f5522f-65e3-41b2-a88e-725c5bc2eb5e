package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.client.media.entity.StandardEpisodeBO;
import com.heytap.longvideo.client.media.enums.NewResultCardTypeEnum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.common.media.programsource.CommonSourceFilterConfig;
import com.heytap.longvideo.common.media.programsource.SourceVersionService;
import com.heytap.longvideo.search.config.ConfigurationShowBrandConfig;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.constants.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.EpisodeVO;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.StandardEpisodeRpcApiProxy;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.service.thirdparty.ThirdPartyCommonService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.*;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.*;

/*
 * Description 结果聚合及排序-端外
 * Date 10:11 2024/9/20
 * Author chuanxw
 */
@Slf4j
@LiteflowComponent("outSideResultAggregationCmp")
public class OutSideResultAggregationCmp extends NodeComponent {

    private final SearchProperties searchProperties;

    private final ConfigurationShowBrandConfig configurationConfig;

    private final ResultAggregationCmp resultAggregationCmp;

    private final ThirdPartyCommonService thirdPartyCommonService;

    private final YoukuSourceFilterService youkuSourceFilterService;

    private final SourceFilterConfig sourceFilterConfig;

    private final CommonSourceFilterConfig commonSourceFilterConfig;

    private final OutSidePostHandleCmp outSidePostHandleCmp;

    private final StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy;

    private final VipRelatedService vipRelatedService;

    private final TaskUnlockEpisodeService taskUnlockEpisodeService;

    private final DeepLinkUtils deepLinkUtils;

    private final SourceVersionService sourceVersionService;

    private final static Map<String, String> CONTENT_TYPE_NAME = new HashMap<>();

    static {
        Map<String,String> map= StandardConstant.ALBUM_TYPE_MAP;
        for (String key : map.keySet()) {
            CONTENT_TYPE_NAME.put(map.get(key),key);
        }
    }

    public OutSideResultAggregationCmp(ConfigurationShowBrandConfig configurationConfig,
                                       ResultAggregationCmp resultAggregationCmp,
                                       ThirdPartyCommonService thirdPartyCommonService,
                                       SearchProperties searchProperties,
                                       YoukuSourceFilterService youkuSourceFilterService,
                                       SourceFilterConfig sourceFilterConfig,
                                       CommonSourceFilterConfig commonSourceFilterConfig,
                                       OutSidePostHandleCmp outSidePostHandleCmp,
                                       StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy,
                                       VipRelatedService vipRelatedService,
                                       TaskUnlockEpisodeService taskUnlockEpisodeService,
                                       DeepLinkUtils deepLinkUtils,
                                       SourceVersionService sourceVersionService) {
        this.configurationConfig = configurationConfig;
        this.resultAggregationCmp = resultAggregationCmp;
        this.thirdPartyCommonService = thirdPartyCommonService;
        this.searchProperties = searchProperties;
        this.youkuSourceFilterService = youkuSourceFilterService;
        this.sourceFilterConfig = sourceFilterConfig;
        this.commonSourceFilterConfig = commonSourceFilterConfig;
        this.outSidePostHandleCmp = outSidePostHandleCmp;
        this.standardEpisodeRpcApiProxy = standardEpisodeRpcApiProxy;
        this.vipRelatedService = vipRelatedService;
        this.taskUnlockEpisodeService = taskUnlockEpisodeService;
        this.deepLinkUtils = deepLinkUtils;
        this.sourceVersionService = sourceVersionService;
    }

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        resultAggregation(context);
    }

    /**
     * 搜索干预结果与返回数据处理
     * @param context 上下文
     */
    public void resultAggregation(SearchByKeyWordContext context){
        KeyWordSearchParamV2 param = context.getRequestParam();
        SearchResponse response = context.getSearchResponse();
        List<KeyWordSearchResponse> searchResult = context.getBaseSearchResult();
        response.setLongVideoSearchResult(searchResult);

        if (CollectionUtils.isNotEmpty(searchResult)) {
            response.setHasMore(param.getHasMore());
        }

        if (param.getPageIndex() >= 10) {
            response.setHasMore(0);
        }

        //将端内干预结果、全网搜es结果、全网搜干预插入ES匹配结果中
        resultAggregationCmp.insertInterveneAndAllWebContent(context);

        //数据截取（前三页会一次取30条数据，重新排序截取）
        if (param.getPageIndex() < 4) {
            int start = (param.getPageIndex() - 1) * param.getPageSize();
            if (start > searchResult.size()) {
                searchResult = new ArrayList<>();
            }else {
                int end = param.getPageIndex() * param.getPageSize();
                end = Math.min(searchResult.size(), end);
                searchResult = searchResult.subList(start, end);
            }
        }

        for (int i = 0; i < searchResult.size(); i++) {
            KeyWordSearchResponse keyWordSearchResponse = searchResult.get(i);
            if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(param.getAppId())) {
                // 处理剧集(只有首位节目且非优酷合作节目才下发)
                if (i == 0 && keyWordSearchResponse.getSourceKind() == 1
                        && !CopyrightConstant.COPYRIGHT_YOUKU_MOBILE.equals(keyWordSearchResponse.getSource())) {
                    handleEpisodes(keyWordSearchResponse);
                }
                // 处理播放源
                handlePlaySource(keyWordSearchResponse, param);
                // 处理竖图（素材库>媒资库）
                outSidePostHandleCmp.handleVerticalIcon(keyWordSearchResponse);
                // 处理标签
                outSidePostHandleCmp.handleTags(keyWordSearchResponse);
                // 处理播放链接
                handleDeepLink4BrowserPlay(keyWordSearchResponse, param);
                // 处理免费解锁角标
                handleUnlockMarkCode(response, param, context);
                // 处理演员和导演
                handleDirectorsAndStars(keyWordSearchResponse);
            } else {
                // 处理dp
                handleDeepLink(keyWordSearchResponse, param, context.getDpDetailPageStyle());
            }
        }

        response.setLongVideoSearchResult(searchResult);
        response.setPageSize(param.getPageSize());
        response.setPageIndex(param.getPageIndex());
    }

    private void handleDeepLink(KeyWordSearchResponse item, KeyWordSearchParamV2 param, int dpDetailPageStyle) {
        try {
            // 全网搜节目的dp，在SearchAllWebCmp中已经处理--全搜和浏览器
            if (2 == item.getButtonStatus() && (SEARCH_FROM_QUANSOU.equals(param.getAppId()) || SEARCH_FROM_BROWSER.equals(param.getAppId()))) {
                return;
            }

            // 端内节目的处理
            String appId = param.getAppId();
            String openFrom = appId;
            String contentType = item.getContentType();
            item.setContentTypeName(MapUtils.isNotEmpty(CONTENT_TYPE_NAME) && CONTENT_TYPE_NAME.containsKey(contentType) ? CONTENT_TYPE_NAME.get(contentType) : null);
            if (Objects.equals(appId, SearchConstant.SEARCH_FROM_MAGAZINE)) {
                openFrom = "screenoff_searchbanner";
            } else if (Objects.equals(appId, SearchConstant.SEARCH_FROM_BREENO)) {
                openFrom = "breeno_searchresults";
            }

            setDeepLinkBySource(item, openFrom);

            // 合作内容的dp需下发showBrand
            String showBrandParam = configurationConfig.getShowBrandConfigByAppId(openFrom);
            if (StringUtils.isNotEmpty(showBrandParam)) {
                item.setDeepLink(item.getDeepLink() + showBrandParam);
            }
            String dpSuffix = thirdPartyCommonService.getThirdPartyMediaParams(ThirdPartyMediaTypeEnum.getMediaTypeByAppId(param.getAppId()),
                    dpDetailPageStyle, param.getVersion(), item.getSource(), StrategyUtil.isTransparentKkua(param.getAttributeValues()));
            if (StringUtils.isNotBlank(dpSuffix)) {
                item.setDeepLink(item.getDeepLink() + dpSuffix);
            }

            // 对于非合作内容的非入库详情页dp不下发showBrand
            if (MapUtils.isNotEmpty(searchProperties.getThirdSearchMap()) && searchProperties.getThirdSearchMap().containsKey(item.getSource())) {
                handleThirdSearchDeepLink(item, openFrom, param);
            } else {
                // 合作内容需要下发外显来源
                handleTargetAppName(item, param);
            }

            // 锁屏和小布的搜索请求需要下发无感调端参数_SWL_=1 & 免解锁参数
            if (SearchConstant.EXACT_MATCH_SEARCH_SOURCE.contains(param.getAppId())) {
                modifyOutDeepLink(item);
            }
            setIqiyiMobileDeepLinkForBreeno(appId,item);
            handleUpgradePageDp(param, item);

        } catch (Exception e) {
            log.error("searchAlbumCmp err, sid:{}", item.getSid(), e);
        }
    }

    private void setIqiyiMobileDeepLinkForBreeno(String appId, KeyWordSearchResponse item) {
        if (SEARCH_FROM_BREENO.equals(appId) && SourceEnum.IQIYI_MOBILE.getDataSource().equals(item.getSource()) && StringUtils.isNotBlank(item.getSourceAlbumId())) {
            // 小布的移动端爱奇艺媒资跳转爱奇艺app
            if (ContentTypeEnum.MOVIE.getCode().equals(item.getContentType())) {
                item.setDeepLink(String.format(searchProperties.getIqiyiMmobileDeepLink(), "", item.getSourceAlbumId()));
            } else {
                item.setDeepLink(String.format(searchProperties.getIqiyiMmobileDeepLink(), item.getSourceAlbumId(), ""));
            }
        }
    }

    private void handleUpgradePageDp(KeyWordSearchParamV2 param, KeyWordSearchResponse item) {
        // 如果是由于快应用版本不满足导致下发的升级页，拼接normalDp参数，h5页面中快应用升级后根据此值自动跳转至视频app
        String newDeepLink = thirdPartyCommonService.handleUpgradePageDp(item.getDeepLink(), item.getDpLinkType(),
                param.getQuickEngineVersion(), item.getSource(), item.getSid(), item.getSourceWebUrl());
        item.setDeepLink(newDeepLink);
    }

    private void setDeepLinkBySource(KeyWordSearchResponse item, String openFrom) {
        if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(item.getSource())) {
            handleYoukuMobileSuffix(item, openFrom);
        } else if (StringUtil.isNotBlank(item.getWebUrl())) {
            item.setDeepLink("yoli://yoli.com/yoli/h5?showSplashAd=0&openMainActivity=1&openFrom=" + openFrom + "&url=" + item.getWebUrl());
        } else if (MapUtils.isEmpty(searchProperties.getThirdSearchMap()) || !searchProperties.getThirdSearchMap().containsKey(item.getSource())) {
            item.setDeepLink(String.format(searchProperties.getDetailDeepLink(), item.getSid()) + "&showSplashAd=0&openMainActivity=1&datawarning=1&openFrom=" + openFrom);
        }
    }

    private void handleYoukuMobileSuffix(KeyWordSearchResponse item, String openFrom) {
        if (StringUtil.isNotBlank(item.getWebUrl())) {
            item.setDeepLink(item.getDeepLink() + "&showSplashAd=0&openMainActivity=1&openFrom=" + openFrom + "&url=" + item.getWebUrl());
        } else {
            item.setDeepLink(item.getDeepLink() + "&showSplashAd=0&openMainActivity=1&datawarning=1&openFrom=" + openFrom);
        }
    }

    private void handleTargetAppName(KeyWordSearchResponse item, KeyWordSearchParamV2 param) {
        if (Objects.equals(SearchConstant.SEARCH_FROM_BREENO, param.getAppId())
                && Objects.nonNull(param.getAttributeValues())) {
            String channel = param.getAttributeValues().getChannel();
            if (StringUtils.isNotEmpty(channel)) {
                item.setTargetAppName(CommonConstant.targetAppNameMap.getOrDefault(channel.toLowerCase(), null));
            }
        }
    }

    private void handleThirdSearchDeepLink(KeyWordSearchResponse item, String openFrom, KeyWordSearchParamV2 param) {
        if (CommonUtils.compareVersion(param.getAppVersion(), "8.7.0") >= 0) {
            item.setDeepLink(String.format("yoli://yoli.com/detail/longvideo/outofstockvideodetail?linkValue=%s",
                    item.getSid()) + "&showSplashAd=0&openMainActivity=1&datawarning=1&openFrom=" + openFrom + "&title=" + item.getTitle());
        } else {
            item.setDeepLink(String.format("yoli://yoli.com/YoliSearch/search?query=%s",
                    param.getKeyword()) + "&showSplashAd=0&openMainActivity=1&datawarning=1&openFrom=" + openFrom);
        }
    }

    /**
     * 给锁屏用的无感调端只需要加swl参数即可，锁屏侧进行换链，其它调用方需要我们进行换链
     * @param keyWordSearchResponse
     */
    private void modifyOutDeepLink(KeyWordSearchResponse keyWordSearchResponse) {
        String deepLink = keyWordSearchResponse.getDeepLink();
        if (CopyrightConstant.COPYRIGHT_YOUKU_MOBILE.equals(keyWordSearchResponse.getSource())) {
            deepLink =  deepLink +  UrlCoderUtil.encode("&_SWL_=1", StandardCharsets.UTF_8);
        }

        if (StringUtils.isBlank(deepLink)) {
            return;
        }

        keyWordSearchResponse.setDeepLink(deepLink + "&showWhenLocked=1&swl=1");
    }

    private void handleDeepLink4BrowserPlay(KeyWordSearchResponse item, KeyWordSearchParamV2 param) {
        // 下发下载页
        setDownloadUrl(item, param.getAttributeValues().getRom());

        // 对不满足版本要求的节目，下发升级页链接
        if (setUpgradeUrl(item, param)) {
            return;
        }

        // 合作节目，需添加无感调端等参数
        if (item.getSourceKind() == 1) {
            String deepLink = item.getDeepLink();
            // 7.15.1以上版本下发无感调端链接
            if (param.getVersion() >= 71501) {
                String sourceWebUrl = extractLinkValue(deepLink);
                deepLink = deepLinkUtils.getDirectPageDeepLink(item.getSource(), item.getSid(), sourceWebUrl);
            }
            deepLink = deepLink + "&showBrand=1&showSplashAd=0&openMainActivity=1&datawarning=1&openFrom=" + param.getAppId();
            setAlbumAndEpisodesDeepLink(item, deepLink, false);
        }
    }

    private String extractLinkValue(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        // 假设URL格式固定：前缀是 "linkValue="，后面跟着目标值
        int startIndex = url.indexOf("linkValue=");
        if (startIndex == -1) {
            return null; // 没有找到参数
        }
        startIndex += "linkValue=".length(); // 跳转到值的起始位置
        int endIndex = url.indexOf('&', startIndex); // 查找下一个参数分隔符
        if (endIndex == -1) {
            endIndex = url.length(); // 如果没有其他参数，截取到字符串末尾
        }
        return url.substring(startIndex, endIndex);
    }

    private boolean setUpgradeUrl(KeyWordSearchResponse item, KeyWordSearchParamV2 param) {
        // 只有合作源节目需要下发升级页
        if (item.getSourceKind() != 1) {
            return false;
        }

        String upgradeUrl = searchProperties.getAppUpgradePage();

        // 若版本低于渠道最低要求
        if (!checkSourceVersion(item.getSource(), param.getVersion())) {
            if (CopyrightConstant.COPYRIGHT_YOUKU_MOBILE.equals(item.getSource())) {
                upgradeUrl = youkuSourceFilterService.getUpGradeUrl(item.getSid(), item.getSource(), item.getTitle());
            }
            setAlbumAndEpisodesDeepLink(item, upgradeUrl, true);
            return true;
        }
        // 若快应用版本低于优酷节目最低要求
        else if (CopyrightConstant.COPYRIGHT_YOUKU_MOBILE.equals(item.getSource())
                && (param.getQuickEngineVersion() == null ||  sourceFilterConfig.getMinWebQuickVersion() > param.getQuickEngineVersion())) {
            upgradeUrl = youkuSourceFilterService.getUpGradeUrl(item.getSid(), item.getSource(), item.getTitle());
            setAlbumAndEpisodesDeepLink(item, upgradeUrl, true);
            return true;
        }

        return false;
    }

    private boolean checkSourceVersion(String source, Integer version) {
        Integer minVersion = commonSourceFilterConfig.getSourceAndVersionMapping().getOrDefault(source, Integer.MIN_VALUE);
        return version.compareTo(minVersion) >= 0;
    }

    private void setDownloadUrl(KeyWordSearchResponse item, String rom) {
        // 默认40包
        String version = "40";

        try {
            if (StringUtils.isNotEmpty(rom)) {
                String lowerRom = rom.toLowerCase();
                // 提取数字部分
                String numberStr = lowerRom.substring("android".length());
                int versionNumber = Integer.parseInt(numberStr);
                version = versionNumber > 10 ? "40" : "20";
            }
        } catch (Exception e) {
            log.error("setDownloadUrl err, rom:{}", rom, e);
        }

        String downloadUrl = searchProperties.getAppDownloadPageMap().get(version);
        item.setDownloadUrl(downloadUrl);
    }

    private void setAlbumAndEpisodesDeepLink(KeyWordSearchResponse item, String url, boolean upgrade) {
        item.setDeepLink(url);
        if (CollectionUtils.isNotEmpty(item.getEpisodes())) {
            item.getEpisodes().forEach(episodeVO -> {
                // APP需要更新或显示更多的剧集，剧头链接与剧集链接保持一致
                if (upgrade || NewResultCardTypeEnum.MORE.getValue() == episodeVO.getType()) {
                    episodeVO.setDeepLink(url);
                } else {
                    episodeVO.setDeepLink(url + "&videoEid=" + episodeVO.getEid() + "&playSource=" + episodeVO.getSource());
                }
            });
        }
    }

    private void handlePlaySource(KeyWordSearchResponse response, KeyWordSearchParamV2 param) {
        if (response.getSourceKind() == 1) {
            if ("OPPO".equalsIgnoreCase(param.getAttributeValues().getChannel())) {
                response.setPlaySourceName("OPPO视频");
            } else {
                response.setPlaySourceName("视频");
            }
            response.setPlaySourceIcon(searchProperties.getSourceIconMap().get("oppo"));
        } else if (response.getSourceKind() == 2) {
            CopyrightEnum sourceEnum = CopyrightEnum.getBySource(response.getCopyrightCode());
            if (sourceEnum != null) {
                response.setPlaySourceName(sourceEnum.getDesc());
                response.setPlaySourceIcon(searchProperties.getSourceIconMap().get(sourceEnum.getSource()));
            }
        }
    }

    private void handleEpisodes(KeyWordSearchResponse response) {
        boolean isShow = ContentTypeEnum.SHOW.getCode().equals(response.getContentType());
        List<StandardEpisodeBO> standardEpisodeBOList = standardEpisodeRpcApiProxy.getStartEndEpisodeBySid(
                response.getSid(), searchProperties.getBrowserPlayEpisodeStartNum(), searchProperties.getBrowserPlayEpisodeEndNum(),
                searchProperties.getBrowserPlayEpisodeDescNum(), isShow);

        //拼接episodeTitle
        standardEpisodeBOList.parallelStream().forEach(standardEpisodeBO -> {
            if (response.getUnit() != null && NewResultCardTypeEnum.MORE.getValue() != standardEpisodeBO.getType()) {
                standardEpisodeBO.setEpisodeTitle(CommonUtils.getEpisodeTitle(standardEpisodeBO.getEpisode(), response.getUnit(), response.getContentType()));
            }
        });

        List<EpisodeVO> episodeVOList = standardEpisodeBOList.stream()
                .map(standardEpisodeBO -> {
                    EpisodeVO episodeVO = new EpisodeVO();
                    BeanUtils.copyProperties(standardEpisodeBO, episodeVO);
                    return episodeVO;
                })
                .collect(Collectors.toList());
        response.setEpisodes(episodeVOList);
    }

    private void handleUnlockMarkCode(SearchResponse response, KeyWordSearchParamV2 param, SearchByKeyWordContext context) {
        String vipType = vipRelatedService.getVipType(context.getVipInfo());
        taskUnlockEpisodeService.handleMarkCode4OutSearch(param, context.getUserId(), vipType,
                context.getStrategyMatchResult(), response);
    }

    private void handleDirectorsAndStars(KeyWordSearchResponse item) {
        if (StringUtils.isNotBlank(item.getDirectors())) {
            item.setDirectors(item.getDirectors().replace("|", ","));
        }
        if (StringUtils.isNotBlank(item.getStars())) {
            item.setStars(item.getStars().replace("|", ","));
        }
    }
}