package com.heytap.longvideo.search.liteflow.cmp;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.SearchConstant;
import com.heytap.longvideo.search.constants.UserVipConstants;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.BaseRequest;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.rpc.consumer.LvContentMaterialRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.HttpsConvertUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @Description: 端外搜索 - 后处理
 * @Author: 80398885WT
 * @Date: 2025/5/30
 */
@Slf4j
@LiteflowComponent(name = "outSidePostHandleCmp")
public class OutSidePostHandleCmp extends NodeComponent {

    @HeraclesDynamicConfig(key = "third.search", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, Map<String, Integer>> thirdSearchSourceMap;

    private final LvContentMaterialRpcApiProxy lvContentMaterialRpcApiProxy;

    private final StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    private final VipRelatedService vipRelatedService;

    private final TaskUnlockEpisodeService taskUnlockEpisodeService;

    public OutSidePostHandleCmp(LvContentMaterialRpcApiProxy lvContentMaterialRpcApiProxy,
                                StandardAlbumRpcApiProxy standardAlbumRpcApiProxy,
                                VipRelatedService vipRelatedService,
                                TaskUnlockEpisodeService taskUnlockEpisodeService) {
        this.lvContentMaterialRpcApiProxy = lvContentMaterialRpcApiProxy;
        this.standardAlbumRpcApiProxy = standardAlbumRpcApiProxy;
        this.vipRelatedService = vipRelatedService;
        this.taskUnlockEpisodeService = taskUnlockEpisodeService;
    }

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (Objects.isNull(context)) {
            throw new RuntimeException("context is null");
        }

        KeyWordSearchParamV2 paramV2 = context.getRequestParam();
        if (Objects.isNull(paramV2)) {
            throw new RuntimeException("param is null");
        }

        SearchResponse response = context.getSearchResponse();
        List<KeyWordSearchResponse> searchResult = response.getLongVideoSearchResult();

        if (CollectionUtils.isEmpty(searchResult)) {
            log.info("baseSearchResult is empty");
            return;
        }

        for (KeyWordSearchResponse keyWordSearchResponse : searchResult) {
            String sid = keyWordSearchResponse.getSid();
            if (StringUtils.isEmpty(sid)) {
                continue;
            }

            try {
                // 竖图处理
                handleVerticalIcon(keyWordSearchResponse);

                // http ——> https 转换
                String originUrl = keyWordSearchResponse.getVerticalIcon();
                String newUrl = HttpsConvertUtil.convertToHttps(originUrl, true);
                keyWordSearchResponse.setVerticalIcon(StringUtils.isEmpty(newUrl) ? originUrl : newUrl);

                // 评分处理
                if (Objects.nonNull(keyWordSearchResponse.getSourceScore())) {
                    if (keyWordSearchResponse.getSourceScore() >= 6) {
                        keyWordSearchResponse.setSourceScore(Float.parseFloat(String.format("%.1f", keyWordSearchResponse.getSourceScore())));
                    } else {
                        keyWordSearchResponse.setSourceScore(null);
                    }
                }

                // 标签处理
                handleTags(keyWordSearchResponse);

                // 按钮文案处理
                handleButtonText(keyWordSearchResponse, context);
            } catch (Exception e) {
                log.error("OutSidePostHandleCmp error, sid:{}", keyWordSearchResponse.getSid(), e);
            }
        }
    }

    public void handleVerticalIcon(KeyWordSearchResponse keyWordSearchResponse) {
        String sid = keyWordSearchResponse.getSid();
        Map<String, List<LvContentMaterialVO>> materialMap = lvContentMaterialRpcApiProxy.getMaterials(Lists.newArrayList(sid));
        if (MapUtils.isNotEmpty(materialMap)) {
            List<LvContentMaterialVO> materialVOList = materialMap.get(sid);
            for (LvContentMaterialVO materialVO : materialVOList) {
                if (Objects.equals(materialVO.getImgStyle(), 2)
                        && Objects.equals(materialVO.getMaterialType(), 0)
                        && !Objects.equals(materialVO.getImgType(), "gif")) {
                    keyWordSearchResponse.setVerticalIcon(materialVO.getImgUrl());
                    keyWordSearchResponse.setBrief(materialVO.getBrief());
                    break;
                }
            }
        } else {
            StandardAlbum standardAlbum = standardAlbumRpcApiProxy.getBySid(sid);
            if (Objects.nonNull(standardAlbum)) {
                keyWordSearchResponse.setVerticalIcon(standardAlbum.getVerticalIcon());
                keyWordSearchResponse.setBrief(standardAlbum.getBrief());
            }
        }
    }

    public void handleTags(KeyWordSearchResponse keyWordSearchResponse) {
        if (StringUtils.isEmpty(keyWordSearchResponse.getTags())) {
            return;
        }

        Set<String> tags = Sets.newHashSet(StringUtils.split(keyWordSearchResponse.getTags().trim(), "|"));
        tags.addAll(Sets.newHashSet(StringUtils.split(keyWordSearchResponse.getTags().trim(), "，")));
        tags.addAll(Sets.newHashSet(StringUtils.split(keyWordSearchResponse.getTags().trim(), ",")));

        Iterator<String> iterator = tags.iterator();
        while (iterator.hasNext()) {
            String tag = iterator.next();
            if (StringUtils.isEmpty(tag)) {
                iterator.remove();
            }

            if (tag.contains("|") || tag.contains(",") || tag.contains("，")) {
                iterator.remove();
            }
        }

        if (CollectionUtils.isNotEmpty(tags)) {
            tags.removeAll(SearchConstant.FILTER_TAGS);
            if (CollectionUtils.isNotEmpty(tags)) {
                keyWordSearchResponse.setTags(tags.size() > 2 ? String.join("|", Lists.newArrayList(tags).subList(0, 3))
                        : String.join("|", tags));
            }
        }
    }

    private void handleButtonText(KeyWordSearchResponse keyWordSearchResponse, SearchByKeyWordContext context) {
        if (MapUtils.isNotEmpty(thirdSearchSourceMap) && thirdSearchSourceMap.containsKey(keyWordSearchResponse.getSource())) {
            keyWordSearchResponse.setButtonText(SearchConstant.THIRD_SEARCH_TEXT);
            return;
        }

        keyWordSearchResponse.setButtonText(SearchConstant.NOT_VIP_BUTTON_TEXT);
        String vipType = vipRelatedService.getVipType(context.getVipInfo());
        if (Objects.equals(vipType, UserVipConstants.VIDEO_VIP)
                && UserVipConstants.VIDEO_VIP_SOURCE_LIST.contains(keyWordSearchResponse.getSource())) {
            keyWordSearchResponse.setButtonText(SearchConstant.VIP_BUTTON_TEXT);
        } else if (Objects.equals(vipType, UserVipConstants.MONGO_VIDEO_VIP)
                && Objects.equals(keyWordSearchResponse.getSource(), SourceEnum.MG_MOBILE.getSpiSource())) {
            keyWordSearchResponse.setButtonText(SearchConstant.VIP_BUTTON_TEXT);
        } else if (Objects.equals(vipType, UserVipConstants.MONGO_AND_VIDEO_VIP)
                && UserVipConstants.VIP_ALL_SOURCE_LIST.contains(keyWordSearchResponse.getSource())) {
            keyWordSearchResponse.setButtonText(SearchConstant.VIP_BUTTON_TEXT);
        } else {
            // 非会员
            // 支持免费解锁的内容也下发“免费看”
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setAppVersion(context.getRequestParam().getAppVersion());
            baseRequest.setAttributeValues(context.getRequestParam().getAttributeValues());
            List<String> unlockSidList = taskUnlockEpisodeService.getUnlockAlbumList(baseRequest, vipType, context.getUserId(),
                    context.getStrategyMatchResult(), Lists.newArrayList(keyWordSearchResponse));
            if (CollectionUtils.isNotEmpty(unlockSidList)) {
                keyWordSearchResponse.setButtonText(SearchConstant.VIP_BUTTON_TEXT);
            }
        }
    }
}