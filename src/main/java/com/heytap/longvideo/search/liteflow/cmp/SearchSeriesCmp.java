package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.LvMediaSeriesItemRpcApi;
import com.heytap.longvideo.client.media.LvMediaSeriesRpcApi;
import com.heytap.longvideo.client.media.entity.oppomedia.LvMediaSeries;
import com.heytap.longvideo.client.media.entity.oppomedia.LvMediaSeriesItem;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/*
 * Description 搜索系列剧
 * Date 10:11 2023/7/21
 * Author songjiajia 80350688
 */
@Slf4j
@LiteflowComponent("searchSeriesCmp")
public class SearchSeriesCmp extends NodeComponent {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private LvMediaSeriesRpcApi lvMediaSeriesRpcApi;


    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private LvMediaSeriesItemRpcApi lvMediaSeriesItemRpcApi;

    @Autowired
    private ConvertResponseService convertResponseService;

    @Autowired
    private SearchProperties searchProperties;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor commonThreadPool;


    @Autowired
    private DeepLinkUtils deepLinkUtils;

    @Autowired
    private CommonService commonService;

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = context.getInterveneConfigMap();
        if (!interveneConfigMap.containsKey(InterveneTypeEnum.SERIES)) {
            return;
        }
        SearchInterveneCardResponse searchSeriesResponse = FutureUtil.getFutureIgnoreException(
                getSeries(interveneConfigMap.get(InterveneTypeEnum.SERIES), 1, 13,
                        context.getRequestParam().getVipType(), context.getRequestParam(), null, SortEnum.HOT.getType()));
        if (searchSeriesResponse != null && CollectionUtils.isNotEmpty(searchSeriesResponse.getContents())) {
            SearchResponse searchResponse = context.getSearchResponse();
            searchResponse.setLongVideoSeries(searchSeriesResponse);
        }
    }


    public CompletableFuture<SearchInterveneCardResponse> getSeries(
            LvSearchIntervene lvSearchIntervene, Integer pageIndex, Integer pageSize, String vipType,
            KeyWordSearchParamV2 requestParam, String contentType, Integer sortType) {
        Long seriesId = Long.parseLong(lvSearchIntervene.getLinkValue());
        // 获取有效的系列剧
        RpcResult<LvMediaSeries> seriesRpcResult = FutureUtil.getFutureIgnoreException(lvMediaSeriesRpcApi.getValidSeriesBySeriesId(seriesId));
        if (seriesRpcResult == null || seriesRpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
            log.error("getValidSeriesBySeriesId error, the request:{}, the response:{}", seriesId, seriesRpcResult);
            return null;
        }
        LvMediaSeries lvMediaSeries = seriesRpcResult.getData();
        if (lvMediaSeries == null || lvMediaSeries.getStatus() != 1) {
            return null;
        }
        return lvMediaSeriesItemRpcApi.getValidSeriesItemBySeriesId(seriesId).handleAsync((itemRpcResult, itemException) -> {
            if (itemException != null || itemRpcResult == null || itemRpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getValidSeriesItemBySeriesId error, the request:{}, the response:{}", seriesId, itemRpcResult, itemException);
                return null;
            }
            List<LvMediaSeriesItem> list = itemRpcResult.getData();
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }

            // 获取系列剧配置的全部sid
            List<String> sidList = fetchSidList(list, vipType, contentType);
            if (sidList.size() <= (pageIndex - 1) * pageSize) {
                return null;
            }

            List<String> distinctContentTypes = list.stream().map(LvMediaSeriesItem::getProgramType).distinct()
                    .filter(item -> searchProperties.getSearchFilterList().contains(item))
                    .collect(Collectors.toList());

            // 根据sid查询剧头并排序
            List<KeyWordSearchResponse> contents = convertResponseService.getResponseBySidList(
                    sidList, lvSearchIntervene.getIsShowMarkCode(), requestParam, sortType);

            // 根据分页参数做截取
            Boolean hasMore = false;
            int end = contents.size();
            if (contents.size() > pageIndex * pageSize) {
                hasMore = true;
                end = pageIndex * pageSize;
            }
            contents = contents.subList((pageIndex - 1) * pageSize, end);

            SearchInterveneCardResponse response = new SearchInterveneCardResponse();
            response.setContents(contents);
            response.setTitle(lvSearchIntervene.getTitle());
            response.setHasMore(hasMore);
            response.setCode(String.valueOf(lvSearchIntervene.getId()));
            response.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.SERIES_ALBUM.getCode(), String.valueOf(lvSearchIntervene.getId()), lvSearchIntervene.getTitle()));
            convertResponseService.handleFilterList(response, distinctContentTypes);
            if (requestParam.getVersion() >= searchProperties.getSearchCardOptVersion()) {
                response.setSortTypeList(searchProperties.getSortList());
            }

            // 处理搜狐VIP内容
            commonService.handleSohuVipContents(response);

            return response;
        }, commonThreadPool);


    }

    private List<String> fetchSidList(List<LvMediaSeriesItem> list, String vipType, String contentType) {
        TreeMap<Integer, List<LvMediaSeriesItem>> map = new TreeMap<>(Collections.reverseOrder());
        for (LvMediaSeriesItem lvMediaSeriesItem : list) {
            if (map.containsKey(lvMediaSeriesItem.getChapter())) {
                map.get(lvMediaSeriesItem.getChapter()).add(lvMediaSeriesItem);
            } else {
                List<LvMediaSeriesItem> subList = new ArrayList<>();
                subList.add(lvMediaSeriesItem);
                map.put(lvMediaSeriesItem.getChapter(), subList);
            }
        }

        List<String> sidList = new ArrayList<>();
        for (List<LvMediaSeriesItem> lvMediaSeriesItemList : map.values()) {
            LvMediaSeriesItem priorityItem = getByPriority(lvMediaSeriesItemList, vipType);
            if (priorityItem == null) {
                continue;
            }
            if (StringUtil.isBlank(contentType) || "all".equals(contentType) || contentType.equals(priorityItem.getProgramType())) {
                sidList.add(priorityItem.getSid());
            }
        }

        return sidList;
    }

    public LvMediaSeriesItem getByPriority(List<LvMediaSeriesItem> list, String vipType) {
        if (list.size() == 1) {
            return list.get(0);
        }

        // 1. 按语言分组（普通话优先）
        Map<Boolean, List<LvMediaSeriesItem>> groupedByLanguage = list.stream()
                .collect(Collectors.partitioningBy(
                        item -> "普通话".equals(item.getLanguage())
                ));

        List<LvMediaSeriesItem> chineseList = groupedByLanguage.get(true);
        List<LvMediaSeriesItem> otherList = groupedByLanguage.get(false);

        if (chineseList.size() == 1) {
            return chineseList.get(0);
        }

        List<String> priorityList = searchProperties.getCopyRightPriorityMap().get(vipType);
        // 优先检查普通话项
        LvMediaSeriesItem result = findFirstBySourcePriority(chineseList, priorityList);
        if (result != null) {
            return result;
        }

        // 其次检查其他语言项
        return findFirstBySourcePriority(otherList, priorityList);
    }

    private LvMediaSeriesItem findFirstBySourcePriority(List<LvMediaSeriesItem> items, List<String> prioritySources) {
        return prioritySources.stream()
                .flatMap(source -> items.stream()
                        .filter(item -> source.equals(item.getSource()))
                        .limit(1)
                )
                .findFirst()
                .orElse(null);
    }

}