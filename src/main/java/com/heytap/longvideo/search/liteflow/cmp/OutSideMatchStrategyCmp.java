package com.heytap.longvideo.search.liteflow.cmp;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.heytap.longvideo.search.constants.SearchConstant;
import com.heytap.longvideo.search.constants.ThirdPartyMediaTypeEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.outside.OutSideSearchButtonConfig;
import com.heytap.longvideo.search.service.common.StrategyService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.browser.common.app.lib.strategy.AttributeValuesCreator;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.heytap.longvideo.search.constants.OutSideSearchConstant.*;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/6/5 下午4:14
 */

@Slf4j
@LiteflowComponent(name = "outSideMatchStrategyCmp")
public class OutSideMatchStrategyCmp extends NodeComponent {

    private final StrategyService strategyService;

    private final VipRelatedService vipRelatedService;

    public static final List<String> appIds = Lists.newArrayList(
            ThirdPartyMediaTypeEnum.QUAN_SOU.getAppId(),
            ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId());

    public OutSideMatchStrategyCmp(StrategyService strategyService,
                                   VipRelatedService vipRelatedService) {
        this.strategyService = strategyService;
        this.vipRelatedService = vipRelatedService;
    }

    @Override
    public void process() throws Exception {
        try {
            getStrategyForOutSideSearch();
        } catch (Exception e) {
            log.error("Error occurred while getting strategy for outside search", e);
        }
    }

    private void getStrategyForOutSideSearch() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (Objects.isNull(context)) {
            return;
        }

        KeyWordSearchParamV2 param = context.getRequestParam();
        if (Objects.isNull(param)) {
            return;
        }

        if (!appIds.contains(param.getAppId())) {
            return;
        }

        // 获取用户的会员身份信息
        String userId = Optional.ofNullable(param.getSsoid())
                .orElse(param.getAttachments().get("ssoId"));
        if (StringUtils.isNotEmpty(userId)) {
            context.setUserId(userId);
            CompletableFuture<VideoVipInfo> vipInfoCF = vipRelatedService.getVipInfo(userId);
            VideoVipInfo vipInfo = FutureUtil.getFutureIgnoreException(vipInfoCF);
            if (Objects.nonNull(vipInfo)) {
                param.setVideoVipInfo(vipInfo);
                context.setVipInfo(vipInfo);
            }
        }

        // 匹配策略
        AttributeValues attributeValues = param.getAttributeValues();
        if (attributeValues == null) {
            return;
        }
        attributeValues.setClientFullBrowserVersion(param.getVersionName());
        AttributeValuesCreator.buildBrowserVersion(attributeValues);
        CompletableFuture<Map<String, MatchStrategyResponseItem>> matchStrategyCF = strategyService.matchSearchStrategy(param, new String[]{SERVICE_ID_OUTSIDE_BUTTON, SERVICE_ID_SEARCH_NET_RESULTS, TASK_FOR_EPISODE, TASK_FOR_EPISODE_MONGO});
        Map<String, MatchStrategyResponseItem> strategyMap = FutureUtil.getFutureIgnoreException(matchStrategyCF);
        if (MapUtils.isNotEmpty(strategyMap)) {
            context.setStrategyMatchResult(strategyMap);
            // 服务端配置项控制是否放出全网搜策略
            MatchStrategyResponseItem strategyResponseItem = strategyMap.get(SERVICE_ID_SEARCH_NET_RESULTS);
            if (Objects.nonNull(strategyResponseItem) && StringUtils.isNotEmpty(strategyResponseItem.getAttachment())) {
                Map<String, Integer> searchNetResults = JsonUtil.fromStr(strategyResponseItem.getAttachment(), new TypeReference<Map<String, Integer>>() {
                });
                if (Objects.nonNull(searchNetResults)) {
                    context.setSearchNetResults(searchNetResults);
                }
            }

            // 对外搜索按钮文案策略
            MatchStrategyResponseItem outSideButtonStrategy = strategyMap.get(SERVICE_ID_OUTSIDE_BUTTON);
            if (Objects.nonNull(outSideButtonStrategy) && StringUtils.isNotEmpty(outSideButtonStrategy.getAttachment())) {
                JsonNode rootNode = JsonUtil.readTree(outSideButtonStrategy.getAttachment());
                JsonNode search = rootNode.path("search");
                OutSideSearchButtonConfig outSideSearchButtonConfig = JsonUtil.fromStr(search.toString(), new TypeReference<OutSideSearchButtonConfig>() {
                });
                if (Objects.nonNull(outSideSearchButtonConfig)) {
                    context.setOutSideSearchButtonConfig(outSideSearchButtonConfig);
                }
            }
        }
    }
}