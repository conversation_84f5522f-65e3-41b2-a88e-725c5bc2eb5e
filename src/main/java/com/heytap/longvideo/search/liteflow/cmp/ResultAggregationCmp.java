package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardEpisodeBO;
import com.heytap.longvideo.client.media.enums.NewResultCardTypeEnum;
import com.heytap.longvideo.common.lib.constants.MarkStyleEnum;
import com.heytap.longvideo.common.lib.rpc.resp.activity.UnlockEpisodeInfo;
import com.heytap.longvideo.search.constants.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.EpisodeVO;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.AlbumRankRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.QuerySidSearchRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.StandardEpisodeRpcApiProxy;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.RelationListService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.common.utils.CollectionUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/*
 * Description 结果聚合及排序
 * Date 18:41 2023/9/15
 * Author songjiajia 80350688
 */
@Slf4j
@LiteflowComponent("resultAggregationCmp")
public class ResultAggregationCmp extends NodeComponent {

    @Autowired
    private QuerySidSearchRpcApiProxy querySidSearchRpcApiProxy;

    @Autowired
    private StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private AlbumRankRpcApiProxy albumRankRpcApiProxy;

    @Autowired
    private RelationListService relationListService;

    @Autowired
    private TaskUnlockEpisodeService taskUnlockEpisodeService;

    @Autowired
    private ConvertResponseService convertResponseService;

    @Autowired
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    private static final List<String> NEW_RESULT_CARD_FILTER_CONTENT_TYPE = Arrays.asList(ContentTypeEnum.TV.code,
            ContentTypeEnum.KIDS.code, ContentTypeEnum.COMIC.code, ContentTypeEnum.SHOW.code);

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        resultAggregation(context);

    }

    public void resultAggregation(SearchByKeyWordContext context) {
        KeyWordSearchParamV2 keyWordSearchParamV2 = context.getRequestParam();
        SearchResponse searchResponse = context.getSearchResponse();
        List<KeyWordSearchResponse> baseSearchResult = context.getBaseSearchResult();
        deleteByActorCard(searchResponse, baseSearchResult);

        if (CollectionUtils.isNotEmpty(baseSearchResult)) {
            searchResponse.setHasMore(keyWordSearchParamV2.getHasMore());
        }
        if (keyWordSearchParamV2.getPageIndex() >= 10) {
            searchResponse.setHasMore(0);
        }

        insertInterveneAndAllWebContent(context);

        //数据截取（前三页会一次取30条数据，重新排序截取）
        if (keyWordSearchParamV2.getPageIndex() < 4) {
            int start = (keyWordSearchParamV2.getPageIndex() - 1) * keyWordSearchParamV2.getPageSize();
            if (start > baseSearchResult.size()) {
                baseSearchResult = new ArrayList<>();
            } else {
                int end = keyWordSearchParamV2.getPageIndex() * keyWordSearchParamV2.getPageSize();
                end = Math.min(baseSearchResult.size(), end);
                baseSearchResult = baseSearchResult.subList(start, end);
            }
        }

        if (searchResponse.getLongVideoSeries() != null) {
            // 若存在系列卡，取系列卡第一条节目作为结果卡
            baseSearchResult = new ArrayList<>();
            baseSearchResult.add(searchResponse.getLongVideoSeries().getContents().remove(0));
            searchResponse.setHasMore(0);
        } else if ("1".equals(keyWordSearchParamV2.getSearchType()) && baseSearchResult.size() > keyWordSearchParamV2.getPageSize()) {
            baseSearchResult = baseSearchResult.subList(0, keyWordSearchParamV2.getPageSize());
            searchResponse.setHasMore(1);
        }

        if (jumpDuanjuTab(context)) {
            return;
        }

        deleteRecommendCard(searchResponse, baseSearchResult);

        // 8.9 综合tab下发新结果卡
        if (searchProperties.getSearchCardOptVersion() <= keyWordSearchParamV2.getVersion()
                && (SearchTabEnum.SUMMARY.getCode().equals(keyWordSearchParamV2.getSearchType())
                || SearchTabEnum.LONGVIDEO.getCode().equals(keyWordSearchParamV2.getSearchType()) )
                && keyWordSearchParamV2.getDeviceType() == 0) {
            buildNewResultCardInfo(baseSearchResult, keyWordSearchParamV2, searchResponse);
        }
        setSubscribeMsg(context.getRequestParam().getVersion(), baseSearchResult);
        searchResponse.setLongVideoSearchResult(baseSearchResult);
        searchResponse.setPageSize(context.getRequestParam().getPageSize());
        searchResponse.setPageIndex(context.getRequestParam().getPageIndex());
        searchResponse.setLongVideoBannerList(context.getBannerList());
        searchResponse.setSearchTab(context.getRequestParam().getSearchType());
        //统一处理标签卡系列卡影人卡数量小于3不下发
        handleSearchResponse(searchResponse);
        // 免费解锁角标处理
        taskUnlockEpisodeService.handleMarkCode4SearchPage(keyWordSearchParamV2, searchResponse);

        context.setSearchResponse(searchResponse);
    }

    //统一处理标签卡系列卡影人卡数量小于3不下发
    private void handleSearchResponse(SearchResponse searchResponse){
        if(searchResponse == null){
            return ;
        }
        //标签卡
        if(searchResponse.getLongVideoTag() != null && CollectionUtils.isNotEmpty(searchResponse.getLongVideoTag().getContents())){
            if(searchResponse.getLongVideoTag().getContents().size() < 3){
                searchResponse.setLongVideoTag(null);
            }
        }
        if(searchResponse.getLongVideoSeries() != null && CollectionUtils.isNotEmpty(searchResponse.getLongVideoSeries().getContents())){
            if(searchResponse.getLongVideoSeries().getContents().size() < 3){
                searchResponse.setLongVideoSeries(null);
            }
        }
        if(searchResponse.getLongVideoActor() != null && CollectionUtils.isNotEmpty(searchResponse.getLongVideoActor().getContents())){
            if(searchResponse.getLongVideoActor().getContents().size() < 3){
                searchResponse.setLongVideoActor(null);
            }
        }
        if(searchResponse.getLongVideoRecommend() != null && CollectionUtils.isNotEmpty(searchResponse.getLongVideoRecommend().getContents())){
            if(searchResponse.getLongVideoRecommend().getContents().size() < 3){
                searchResponse.setLongVideoRecommend(null);
            }
        }

    }

    //构建新结果卡的信息
    private void buildNewResultCardInfo(List<KeyWordSearchResponse> baseSearchList,
                                        KeyWordSearchParamV2 keyWordSearchParamV2, SearchResponse searchResponse) {
        String userId = CommonUtils.getUid(keyWordSearchParamV2.getSession());
        String keyword = keyWordSearchParamV2.getKeyword();
        Integer version = keyWordSearchParamV2.getVersion();
        Integer pageSize = keyWordSearchParamV2.getPageSize();
        if (CollectionUtils.isEmpty(baseSearchList)) {
            return;
        }
        Set<String> baseSearchSidSet = baseSearchList.stream().map(KeyWordSearchResponse::getSid).collect(Collectors.toSet());
        Map<String, AlbumRecommendInfo> recommendInfoMap = FutureUtil.getFutureIgnoreException(
                albumRankRpcApiProxy.getAlbumRecommendInfo(baseSearchSidSet, true));
        //收藏和预约都在favoriteMap中
        Map<String, Integer> favoriteMap = buildNewResultFavoriteMap(baseSearchList, userId);
        List<String> canSubscribeSidList = convertResponseService.getCanSubscribeSid(baseSearchList);
        List<KeyWordSearchResponse> removeBaseSearchList = new ArrayList<>();
        for (int i = 0; i < baseSearchList.size(); i++) {
            KeyWordSearchResponse keyWordSearchResponse = baseSearchList.get(i);
            keyWordSearchResponse.setCardType(CardTypeEnum.SMALL_CARD.name());
            //如果没有竖图,需要下发兜底竖图
            if (StringUtils.isBlank(keyWordSearchResponse.getVerticalIcon())) {
                keyWordSearchResponse.setVerticalIcon(searchProperties.getDefaultVerticalIcon());
            }
            //如果是第一位的结果 && 没有系列卡,需要判断是否大卡展示
            if (i == 0 && searchResponse.getLongVideoSeries() == null) {
                boolean isBigCard = checkBigCard(keyWordSearchResponse, baseSearchList.size(), keyword);
                if (isBigCard) {
                    keyWordSearchResponse.setCardType(CardTypeEnum.BIG_CARD.name());
                    // 综合tab下发大卡信息，影视tab只下发大卡标记
                    if (SearchTabEnum.SUMMARY.getCode().equals(keyWordSearchParamV2.getSearchType())) {
                        buildEpisodes(keyWordSearchResponse);
                        //如果命中的大卡,则搜索结果大于1时下发hasMore为true
                        if (baseSearchList.size() > 1) {
                            baseSearchList.subList(1, baseSearchList.size()).clear();
                            searchResponse.setHasMore(HasMoreTypeEnum.TRUE_VALUE.getCode());
                        }
                    }
                }
            } else {
                //如果未命中大卡,则搜索的结果要大于pageSize时,hasMore为true
                if (baseSearchList.size() > pageSize) {
                    searchResponse.setHasMore(HasMoreTypeEnum.TRUE_VALUE.getCode());
                }
            }
            //构建showMsg字段文案
            convertResponseService.handleShowMsg(keyWordSearchResponse, true, version);
            //构建简介
            convertResponseService.buildNewResultCardBaseInfo(keyWordSearchResponse);
            //构建推荐理由
            convertResponseService.buildNewResultHighLights(keyWordSearchResponse, recommendInfoMap);
            //构建下发的按钮列表
            convertResponseService.buildNewResultButtons(keyWordSearchResponse, favoriteMap, canSubscribeSidList,removeBaseSearchList);
        }
        baseSearchList.removeAll(removeBaseSearchList);
    }

    private void buildEpisodes(KeyWordSearchResponse keyWordSearchResponse) {
        //如果需要大卡展示,则去获取响应的标准剧集信息
        List<StandardEpisodeBO> standardEpisodeBOList = standardEpisodeRpcApiProxy.listStandardEpisodeBoBySid(
                keyWordSearchResponse.getSid(), ContentTypeEnum.SHOW.getCode().equals(keyWordSearchResponse.getContentType()));
        if (CollectionUtils.isNotEmpty(standardEpisodeBOList)) {
            addMarkCodeUrl(standardEpisodeBOList);
            if (ContentTypeEnum.SHOW.getCode().equals(keyWordSearchResponse.getContentType())) {
                //综艺需要特殊处理,先过滤掉type=1的StandardEpisodeBO的实例,然后按照orderNum倒排
                if (CollectionUtil.isNotEmpty(standardEpisodeBOList)) {
                    //尝试获取到type为1的对象,如果该对象不为空,则证明需要显示更多
                    standardEpisodeBOList.stream().filter(standardEpisodeBO ->
                                    NewResultCardTypeEnum.MORE.getValue() == standardEpisodeBO.getType())
                            .findFirst()
                            .ifPresent(moreStandardEpisodeBO -> keyWordSearchResponse.setHasMoreEpisodes(true));

                    //综艺过滤掉多出的more类型的对象(具体逻辑见standardEpisodeRpcApi.listStandardEpisodeBoBySid实现)
                    standardEpisodeBOList = standardEpisodeBOList.stream()
                            .filter(standardEpisodeBO -> NewResultCardTypeEnum.NORMAL.getValue() == standardEpisodeBO.getType())
                            .collect(Collectors.toList());
                }
            }
            //拼接episodeTitle
            appendEpisodeTitle(standardEpisodeBOList, keyWordSearchResponse);
            List<EpisodeVO> episodeVOList = standardEpisodeBOList.stream()
                    .map(standardEpisodeBO -> {
                        EpisodeVO episodeVO = new EpisodeVO();
                        BeanUtils.copyProperties(standardEpisodeBO, episodeVO);
                        return episodeVO;
                    })
                    .collect(Collectors.toList());
            keyWordSearchResponse.setEpisodes(episodeVOList);
        }
    }

    //添加episodeTitle字段
    private void appendEpisodeTitle(List<StandardEpisodeBO> standardEpisodeBOList, KeyWordSearchResponse keyWordSearchResponse) {
        if (CollectionUtils.isEmpty(standardEpisodeBOList)) {
            return;
        }
        standardEpisodeBOList.parallelStream().forEach(standardEpisodeBO -> {
            if (keyWordSearchResponse.getUnit() != null && NewResultCardTypeEnum.MORE.getValue() != standardEpisodeBO.getType()) {
                standardEpisodeBO.setEpisodeTitle(CommonUtils.getEpisodeTitle(standardEpisodeBO.getEpisode(), keyWordSearchResponse.getUnit(), keyWordSearchResponse.getContentType()));
            }
        });
    }

    //添加会员角标url
    private void addMarkCodeUrl(List<StandardEpisodeBO> standardEpisodeBOList) {
        if (CollectionUtils.isEmpty(standardEpisodeBOList)) {
            return;
        }
        for (StandardEpisodeBO standardEpisodeBO : standardEpisodeBOList) {
            //如果是显示更多的类型直接跳过
            if (NewResultCardTypeEnum.MORE.getValue() == standardEpisodeBO.getType()) {
                continue;
            }
            try {
                String markCodeUrl = imageTagRpcApiProxy.getImageUrl(standardEpisodeBO.getMarkCode());
                standardEpisodeBO.setMarkCodeUrl(markCodeUrl);
            } catch (Exception e) {
                //此处出现异常继续运行,不影响
                log.error("[ResultAggregationCmp.addMarkCodeUrl] error,markCode: {}", standardEpisodeBO.getMarkCode(), e);
            }
        }
    }

    private Map<String, Integer> buildNewResultFavoriteMap(List<KeyWordSearchResponse> baseSearchList, String userId) {
        return relationListService.requestRelationList(userId, baseSearchList.stream().map(KeyWordSearchResponse::getSid).collect(Collectors.toList()));
    }

    /**
     * 是否按大卡展示
     */
    public boolean checkBigCard(KeyWordSearchResponse keyWordSearchResponse, int size, String keyword) {
        //如果是非合作方内容
        if (searchProperties.getThirdSearchMap().containsKey(keyWordSearchResponse.getSource())) {
            return false;
        }
        //如果source是优酷或者搜狐
        if (CopyrightConstant.COPYRIGHT_YOUKU.equals(keyWordSearchResponse.getSource()) ||
                CopyrightConstant.COPYRIGHT_YOUKU_MOBILE.equals(keyWordSearchResponse.getSource()) ||
                CopyrightConstant.COPYRIGHT_SOHU.equals(keyWordSearchResponse.getSource())) {
            return false;
        }
        //如果是电视剧、少儿、动漫、综艺
        if (!NEW_RESULT_CARD_FILTER_CONTENT_TYPE.contains(keyWordSearchResponse.getContentType())) {
            return false;
        }
        Integer episodeCount = standardEpisodeRpcApiProxy.countStandardEpisodeBySid(keyWordSearchResponse.getSid());
        //如果没有获取到该搜索结果的标准剧集信息,则直接不展示大卡
        if (episodeCount == null) {
            return false;
        }
        //如果获取到的期数小于3或者总集数与实际剧集数不相等,则不大卡展示
        if (episodeCount < 3 || !episodeCount.equals(keyWordSearchResponse.getTotalEpisode())) {
            return false;
        }
        //搜索结果只有1个 || 当前为人工干预 || 标题与搜索词的文本匹配率>配置值 || 搜索词当前结果可以在数分同步表中查到
        return size == 1 || keyWordSearchResponse.getSortIndex() != null ||
                StringUtil.isMatchRateQualified(keyword, keyWordSearchResponse.getTitle()) ||
                querySidSearchRpcApiProxy.getQuerySidSearch(keyword, keyWordSearchResponse.getSid()) != null;
    }

    private boolean isRepeat(List<KeyWordSearchResponse> list, KeyWordSearchResponse response, boolean intervene) {
        for (KeyWordSearchResponse keyWordSearchResponse : list) {
            if (keyWordSearchResponse.getTitle().equals(response.getTitle()) &&
                    keyWordSearchResponse.getContentType().equals(response.getContentType())) {
                // 针对干预内容的去重更宽松，年份不一样就不算重复
                if (!intervene || keyWordSearchResponse.getYear().equals(response.getYear())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将人工干预结果与全网搜结果插入ES匹配结果中
     * 优先级（先插入的优先级低）：非合作干预内容 > 合作干预内容 > 非合作匹配内容 > 合作匹配内容
     */
    public void insertInterveneAndAllWebContent(SearchByKeyWordContext context) {
        KeyWordSearchParamV2 requestParam = context.getRequestParam();
        // 先插入非合作匹配内容
        if (CollectionUtils.isNotEmpty(context.getAllWebSearchResult())) {
            // 非合作匹配内容与合作干预内容重复（title+contentType相同）时，剔除非合作匹配内容中的重复项
            // 当精准匹配时 剔除名字对不上的节目
            context.getAllWebSearchResult().removeIf(result -> isRepeat(context.getSearchInterveneResult(), result, false)
                    || (context.getExactMatch() && !Objects.equals(result.getTitle(), context.getRequestParam().getKeyword())));
            insert(context.getBaseSearchResult(), context.getAllWebSearchResult());
        }

        // 再插入合作干预内容
        if (CollectionUtils.isNotEmpty(context.getSearchInterveneResult())) {
            context.getSearchInterveneResult().stream()
                    // 当干预内容的指定序号超过展示数量时，不再插入
                    .filter(searchIntervene -> !("1".equals(requestParam.getSearchType()) &&
                            searchIntervene.getSortIndex() > context.getBaseSearchResult().size() &&
                            context.getBaseSearchResult().size() > requestParam.getPageSize()))
                    .forEach(searchIntervene -> {
                        int insertIndex = Math.min(searchIntervene.getSortIndex(), context.getBaseSearchResult().size());
                        context.getBaseSearchResult().add(insertIndex, searchIntervene);
                    });
        }

        // 后插入非合作干预内容
        if (CollectionUtils.isNotEmpty(context.getAllWebInterveneResult())) {
            // 非合作干预内容与合作干预内容重复（title+contentType+year相同）时，剔除非合作干预内容中的重复项
            context.getAllWebInterveneResult().removeIf(result -> isRepeat(context.getSearchInterveneResult(), result, true));

            context.getAllWebInterveneResult().stream()
                    .filter(searchAllWeb -> !("1".equals(requestParam.getSearchType()) &&
                            searchAllWeb.getSortIndex() > context.getBaseSearchResult().size() &&
                            context.getBaseSearchResult().size() > requestParam.getPageSize()))
                    .forEach(searchAllWeb -> {
                        int insertIndex = Math.min(searchAllWeb.getSortIndex(), context.getBaseSearchResult().size());
                        context.getBaseSearchResult().add(insertIndex, searchAllWeb);
                    });
        }
        if (log.isInfoEnabled()) {
            log.info("requestId={} keyword={}, baseSearchResult={}", requestParam.getRequestId(),
                    requestParam.getKeyword(), JacksonUtil.toJSONString(context.getBaseSearchResult()));
        }
    }

    public void insert(List<KeyWordSearchResponse> baseSearchResult, List<KeyWordSearchResponse> normalResult) {
        // 按sortDefine排序，将非合作内容插入到合作内容中
        int index = 0;
        for (int i = 0; i < baseSearchResult.size() && index < normalResult.size(); i++) {
            KeyWordSearchResponse partnerResponse = baseSearchResult.get(i);
            if (partnerResponse.getSortDefine() > normalResult.get(index).getSortDefine()) {
                continue;
            }
            baseSearchResult.add(i, normalResult.get(index++));
        }
        while (index < normalResult.size()) {
            baseSearchResult.add(normalResult.get(index++));
        }
    }

    /**
     * 根据影人卡内容，删除结果卡中元素，并清空默认推荐卡
     */
    private void deleteByActorCard(SearchResponse searchResponse, List<KeyWordSearchResponse> baseSearchResult) {
        if (searchResponse.getLongVideoActor() != null) {
            // 存在影人卡，说明无干预的相关推荐卡，需要清空算法的相关推荐卡
            searchResponse.setLongVideoRecommend(null);
            // 去除结果卡中 与影人卡重复的内容
            Set<String> actorCardSidSet = searchResponse.getLongVideoActor().getContents().stream()
                    .map(KeyWordSearchResponse::getSid)
                    .collect(Collectors.toSet());
            Set<String> actorCardTitleSet = searchResponse.getLongVideoActor().getContents().stream()
                    .map(KeyWordSearchResponse::getTitle)
                    .collect(Collectors.toSet());
            baseSearchResult.removeIf(content ->
                    actorCardSidSet.contains(content.getSid()) || actorCardTitleSet.contains(content.getTitle()));
        }
    }

    /**
     * 删除推荐卡中，已经在结果卡里的内容
     *
     * @param searchResponse
     * @param baseSearchResult
     */
    private void deleteRecommendCard(SearchResponse searchResponse, List<KeyWordSearchResponse> baseSearchResult) {
        if (searchResponse.getLongVideoRecommend() != null) {
            Set<String> searchCardSidSet = baseSearchResult.stream().map(KeyWordSearchResponse::getSid).collect(Collectors.toSet());
            Set<String> searchCardTitleSet = baseSearchResult.stream().map(KeyWordSearchResponse::getTitle).collect(Collectors.toSet());
            searchResponse.getLongVideoRecommend().getContents().removeIf(content ->
                    searchCardSidSet.contains(content.getSid()) || searchCardTitleSet.contains(content.getTitle()));
            // 推荐卡内容小于4个不展示
            if (searchResponse.getLongVideoRecommend().getContents().size() < 4) {
                searchResponse.setLongVideoRecommend(null);
            }
        }
    }

    /**
     * 8.7 当短剧结果包含搜索词&&结果卡不含搜索词时，只返回短剧内容
     */
    public boolean jumpDuanjuTab(SearchByKeyWordContext context) {
        // 检查基础搜索结果是否匹配搜索词
        if (hasExactMatch(context.getBaseSearchResult(), context.getRequestParam().getKeyword())) {
            return false;
        }

        // 检查短剧搜索结果是否匹配搜索词
        if (hasExactMatch(context.getDuanjuSearchResult(), context.getRequestParam().getKeyword())) {
            context.setSearchResponse(SearchResponse.duanjuResult(
                    context.getDuanjuSearchResult(), context.getRequestParam().getPageIndex(),
                    context.getRequestParam().getDuanjuHasMore()));
            return true;
        }

        return false;
    }

    private boolean hasExactMatch(List<KeyWordSearchResponse> results, String keyword) {
        return CollectionUtils.isNotEmpty(results) &&
                results.stream().anyMatch(item -> keyword.equals(item.getTitle()));
    }

    private void setSubscribeMsg(Integer version, List<KeyWordSearchResponse> baseSearchResult) {
        if (version < 71100) {
            return;
        }
        List<String> canSubscribeSidList = convertResponseService.getCanSubscribeSid(baseSearchResult);
        if (CollectionUtils.isEmpty(canSubscribeSidList)) {
            return;
        }
        for (KeyWordSearchResponse keyWordSearchResponse : baseSearchResult) {
            if (canSubscribeSidList.contains(keyWordSearchResponse.getSid())) {
                keyWordSearchResponse.setAllowChaseAlbum(1);
                keyWordSearchResponse.setButtonStatus(3);
                keyWordSearchResponse.setButtonText("立即预约");
            }
        }
    }

}
