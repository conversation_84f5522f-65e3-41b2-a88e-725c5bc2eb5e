package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.*;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.video.client.entity.video.ActorInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@LiteflowComponent("searchActorCmp")
public class SearchActorCmp extends NodeComponent {

    @Autowired
    private ConvertResponseService convertResponseService;

    @Autowired
    private ListFilterService listFilterService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private DeepLinkUtils deepLinkUtils;

    private final static int PAGE_SIZE = 12;

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (!check(context.getInterveneConfigMap(), context.getRequestParam())) {
            return;
        }

        SearchInterveneCardResponse response = queryActorCard(context.getRequestParam(), 1, "all");
        context.getSearchResponse().setLongVideoActor(handle(response, context.getSearchInterveneResult()));
    }


    public boolean check(Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap, KeyWordSearchParamV2 param) {
        if (interveneConfigMap.containsKey(InterveneTypeEnum.SERIES)
                || interveneConfigMap.containsKey(InterveneTypeEnum.TAG)
                || interveneConfigMap.containsKey(InterveneTypeEnum.RECOMMEND)) {
            log.info("SearchActorCmp check fail: 包含优先级更高的干预");
            return false;
        }
        if (param.getVersion() < 80200 || param.getPageIndex() != 1 || param.getDeviceType() != 0 || !"1".equals(param.getSearchType())) {
            log.info("SearchActorCmp check fail: 入参条件不查影人卡");
            return false;
        }
        return true;
    }


    public SearchInterveneCardResponse queryActorCard(KeyWordSearchParamV2 param, Integer sortType, String contentType) {
        ListFilterParam listFilterParam = buildListFilterParam(param, sortType, contentType);
        List<ProgramAlbumEs> esList = listFilterService.listFilter(listFilterParam);
        List<String> distinctContentTypes = listFilterService.listDistinctContentTypes(listFilterParam);
        return buildCardResponse(param, listFilterParam, esList, sortType, distinctContentTypes);
    }

    private ListFilterParam buildListFilterParam(KeyWordSearchParamV2 param, Integer sortType, String contentType) {
        ListFilterParam listFilterParam = new ListFilterParam();
        listFilterParam.setOffset(param.getPageIndex());
        listFilterParam.setNumber(PAGE_SIZE);
        listFilterParam.setVersion(param.getVersion());
        listFilterParam.setVipType(param.getVipType());
        listFilterParam.setCallType(2);

        //构建urlPack
        Urlpack urlpack = new Urlpack();
        urlpack.setActor(param.getKeyword());
        //默认最热排序
        urlpack.setSort("hot");
        if (SortEnum.NEW.getType().equals(sortType)) {
            urlpack.setSort("showTime");
        } else if (SortEnum.FREE.getType().equals(sortType)) {
            urlpack.setSort("free");
        }
        urlpack.setVersion_tag(CommonUtils.getVersionTag(param.getVersion()));
        urlpack.setContentType(StringUtil.isBlank(contentType) ? "all" : contentType);
        urlpack.setSidBlackList(param.getSidBlackList());

        Map<String, Object> urlPackMap = new HashMap<>();
        urlPackMap.put("cmd_vod", urlpack);

        listFilterParam.setUrlpack(JacksonUtil.toJSONString(urlPackMap));
        return listFilterParam;
    }

    private SearchInterveneCardResponse buildCardResponse(KeyWordSearchParamV2 param, ListFilterParam listFilterParam,
                                                          List<ProgramAlbumEs> esList, Integer sortType, List<String> distinctContentTypes) {
        if (CollectionUtils.isEmpty(esList)) {
            log.info("search actor esList is empty, keyword:{}}", param.getKeyword());
            return null;
        }
        List<KeyWordSearchResponse> contentList = new ArrayList<>();
        for (ProgramAlbumEs programAlbumEs : esList) {
            KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
            BeanUtils.copyProperties(programAlbumEs, keyWordSearchResponse);
            keyWordSearchResponse.setAlbumFeatureType(programAlbumEs.getFeatureType());
            keyWordSearchResponse.setLanguages(programAlbumEs.getLanguage());
            keyWordSearchResponse.setStars(programAlbumEs.getActor());
            keyWordSearchResponse.setDirectors(programAlbumEs.getDirector());
            keyWordSearchResponse.setLinkValue(programAlbumEs.getSid());
            keyWordSearchResponse.setRecommendInfo(programAlbumEs.getBrief());
            convertResponseService.handleBackgroundColor(keyWordSearchResponse, programAlbumEs.getBackGroundColorJson());
            convertResponseService.handleShowMsg(keyWordSearchResponse, false, param.getVersion());
            contentList.add(keyWordSearchResponse);
        }

        //构建搜索标签响应
        SearchInterveneCardResponse actorCardResponse = new SearchInterveneCardResponse();
        String title = param.getKeyword() + "的作品";
        actorCardResponse.setTitle(title);
        actorCardResponse.setCode(param.getKeyword());
        actorCardResponse.setContents(contentList);
        //listFilterParam的hasMore在listFilter逻辑里查询了总的es响应数据后赋值
        actorCardResponse.setHasMore(listFilterParam.getHasMore());
        //linkValue为标签卡id
        actorCardResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.ACTOR.getCode(),
                param.getKeyword(), title, sortType.toString()));
        actorCardResponse.setSortTypeList(listFilterParam.getVersion() < searchProperties.getSearchCardOptVersion() ?
                searchProperties.getSortTypeList() : searchProperties.getSortList());
        convertResponseService.handleFilterList(actorCardResponse, distinctContentTypes);

        // 影人信息区
        ActorInfo actorInfo = new ActorInfo();
        actorInfo.setName(param.getKeyword());
        actorInfo.setRole("actor");
        actorCardResponse.setActorInfo(actorInfo);

        convertResponseService.handleSubTitle(contentList, param.getVersion());

        // 处理搜狐VIP内容
        commonService.handleSohuVipContents(actorCardResponse);

        log.info("actorCardResponse={}", JacksonUtil.toJSONString(actorCardResponse));
        return actorCardResponse;
    }

    public SearchInterveneCardResponse handle(SearchInterveneCardResponse response, List<KeyWordSearchResponse> searchInterveneResult) {
        if (response != null && CollectionUtils.isNotEmpty(response.getContents())) {
            // 去除影人卡中 与结果干预相同的内容
            if (CollectionUtils.isNotEmpty(searchInterveneResult)) {
                // 存在合作内容干预，则影人卡排在结果卡后，这里进行打标
                response.setHasIntervene(true);
                Set<String> actorCardSidSet = searchInterveneResult.stream()
                        .map(KeyWordSearchResponse::getSid)
                        .collect(Collectors.toSet());
                Set<String> actorCardTitleSet = searchInterveneResult.stream()
                        .map(KeyWordSearchResponse::getTitle)
                        .collect(Collectors.toSet());
                response.getContents().removeIf(content ->
                        actorCardSidSet.contains(content.getSid()) || actorCardTitleSet.contains(content.getTitle()));
            }
            if (response.getContents().size() > 3) {
                return response;
            }
        }
        return null;
    }
}
