package com.heytap.longvideo.search.liteflow.cmp;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.entity.MtvMultiparams;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.*;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.MultiParamsRpcApiProxy;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.entity.MultisearchNodeDetailVo;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Cache;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Caffeine;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.FEATURE_TYPE_YG;
import static com.heytap.longvideo.search.constants.SearchConstant.MARK_CODE_YG;

/**
 * <AUTHOR> Yanping
 * @date 2023/10/23
 */
@Slf4j
@LiteflowComponent("searchTagCmp")
public class SearchTagCmp extends NodeComponent {

    @Autowired
    private SearchIntentService searchIntentService;

    @Autowired
    private ListFilterService listFilterService;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private MultiParamsRpcApiProxy multiParamsRpcApiProxy;

    @Autowired
    private ArrangeRpcApiProxy arrangeRpcApiProxy;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor commonThreadPool;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Autowired
    private DeepLinkUtils deepLinkUtils;

    @Autowired
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ConvertResponseService convertResponseService;

    Cache<String, Map<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>>> multiParamsCache = Caffeine.newBuilder()
            .maximumSize(16)
            .expireAfterWrite(35, TimeUnit.MINUTES)
            .build();

    private final String contentTypeTagListMapKey = "contentTypeTagList";

    private final static int PAGE_SIZE = 12;


    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = context.getInterveneConfigMap();
        if (!interveneConfigMap.containsKey(InterveneTypeEnum.TAG)) {
            return;
        }
        SearchInterveneCardResponse searchTagResponse = FutureUtil.getFutureIgnoreException(
                getTagContent(context.getRequestParam(), interveneConfigMap.get(InterveneTypeEnum.TAG), 1, 1, null));
        if (searchTagResponse != null && CollectionUtils.isNotEmpty(searchTagResponse.getContents())) {
            SearchResponse searchResponse = context.getSearchResponse();
            searchResponse.setLongVideoTag(searchTagResponse);
        }
    }

    public CompletableFuture<SearchInterveneCardResponse> getTagContent(
            KeyWordSearchParamV2 requestParam, LvSearchIntervene lvSearchIntervene, Integer pageIndex,
            Integer sortType, String contentType) {
        if (lvSearchIntervene.getLinkType() == null) {
            log.error("linkType is null, lvSearchIntervene:{}", lvSearchIntervene);
            return CompletableFuture.completedFuture(null);
        }
        if (TemplateLinkTypeEnum.CONTENT_POOL.getCode() == lvSearchIntervene.getLinkType()) {
            //关联的是内容池
            return arrangeRpcApiProxy.getLvContentItems(lvSearchIntervene.getLinkValue(), 1, pageIndex, PAGE_SIZE).thenApplyAsync(pageResult -> {
                if (pageResult == null) {
                    log.error("pageResult is null, lvSearchIntervene:{}", lvSearchIntervene);
                    return null;
                }

                SearchInterveneCardResponse searchTagCardResponse = new SearchInterveneCardResponse();
                searchTagCardResponse.setTitle(lvSearchIntervene.getTitle());
                searchTagCardResponse.setCode(String.valueOf(lvSearchIntervene.getId()));
                searchTagCardResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.TAG.getCode(), String.valueOf(lvSearchIntervene.getId()), lvSearchIntervene.getTitle()));
                searchTagCardResponse.setHasMore(pageResult.hasNext());
                searchTagCardResponse.setContents(searchIntentService.handleContentPoolPageResult(pageResult, lvSearchIntervene, requestParam,sortType));
                if(requestParam.getVersion() >= searchProperties.getSearchCardOptVersion()){
                    searchTagCardResponse.setSortTypeList(searchProperties.getSortList());
                }

                // 处理搜狐VIP内容
                commonService.handleSohuVipContents(searchTagCardResponse);

                return searchTagCardResponse;
            });
        } else if (TemplateLinkTypeEnum.TAG.getCode() == lvSearchIntervene.getLinkType()) {
            //关联的是标签
            //构建筛选条件，调用筛选服务获取筛选数据
            InterveneCardParam param = new InterveneCardParam();
            param.setVersion(requestParam.getVersion());
            param.setPageIndex(pageIndex);
            param.setPageSize(PAGE_SIZE);
            //区分排序方式，关联内容池时不区分排序方式
            param.setSortType(sortType);
            param.setContentType(contentType);
            param.setVipType(requestParam.getVipType());
            return getItemByTag(lvSearchIntervene, param);
        }
        return CompletableFuture.completedFuture(null);
    }


    /**
     * 根据标签卡标签匹配节目内容
     *
     * @param lvSearchIntervene
     * @param interveneCardParam
     * @return
     */
    public CompletableFuture<SearchInterveneCardResponse> getItemByTag(LvSearchIntervene lvSearchIntervene, InterveneCardParam interveneCardParam) {
        String[] tagContentTypeStrArray = lvSearchIntervene.getLinkValue().split(":");
        //eg. 爱情:all 、爱情:电视剧
        if (tagContentTypeStrArray.length < 2) {
            log.error("linked tag is empty, linkValue:{}", lvSearchIntervene.getLinkValue());
            return CompletableFuture.completedFuture(null);
        }
        //获取品类-标签-映射标签列表关系Map
        return getContentTagBeansMap().thenComposeAsync(contentTypeTagBeansMap -> {
            if (contentTypeTagBeansMap == null || contentTypeTagBeansMap.isEmpty()) {
                log.error("contentTypeTagBeansMap is empty, lvSearchIntervene:{}", JSON.toJSONString(lvSearchIntervene));
                return CompletableFuture.completedFuture(null);
            }
            String tag = tagContentTypeStrArray[0];
            //获取该标签匹配的品类
            String contentType = "";
            if ("all".equals(tagContentTypeStrArray[1])) {
                //多品类标签卡，动态查找其标签对应的品类
                Map<String, String> tagContentTypeMap = getTagContentTypeMap(contentTypeTagBeansMap);
                contentType = tagContentTypeMap.get(tag);
            } else {
                //单品类标签卡，直接使用linkValue值里的内容品类
                contentType = StandardConstant.ALBUM_TYPE_MAP.get(tagContentTypeStrArray[1]);
            }
            String tags = getTagRelationMap(contentTypeTagBeansMap, contentType).get(tag);
            // 若指定了筛选类型，则覆盖配置里的
            if (StringUtils.isNotBlank(interveneCardParam.getContentType()) && !"all".equals(contentType)) {
                contentType = interveneCardParam.getContentType();
            }
            ListFilterParam listFilterParam = buildListFilterParam(interveneCardParam, tags, contentType);
            List<ProgramAlbumEs> list = listFilterService.listFilter(listFilterParam);
            List<String> distinctContentTypes = listFilterService.listDistinctContentTypes(listFilterParam);
            return buildSearchTagResponse(lvSearchIntervene, listFilterParam, list, interveneCardParam, distinctContentTypes);
        }, commonThreadPool);
    }

    private CompletableFuture<SearchInterveneCardResponse> buildSearchTagResponse(
            LvSearchIntervene lvSearchIntervene, ListFilterParam listFilterParam, List<ProgramAlbumEs> list,
            InterveneCardParam interveneCardParam, List<String> distinctContentTypes) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("search tag list is empty, appVersion:{}, lvSearchIntervene.id:{}, linkType:{}, linkValue:{}, pageIndex:{}",
                    listFilterParam.getVersion(), lvSearchIntervene.getId(), lvSearchIntervene.getLinkType(), lvSearchIntervene.getLinkValue(), listFilterParam.getNumber());
            return CompletableFuture.completedFuture(null);
        }
        Integer showMarkCode = lvSearchIntervene.getIsShowMarkCode();
        List<KeyWordSearchResponse> returnList = new ArrayList<>();
        for (ProgramAlbumEs programAlbumEs : list) {
            KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
            BeanUtils.copyProperties(programAlbumEs, keyWordSearchResponse);
            // 【坑】外层转换时用的albumFeatureType，需要赋值
            keyWordSearchResponse.setAlbumFeatureType(programAlbumEs.getFeatureType());
            keyWordSearchResponse.setLanguages(programAlbumEs.getLanguage());
            keyWordSearchResponse.setStars(programAlbumEs.getActor());
            keyWordSearchResponse.setDirectors(programAlbumEs.getDirector());
            keyWordSearchResponse.setLinkValue(programAlbumEs.getSid());
            keyWordSearchResponse.setRecommendInfo(programAlbumEs.getBrief());
            if ((showMarkCode != null && showMarkCode == 1) || FEATURE_TYPE_YG == keyWordSearchResponse.getFeatureType()) {
                if (FEATURE_TYPE_YG == keyWordSearchResponse.getFeatureType()) {
                    keyWordSearchResponse.setMarkCode(MARK_CODE_YG);
                }
                keyWordSearchResponse.setMarkCodeUrl(imageTagRpcApiProxy.getImageUrl(keyWordSearchResponse.getMarkCode()));
            } else {
                keyWordSearchResponse.setMarkCode("");
                keyWordSearchResponse.setMarkCodeUrl("");
            }
            convertResponseService.handleBackgroundColor(keyWordSearchResponse, programAlbumEs.getBackGroundColorJson());
            convertResponseService.handleShowMsg(keyWordSearchResponse, false, interveneCardParam.getVersion());
            returnList.add(keyWordSearchResponse);
        }

        //构建搜索标签响应
        SearchInterveneCardResponse searchTagCardResponse = new SearchInterveneCardResponse();
        searchTagCardResponse.setTitle(lvSearchIntervene.getTitle());
        searchTagCardResponse.setCode(String.valueOf(lvSearchIntervene.getId()));
        searchTagCardResponse.setContents(returnList);
        //listFilterParam的hasMore在listFilter逻辑里查询了总的es响应数据后赋值
        searchTagCardResponse.setHasMore(listFilterParam.getHasMore());
        //linkValue为标签卡id
        searchTagCardResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.TAG.getCode(), String.valueOf(lvSearchIntervene.getId()),
                lvSearchIntervene.getTitle()) + "&sortType=" + interveneCardParam.getSortType());
        searchTagCardResponse.setSortTypeList(listFilterParam.getVersion() < searchProperties.getSearchCardOptVersion() ?
                searchProperties.getSortTypeList() : searchProperties.getSortList());
        convertResponseService.handleFilterList(searchTagCardResponse, distinctContentTypes);
        convertResponseService.handleSubTitle(returnList, interveneCardParam.getVersion());

        // 处理搜狐VIP内容
        commonService.handleSohuVipContents(searchTagCardResponse);

        return CompletableFuture.completedFuture(searchTagCardResponse);
    }

    /**
     * 构建筛选服务的请求参数
     *
     * @param param
     * @param tags
     * @param contentType
     * @return
     */
    private ListFilterParam buildListFilterParam(InterveneCardParam param, String tags, String contentType) {
        ListFilterParam listFilterParam = new ListFilterParam();
        listFilterParam.setOffset(param.getPageIndex());
        listFilterParam.setNumber(param.getPageSize());
        listFilterParam.setVersion(param.getVersion());
        listFilterParam.setVipType(param.getVipType());
        listFilterParam.setCallType(2);

        //构建urlPack
        Urlpack urlpack = new Urlpack();
        urlpack.setTag(tags);
        urlpack.setContentType(contentType);
        urlpack.setVersion_tag(CommonUtils.getVersionTag(param.getVersion()));
        //默认最热排序
        urlpack.setSort("hot");
        if (SortEnum.NEW.getType().equals(param.getSortType())) {
            urlpack.setSort("showTime");
        } else if (SortEnum.FREE.getType().equals(param.getSortType())) {
            urlpack.setSort("free");
        }
        Map<String, Object> urlPackMap = new HashMap<>();
        urlPackMap.put("cmd_vod", urlpack);

        listFilterParam.setUrlpack(JSON.toJSONString(urlPackMap));
        return listFilterParam;
    }


    /**
     * eg. 爱情:电影,电视剧
     *
     * @return
     */
    private Map<String, String> getTagContentTypeMap(Map<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>> contentTypeTagBeansMap) {
        Map<String, String> tagContentTypeMap = new HashMap<>();
        for (Map.Entry<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>> entry : contentTypeTagBeansMap.entrySet()) {
            String contentType = entry.getKey().trim();
            List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean> childrenBeans = entry.getValue();
            for (MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean childrenBean : childrenBeans) {

                if (StringUtils.isBlank(childrenBean.getCode()) || StringUtils.isBlank(childrenBean.getName())) {
                    continue;
                }
                if ("all".equals(childrenBean.getCode())) {
                    continue;
                }
                //构建标签：匹配的品类列表，eg, "爱情":"电影,电视剧"
                String contentTypeValue;
                String tag = childrenBean.getName().trim();
                if (tagContentTypeMap.containsKey(tag)) {
                    contentTypeValue = tagContentTypeMap.get(tag) + "," + contentType;
                } else {
                    contentTypeValue = contentType;
                }
                tagContentTypeMap.put(tag, contentTypeValue);
            }
        }
        return tagContentTypeMap;
    }

    /**
     * tv: (爱情：恋爱|情爱|爱情)
     */
    private Map<String, String> getTagRelationMap(Map<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>> contentTypeTagBeansMap, String contentType) {
        List<String> contentTypeList = StringUtil.isBlank(contentType) ?
                new ArrayList<>() : new ArrayList<>(Arrays.asList(contentType.split(",")));
        //使用set防止聚合时出现重复标签
        Map<String, List<String>> tagRelationListMap = new HashMap<>();
        contentTypeTagBeansMap.entrySet().forEach(entry -> handleSingleChildrenBean(contentTypeList, entry, tagRelationListMap));

        Map<String, String> tagRelationMap = new HashMap<>();
        //将映射标签转化为"|"分隔的字符串
        for (Map.Entry<String, List<String>> entry : tagRelationListMap.entrySet()) {
            //去重
            List<String> tagsList = entry.getValue().stream().distinct().collect(Collectors.toList());
            tagRelationMap.put(entry.getKey(), String.join("|", tagsList));
        }
        return tagRelationMap;
    }

    private void handleSingleChildrenBean(
            List<String> contentTypeList, Map.Entry<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>> entry,
            Map<String, List<String>> tagRelationListMap) {
        // 支取限定品类下的原始标签
        if (CollectionUtils.isNotEmpty(contentTypeList) && !contentTypeList.contains(entry.getKey())) {
            return;
        }
        for (MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean childrenBean : entry.getValue()) {
            if (StringUtils.isBlank(childrenBean.getCode()) || StringUtils.isBlank(childrenBean.getName())) {
                continue;
            }
            if ("all".equals(childrenBean.getCode())) {
                continue;
            }
            //eg. 爱情 + "|" + "恋爱|情爱" ===> "爱情|恋爱|情爱"
            List<String> tagsList;
            if (tagRelationListMap.containsKey(childrenBean.getName())) {
                tagsList = tagRelationListMap.get(childrenBean.getName());
            } else {
                tagsList = new ArrayList<>();
            }
            tagsList.addAll(Arrays.asList(childrenBean.getCode().split("\\|")));
            tagRelationListMap.put(childrenBean.getName(), tagsList);
        }
    }

    /**
     * 从mtv_multiparams表里查询tag对应的映射前的标签
     *
     * @return
     */
    private CompletableFuture<Map<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>>> getContentTagBeansMap() {
        Map<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>> cacheContentTypeTagBeansMap = multiParamsCache.getIfPresent(contentTypeTagListMapKey);
        if (cacheContentTypeTagBeansMap != null) {
            return CompletableFuture.completedFuture(cacheContentTypeTagBeansMap);
        }
        return multiParamsRpcApiProxy.findFilterAll().handle((multiParamsList, e) -> {
            if (e != null || CollectionUtils.isEmpty(multiParamsList)) {
                log.error("multiParamsList is empty, error:", e);
                return Collections.emptyMap();
            }
            multiParamsList = multiParamsList.stream().sorted(Comparator.comparing(MtvMultiparams::getId)).collect(Collectors.toList());
            Map<String, List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean>> contentTypeTagBeansMap = new HashMap<>();
            for (MtvMultiparams mtvMultiparams : multiParamsList) {
                //eg, "爱情":"恋爱|情感|悲情|爱情|言情|言情剧|偶像|偶像剧"
                Map<String, String> tagRelationMap = new HashMap<>();
                MultisearchNodeDetailVo multisearchNodeDetailVo = JSON.parseObject(mtvMultiparams.getMultiparamsJson(), MultisearchNodeDetailVo.class);

                List<MultisearchNodeDetailVo.ChildrenBeanX.ChildrenBean> childrenBeanList = multisearchNodeDetailVo.getChildren()
                        .stream()
                        .filter(childrenBeanX -> "tag".equals(childrenBeanX.getCode()))
                        .flatMap(childrenBeanX -> childrenBeanX.getChildren().stream()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(childrenBeanList)) {
                    continue;
                }
                contentTypeTagBeansMap.put(multisearchNodeDetailVo.getCode(), childrenBeanList);
            }
            multiParamsCache.put(contentTypeTagListMapKey, contentTypeTagBeansMap);
            return contentTypeTagBeansMap;
        });
    }

}
