package com.heytap.longvideo.search.liteflow.cmp;

import com.github.stuxuhai.jpinyin.ChineseHelper;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.constants.CopyrightConstant;
import com.heytap.longvideo.search.constants.KeywordSearchTypeEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.common.ActorService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.search.SearchAlbumService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.SEARCH_FROM_QUANSOU;

/**
 * @Description: 根据关键词搜索(精准匹配) 对外
 * @Author: 80398885WT
 * @Date: 2025/5/30
 */
@Slf4j
@LiteflowComponent(name = "exactSearchCmp")
public class ExactSearchCmp extends NodeComponent {

    @HeraclesDynamicConfig(key = "outside.search.size", fileName = "search_config.properties")
    private Integer outsideSearchSize = 15;

    private final ConvertResponseService beanConvertService;

    private final ActorService actorService;

    private final SearchProperties searchConfig;

    private final YoukuSourceFilterService youkuSourceFilterService;

    private final ElasticsearchRestTemplate restTemplate;

    private final SearchAlbumService searchAlbumService;

    private final CommonService commonService;

    private final StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    private final DeepLinkUtils deepLinkUtils;

    public ExactSearchCmp(ConvertResponseService beanConvertService,
                          ActorService actorService,
                          SearchProperties searchConfig,
                          YoukuSourceFilterService youkuSourceFilterService,
                          ElasticsearchRestTemplate restTemplate,
                          SearchAlbumService searchAlbumService,
                          CommonService commonService,
                          StandardAlbumRpcApiProxy standardAlbumRpcApiProxy,
                          DeepLinkUtils deepLinkUtils) {
        this.beanConvertService = beanConvertService;
        this.actorService = actorService;
        this.searchConfig = searchConfig;
        this.youkuSourceFilterService = youkuSourceFilterService;
        this.restTemplate = restTemplate;
        this.searchAlbumService = searchAlbumService;
        this.commonService = commonService;
        this.standardAlbumRpcApiProxy = standardAlbumRpcApiProxy;
        this.deepLinkUtils = deepLinkUtils;
    }

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (Objects.isNull(context)) {
            return;
        }

        // 模糊匹配直接return
        if (!context.getExactMatch()) {
            return;
        }

        List<ProgramAlbumEs> list = exactSearch(context.getRequestParam());
        List<KeyWordSearchResponse> keyWordSearchResponseList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(list)) {
            List<String> unlockAlbumList = new ArrayList<>();
            if (context.getRequestParam() != null && SEARCH_FROM_QUANSOU.equals(context.getRequestParam().getAppId())) {
                unlockAlbumList = beanConvertService.getUnlockAlbumList(context, list);
            }
            for (ProgramAlbumEs programAlbumEs : list) {
                try {
                    KeyWordSearchResponse keyWordSearchResponse = beanConvertService.programAlbumEsConvertResponse(programAlbumEs, context, unlockAlbumList);
                    keyWordSearchResponseList.add(keyWordSearchResponse);
                } catch (Exception e) {
                    log.error("exactSearchCmp error, sid:{}", programAlbumEs.getSid(), e);
                }
            }
        }

        context.setBaseSearchResult(keyWordSearchResponseList);
    }

    /**
     * 精准匹配
     */
    private List<ProgramAlbumEs> exactSearch(KeyWordSearchParamV2 param) {
        boolean newSearch = param.getVersionTag() >= 5;

        // 1.参数校验
        List<ProgramAlbumEs> list = new ArrayList<>();
        String keyword = param.getKeyword();
        if (StringUtil.isBlank(keyword) || keyword.length() > 20) {
            return list;
        }

        keyword = keyword.trim();
        keyword = ChineseHelper.convertToSimplifiedChinese(keyword);
        log.info("search params:{}", param);
        param.setKeyword(keyword);

        // 2.构造查询参数
        BoolQueryBuilder boolQuery = buildBaseQuery(keyword, param);

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        PageRequest pageRequest = PageRequest.of(0, outsideSearchSize);
        if (newSearch && param.getPageIndex() >= 4) {
            pageRequest = PageRequest.of(param.getPageIndex() - 1, param.getPageSize());
        }

        NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQuery).withPageable(pageRequest).build();

        // 4.解析响应
        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        log.info("searchAlbumCmp requestId={}, searchHits={}", param.getRequestId(), JacksonUtil.toJSONString(searchHits));

        if (searchHits.getTotalHits() > ((long) param.getPageIndex()) * param.getPageSize()) {
            param.setHasMore(1);
        }

        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            float score = searchHit.getScore();
            ProgramAlbumEs es = searchHit.getContent();
            es.setReleScore(score);
            list.add(es);
        }

        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        // 5.过滤
        list = searchAlbumService.filterByMatch(list, keyword);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        // 6.版本过滤、去重
        list = searchAlbumService.filterVirtualProgram(list, param);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        // 7.数据重排序
        list = searchAlbumService.keywordSearchSort(list, keyword);

        List<String> sidList = list.stream()
                .map(ProgramAlbumEs::getSid)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(sidList)) {
            Map<String, StandardAlbum> albumMap = commonService.fetchAlbumMap(sidList);
            for (ProgramAlbumEs albumEs : list) {
                // 找到对应剧头 判断是否是搜狐VIP内容
                StandardAlbum album = albumMap.get(albumEs.getSid());
                if (commonService.isSohuVipContent(album)) {
                    albumEs.setProgramInfo("");
                }
            }
        }

        // sidList一定不为空，不判断 关键词搜索 设置deepLink
        Map<String, StandardAlbum> albumRpcResultMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);
        for (ProgramAlbumEs albumEs : list) {
            albumEs.setSourceWebUrl(albumRpcResultMap.get(albumEs.getSid()).getSourceWebUrl());
            if (youkuSourceFilterService.thirdPartyFastAppFilter(albumEs.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                albumEs.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), albumRpcResultMap.get(albumEs.getSid()).getSourceWebUrl()));
            } else {
                albumEs.setDeepLink(youkuSourceFilterService.getUpGradeUrl(albumEs.getSid(), albumEs.getSource(), albumEs.getTitle()));
                albumEs.setDpLinkType(TemplateLinkTypeEnum.UPGRADE_PAGE.getCode());
            }
        }
        return list;
    }

    /**
     * 构造查询条件
     */
    private BoolQueryBuilder buildBaseQuery(String keyword, KeyWordSearchParamV2 param) {
        KeywordSearchTypeEnum searchTypeEnum = param.getSearchTypeEnum();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (StringUtil.isNumericOrChar(keyword)) {
            keyword = keyword.trim().toLowerCase();
            boolQuery.should(QueryBuilders.termQuery("titlePinyin.keyword", keyword).boost(3f));
            boolQuery.should(QueryBuilders.termQuery("actorPinyin.keyword", keyword).boost(2f));
            boolQuery.should(QueryBuilders.termQuery("directorPinyin.keyword", keyword).boost(1f));
            boolQuery.minimumShouldMatch(1);
            searchTypeEnum = KeywordSearchTypeEnum.PINYIN;
        } else if (actorService.checkIsActorAndDirector(keyword)) {
            boolQuery.should(QueryBuilders.termQuery("actor.keyword", keyword).boost(4f));
            boolQuery.should(QueryBuilders.termQuery("director.keyword", keyword).boost(2f));
            boolQuery.should(QueryBuilders.termQuery("title.keyword", keyword).boost(1f));
            searchTypeEnum = KeywordSearchTypeEnum.ACTOR;
        } else {
            boolQuery.should(QueryBuilders.termQuery("title.keyword", keyword).boost(4f));
            boolQuery.should(QueryBuilders.termQuery("actor.keyword", keyword).boost(2f));
            boolQuery.should(QueryBuilders.termQuery("director.keyword", keyword).boost(1f));
        }
        
        // 端外搜索，只搜正片
        boolQuery.must(QueryBuilders.termQuery("featureType", 1));

        // 低版本过滤
        if (param.getVersionTag() < searchConfig.getLetvAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_LETV));
        }
        if (param.getVersionTag() < searchConfig.getMgAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MGMOBILE));
        }
        if (param.getVersionTag() < searchConfig.getMiguOlympicAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MIGUOLMPIC));
        }
        if (param.getVersionTag() < searchConfig.getYstAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YST));
        }

        // 关键词搜索 风行和微迪欧 过滤
        if (param.getVersionTag() < searchConfig.getFunshionlongvideoAndWeidiouAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_WEIDIOU));
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_FUNSHION_LONGVIDEO));
        }

        // 一期搜索先屏蔽咪咕
        boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MIGUOLMPIC));

        // 关键词搜索 根据过滤条件进行更改
        if (youkuSourceFilterService.thirdPartyFilter(param.getQuickEngineVersion(), param.getVersion(), param.getAppId())) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YOUKU_MOBILE));
        }
        
        boolQuery.minimumShouldMatch(1);
        param.setSearchTypeEnum(searchTypeEnum);
        return boolQuery;
    }
}