package com.heytap.longvideo.search.liteflow.cmp;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.entity.LvAlbumListItem;
import com.heytap.longvideo.client.arrange.entity.MtvSubjectitem;
import com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.common.media.programsource.SourceVersionService;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.RecommendAlgorithmData;
import com.heytap.longvideo.search.model.RecommendAlgorithmResponse;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.model.param.app.TransparentInfo;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.UrlCoderUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.FEATURE_TYPE_YG;
import static com.heytap.longvideo.search.constants.SearchConstant.MARK_CODE_YG;

/*
 * Description 搜索推荐
 * Date 10:11 2023/7/21
 * Author songjiajia 80350688
 */
@Slf4j
@LiteflowComponent("searchRecommendCmp")
public class SearchRecommendCmp extends NodeComponent {

    @Autowired
    HttpDataChannel httpDataChannel;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private ArrangeRpcApiProxy arrangeRpcApiProxy;

    @Autowired
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Autowired
    private SearchIntentService searchIntentService;

    @Autowired
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor commonThreadPool;

    @Autowired
    DeepLinkUtils deepLinkUtils;

    @Autowired
    private CommonService commonService;

    @Autowired
    private YoukuSourceFilterService youkuSourceFilterService;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Autowired
    private SourceVersionService sourceVersionService;

    @Autowired
    private ConvertResponseService convertResponseService;

    @HeraclesDynamicConfig(key = "recommend.minVersion", fileName = "search_config.properties")
    private Integer recommendMinVersion = 61100;

    @HeraclesDynamicConfig(key = "album.detail.deepLink", fileName = "search_config.properties")
    private String detailDeepLink;

    @HeraclesDynamicConfig(key = "recommend.contents.max.pageIndex", fileName = "search_config.properties")
    private int maxPageIndex = 10;

    public static int IMGTYPE_VERTICAL = 2;

    private final static int PAGE_INDEX = 1;
    private final static int PAGE_SIZE = 12;

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (check(context.getInterveneConfigMap(), context.getRequestParam())) {
            handleSearchRecommend(context, context.getBaseSearchResult());
        }
    }


    public boolean check(Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap, KeyWordSearchParamV2 requestParam) {
        if (interveneConfigMap.containsKey(InterveneTypeEnum.SERIES) || interveneConfigMap.containsKey(InterveneTypeEnum.TAG)) {
            return false;
        }
        if (requestParam.getVersion() < recommendMinVersion || requestParam.getPageIndex() != 1
                || requestParam.getDeviceType() != 0 || !"1".equals(requestParam.getSearchType())) {
            return false;
        }
        return true;
    }


    public void handleSearchRecommend(SearchByKeyWordContext context, List<KeyWordSearchResponse> searchResult) {
        CompletableFuture<SearchInterveneCardResponse> cf;
        if (context.getInterveneConfigMap().containsKey(InterveneTypeEnum.RECOMMEND)) {
            cf = getItemByOperation(context, SortEnum.HOT.getType());
        } else {
            cf = getItemByAlgorithm(context.getRequestParam(), searchResult, SortEnum.HOT.getType());
        }
        SearchInterveneCardResponse response = FutureUtil.getFutureIgnoreException(cf);
        if (response != null && CollectionUtils.isNotEmpty(response.getContents()) && response.getContents().size() > 3) {
            SearchResponse searchResponse = context.getSearchResponse();
            searchResponse.setLongVideoRecommend(response);
        }
    }

    public CompletableFuture<SearchInterveneCardResponse> getItemByOperation(SearchByKeyWordContext context, Integer sortType) {
        LvSearchIntervene lvSearchIntervene = context.getInterveneConfigMap().get(InterveneTypeEnum.RECOMMEND);

        CompletableFuture<Pair<List<KeyWordSearchResponse>, Boolean>> completableFuture = null;
        Integer pageIndex = context.getRequestParam() == null ? PAGE_INDEX : context.getRequestParam().getPageIndex();
        if (TemplateLinkTypeEnum.SUBJECT.getCode() == lvSearchIntervene.getLinkType()) {
            completableFuture = getItemBySubject(lvSearchIntervene.getLinkValue(), pageIndex);
        } else if (TemplateLinkTypeEnum.SHEET.getCode() == lvSearchIntervene.getLinkType()) {
            completableFuture = getItemByAlbumList(lvSearchIntervene.getLinkValue(), pageIndex);
        } else if (TemplateLinkTypeEnum.CONTENT_POOL.getCode() == lvSearchIntervene.getLinkType()) {
            completableFuture = searchIntentService.getItemByContentPool(lvSearchIntervene.getLinkValue(), pageIndex, PAGE_SIZE);
        }
        return completableFuture.thenApplyAsync(pairResult -> {
            List<KeyWordSearchResponse> itemList = pairResult.getLeft();
            SearchInterveneCardResponse recommendResponse = new SearchInterveneCardResponse();
            recommendResponse.setTitle(lvSearchIntervene.getTitle());
            recommendResponse.setCode(String.valueOf(lvSearchIntervene.getId()));
            if (TemplateLinkTypeEnum.SUBJECT.getCode() == lvSearchIntervene.getLinkType() || TemplateLinkTypeEnum.SHEET.getCode() == lvSearchIntervene.getLinkType()) {

                recommendResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(lvSearchIntervene.getLinkType(), lvSearchIntervene.getLinkValue()));
            } else {
                recommendResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.RELATION_RECOMMEND.getCode(), String.valueOf(lvSearchIntervene.getId()), lvSearchIntervene.getTitle()));
            }
            recommendResponse.setContents(
                    handleItemList(itemList, Objects.equals(lvSearchIntervene.getIsShowMarkCode(), 1), pageIndex,
                            context.getRequestParam(), sortType));
            recommendResponse.setHasMore(pairResult.getRight());
            recommendResponse.setSortTypeList(searchProperties.getSortList());

            // 处理搜狐VIP内容
            commonService.handleSohuVipContents(recommendResponse);

            return recommendResponse;
        }, commonThreadPool);

    }


    public CompletableFuture<SearchInterveneCardResponse> getItemByAlgorithm(
            KeyWordSearchParamV2 request, List<KeyWordSearchResponse> searchResult, Integer sortType) {
        if (CollectionUtils.isEmpty(searchResult) || searchResult.get(0).getThirdDate() == 1) {
            return CompletableFuture.completedFuture(null);
        }
        List<KeyWordSearchResponse> returnList = new ArrayList<>();
        String sid = searchResult.get(0).getSid();

        CompletableFuture<RecommendAlgorithmResponse> future = queryAlgorithm(request, sid);
        return future.handle((response, e) -> {
            if (e != null || response == null || response.getStatus() == null || response.getStatus() != 0) {
                log.error("getRecommendAlgorithmSids error, sid:{}, response:{}", sid, response, e);
                return null;
            }
            if (CollectionUtils.isEmpty(response.getData())) {
                log.warn("requestAlgorithm returnDate null,sid:{}", sid);
                return null;
            }
            for (RecommendAlgorithmData datum : response.getData()) {
                KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
                keyWordSearchResponse.setSid(datum.getId());
                returnList.add(keyWordSearchResponse);
            }
            SearchInterveneCardResponse recommendResponse = new SearchInterveneCardResponse();
            // 增加算法透传字段
            recommendResponse.setAiSource(1);
            recommendResponse.setTransparent(response.getTransparent());
            List<KeyWordSearchResponse> contentList = handleItemList(
                    returnList, true, request.getPageIndex(), request, sortType);

            // 算法场景 优酷内容拼接算法透传字段
            commonService.addAlgorithmTransparentToDeepLink(recommendResponse);

            recommendResponse.setContents(contentList);
            recommendResponse.setTitle(request.getVersion() < searchProperties.getSearchCardOptVersion() ?
                    "相关影视推荐" : "根据 " + searchResult.get(0).getTitle() + " 为你推荐");
            //算法推荐，更多默认为true，10刷后设置为false
            recommendResponse.setHasMore(true);
            if (request.getPageIndex() >= maxPageIndex) {
                recommendResponse.setHasMore(false);
            }
            //将sid和keyword一起下发给客户端，进行透传
            TransparentInfo transparentInfo = new TransparentInfo();
            transparentInfo.setSid(sid);
            transparentInfo.setKeyword(request.getKeyword());
            recommendResponse.setCode(JSON.toJSONString(transparentInfo));
            recommendResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.RELATION_RECOMMEND.getCode(), UrlCoderUtil.encode(JSON.toJSONString(transparentInfo), StandardCharsets.UTF_8), recommendResponse.getTitle()));
            recommendResponse.setSortTypeList(searchProperties.getSortList());
            List<String> sidList = contentList.stream().map(KeyWordSearchResponse::getSid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sidList)) {
                return recommendResponse;
            }

            handleMaterialInfo(sidList, contentList);

            // 处理搜狐VIP内容
            commonService.handleSohuVipContents(recommendResponse);

            return recommendResponse;
        });

    }

    private CompletableFuture<RecommendAlgorithmResponse> queryAlgorithm(KeyWordSearchParamV2 request, String sid) {
        Map<String, String> params = new HashMap<>();
        params.put("r_dv", request.getDv());
        params.put("route", searchProperties.getRecommendAlgorithmRoute());
        params.put("bidlst", searchProperties.getRecommendAlgorithmBidlst());
        params.put("cid", searchProperties.getRecommendAlgorithmCid());
        params.put("r_predict_id", request.getRequestId());
        params.put("num", String.valueOf(PAGE_SIZE));
        if (StringUtils.isNotBlank(request.getBuuid())) {
            params.put("r_buuid", String.valueOf(request.getBuuid()));
        }
        params.put("r_page", String.valueOf(request.getPageIndex() - 1));
        params.put("docId", sid);
        params.put("query_keyword", request.getKeyword());
        if (request.getVersion() != null) {
            params.put("r_source_list", sourceVersionService.getSourceStrByVersion(request.getVersion()));
        }

        try {
            return httpDataChannel.asyncGetForObject(searchProperties.getRecommendAlgorithmUrl(), RecommendAlgorithmResponse.class, params, searchProperties.getRecommendAlgorithmTimeout());
        } catch (Exception e) {
            log.error("getRecommendAlgorithmSids error, the params:{}, the exception:{}", params, e);
            return CompletableFuture.completedFuture(null);
        }
    }

    private void handleMaterialInfo(List<String> sidList, List<KeyWordSearchResponse> contentList) {
        Map<String, LvContentMaterialVO> materialMap = new HashMap<>();
        RpcResult<Map<String, List<LvContentMaterialVO>>> results = FutureUtil.getFutureIgnoreException(
                arrangeRpcApiProxy.getMaterials(sidList, "0"), 1, TimeUnit.SECONDS);
        if (results == null || results.getCode() != 0 || MapUtils.isEmpty(results.getData())) {
            return;
        }
        Map<String, List<LvContentMaterialVO>> contentMaterialMap = results.getData();
        for (String s : contentMaterialMap.keySet()) {
            List<LvContentMaterialVO> contentMaterialList = contentMaterialMap.get(s).stream()
                    .filter(im -> IMGTYPE_VERTICAL == im.getImgStyle()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentMaterialList)) {
                continue;
            }
            for (LvContentMaterialVO lvContentMaterialVO : contentMaterialList) {
                if ("gif".equals(lvContentMaterialVO.getImgType())) {
                    continue;
                }
                materialMap.put(s, lvContentMaterialVO);
            }
        }
        for (KeyWordSearchResponse keyWordSearchResponse : contentList) {
            keyWordSearchResponse.setAiSource(1);
            if (materialMap.containsKey(keyWordSearchResponse.getSid())) {
                keyWordSearchResponse.setRecommendInfo(materialMap.get(keyWordSearchResponse.getSid()).getBrief());
                keyWordSearchResponse.setVerticalIcon(materialMap.get(keyWordSearchResponse.getSid()).getImgUrl());
            }
        }
    }

    /**
     * 当模板关联专题时，获取元素
     */
    public CompletableFuture<Pair<List<KeyWordSearchResponse>, Boolean>> getItemBySubject(String code, Integer pageIndex) {
        List<KeyWordSearchResponse> returnList = new ArrayList<>();
        return arrangeRpcApiProxy.getSubjectItemForListCardWithMore(code, TemplateLinkTypeEnum.ALBUM.getCode(), pageIndex, PAGE_SIZE).handleAsync((itemForListCardWithMoreVO, e) -> {
            if (itemForListCardWithMoreVO == null || CollectionUtils.isEmpty(itemForListCardWithMoreVO.getMtvSubjectItemList())) {
                return new ImmutablePair<>(returnList, false);
            }
            for (MtvSubjectitem item : itemForListCardWithMoreVO.getMtvSubjectItemList()) {
                KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
                keyWordSearchResponse.setSid(item.getLinkValue());
                keyWordSearchResponse.setTitle(item.getTitle());
                keyWordSearchResponse.setVerticalIcon(item.getVerticalIcon());
                keyWordSearchResponse.setHorizontalIcon(item.getHorizontalIcon());
                keyWordSearchResponse.setMarkCode(item.getSubscriptCode());
                returnList.add(keyWordSearchResponse);
            }
            return new ImmutablePair<>(returnList, itemForListCardWithMoreVO.getHasMore() == 1);
        });
    }


    /**
     * 当模板关联片单时，获取元素
     *
     * @return
     */
    public CompletableFuture<Pair<List<KeyWordSearchResponse>, Boolean>> getItemByAlbumList(String code, Integer pageIndex) {
        List<KeyWordSearchResponse> returnList = new ArrayList<>();
        return arrangeRpcApiProxy.getLvAlbumListByAlbumListCode(code, 1, pageIndex, PAGE_SIZE).handleAsync((result, e) -> {
            if (result == null || CollectionUtils.isEmpty(result.getRecords())) {
                return new ImmutablePair<>(returnList, false);
            }
            List<LvAlbumListItem> lvAlbumListItemList = result.getRecords();
            if (Objects.equals(lvAlbumListItemList.get(0).getLinkType(), 2)) {
                //关联内容池
                return FutureUtil.getFutureIgnoreException(searchIntentService.getItemByContentPool(lvAlbumListItemList.get(0).getLinkValue(), PAGE_INDEX, PAGE_SIZE));
            } else {
                for (LvAlbumListItem item : lvAlbumListItemList) {
                    KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
                    keyWordSearchResponse.setSid(item.getLinkValue());
                    keyWordSearchResponse.setTitle(item.getTitle());
                    keyWordSearchResponse.setRecommendInfo(item.getBrief());
                    keyWordSearchResponse.setVerticalIcon(item.getImageV());
                    keyWordSearchResponse.setHorizontalIcon(item.getImage());
                    keyWordSearchResponse.setMarkCode(item.getOperateCode());
                    returnList.add(keyWordSearchResponse);
                }
            }
            return new ImmutablePair<>(returnList, result.hasNext());
        });
    }

    private List<KeyWordSearchResponse> handleItemList(List<KeyWordSearchResponse> itemList, boolean isShowMarkCode,
                                                       Integer pageIndex, KeyWordSearchParamV2 request, Integer sortType) {
        if (CollectionUtils.isEmpty(itemList) || (itemList.size() < 4 && pageIndex == 1)) {
            log.info("getSearchRecommend itemList empty,size:{}", CollectionUtils.isEmpty(itemList) ? 0 : itemList.size());
            return Collections.emptyList();
        }
        List<String> sidList = itemList.stream().map(KeyWordSearchResponse::getSid).collect(Collectors.toList());
        Map<String, StandardAlbum> standardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);
        Iterator<KeyWordSearchResponse> it = itemList.iterator();
        while (it.hasNext()) {
            KeyWordSearchResponse keyWordSearchResponse = it.next();
            StandardAlbum standardAlbum = standardAlbumMap.get(keyWordSearchResponse.getSid());
            // 过滤节目
            if (!filter(standardAlbum, request.getVersion())) {
                it.remove();
                continue;
            }
            buildKeyWordSearchResponse(keyWordSearchResponse, standardAlbum, request, isShowMarkCode);
        }

        convertResponseService.handleSubTitle(itemList, request.getVersion());
        convertResponseService.sort(itemList, sortType, standardAlbumMap, request.getVersion());

        return itemList;
    }

    private boolean filter(StandardAlbum standardAlbum, Integer version) {
        if (standardAlbum == null) {
            return false;
        }
        // 推荐卡 过滤风行和微迪欧
        if (funshionLongVideoAndWeidiouFilterService.filterItemBySource(standardAlbum.getSource(), version)) {
            return false;
        }
        // 推荐卡 根据开关屏蔽优酷
        if (youkuSourceFilterService.filterItemBySource(standardAlbum.getSource(), version)) {
            return false;
        }
        if ("ztv".equals(standardAlbum.getSource())) {
            return false;
        }
        return true;
    }

    private void buildKeyWordSearchResponse(KeyWordSearchResponse keyWordSearchResponse, StandardAlbum standardAlbum,
                                            KeyWordSearchParamV2 request,  boolean isShowMarkCode) {
        setDeepLink(keyWordSearchResponse, standardAlbum, request);

        if (StringUtils.isBlank(keyWordSearchResponse.getTitle())) {
            keyWordSearchResponse.setTitle(standardAlbum.getTitle());
        }
        if (StringUtils.isBlank(keyWordSearchResponse.getHorizontalIcon())) {
            keyWordSearchResponse.setHorizontalIcon(standardAlbum.getHorizontalIcon());
        }
        if (StringUtils.isBlank(keyWordSearchResponse.getVerticalIcon())) {
            keyWordSearchResponse.setVerticalIcon(standardAlbum.getVerticalImage());
        }
        if (StringUtils.isBlank(keyWordSearchResponse.getRecommendInfo())) {
            keyWordSearchResponse.setRecommendInfo(standardAlbum.getBrief());
        }
        if (StringUtils.isBlank(keyWordSearchResponse.getProgramInfo())) {
            keyWordSearchResponse.setProgramInfo(standardAlbum.getProgramInfo());
        }
        convertResponseService.handleBackgroundColor(keyWordSearchResponse,standardAlbum.getBackGroundColorJson());
        keyWordSearchResponse.setSourceScore(StringUtils.isBlank(standardAlbum.getSourceScore()) ? 0.0F : Float.parseFloat(standardAlbum.getSourceScore()));
        keyWordSearchResponse.setFeatureType(standardAlbum.getFeatureType());
        if (!isShowMarkCode && FEATURE_TYPE_YG != keyWordSearchResponse.getFeatureType()) {
            keyWordSearchResponse.setMarkCode("");
            keyWordSearchResponse.setMarkCodeUrl("");
        } else {
            if (StringUtils.isBlank(keyWordSearchResponse.getMarkCode())) {
                keyWordSearchResponse.setMarkCode(standardAlbum.getMarkCode());
            }

            if (FEATURE_TYPE_YG == keyWordSearchResponse.getFeatureType()) {
                keyWordSearchResponse.setMarkCode(MARK_CODE_YG);
            }

            if (StringUtils.isNotBlank(keyWordSearchResponse.getMarkCode())) {
                keyWordSearchResponse.setMarkCodeUrl(imageTagRpcApiProxy.getImageUrl(keyWordSearchResponse.getMarkCode()));
            }
        }
        keyWordSearchResponse.setContentType(standardAlbum.getProgramType());
        keyWordSearchResponse.setSource(standardAlbum.getSource());
        keyWordSearchResponse.setCopyrightCode(standardAlbum.getSource());
        keyWordSearchResponse.setPayStatus(standardAlbum.getPayStatus());
        keyWordSearchResponse.setFeatureType(standardAlbum.getFeatureType());
        keyWordSearchResponse.setAlbumFeatureType(standardAlbum.getFeatureType());
        convertResponseService.handleShowMsg(keyWordSearchResponse, false, request.getVersion());
    }

    private void setDeepLink(KeyWordSearchResponse keyWordSearchResponse, StandardAlbum standardAlbum, KeyWordSearchParamV2 request) {
        // 推荐卡 设置deepLink
        if (youkuSourceFilterService.setDeepLinkFilter(standardAlbum.getSource(), request.getQuickEngineVersion(), request.getVersion())) {
            keyWordSearchResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), standardAlbum.getSourceWebUrl()));
        } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(standardAlbum.getSource())) {
            keyWordSearchResponse.setDeepLink(youkuSourceFilterService.getUpGradeUrl(standardAlbum.getSid(), standardAlbum.getSource(), standardAlbum.getTitle()));
        }
        if (!SourceEnum.YOUKU_MOBILE.getDataSource().equals(standardAlbum.getSource())) {
            keyWordSearchResponse.setDeepLink(String.format(detailDeepLink, standardAlbum.getSid()));
        }
    }
}