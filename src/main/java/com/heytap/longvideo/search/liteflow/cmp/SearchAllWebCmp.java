package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.search.api.LvSearchKeywordRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchKeyword;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.common.lib.rpc.ResultCode;
import com.heytap.longvideo.search.constants.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.outside.OutSideSearchButtonConfig;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.heytap.longvideo.search.utils.SensitiveWordUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.longvideo.search.utils.UrlCoderUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.common.context.RpcContext;
import esa.rpc.common.exception.ESATimeoutException;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.heytap.longvideo.client.constant.SourceConstant.SOURCE_IQIYI;

import static com.heytap.longvideo.search.constants.OutSideSearchConstant.NON_INSTOCK_DETAIL_CERSION;
import static com.heytap.longvideo.search.constants.SearchConstant.*;
import static com.heytap.longvideo.search.utils.IllegalCharacterCheckerUtil.containsInvalidCharacters;


/*
 * Description 全网搜（搜索非合作内容，包括干预节目+普通节目）
 * Date 9:38 2023/1/30
 * Author songjiajia 80350688
 */
@LiteflowComponent("allWebSearchCmp")
@Slf4j
public class SearchAllWebCmp extends NodeComponent {

    @HeraclesDynamicConfig(key = "outside.browser.search.all.web.switch", fileName = "search_config.properties")
    private boolean browserSearchAllWebSwitch = false;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private HotVideoService hotVideoService;

    @Autowired
    private ConvertResponseService convertResponseService;

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 0, timeout = 1000)
    private LvSearchKeywordRpcApi lvSearchKeywordRpcApi;

    static Pattern numberPattern;

    /**
     * 视频APP版本低于8.7，跳转到搜索结果页面，并把query词带入到页面内
     */
    @HeraclesDynamicConfig(key = "outside.search.yoli.search.deeplink", fileName = "search_config.properties")
    private String yoliSearchDeepLink = "yoli://yoli.com/YoliSearch/search?query=%s&openfrom=%s&showSplashAd=0";

    /**
     * AppV8.7及以上版本,跳转非在库内容详情页
     */
    @HeraclesDynamicConfig(key = "outside.search.non.instock.detail.deeplink", fileName = "search_config.properties")
    private String nonInStockDetailDeepLink = "yoli://yoli.com/detail/longvideo/outofstockvideodetail?linkValue=%s&title=%s&openfrom=%s&showSplashAd=0";

    /**
     * 全网搜默认推荐语
     */
    @HeraclesDynamicConfig(key = "outside.search.default.brief", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, List<String>> defaultBriefMap;

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        KeyWordSearchParamV2 param = context.getRequestParam();
        if (!check(param)) {
            return;
        }

        // 来自于锁屏、小布、全搜的搜索请求
        if (SearchConstant.STRATEGY_CONTROL_SEARCH_SOURCE.contains(param.getAppId())) {
            if (SEARCH_FROM_BREENO.equals(param.getAppId())) {

                if ((MapUtils.isEmpty(context.getSearchNetResults()) || (MapUtils.isNotEmpty(context.getSearchNetResults()) && !Objects.equals(context.getSearchNetResults().get(param.getAppId()), 1)))
                        && !searchProperties.isBreenoSearchIqiyiMmobileSwitch()) {
                    // 对于小布 这种情况下不出全网搜
                    return;
                }
                context.setAllWebInterveneResult(queryInterveneResult(context.getRequestParam(), context.getBaseSearchResult(), context.getOutSideSearchButtonConfig(), context.getSearchNetResults()));
                context.setAllWebSearchResult(querySearchResult(param, context.getBaseSearchResult(), context.getAllWebInterveneResult(), context.getOutSideSearchButtonConfig(), context.getSearchNetResults()));
                return;
            } else {
                if (MapUtils.isEmpty(context.getSearchNetResults())) {
                    return;
                }

                if (Objects.equals(context.getSearchNetResults().get(param.getAppId()), 1)) {
                    context.setAllWebInterveneResult(queryInterveneResult(context.getRequestParam(), context.getBaseSearchResult(), context.getOutSideSearchButtonConfig(), context.getSearchNetResults()));
                    context.setAllWebSearchResult(querySearchResult(param, context.getBaseSearchResult(), context.getAllWebInterveneResult(), context.getOutSideSearchButtonConfig(), context.getSearchNetResults()));
                }
                return;
            }

        }

        if ((Objects.equals(SearchConstant.SEARCH_FROM_BROWSER, param.getAppId()) && (!browserSearchAllWebSwitch || param.getVersion() < NON_INSTOCK_DETAIL_CERSION))) {
            return;
        }
        context.setAllWebInterveneResult(queryInterveneResult(context.getRequestParam(), context.getBaseSearchResult(), context.getOutSideSearchButtonConfig(), null));
        context.setAllWebSearchResult(querySearchResult(param, context.getBaseSearchResult(), context.getAllWebInterveneResult(), context.getOutSideSearchButtonConfig(), null));
    }

    static {
        numberPattern = Pattern.compile("\\d+");
    }

    public boolean check(KeyWordSearchParamV2 param) {
        if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(param.getAppId())) {
            return true;
        }
        if (param.getVersion() < 60300 || param.getPageIndex() != 1) {
            return false;
        }
        if (StringUtil.isBlank(param.getKeyword()) || param.getKeyword().length() > 20) {
            return false;
        }
        Integer searchSwitch = searchProperties.getSearchSwitchConfig().get(String.valueOf(param.getDeviceType()));
        return searchSwitch != null && searchSwitch != 0;
    }

    /**
     * 获取非合作内容的干预配置（arrange查数据库）
     */
    private List<LvSearchKeyword> getLvSearchKeywordList(String keyword) {
        try {
            RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
            RpcResult<List<LvSearchKeyword>> lvSearchKeywordRpcResult = lvSearchKeywordRpcApi.selectThirdSearchKeyWordList("pre_" + keyword);
            if (lvSearchKeywordRpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("lvSearchKeywordRpcResult error:{}", JacksonUtil.toJSONString(lvSearchKeywordRpcResult));
                return Collections.emptyList();
            }
            return lvSearchKeywordRpcResult.getData();
        } catch (ESATimeoutException e) {
            // 该接口涉及MySQL全文搜索，经常超时，为避免干扰告警，超时返空
            log.error("selectThirdSearchKeyWordList timeout", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查非合作干预内容
     */
    public List<KeyWordSearchResponse> queryInterveneResult(KeyWordSearchParamV2 param, List<KeyWordSearchResponse> baseSearchResult, OutSideSearchButtonConfig outSideSearchButtonConfig, Map<String, Integer> searchNetResults) {
        List<KeyWordSearchResponse> interveneResult = new ArrayList<>();

        try {
            // 查询干预内容
            List<LvSearchKeyword> lvSearchKeywordList = getLvSearchKeywordList(param.getKeyword());
            interveneResult = lvSearchKeywordList.stream()
                    .filter(item -> item.getOrderIndex() >= 1 && item.getOrderIndex() <= 10)
                    .map(item -> {
                        List<UnofficialAlbumEs> result = getUnofficialAlbumEsList(item.getSid(), null, param.getAppId(), searchNetResults);
                        if (CollectionUtils.isEmpty(result)) {
                            return null;
                        }
                        UnofficialAlbumEs es = result.get(0);
                        if (isRepeat(baseSearchResult, es, true)) {
                            return null;
                        }
                        return convertEs2SearchResponse(es, item.getOrderIndex(), param.getVersion(), param.getAppId(), outSideSearchButtonConfig, searchNetResults);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            log.info("queryInterveneResult requestId={} keyword={}, interveneResult={}",
                    param.getRequestId(), param.getKeyword(), JacksonUtil.toJSONString(interveneResult));
        } catch (Exception e) {
            log.error("getAllWebSearchResultV2 error,keyword=" + param.getKeyword(), e);
        }

        return interveneResult;
    }

    /**
     * 端内全搜和端外全搜分别控制,是否出指定源的节目--电视，全网，端内
     *
     * @param appid
     * @param searchNetResults
     * @return
     */
    public Map<String, Map<String, Integer>> getThirdSearchMap(String appid, Map<String, Integer> searchNetResults) {
        if (SEARCH_FROM_QUANSOU.equals(appid) || SEARCH_FROM_BROWSER.equals(appid) ||
                SEARCH_FROM_MAGAZINE.equals(appid)) {
            return searchProperties.getOutSideThirdSearchMap();
        }
        if (SEARCH_FROM_BREENO.equals(appid)) {
            return getThirdSearchMapForBreeno(appid, searchNetResults);
        }

        // 端内全网搜的配置
        return searchProperties.getThirdSearchMap();
    }

    private Map<String, Map<String, Integer>> getThirdSearchMapForBreeno(String appid, Map<String, Integer> searchNetResults) {
        //此处复制map防止remove影响原来的配置(注意LinkedHashMap不影响顺序)
        LinkedHashMap<String, Map<String, Integer>> copyMap = new LinkedHashMap<>(searchProperties.getOutSideThirdSearchMap());
        // 小布的全网搜不包含电视端的爱奇艺
        copyMap.remove(SOURCE_IQIYI);
        if ((MapUtils.isEmpty(searchNetResults) || (MapUtils.isNotEmpty(searchNetResults) && !Objects.equals(searchNetResults.get(appid), 1)))
                && searchProperties.isBreenoSearchIqiyiMmobileSwitch()) {
            // 情况1：不返回全网搜 但是只返回移动端爱奇艺
            Map<String, Integer> iqiyiMobileMap = copyMap.get(SourceEnum.IQIYI_MOBILE.getDataSource());
            Map<String, Map<String, Integer>> map = new HashMap<>();
            map.put(SourceEnum.IQIYI_MOBILE.getDataSource(), iqiyiMobileMap);
            return map;
        }

        if (!searchProperties.isBreenoSearchIqiyiMmobileSwitch()) {
            // 情况2 & 3：返回全网搜(根据配置决定是否包含移动端爱奇艺)
            copyMap.remove(SourceEnum.IQIYI_MOBILE.getDataSource());
        }
        return copyMap;
    }

    /**
     * 查非合作匹配内容
     */
    public List<KeyWordSearchResponse> querySearchResult(KeyWordSearchParamV2 param,
                                                         List<KeyWordSearchResponse> baseSearchResult, List<KeyWordSearchResponse> interveneResult, OutSideSearchButtonConfig outSideSearchButtonConfig, Map<String, Integer> searchNetResults) {
        List<KeyWordSearchResponse> searchResult = new ArrayList<>();
        try {
            List<UnofficialAlbumEs> esList = getUnofficialAlbumEsList(null, param.getKeyword(), param.getAppId(), searchNetResults);

            List<String> sourcePriority = new ArrayList<>(getThirdSearchMap(param.getAppId(), searchNetResults).keySet());
            List<String> doubanPriority = searchProperties.getDoubanPriority();
            searchResult = esList.stream()
                    .filter(es -> StringUtil.isNotBlank(es.getManualWebUrl()) || !"douban".equals(es.getSource()) || doubanPriority.contains(es.getCopyrightCode()))
                    .filter(es -> StringUtil.isNotBlank(es.getSourceWebUrl()) || StringUtil.isNotBlank(es.getManualWebUrl()))
                    // 与合作内容去重 + 与非合作干预内容去重
                    .filter(es -> !isRepeat(baseSearchResult, es, false) && !isRepeat(interveneResult, es, false))
                    // 内部去重，标题与品类相同时，按配置的源优先级
                    .collect(Collectors.toMap(
                            es -> es.getTitle() + es.getProgramType(),
                            es -> es,
                            (es1, es2) -> sourcePriority.indexOf(es1.getSource()) < sourcePriority.indexOf(es2.getSource()) ? es1 :
                                    "douban".equals(es1.getSource()) &&
                                            doubanPriority.indexOf(es1.getCopyrightCode()) < doubanPriority.indexOf(es2.getCopyrightCode()) ? es1 :
                                            es2))
                    .values().stream()
                    .map(es -> convertEs2SearchResponse(es, null, param.getVersion(), param.getAppId(), outSideSearchButtonConfig, searchNetResults))
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(KeyWordSearchResponse::getSortDefine).reversed())
                    .collect(Collectors.toList());
            log.info("querySearchResult requestId={} keyword={}, searchResult={}",
                    param.getRequestId(), param.getKeyword(), JacksonUtil.toJSONString(searchResult));
        } catch (Exception e) {
            log.error("getAllWebSearchResultV2 error,keyword=" + param.getKeyword(), e);
        }
        return searchResult;
    }


    public List<UnofficialAlbumEs> getUnofficialAlbumEsList(String sid, String title, String appid, Map<String, Integer> searchNetResults) {
        List<UnofficialAlbumEs> result = new ArrayList<>();
        // 构造参数
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtil.isNotBlank(sid)) {
            // 全网搜干预内容搜索 要求switch>=1
            List<String> sourceList = getThirdSearchMap(appid, searchNetResults).entrySet().stream()
                    .filter(entry -> entry.getValue().getOrDefault("switch", 0) >= 1)
                    .map(Map.Entry::getKey)
                    .filter(source -> filterSource4BrowserPlay(appid, source))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sourceList)) {
                return result;
            }
            boolQuery.must(QueryBuilders.termsQuery("source", sourceList));
            boolQuery.must(QueryBuilders.termQuery("sid", sid));
            // 源状态=可用
            boolQuery.must(QueryBuilders.termQuery("sourceStatus", 1));
            // 状态=生效
            boolQuery.must(QueryBuilders.termQuery("status", 1));
        } else if (StringUtil.isNotEmpty(title)) {
            // 普通搜索 要求switch=2
            List<String> sourceList = getThirdSearchMap(appid, searchNetResults).entrySet().stream()
                    .filter(entry -> entry.getValue().getOrDefault("switch", 0) == 2)
                    .map(Map.Entry::getKey)
                    .filter(source -> filterSource4BrowserPlay(appid, source))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sourceList)) {
                return result;
            }

            buildQueryByTitle(title, boolQuery, sourceList);
        }
        // 全搜场景，不下发横图为空（字段不存在，为null, 为“”）的数据
        if (SEARCH_FROM_QUANSOU.equals(appid)) {
            boolQuery.must(
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.existsQuery("horizontalIcon"))
                            .mustNot(QueryBuilders.termQuery("horizontalIcon", ""))
            );
        }
        // 小布 不出豆瓣爱奇艺
        if (SEARCH_FROM_BREENO.equals(appid)) {
            boolQuery.mustNot(
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("source", UnofficialMediaConstant.SOURCE_DOUBAN))
                            .must(QueryBuilders.termQuery("copyrightCode", CopyrightConstant.COPYRIGHT_IQIYI))
            );
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        PageRequest pageRequest = PageRequest.of(0, 50);
        NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQuery)
                .withPageable(pageRequest)
                .build();
        // 查询ES
        SearchHits<UnofficialAlbumEs> searchHits = restTemplate.search(searchQuery, UnofficialAlbumEs.class);
        for (SearchHit<UnofficialAlbumEs> searchHit : searchHits.getSearchHits()) {
            UnofficialAlbumEs es = searchHit.getContent();
            // 计算排序分
            Float sortDefine = calcSortDefine(es, title, searchHit.getScore());
            es.setSortDefine(sortDefine);
            result.add(es);
        }
        return result;
    }

    /**
     * 搜推场景只下发非合作官方源
     * @param appid
     * @param source
     * @return
     */
    private boolean filterSource4BrowserPlay(String appid, String source) {
        if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(appid)) {
            return UnofficialMediaConstant.SOURCE_DOUBAN.equals(source);
        }
        return true;
    }

    private void buildQueryByTitle(String title, BoolQueryBuilder boolQuery, List<String> sourceList) {
        if (title.contains(" ")) {
            title = title.replaceAll(" +", " ");
            String[] keywords = title.split(" ");
            for (int i = 0; i < keywords.length; i++) {
                if (i <= 1) {
                    boolQuery.must(QueryBuilders.multiMatchQuery(keywords[i], "title").minimumShouldMatch("80%"));
                }
            }
        } else {
            if (title.length() >= 4) {
                // ES最低匹配数为向下取整，如搜索词长度为4，乘80%等于3.2，则只需3个词匹配则满足条件
                boolQuery.should(QueryBuilders.matchQuery("title", title).boost(4f).operator(Operator.OR).minimumShouldMatch("80%"));
            }
            boolQuery.should(QueryBuilders.matchPhraseQuery("title", title).boost(4f));
            boolQuery.minimumShouldMatch(1);
        }
        boolQuery.must(QueryBuilders.termsQuery("source", sourceList));
        boolQuery.must(QueryBuilders.existsQuery("sourceWebUrl"));
        // 源状态=可用
        boolQuery.must(QueryBuilders.termQuery("sourceStatus", 1));
        // 状态=生效
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        // 正片
        boolQuery.must(QueryBuilders.termQuery("featureType", 1));
    }

    /**
     * 将ES结果转为搜索结果
     */
    public KeyWordSearchResponse convertEs2SearchResponse(UnofficialAlbumEs es, Integer orderIndex, Integer version, String appid, OutSideSearchButtonConfig outSideSearchButtonConfig, Map<String, Integer> searchNetResults) {
        Map<String, Integer> searchConfig = getThirdSearchMap(appid, searchNetResults).get(es.getSource());
        if (MapUtils.isEmpty(searchConfig)) {
            return null;
        }

        KeyWordSearchResponse searchResponse = new KeyWordSearchResponse();
        searchResponse.setSid(es.getSid());
        searchResponse.setTitle(es.getTitle());
        searchResponse.setDirectors(es.getDirector());
        searchResponse.setSource(es.getSource());
        searchResponse.setVerticalIcon(StringUtils.isBlank(es.getVerticalIconOcs()) ? es.getVerticalIcon() : es.getVerticalIconOcs());
        searchResponse.setArea(es.getArea());
        searchResponse.setYear(es.getYear());
        searchResponse.setCopyrightCode(es.getCopyrightCode());
        searchResponse.setContentType(es.getProgramType());
        searchResponse.setHorizontalIcon(es.getHorizontalIcon());
        String programInfo = es.getProgramInfo();
        if (StringUtil.isNotBlank(programInfo) && !programInfo.contains(" ")) {
            Matcher matcher = numberPattern.matcher(programInfo);
            if (matcher.find()) {
                programInfo = programInfo.substring(0, matcher.start()) + " " +
                        programInfo.substring(matcher.start(), matcher.end()) + " " +
                        programInfo.substring(matcher.end());
            }
        }
        searchResponse.setProgramInfo(programInfo);
        searchResponse.setLanguages(es.getLanguage());
        searchResponse.setStars(es.getActor());
        searchResponse.setTags(es.getMappingTags());
        searchResponse.setFeatureType(es.getFeatureType());
        // 【坑】外层转换时用的albumFeatureType，需要赋值
        searchResponse.setAlbumFeatureType(es.getFeatureType());
        searchResponse.setBrief(es.getBrief());
        searchResponse.setSortDefine(Double.valueOf(es.getSortDefine()));
        searchResponse.setSourceScore(StringUtil.isBlank(es.getSourceScore()) ? 0.0F : Float.parseFloat(es.getSourceScore()));
        if (orderIndex != null) {
            searchResponse.setSortIndex(Math.max(orderIndex - 1, 0));
        }
        searchResponse.setThirdDate(1);
        searchResponse.setButtonStatus(2);
        searchResponse.setButtonText("全网搜");
        searchResponse.setSourceAlbumId(es.getSourceAlbumId());

        convertResponseService.handleSourceKind(searchResponse);

        // 对外搜索使用
        if (SEARCH_FROM_QUANSOU.equals(appid) || SEARCH_FROM_BROWSER.equals(appid)) {
            setBrief(es, searchResponse);
            if (outSideSearchButtonConfig != null) {
                searchResponse.setButtonWord(outSideSearchButtonConfig.getOutOfStockText());
            }
        }
        setJumpLinkForInSideOrOutSide(searchResponse, es, version, searchConfig.get("jump"), appid);

        return searchResponse;
    }

    protected void setBrief(UnofficialAlbumEs es, KeyWordSearchResponse searchResponse) {
        if (StringUtils.isNotEmpty(es.getBrief())) {
            searchResponse.setBrief(es.getBrief());
            return;
        }
        if (StringUtils.isEmpty(es.getActor())) {
            // 默认推荐语
            addDefaultBrief(searchResponse, es.getProgramType());
        } else {
            ArrayList<String> brief = new ArrayList<>();
            String[] actors = es.getActor().split("\\|");
            for (String actor : actors) {
                if (containsInvalidCharacters(actor)) {
                    continue;
                }
                brief.add(actor);
                if (brief.size() == 3) {
                    break;
                }
            }
            if (CollectionUtils.isEmpty(brief)) {
                // 默认推荐语
                addDefaultBrief(searchResponse, es.getProgramType());
            } else {
                String join = String.join(" ", brief);
                searchResponse.setBrief(join);
            }
        }
    }

    private void addDefaultBrief(KeyWordSearchResponse searchResponse, String programType) {
        List<String> briefList = defaultBriefMap.get(programType);
        if (CollectionUtils.isEmpty(briefList)) {
            briefList = defaultBriefMap.get("other");
        }
        if (CollectionUtils.isNotEmpty(briefList)) {
            Random random = new Random();
            int rangeIndex = random.nextInt(briefList.size());
            searchResponse.setBrief(briefList.get(rangeIndex));
        } else {
            log.warn("warn: default brief list is null");
        }
    }


    private Float calcSortDefine(UnofficialAlbumEs es, String keyword, Float score) {
        Float sortDefine = score;
        if (StringUtil.isBlank(keyword)) {
            // 搜索词为空，说明是指定sid的干预查询，无需计算排序分
            return sortDefine;
        } else if (keyword.equals(es.getTitle())) {
            sortDefine = sortDefine * 100;
        } else if (es.getTitle().startsWith(keyword)) {
            sortDefine = sortDefine * 2.5f;
        }
        if (searchProperties.getHotKeyWordSwitch() == 1) {
            addPriorityByHotVideo(es);
        }

        // 非合作内容，权重降低
        return sortDefine * searchProperties.getThirdSearchScore();
    }

    public void setJumpLinkForInSideOrOutSide(KeyWordSearchResponse searchResponse, UnofficialAlbumEs es, Integer version, Integer jump, String appid) {
        setJumpLinkForInSide(searchResponse, es, version, jump);
        setJumpLinkForOutSide(searchResponse, es, version, appid);
    }

    /**
     * 端内跳转链接设置
     */
    public void setJumpLinkForInSide(KeyWordSearchResponse searchResponse, UnofficialAlbumEs es, Integer version, Integer jump) {
        if (version < 80000) {
            // 客户端版本低于8.0，固定跳转浏览器搜索页
            searchResponse.setDeepLink(String.format(searchProperties.getBrowserDeepLink(), searchResponse.getTitle()));
        } else {
            if (jump == 0) {
                // APP打开三方H5页
                if (CopyrightConstant.COPYRIGHT_IQIYI.equals(es.getSource())) {
                    // 媒资库中没有爱奇艺的播放页地址，只能手动拼搜索页
                    es.setSourceWebUrl(String.format(searchProperties.getIqiyiSearchUrl(), es.getTitle()));
                }
                searchResponse.setWebUrl(es.getSourceWebUrl());
            } else if (jump == 1) {
                // 浏览器打开搜索页
                searchResponse.setDeepLink(String.format(searchProperties.getBrowserDeepLink(), searchResponse.getTitle()));
            } else if (jump == 2) {
                // 浏览器打开三方H5页
                if (CopyrightConstant.COPYRIGHT_IQIYI.equals(es.getSource())) {
                    es.setSourceWebUrl(String.format(searchProperties.getIqiyiSearchUrl(), es.getTitle()));
                }
                // 处理人工添加的webUrl
                if (StringUtils.isNotEmpty(es.getManualWebUrl()) && (StringUtils.isEmpty(es.getSourceWebUrl()) || es.getManualWebUrlPriority())) {
                    es.setSourceWebUrl(es.getManualWebUrl());
                }
                // 嵌套的URL需要进行编码，否则会被外层误识别
                String url = UrlCoderUtil.encode(es.getSourceWebUrl());
                searchResponse.setDeepLink(String.format(searchProperties.getBrowserUrlLink(), url));
            } else if (jump == 3) {
                // APP打开百度搜索页
                searchResponse.setWebUrl(String.format(searchProperties.getBaiduSearchUrl(), searchResponse.getTitle()));
            }
        }
    }

    /**
     * 端外跳转链接设置
     */
    public void setJumpLinkForOutSide(KeyWordSearchResponse searchResponse, UnofficialAlbumEs es, Integer version, String appid) {
        // 全搜场景下发搜索页/非在库详情页
        if (SEARCH_FROM_QUANSOU.equals(appid)) {
            if (version < NON_INSTOCK_DETAIL_CERSION) {
                searchResponse.setDeepLink(String.format(yoliSearchDeepLink, searchResponse.getTitle(), SEARCH_FROM_QUANSOU));
            } else {
                searchResponse.setDeepLink(String.format(nonInStockDetailDeepLink, es.getSid(), es.getTitle(), SEARCH_FROM_QUANSOU));
            }
        }
        // 浏览器搜索直达场景下发非在库详情页
        else if (SEARCH_FROM_BROWSER.equals(appid)) {
            searchResponse.setDeepLink(String.format(nonInStockDetailDeepLink, es.getSid(), es.getTitle(), SEARCH_FROM_BROWSER));
        }
        // 浏览器播放场景直接下发三方链接
        else if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(appid)) {
            searchResponse.setDeepLink(es.getSourceWebUrl());
        }
    }

    private boolean isRepeat(List<KeyWordSearchResponse> baseSearchResult, UnofficialAlbumEs es, boolean intervene) {
        for (KeyWordSearchResponse keyWordSearchResponse : baseSearchResult) {
            if (keyWordSearchResponse.getTitle().equals(es.getTitle()) &&
                    keyWordSearchResponse.getContentType().equals(es.getProgramType())) {
                // 针对干预内容的去重更宽松，年份不一样就不算重复
                if (!intervene || keyWordSearchResponse.getYear().equals(es.getYear())) {
                    return true;
                }
            }
        }
        return false;
    }

    public void addPriorityByHotVideo(UnofficialAlbumEs es) {
        try {
            ContentTypeEnum contentTypeEnum = ContentTypeEnum.getByCode(es.getProgramType());
            if (contentTypeEnum == null) {
                return;
            }
            ConcurrentHashMap concurrentHashMap = hotVideoService.getHashMap(contentTypeEnum);
            if (concurrentHashMap == null) {
                return;
            }
            boolean isHotKey = SensitiveWordUtil.isContaintSensitiveWord(es.getTitle(), concurrentHashMap);
            if (isHotKey) {
                es.setSortDefine(es.getSortDefine() * 3);
            }
        } catch (Exception e) {
            log.error("addPriorityByHotVideo error", e);
        }
    }
}
