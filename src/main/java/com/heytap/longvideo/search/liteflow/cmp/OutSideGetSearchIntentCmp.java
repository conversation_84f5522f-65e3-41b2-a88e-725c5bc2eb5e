package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.common.lib.rpc.ResultCode;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/*
 * Description 获取搜索人工干预意图-端外
 * Date 10:11 2024/9/20
 * Author chuanxw
 */
@Slf4j
@LiteflowComponent("outSideGetSearchIntentCmp")
public class OutSideGetSearchIntentCmp extends NodeComponent {

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 0, timeout = 1000)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (Objects.isNull(context)) {
            return;
        }

        KeyWordSearchParamV2 param = context.getRequestParam();
        if (Objects.isNull(param)) {
            return;
        }

        if (param.getPageIndex() != 1) {
            return;
        }

        Map<InterveneTypeEnum, LvSearchIntervene> map = FutureUtil.getFutureIgnoreException(getSearchIntervene(context.getRequestParam()));
        context.setInterveneConfigMap(map);
    }

    /**
     * 根据关键字，查询搜索干预结果配置数据
     */
    public CompletableFuture<Map<InterveneTypeEnum, LvSearchIntervene>> getSearchIntervene(KeyWordSearchParamV2 request) {
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getSearchInterveneList(request.getKeyword()).handleAsync((rpcResult, e) -> {
            Map<InterveneTypeEnum, LvSearchIntervene> map = new HashMap<>();
            if (e != null || rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getSearchIntervene error, the request:{}, the response:{}", request.getKeyword(), rpcResult, e);
                return map;
            }
            List<LvSearchIntervene> interveneList = rpcResult.getData();
            if (CollectionUtils.isEmpty(interveneList)) {
                return map;
            }
            // 只保留搜索结果干预，并且id为最小的数据
            interveneList.stream()
                    .filter(intervene -> Objects.equals(InterveneTypeEnum.NORMAL.getCode(), intervene.getInterveneType()))
                    .forEach(intervene -> map.merge(
                            InterveneTypeEnum.NORMAL,
                            intervene,
                            (existing, newOne) -> existing.getId() < newOne.getId() ? existing : newOne
                    ));
            return map;
        });
    }


}