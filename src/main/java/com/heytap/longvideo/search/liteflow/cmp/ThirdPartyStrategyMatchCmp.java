package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.search.constants.ThirdPartyMediaTypeEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.thirdparty.ThirdPartyStrategyService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.heytap.longvideo.search.constants.ThirdPartyConstant.THIRD_PARTY_DETAIL_PAGE_VERSION;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
@Slf4j
@LiteflowComponent("thirdPartyStrategyMatchCmp")
public class ThirdPartyStrategyMatchCmp extends NodeComponent {

    @Autowired
    private ThirdPartyStrategyService thirdPartyStrategyService;

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        KeyWordSearchParamV2 request = context.getRequestParam();
        if (StringUtils.isBlank(request.getVersionName()) || StringUtils.isBlank(request.getAppId())) {
            log.warn("versionName is null , or appId is null, request:{}", request.toString());
            return;
        }

        // 低版本无法跳转至新详情页，故直接不请求三方跳转新详情页配置策略
        if (request.getVersion() < THIRD_PARTY_DETAIL_PAGE_VERSION) {
            log.info("versionName is null , or appId is null, request:{}", request.toString());
            return;
        }

        if (request.getAttributeValues() == null) {
            return;
        }
        Map<String, MatchStrategyResponseItem> strategyResponse = FutureUtil.getFutureIgnoreException(thirdPartyStrategyService.getStrategyServerConfig(request, request.getVersionName()), 1, TimeUnit.SECONDS);
        int dpDetailPageStyle = thirdPartyStrategyService.getThirdPartyDetailPageStyle(ThirdPartyMediaTypeEnum.getMediaTypeByAppId(request.getAppId()), strategyResponse);
        log.info("dpDetailPageStyle:{}, version:{}, appId:{}, duid:{}", dpDetailPageStyle, request.getVersionName(), request.getAppId(), request.getAttributeValues().getDuid());
        context.setDpDetailPageStyle(dpDetailPageStyle);
    }
}
