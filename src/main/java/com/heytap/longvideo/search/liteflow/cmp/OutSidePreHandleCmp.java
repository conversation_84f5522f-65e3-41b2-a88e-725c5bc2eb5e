package com.heytap.longvideo.search.liteflow.cmp;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heytap.longvideo.common.lib.constants.ActivityTaskUnlockEpisodeConstant;
import com.heytap.longvideo.search.constants.SearchConstant;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.common.StrategyService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.browser.common.app.lib.strategy.AttributeValuesCreator;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.heytap.longvideo.search.constants.OutSideSearchConstant.SERVICE_ID_SEARCH_NET_RESULTS;

/**
 * @Description: 对外搜索 - 前置处理
 * @Author: 80398885WT
 * @Date: 2025/6/4
 */
@Slf4j
@LiteflowComponent(name = "outSidePreHandleCmp")
public class OutSidePreHandleCmp extends NodeComponent {

    private final StrategyService strategyService;

    private final VipRelatedService vipRelatedService;

    public OutSidePreHandleCmp(StrategyService strategyService,
                               VipRelatedService vipRelatedService) {
        this.strategyService = strategyService;
        this.vipRelatedService = vipRelatedService;
    }

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (Objects.isNull(context)) {
            return;
        }

        KeyWordSearchParamV2 param = context.getRequestParam();
        if (Objects.isNull(param)) {
            return;
        }

        // 搜索来源非锁屏和小布 直接return
        if (!SearchConstant.EXACT_MATCH_SEARCH_SOURCE.contains(param.getAppId())) {
            return;
        }

        try {
            // 获取用户的会员身份信息
            String userId = Optional.ofNullable(param.getSsoid()).orElseGet(() -> param.getAttachments().get("ssoid"));
            if (StringUtils.isNotEmpty(userId)) {
                context.setUserId(userId);
                CompletableFuture<VideoVipInfo> vipInfoCF = vipRelatedService.getVipInfo(userId);
                VideoVipInfo vipInfo = FutureUtil.getFutureIgnoreException(vipInfoCF);
                if (Objects.nonNull(vipInfo)) {
                    param.setVideoVipInfo(vipInfo);
                    context.setVipInfo(vipInfo);
                }
            }

            // 匹配策略
            // 小布侧来源的搜索请求匹配策略是需将attachments中携带的请求头放在attributeValues中
            AttributeValues attributeValues = param.getAttributeValues();
            Map<String, String> attachments = param.getAttachments();
            if (Objects.nonNull(attributeValues) && MapUtils.isNotEmpty(attachments)) {
                if (Objects.equals(SearchConstant.SEARCH_FROM_BREENO, param.getAppId())) {
                    attributeValues.setDuid(attachments.get("duid"));
                    attributeValues.setOuid(attachments.get("ouid"));
                    attributeValues.setChannel(attachments.get("bc"));
                    attributeValues.setPhone(attachments.get("dv"));
                }
                attributeValues.setClientFullBrowserVersion(param.getVersionName());
                AttributeValuesCreator.buildBrowserVersion(attributeValues);
            }

            CompletableFuture<Map<String, MatchStrategyResponseItem>> matchStrategyCF = strategyService.matchSearchStrategy(param, new String[] {
                    SERVICE_ID_SEARCH_NET_RESULTS,
                    ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE,
                    ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE_MONGO});
            Map<String, MatchStrategyResponseItem> strategyMap = FutureUtil.getFutureIgnoreException(matchStrategyCF);
            if (MapUtils.isNotEmpty(strategyMap)) {
                context.setStrategyMatchResult(strategyMap);
                MatchStrategyResponseItem strategyResponseItem = strategyMap.get(SERVICE_ID_SEARCH_NET_RESULTS);
                if (Objects.nonNull(strategyResponseItem) && StringUtils.isNotEmpty(strategyResponseItem.getAttachment())) {
                    Map<String, Integer> searchNetResults = JsonUtil.fromStr(strategyResponseItem.getAttachment(), new TypeReference<Map<String, Integer>>() {});
                    if (MapUtils.isNotEmpty(searchNetResults)) {
                        context.setSearchNetResults(searchNetResults);
                    }
                }
            }
        } catch (Exception e) {
            log.error("OutSidePreHandleCmp error:", e);
        }
    }
}