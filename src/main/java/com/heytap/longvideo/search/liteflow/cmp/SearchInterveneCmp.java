package com.heytap.longvideo.search.liteflow.cmp;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.arrange.search.model.bo.LvSearchInterveneDetailBO;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/*
 * Description 搜索干预
 * Date 10:11 2023/7/21
 * Author songjiajia 80350688
 */
@Slf4j
@LiteflowComponent("searchInterveneCmp")
public class SearchInterveneCmp extends NodeComponent {

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 0, timeout = 500)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Autowired
    HttpDataChannel httpDataChannel;

    @Autowired
    ConvertResponseService beanConvertService;

    @Autowired
    private YoukuSourceFilterService youkuSourceFilterService;

    @Autowired
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;


    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor commonThreadPool;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Autowired
    DeepLinkUtils deepLinkUtils;

    @Override
    public void process() {
        try {
            SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
            Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = context.getInterveneConfigMap();
            if (!interveneConfigMap.containsKey(InterveneTypeEnum.NORMAL)) {
                return;
            }
            List<KeyWordSearchResponse> list = getSearchIntervene(context);
            context.setSearchInterveneResult(list);
        } catch (Exception e) {
            log.error("SearchInterveneCmp process error", e);
        }
    }


    public List<KeyWordSearchResponse> getSearchIntervene(SearchByKeyWordContext context) {
        LvSearchIntervene lvSearchIntervene = context.getInterveneConfigMap().get(InterveneTypeEnum.NORMAL);
        List<LvSearchInterveneDetailBO> detailBOList = JSON.parseArray(lvSearchIntervene.getInterveneDetail(), LvSearchInterveneDetailBO.class);
        if (CollectionUtils.isEmpty(detailBOList)) {
            return Collections.emptyList();
        }
        List<String> sidList = detailBOList.stream().map(LvSearchInterveneDetailBO::getSid).collect(Collectors.toList());
        Map<String, StandardAlbum> standardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);
        detailBOList = detailBOList.stream().sorted(Comparator.comparing(LvSearchInterveneDetailBO::getOrderIndex)).collect(Collectors.toList());
        List<KeyWordSearchResponse> responseList = detailBOList.stream()
                // 1. 过滤无效节目
                .filter(detailBO -> standardAlbumMap.containsKey(detailBO.getSid()))
                // 2. 转换对象并过滤空值
                .map(detailBO -> {
                    StandardAlbum album = standardAlbumMap.get(detailBO.getSid());
                    KeyWordSearchResponse response = beanConvertService.standardAlbumToSearchResponse(album, context.getRequestParam());
                    return response != null ? Pair.of(detailBO, response) : null;
                })
                .filter(Objects::nonNull)
                // 3. 优酷源过滤
                .filter(pair -> {
                    KeyWordSearchResponse response = pair.getRight();
                    if (!SourceEnum.YOUKU_MOBILE.getDataSource().equals(response.getSource())) {
                        return true;
                    }
                    KeyWordSearchParamV2 param = context.getRequestParam();
                    boolean shouldFilter = param.getIsOut() == 1 ?
                            youkuSourceFilterService.thirdPartyFilter(param.getQuickEngineVersion(), param.getVersion(), param.getAppId()) :
                            youkuSourceFilterService.filterItem(param.getVersion());
                    return !shouldFilter;
                })
                // 4. 风行&微迪欧过滤
                .filter(pair -> {
                    KeyWordSearchResponse response = pair.getRight();
                    KeyWordSearchParamV2 param = context.getRequestParam();
                    return !funshionLongVideoAndWeidiouFilterService.filterItemBySource(
                            response.getSource(), param.getVersion());
                })
                // 5. 设置排序索引
                .peek(pair -> {
                    LvSearchInterveneDetailBO detailBO = pair.getLeft();
                    KeyWordSearchResponse response = pair.getRight();
                    response.setSortIndex(Math.max(detailBO.getOrderIndex() - 1, 0));
                })
                // 6. 提取最终对象
                .map(Pair::getRight)
                .collect(Collectors.toList());

        Set<String> titleSet = responseList.stream().map(KeyWordSearchResponse::getTitle).collect(Collectors.toSet());
        context.getBaseSearchResult().removeIf(keyWordSearchResponse -> titleSet.contains(keyWordSearchResponse.getTitle()));
        return responseList;

    }

}