package com.heytap.longvideo.search.liteflow.context;

import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.model.param.outside.OutSideSearchButtonConfig;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * app搜索接口上下文对象
 */
@Data
public class SearchByKeyWordContext {

    private KeyWordSearchParamV2 requestParam;

    /**
     * 合作内容搜索结果
     */
    private List<KeyWordSearchResponse> baseSearchResult = new ArrayList<>();

    /**
     * 全网搜干预结果
     */
    private List<KeyWordSearchResponse> allWebInterveneResult = new ArrayList<>();

    /**
     * 全网搜匹配结果
     */
    private List<KeyWordSearchResponse> allWebSearchResult = new ArrayList<>();

    /**
     * 合作内容干预结果
     */
    private List<KeyWordSearchResponse> searchInterveneResult = new ArrayList<>();

    /**
     * banner干预结果
     */
    private List<LvDrawerItemVO> bannerList = new ArrayList<>();

    private SearchResponse searchResponse = new SearchResponse();

    /**
     * 搜索意图识别（人工干预部分）
     */
    private Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap;

    /**
     * 短剧搜索结果
     */
    private List<KeyWordSearchResponse> duanjuSearchResult = new ArrayList<>();

    /**
     * 是否走精准匹配
     */
    private Boolean exactMatch = false;

    /**
     * 服务端配置项控制是否放出全网搜 结果，区分媒体配置
     */
    private Map<String, Integer> searchNetResults;

    /**
     * 会员信息
     */
    private VideoVipInfo vipInfo;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 策略匹配结果
     */
    private Map<String, MatchStrategyResponseItem> strategyMatchResult;

    /**
     * 短视频策略后台配置：对外搜索节目按钮展示类型和文案
     */
    private OutSideSearchButtonConfig outSideSearchButtonConfig;


    private int dpDetailPageStyle;
}
