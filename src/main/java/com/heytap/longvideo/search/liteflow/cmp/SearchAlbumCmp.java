package com.heytap.longvideo.search.liteflow.cmp;

import com.github.stuxuhai.jpinyin.ChineseHelper;
import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.github.stuxuhai.jpinyin.PinyinHelper;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.constants.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.heytap.longvideo.search.service.app.ProgramMatchService;
import com.heytap.longvideo.search.service.common.ActorService;
import com.heytap.longvideo.search.service.common.CommonService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.VersionFilterService;
import com.heytap.longvideo.search.service.common.VirtualProgramService;
import com.heytap.longvideo.search.service.search.SearchAlbumService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FieldValueFactorFunctionBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.SEARCH_FROM_BROWSER;
import static com.heytap.longvideo.search.constants.SearchConstant.SEARCH_FROM_QUANSOU;

/*
 * 根据关键词搜索
 * Date 18:58 2021/12/21
 * Author songjiajia 80350688
 */
@Slf4j
@LiteflowComponent("searchAlbumCmp")
public class SearchAlbumCmp extends NodeComponent {

    private final ElasticsearchRestTemplate restTemplate;

    private final SearchProperties searchConfig;

    private final VirtualProgramService virtualProgramService;

    private final ActorService actorService;

    private final VersionFilterService versionFilterService;

    private final HotVideoService hotVideoService;

    private final CommonService commonService;

    private final SearchProperties searchProperties;

    private final ProgramMatchService programMatchService;

    private final YoukuSourceFilterService youkuSourceFilterService;

    private final DeepLinkUtils deepLinkUtils;

    public static Pattern HANYU_PATTERN;

    public static Pattern SERIES_PATTERN;

    @HeraclesDynamicConfig(key = "outside.search.size", fileName = "search_config.properties")
    private Integer outsideSearchSize = 15;

    private final ConvertResponseService beanConvertService;

    private final SourceFilterConfig sourceFilterConfig;

    private final StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    private final SearchAlbumService searchAlbumService;

    private final VipRelatedService vipRelatedService;

    static {
        HANYU_PATTERN = Pattern.compile("[\\u4E00-\\u9FA5]+");

        SERIES_PATTERN = Pattern.compile("^*[0-9]+$");

    }

    public SearchAlbumCmp(ElasticsearchRestTemplate restTemplate,
                          SearchProperties searchConfig,
                          VirtualProgramService virtualProgramService,
                          ActorService actorService,
                          VersionFilterService versionFilterService,
                          HotVideoService hotVideoService,
                          CommonService commonService,
                          SearchProperties searchProperties,
                          ProgramMatchService programMatchService,
                          YoukuSourceFilterService youkuSourceFilterService,
                          DeepLinkUtils deepLinkUtils,
                          @Lazy ConvertResponseService beanConvertService,
                          SourceFilterConfig sourceFilterConfig,
                          StandardAlbumRpcApiProxy standardAlbumRpcApiProxy,
                          SearchAlbumService searchAlbumService,
                          VipRelatedService vipRelatedService) {
        this.restTemplate = restTemplate;
        this.searchConfig = searchConfig;
        this.virtualProgramService = virtualProgramService;
        this.actorService = actorService;
        this.versionFilterService = versionFilterService;
        this.hotVideoService = hotVideoService;
        this.commonService = commonService;
        this.searchProperties = searchProperties;
        this.programMatchService = programMatchService;
        this.youkuSourceFilterService = youkuSourceFilterService;
        this.deepLinkUtils = deepLinkUtils;
        this.beanConvertService = beanConvertService;
        this.sourceFilterConfig = sourceFilterConfig;
        this.standardAlbumRpcApiProxy = standardAlbumRpcApiProxy;
        this.searchAlbumService = searchAlbumService;
        this.vipRelatedService = vipRelatedService;
    }

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);

        // 精准匹配直接return
        if (context.getExactMatch()) {
            return;
        }

        List<ProgramAlbumEs> list = search(context.getRequestParam());
        List<KeyWordSearchResponse> keyWordSearchResponseList = new ArrayList<>();
        String appId = null;
        if (context.getRequestParam() != null) {
            appId = context.getRequestParam().getAppId();
        }
        List<String> unlockAlbumList = new ArrayList<>();
        if (context.getRequestParam() != null && SEARCH_FROM_QUANSOU.equals(context.getRequestParam().getAppId())) {
            unlockAlbumList = beanConvertService.getUnlockAlbumList(context, list);
        }
        for (ProgramAlbumEs programAlbumEs : list) {
            try {
                if (context.getRequestParam() != null && context.getRequestParam().getIsOut() == 1 &&
                        !SEARCH_FROM_QUANSOU.equals(appId) && !SEARCH_FROM_BROWSER.equals(appId)) {
                    // 会员身份替换
                    vipRelatedService.vipReplace(context, programAlbumEs, context.getRequestParam().getAppVersion());
                }
                KeyWordSearchResponse keyWordSearchResponse = beanConvertService.programAlbumEsConvertResponse(programAlbumEs, context, unlockAlbumList);
                keyWordSearchResponseList.add(keyWordSearchResponse);
            } catch (Exception e) {
                log.error("searchAlbumCmp err,sid:{}", programAlbumEs.getSid(), e);
            }
        }
        context.setBaseSearchResult(keyWordSearchResponseList);
    }

    @Override
    public boolean isContinueOnError() {
        return true;
    }

    /**
     * 按关键词搜索
     */
    public List<ProgramAlbumEs> search(KeyWordSearchParamV2 param) {
        boolean newSearch = param.getVersionTag() >= 5;

        //1.参数校验
        List<ProgramAlbumEs> list = new ArrayList<>();
        String keyword = param.getKeyword();
        if (StringUtil.isBlank(keyword) || keyword.length() > 20) {
            return list;
        }

        keyword = keyword.trim();
        keyword = ChineseHelper.convertToSimplifiedChinese(keyword);
        log.info("search params:{}", param);
        param.setKeyword(keyword);

        //2.构造查询参数
        BoolQueryBuilder boolQuery = buildBaseQuery(keyword, param);

        FieldValueFactorFunctionBuilder fieldQuery = new FieldValueFactorFunctionBuilder("functionScore");
        FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(boolQuery, fieldQuery);
        //3.低版本过滤
        buildSourceQuery(param, boolQuery);

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        PageRequest pageRequest = PageRequest.of(0, 30);
        if (param.getIsOut() == 1) {
            pageRequest = PageRequest.of(0, outsideSearchSize);
        }
        if (newSearch && param.getPageIndex() >= 4) {
            pageRequest = PageRequest.of(param.getPageIndex() - 1, param.getPageSize());
        }
        NativeSearchQuery searchQuery = queryBuilder.withQuery(functionScoreQueryBuilder)
                .withPageable(pageRequest)
                .build();
        //4.解析响应
        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        log.info("searchAlbumCmp requestId={},searchHits={}", param.getRequestId(), JacksonUtil.toJSONString(searchHits));
        if (searchHits.getTotalHits() > (long) param.getPageIndex() * param.getPageSize()) {
            param.setHasMore(1);
        }
        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            float score = searchHit.getScore();
            ProgramAlbumEs es = searchHit.getContent();
            es.setReleScore(score);
            list.add(es);
        }
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //5.过滤
        list = searchAlbumService.filterByMatch(list, keyword);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //6.版本过滤、去重
        list = searchAlbumService.filterVirtualProgram(list, param);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //7.数据重排序
        list = searchAlbumService.keywordSearchSort(list, keyword);

        List<String> sidList = list.stream().map(ProgramAlbumEs::getSid).collect(Collectors.toList());

        handleSohuYoukuProgram(sidList, list, param);
        return list;
    }

    /**
     * 构造查询条件
     */
    private BoolQueryBuilder buildBaseQuery(String keyword, KeyWordSearchParamV2 param) {
        KeywordSearchTypeEnum searchTypeEnum;
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtil.isNumericOrChar(keyword)) {
            searchTypeEnum = buildNumbericOrCharQuery(keyword, boolQuery);
        } else if (keyword.contains(" ")) {
            searchTypeEnum = buildBlankQuery(keyword, boolQuery);
        } else if (actorService.checkIsActorAndDirector(keyword)) {
            boolQuery.should(QueryBuilders.matchPhraseQuery("actor", keyword).boost(4f));
            boolQuery.should(QueryBuilders.matchPhraseQuery("director", keyword).boost(2f));
            boolQuery.should(QueryBuilders.matchPhraseQuery("title", keyword).boost(1f));
            boolQuery.minimumShouldMatch(1);
            searchTypeEnum = KeywordSearchTypeEnum.ACTOR;
        } else {
            searchTypeEnum = buildDefaultQuery(keyword, boolQuery);
        }
        param.setSearchTypeEnum(searchTypeEnum);
        // 端外搜索，只搜正片
        if (param.getIsOut() == 1) {
            boolQuery.must(QueryBuilders.termQuery("featureType", 1));
        }
        return boolQuery;
    }

    /**
     * 构建纯数字或英文字符的查询条件
     */
    private KeywordSearchTypeEnum buildNumbericOrCharQuery(String keyword, BoolQueryBuilder boolQuery) {
        keyword = keyword.toLowerCase();
        if (keyword.length() < 5) {
            boolQuery.should(QueryBuilders.matchPhraseQuery("titlePinyin", keyword).boost(3f));
        } else {
            boolQuery.should(QueryBuilders.matchPhraseQuery("titlePinyin", keyword).boost(3f));
            boolQuery.should(QueryBuilders.termsQuery("actorPinyin", keyword).boost(2f));
            boolQuery.should(QueryBuilders.termsQuery("directorPinyin", keyword).boost(1f));
        }
        boolQuery.minimumShouldMatch(1);
        return KeywordSearchTypeEnum.PINYIN;
    }

    /**
     * 构建带空格的查询条件
     */
    public KeywordSearchTypeEnum buildBlankQuery(String keyword, BoolQueryBuilder boolQuery) {
        keyword = keyword.replaceAll(" +", " ");
        String[] keywords = keyword.split(" ");
        // 只处理前两个单词
        for (int i = 0; i < keywords.length; i++) {
            if (i <= 1) {
                boolQuery.must(QueryBuilders.multiMatchQuery(keywords[i], "title", "actor", "director")
                        .minimumShouldMatch("80%"));
            }
        }
        return KeywordSearchTypeEnum.SPACE;
    }

    /**
     * 构建默认的查询条件
     */
    private KeywordSearchTypeEnum buildDefaultQuery(String keyword, BoolQueryBuilder boolQuery) {
        if (keyword.length() >= 5) {
            boolQuery.should(QueryBuilders.matchQuery("title", keyword).boost(4f).operator(Operator.OR).minimumShouldMatch("80%"));
        } else if (keyword.length() == 4) {
            boolQuery.should(QueryBuilders.matchQuery("title", keyword).boost(4f).operator(Operator.OR).minimumShouldMatch("75%"));
        } else if (keyword.length() == 3) {
            StringBuilder pinyinBuild = new StringBuilder();
            char[] hanYuArr = keyword.toCharArray();
            for (char c : hanYuArr) {
                Matcher matcher = HANYU_PATTERN.matcher(Character.toString(c));
                if (matcher.find()) {
                    String[] pys = PinyinHelper.convertToPinyinArray(c, PinyinFormat.WITHOUT_TONE);
                    pinyinBuild.append(pys[0]);
                } else {
                    pinyinBuild.append(c);
                }
            }
            boolQuery.should(QueryBuilders.matchPhraseQuery("titlePinyin", pinyinBuild.toString()));
        }
        boolQuery.should(QueryBuilders.matchPhraseQuery("title", keyword).boost(4f));
        boolQuery.should(QueryBuilders.matchPhraseQuery("actor", keyword).boost(2f));
        boolQuery.should(QueryBuilders.matchPhraseQuery("director", keyword).boost(1f));
        boolQuery.minimumShouldMatch(1);
        return KeywordSearchTypeEnum.DEFAULT;
    }

    private void buildSourceQuery(KeyWordSearchParamV2 param, BoolQueryBuilder boolQuery) {
        //一期搜索先屏蔽咪咕
        boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MIGUOLMPIC));

        // 搜推场景不按版本过滤源（低版本则下发升级链接）
        if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(param.getAppId())) {
            return;
        }

        if (param.getVersionTag() < searchConfig.getMgAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MGMOBILE));
        }
        if (param.getVersionTag() < searchConfig.getLetvAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_LETV));
        }
        if (param.getVersionTag() < searchConfig.getYstAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YST));
        }
        if (param.getVersionTag() < searchConfig.getMiguOlympicAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MIGUOLMPIC));
        }
        // 关键词搜索 风行和微迪欧 过滤
        if (param.getVersionTag() < searchConfig.getFunshionlongvideoAndWeidiouAppVersion()) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_FUNSHION_LONGVIDEO));
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_WEIDIOU));
        }
        // 关键词搜索 根据过滤条件进行更改
        if (1 == param.getIsOut()) {
            if (youkuSourceFilterService.thirdPartyFilter(param.getQuickEngineVersion(), param.getVersion(), param.getAppId())) {
                boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YOUKU_MOBILE));
            }
        } else {
            if (youkuSourceFilterService.filterItem(param.getVersion())) {
                boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YOUKU_MOBILE));
            }
        }
    }


    private void handleSohuYoukuProgram(List<String> sidList, List<ProgramAlbumEs> list, KeyWordSearchParamV2 param) {
        Map<String, StandardAlbum> albumRpcResultMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);

        for (ProgramAlbumEs albumEs : list) {
            // 搜狐VIP内容处理
            StandardAlbum album = albumRpcResultMap.get(albumEs.getSid());
            if (commonService.isSohuVipContent(album)) {
                albumEs.setProgramInfo("");
            }

            // 优酷deeplink处理
            if (1 == param.getIsOut()) {
                albumEs.setSourceWebUrl(albumRpcResultMap.get(albumEs.getSid()).getSourceWebUrl());
                if (youkuSourceFilterService.thirdPartyFastAppFilter(albumEs.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                    albumEs.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(),
                            albumRpcResultMap.get(albumEs.getSid()).getSourceWebUrl()));
                } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(albumEs.getSource())) {
                    albumEs.setDeepLink(youkuSourceFilterService.getUpGradeUrl(albumEs.getSid(), albumEs.getSource(), albumEs.getTitle()));
                    albumEs.setDpLinkType(TemplateLinkTypeEnum.UPGRADE_PAGE.getCode());
                }
            } else {
                if (youkuSourceFilterService.setDeepLinkFilter(albumEs.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                    albumEs.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(),
                            albumRpcResultMap.get(albumEs.getSid()).getSourceWebUrl()));
                } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(albumEs.getSource())) {
                    albumEs.setDeepLink(youkuSourceFilterService.getUpGradeUrl(albumEs.getSid(), albumEs.getSource(), albumEs.getTitle()));
                }
            }
        }
    }
}