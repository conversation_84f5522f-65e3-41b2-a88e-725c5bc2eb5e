package com.heytap.longvideo.search.liteflow.cmp;

import com.heytap.longvideo.client.arrange.entity.LvPage;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@LiteflowComponent("searchBannerCmp")
public class SearchBannerCmp extends NodeComponent {
    @Autowired
    private DeepLinkUtils deepLinkUtils;

    @Autowired
    ArrangeRpcApiProxy arrangeRpcApiProxy;

    @Override
    public void process() throws Exception {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        LvSearchIntervene bannerIntervene = context.getInterveneConfigMap().get(InterveneTypeEnum.BANNER);
        context.setBannerList(buildBanner(context.getRequestParam(), bannerIntervene));
    }


    public List<LvDrawerItemVO> buildBanner(KeyWordSearchParamV2 request, LvSearchIntervene bannerIntervene) {
        List<LvDrawerItemVO> list = new ArrayList<>();
        if (request.getVersion() < 71000 || request.getPageIndex() != 1 || !"1".equals(request.getSearchType())
                || (request.getDeviceType() != 0 && request.getDeviceType() != 5)
                || bannerIntervene == null) {
            return list;
        }
        LvDrawerItemVO lvDrawerItemVO = new LvDrawerItemVO();
        lvDrawerItemVO.setImgUrl(bannerIntervene.getImage());
        lvDrawerItemVO.setDeepLink(deepLinkUtils.getDeeplinkByType(bannerIntervene.getLinkType(), bannerIntervene.getLinkValue()));
        if (TemplateLinkTypeEnum.COMMON_PAGE.getCode() == bannerIntervene.getLinkType()) {
            LvPage lvPage = FutureUtil.getFutureIgnoreException(arrangeRpcApiProxy.findPageByCode(bannerIntervene.getLinkValue()));
            if (lvPage != null) {
                lvDrawerItemVO.setDeepLink(lvDrawerItemVO.getDeepLink() + "&title=" + lvPage.getTitle());
            }
        }
        list.add(lvDrawerItemVO);
        return list;
    }
}
