package com.heytap.longvideo.search.liteflow.cmp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.arrange.search.model.bo.LvSearchInterveneDetailBO;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.common.lib.rpc.ResultCode;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.BaseRequest;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.SEARCH_FROM_QUANSOU;

/*
 * Description 搜索干预 - 端外
 * Date 10:11 2024/9/20
 * Author chuanxw
 */
@Slf4j
@LiteflowComponent("outSideSearchInterveneCmp")
public class OutSideSearchInterveneCmp extends NodeComponent {

    final ConvertResponseService beanConvertService;

    private final YoukuSourceFilterService youkuSourceFilterService;

    private final FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    private final ConvertResponseService convertResponseService;

    private final TaskUnlockEpisodeService taskUnlockEpisodeService;

    private final VipRelatedService vipRelatedService;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private StandardAlbumRpcApi standardAlbumRpcApi;

    public OutSideSearchInterveneCmp(ConvertResponseService beanConvertService,
                                     YoukuSourceFilterService youkuSourceFilterService,
                                     FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService, ConvertResponseService convertResponseService, TaskUnlockEpisodeService taskUnlockEpisodeService, VipRelatedService vipRelatedService) {
        this.beanConvertService = beanConvertService;
        this.youkuSourceFilterService = youkuSourceFilterService;
        this.funshionLongVideoAndWeidiouFilterService = funshionLongVideoAndWeidiouFilterService;
        this.convertResponseService = convertResponseService;
        this.taskUnlockEpisodeService = taskUnlockEpisodeService;
        this.vipRelatedService = vipRelatedService;
    }

    @Override
    public void process() {
        SearchByKeyWordContext context = this.getContextBean(SearchByKeyWordContext.class);
        if (Objects.isNull(context)) {
            return;
        }

        Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = context.getInterveneConfigMap();
        if (MapUtils.isEmpty(interveneConfigMap) || !interveneConfigMap.containsKey(InterveneTypeEnum.NORMAL)) {
            return;
        }
        List<KeyWordSearchResponse> list = getSearchIntervene(context);
        context.setSearchInterveneResult(list);
    }

    /**
     * 查出结果干预时，具体配置的关联剧
     */
    public List<KeyWordSearchResponse> getSearchIntervene(SearchByKeyWordContext context) {
        LvSearchIntervene lvSearchIntervene = context.getInterveneConfigMap().get(InterveneTypeEnum.NORMAL);
        // getInterveneDetail存的是多条 sid、title、order 剧头相关信息
        List<LvSearchInterveneDetailBO> detailBOList = JSON.parseArray(lvSearchIntervene.getInterveneDetail(), LvSearchInterveneDetailBO.class);
        if (CollectionUtils.isEmpty(detailBOList)) {
            return null;
        }
        List<KeyWordSearchResponse> responseList = new ArrayList<>();
        List<String> sidList = detailBOList.stream().map(LvSearchInterveneDetailBO::getSid).collect(Collectors.toList());
        CompletableFuture<RpcResult<Map<String, StandardAlbum>>> albumFuture = standardAlbumRpcApi.getBySidsFilterInvalid(sidList);
        RpcResult<Map<String, StandardAlbum>> albumRpcResult = FutureUtil.getFutureIgnoreException(albumFuture, 1, TimeUnit.SECONDS);
        if (albumRpcResult == null || (albumRpcResult.getCode() != ResultCode.SUCCESS.getCode()) || albumRpcResult.getData() == null) {
            log.warn("albumRpcApiProxy.getBySid code error,sid:{},result:{}", sidList, JSON.toJSONString(albumRpcResult));
            return null;
        }
        Map<String, StandardAlbum> standardAlbumMap = albumRpcResult.getData();
        detailBOList = detailBOList.stream().sorted(Comparator.comparing(LvSearchInterveneDetailBO::getOrderIndex)).collect(Collectors.toList());
        for (LvSearchInterveneDetailBO detailBO : detailBOList) {
            StandardAlbum standardAlbum = standardAlbumMap.getOrDefault(detailBO.getSid(), null);
            if (standardAlbum == null) {
                continue;
            }
            // 三方场景 干预卡过滤优酷
            if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(standardAlbum.getSource()) &&
                    youkuSourceFilterService.thirdPartyFilter(context.getRequestParam().getQuickEngineVersion(),
                            context.getRequestParam().getVersion(), context.getRequestParam().getAppId())) {
                continue;
            }
            // 全搜 过滤风行和微迪欧
            if (funshionLongVideoAndWeidiouFilterService.filterItemBySource(standardAlbum.getSource(), context.getRequestParam().getVersion())) {
                continue;
            }
            KeyWordSearchResponse keyWordSearchResponse = beanConvertService.standardAlbumToSearchResponse(standardAlbum, context.getRequestParam());
            if (keyWordSearchResponse == null) {
                continue;
            }
            keyWordSearchResponse.setSortIndex(Math.max(detailBO.getOrderIndex() - 1, 0));
            ProgramAlbumEs programAlbumEs = new ProgramAlbumEs();
            programAlbumEs.setSource(standardAlbum.getSource());
            programAlbumEs.setFreeStartTime(standardAlbum.getFreeStartTime());
            programAlbumEs.setFreeEndTime(standardAlbum.getFreeEndTime());

            // 对外搜索设置按钮文案, 目前只对全搜作处理
            if (context.getRequestParam() != null && SEARCH_FROM_QUANSOU.equals(context.getRequestParam().getAppId())) {
                BaseRequest baseRequest = new BaseRequest();
                baseRequest.setAppVersion(context.getRequestParam().getAppVersion());
                baseRequest.setAttributeValues(context.getRequestParam().getAttributeValues());
                String vipType = vipRelatedService.getVipType(context.getVipInfo());
                List<String> unlockAlbumList = taskUnlockEpisodeService.getUnlockAlbumList(baseRequest, vipType, context.getUserId(),
                        context.getStrategyMatchResult(), Lists.newArrayList(keyWordSearchResponse));
                convertResponseService.handleButtonWord(programAlbumEs, keyWordSearchResponse, context, unlockAlbumList);
            }
            responseList.add(keyWordSearchResponse);
        }
        // 取出搜索干预返回的结果，所有title
        Set<String> titleSet = responseList.stream().map(KeyWordSearchResponse::getTitle).collect(Collectors.toSet());
        // 如果媒资数据包含了干预结果，就去掉该条数据
        context.getBaseSearchResult().removeIf(keyWordSearchResponse -> titleSet.contains(keyWordSearchResponse.getTitle()));
        return responseList;

    }
}