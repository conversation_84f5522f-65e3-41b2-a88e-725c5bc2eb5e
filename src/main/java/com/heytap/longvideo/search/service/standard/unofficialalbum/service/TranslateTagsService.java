package com.heytap.longvideo.search.service.standard.unofficialalbum.service;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/4/23 下午12:50
 */

@Service
@Slf4j
public class TranslateTagsService {

    @Autowired
    private MultiParamsRpcService multiParamsRpcService;

    @HeraclesDynamicConfig(key = "unofficial.album.program.detail.blackTags", fileName = "configure.properties")
    private Set<String> pageProgramDetailBlackTagsSet = new HashSet<>();

    public String translateTags(String tags, String programType) {
        List<String> tagsList = new ArrayList<>();
        if (StringUtils.isEmpty(tags)) {
            return "其他";
        }
        String[] tempStills = tags.split("\\|");
        if (tempStills.length != 0) {
            tagsList = Arrays.asList(tempStills);
        }
        return convertTags(tagsList, programType, 4);
    }

    public String convertTags(List<String> originTags, String contentType, int maxTagsNum) {
        try {
            if (maxTagsNum < 1) {
                return "";
            }
            if (CollectionUtils.isEmpty(originTags)) {
                return "";
            }
            Map<String, String> detailTagMap = multiParamsRpcService.findByContentType(contentType);
            if (MapUtils.isEmpty(detailTagMap)) {
                return "";
            }
            List<String> result = originTags.stream()
                    .map(detailTagMap::get)
                    .filter(StringUtils::isNotBlank)
                    .filter(tag -> !pageProgramDetailBlackTagsSet.contains(contentType + "_" + tag))
                    .limit(maxTagsNum)
                    .collect(Collectors.toList());
            if (result.size() == 0) {
                result.add("其它");
            }
            StringBuilder sb = new StringBuilder();
            if (CollectionUtils.isNotEmpty(result)) {
                for (String tag : result) {
                    if (StringUtils.isBlank(sb.toString())) {
                        sb.append(tag);
                        continue;
                    }
                    sb.append("|");
                    sb.append(tag);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("getAlbumDetail convertTags error,originTags:{},contentType:{}", originTags, contentType, e);
            return "";
        }
    }
}
