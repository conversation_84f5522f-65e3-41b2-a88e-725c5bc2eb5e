package com.heytap.longvideo.search.service.standard.unofficialalbum.entity;

import lombok.Data;

import java.util.List;

/**
 * @ClassName MutisearchNodeDetailVo
 * @Description
 * <AUTHOR>
 * @Date 2020/1/22 17:13
 * @Version 1.0
 **/
@Data
public class MultisearchNodeDetailVo {


    /**
     * name : 电视剧
     * code : tv
     * children : [{"name":"类型","code":"tag","children":[{"name":"全部","code":"all","children":[]},{"name":"家庭","code":"jiating","children":[]},{"name":"都市","code":"dushi","children":[]},{"name":"爱情","code":"aiqing","children":[]},{"name":"偶像","code":"ouxiang","children":[]},{"name":"罪案","code":"zuian","children":[]},{"name":"军事","code":"junshi","children":[]},{"name":"喜剧","code":"xiju","children":[]},{"name":"武侠","code":"wuxia","children":[]},{"name":"科幻","code":"kehuan","children":[]},{"name":"动作","code":"dongzuo","children":[]},{"name":"历史","code":"lishi","children":[]},{"name":"乡村","code":"xiangcun","children":[]},{"name":"魔幻","code":"mohuan","children":[]},{"name":"其他","code":"qita","children":[]}]},{"name":"地区","code":"area","children":[{"name":"全部","code":"all","children":[]},{"name":"内地","code":"neidi","children":[]},{"name":"中国香港","code":"xianggang","children":[]},{"name":"美国","code":"oumei","children":[]},{"name":"英国","code":"yingguo","children":[]},{"name":"日本","code":"riben","children":[]},{"name":"中国台湾","code":"taiwan","children":[]},{"name":"其他","code":"qita","children":[]}]},{"name":"年代","code":"year","children":[{"name":"全部","code":"all","children":[]},{"name":"2019","code":"2019","children":[]},{"name":"2018","code":"2018","children":[]},{"name":"2017","code":"2017","children":[]},{"name":"2016","code":"2016","children":[]},{"name":"2015","code":"2015","children":[]},{"name":"2014","code":"2014","children":[]},{"name":"2013","code":"2013","children":[]},{"name":"2012","code":"2012","children":[]},{"name":"2011","code":"2011","children":[]},{"name":"2010","code":"2010","children":[]},{"name":"00年代","code":"2000-2009","children":[]},{"name":"90年代","code":"1990-1999","children":[]},{"name":"更早","code":"qita","children":[]}]},{"name":"排序","code":"sort","children":[{"name":"最热","code":"hot","children":[]},{"name":"最新","code":"new","children":[]}]}]
     */

    private String name;
    private String code;
    private List<ChildrenBeanX> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<ChildrenBeanX> getChildren() {
        return children;
    }

    public void setChildren(List<ChildrenBeanX> children) {
        this.children = children;
    }

    public static class ChildrenBeanX {

        /**
         * name : 类型
         * code : tag
         * children : [{"name":"全部","code":"all","children":[]},{"name":"家庭","code":"jiating","children":[]},{"name":"都市","code":"dushi","children":[]},{"name":"爱情","code":"aiqing","children":[]},{"name":"偶像","code":"ouxiang","children":[]},{"name":"罪案","code":"zuian","children":[]},{"name":"军事","code":"junshi","children":[]},{"name":"喜剧","code":"xiju","children":[]},{"name":"武侠","code":"wuxia","children":[]},{"name":"科幻","code":"kehuan","children":[]},{"name":"动作","code":"dongzuo","children":[]},{"name":"历史","code":"lishi","children":[]},{"name":"乡村","code":"xiangcun","children":[]},{"name":"魔幻","code":"mohuan","children":[]},{"name":"其他","code":"qita","children":[]}]
         */

        private String name;
        private String code;
        private List<ChildrenBean> children;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public List<ChildrenBean> getChildren() {
            return children;
        }

        public void setChildren(List<ChildrenBean> children) {
            this.children = children;
        }

        public static class ChildrenBean {

            /**
             * name : 全部
             * code : all
             * children : []
             */

            private String name;
            private String code;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }
        }
    }
}
