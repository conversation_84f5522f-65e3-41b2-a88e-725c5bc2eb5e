package com.heytap.longvideo.search.service.sourcefilter;

import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.constants.ThirdPartyMediaTypeEnum;
import com.heytap.longvideo.search.utils.UrlCoderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2024/10/21 下午7:40
 */
@Component
public class YoukuSourceFilterService {

    @Autowired
    private SourceFilterConfig sourceFilterConfig;

    /**
     * 通过 quickEngineVersion, version 过滤优酷
     * 返回 true 过滤优酷内容
     */
    public boolean filterItem(Integer version) {
        //总开关关闭 直接过滤，或者视频版本为空，直接过滤
        if (sourceFilterConfig.getYoukuSwitch() == 0) {
            return true;
        }
        //两个为1,一定下发
        if (null == version) {
            return !(1 == sourceFilterConfig.getYoukuSwitch() && 1 == sourceFilterConfig.getYoukuVersionSwitch());
        }

        //场景开关关闭且视频和快应用有一个不满足；则过滤优酷内容
        return 0 == sourceFilterConfig.getYoukuVersionSwitch() &&
                sourceFilterConfig.getYoukuMobileVersion() > version;
    }

    /**
     * 通过 source, quickEngineVersion, version 过滤优酷内容
     * 返回 true 过滤优酷内容
     */
    public boolean filterItemBySource(String source, Integer version) {
        //01,00，不为优酷来源不会过滤
        if (!SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            return false;
        }
        //视频版本为空或者总开关关闭，直接过滤
        if (0 == sourceFilterConfig.getYoukuSwitch()) {
            return true;
        }
        //两个为1,一定下发
        if (null == version) {
            return !(1 == sourceFilterConfig.getYoukuSwitch() && 1 == sourceFilterConfig.getYoukuVersionSwitch());
        }
        //10，场景开关关闭并且视频和快应用有一个不满足；则过滤优酷内容
        return 0 == sourceFilterConfig.getYoukuVersionSwitch() &&
                sourceFilterConfig.getYoukuMobileVersion() > version;
    }

    /**
     * 设置deeplink过滤
     * 返回true 设置fastAPP
     */
    public boolean setDeepLinkFilter(String source, Integer quickEngineVersion, Integer version) {
        //source 不为优酷，不会设置fast_app
        if (!SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            return false;
        }
        //视频、快应用一个为null，不会设置fast_app
        if (null == version) {
            return false;
        }
        // 总开关为开启，版本号都满足，设置fast_app
        return 1 == sourceFilterConfig.getYoukuSwitch() &&
                sourceFilterConfig.getYoukuMobileVersion() <= version;
    }

    /**
     * 三方场景过滤优酷
     * 返回 true 过滤优酷内容
     */
    public boolean thirdPartyFilter(Integer quickEngineVersion, Integer version, String appId) {
        //总开关关闭 直接过滤
        if (sourceFilterConfig.getYoukuSwitch() == 0) {
            return true;
        }
        // 搜推场景不过滤优酷节目（低版本则下发升级链接）
        if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(appId)) {
            return false;
        }
        //总开关开启，视频版本号为空或者低于7.16,三方开关关闭，则过滤
        if (null == version || sourceFilterConfig.getYoukuMobileVersion() > version) {
            return 0 == sourceFilterConfig.getYoukuthirdPartySwitch();
        }
        //总开关开启，视频版本>=7.16,快应用版本号为空，下发优酷
        if (null == quickEngineVersion) {
            return false;
        }
        //总开关开启，7.16>=视频版本,快应用低版本过滤
        return sourceFilterConfig.getYoukuthirdPartySwitch() == 0 && sourceFilterConfig.getMinWebQuickVersion() > quickEngineVersion;
    }


    /**
     * 三方场景设置fast_app判断
     * 返回true 设置fast_app
     */
    public boolean thirdPartyFastAppFilter(String source, Integer quickEngineVersion, Integer version) {
        //source 不为优酷，不会设置fast_app
        if (!SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            return false;
        }
        //source 为优酷, 快应用版本为null,设置fast_app，客户端都第
        if (null == quickEngineVersion) {
            return true;
        }
        //source 为优酷, 视频版本为空，或者不满足，不会设置fast_app
        if (null == version || sourceFilterConfig.getYoukuMobileVersion() > version) {
            return false;
        }
        //视频版本满足，快应用版本满足，下发fast_app
        return sourceFilterConfig.getMinWebQuickVersion() <= quickEngineVersion;
    }


    public String getUpGradeUrl(String linkValue, String source, String title) {
        String postUrl = String.format(sourceFilterConfig.getUpgradePageUrl(), linkValue, source, title);
        if (!UrlCoderUtil.hasEnCode(postUrl)) {
            postUrl = UrlCoderUtil.encode(postUrl, StandardCharsets.UTF_8);
        }
        return String.format("yoli://yoli.com/yoli/h5?url=%s", postUrl);
    }

}
