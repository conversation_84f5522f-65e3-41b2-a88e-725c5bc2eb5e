package com.heytap.longvideo.search.service.standard.unofficialalbum.service;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.MultiParamsRpcApi;
import com.heytap.longvideo.client.arrange.entity.MtvMultiparams;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.service.standard.unofficialalbum.entity.MultisearchNodeDetailVo;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Cache;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Caffeine;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/21 17:34
 */
@Service
@Slf4j
public class MultiParamsRpcService {

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo")
    private MultiParamsRpcApi multiParamsRpcApi;

    Cache<String, Map<String, String>> multiParamsCache = Caffeine.newBuilder()
            .maximumSize(16)
            .expireAfterWrite(35, TimeUnit.MINUTES)
            .build();


    public Map<String, String> findByContentType(String contentType) {
        try {
            Map<String, String> multiParamsMap = multiParamsCache.getIfPresent(contentType);
            if (multiParamsMap != null) {
                return multiParamsMap;
            }
            //无需加锁
            RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "cms");
            CompletableFuture<Map<String, String>> completableFuture = multiParamsRpcApi.findByContentType(contentType).handle((rpcResult, e) -> {
                if (e != null) {
                    log.error("MultiParamsRpcApiProxy findByCode error,", e);
                    throw new RuntimeException(e);
                }
                if (rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                    log.error("multiParamsRpcApi findByCode not success,rpcResult:{}", JSON.toJSONString(rpcResult));
                    throw new RuntimeException(rpcResult.getMsg());
                }
                Map<String, String> tagMap = new HashMap<>();
                MtvMultiparams multiParams = rpcResult.getData();
                if (multiParams != null) {
                    MultisearchNodeDetailVo multisearchNodeDetailVo = JSON.parseObject(multiParams.getMultiparamsJson(), MultisearchNodeDetailVo.class);
                    multisearchNodeDetailVo.getChildren().stream().filter(childrenBeanX -> "tag".equals(childrenBeanX.getCode()))
                            .flatMap(childrenBeanX -> childrenBeanX.getChildren().stream()).forEach(childrenBean -> {

                                if (StringUtils.isBlank(childrenBean.getCode()) || StringUtils.isBlank(childrenBean.getName())) {
                                    return;
                                }
                                String[] tags = childrenBean.getCode().split("\\|");
                                Stream.of(tags).forEach(tag -> tagMap.putIfAbsent(tag, childrenBean.getName()));
                            });
                }
                multiParamsCache.put(contentType, tagMap);
                return tagMap;
            });
            return completableFuture.get(2, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("MultiParamsRpcApiProxy findByCode error,", e);
            throw new RuntimeException(e.getMessage());
        }
    }
}
