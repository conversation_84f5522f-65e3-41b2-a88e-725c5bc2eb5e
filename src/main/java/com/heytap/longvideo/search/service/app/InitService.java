package com.heytap.longvideo.search.service.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.heytap.longvideo.client.arrange.entity.MinorPoolCodeRecord;
import com.heytap.longvideo.client.arrange.enums.MinorsPoolCodeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.client.media.query.AlbumSearchParameterModifier;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.mapper.media.VirtualProgramMapper;
import com.heytap.longvideo.search.model.entity.es.HotActorEs;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEsForCreateIndex;
import com.heytap.longvideo.search.properties.HotVideoProperties;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.LvContentPoolRpcApiProxy;
import com.heytap.longvideo.search.service.common.ActorService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.common.SeriesAlbumService;
import com.heytap.longvideo.search.service.common.VirtualProgramService;
import com.heytap.longvideo.search.service.standard.AdjustApiSearchAlbumService;
import com.heytap.longvideo.search.utils.SensitiveWordUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.cpc.video.framework.lib.jins.RowData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.completion.Completion;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/*
 * 数据初始化及更新
 * Date 18:58 2021/12/21
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class InitService {

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private ElasticSearchService elasticsearchService;


    @Autowired
    private ActorService actorService;

    @Autowired
    private VirtualProgramMapper virtualProgramMapper;

    @Autowired
    private SearchProperties searchConfig;

    @Autowired
    private VirtualProgramService virtualProgramService;

    @Autowired
    private HotVideoProperties hotVideoProperties;

    @Autowired
    private HotVideoService hotVideoService;

    @Autowired
    private SeriesAlbumService seriesAlbumService;

    @Autowired
    private StandardAlbumMapper standardAlbumMapper;

    @Autowired
    private LvContentPoolRpcApiProxy lvContentPoolRpcApiProxy;
    @Autowired
    private AdjustApiSearchAlbumService adjustApiSearchAlbumService;

    DateFormat sdf = new SimpleDateFormat("yyyyMM");

    @Autowired
    private JedisCluster jedisCluster;

    @HeraclesDynamicConfig(key = "media.datebaseEnd", fileName = "search_config.properties")
    private static int datebaseEnd;

    @HeraclesDynamicConfig(key = "media.tableEnd", fileName = "search_config.properties")
    private static int tableEnd;


    /**
     * 数据初始化
     */
    public void initData() {
        List<ProgramAlbumEs> albumEsList = virtualProgramService.createCacheAndReturnAllAlbum();
        log.info("initData totalCount:{}", albumEsList.size());
        //<virtualSid, <source, ProgramAlbumEs>>
        Map<String, ProgramAlbumEs> virtualProgramMap = buildVirtualProgramMap();
        Map<String, Integer> actorMap = new HashMap<>(albumEsList.size());
        int nowYear = Calendar.getInstance().get(Calendar.YEAR);
        int i = 0;
        List<IndexQuery> indexQueries = new ArrayList<>();
        elasticsearchService.deleteIndex(ProgramAlbumEs.class);
        elasticsearchService.createIndexAndMapping(ProgramAlbumEs.class);
        for (ProgramAlbumEs programAlbum : albumEsList) {
            i++;
            programAlbum.setOppoHot(0L);
            programAlbum.setDayNo(0);
            programAlbum.setLast7DaysClickPv(0L);
            programAlbum.setLast15DaysClickPv(0L);
            programAlbum.setLast30DaysClickPv(0L);
            programAlbum.setLast7DaysPlayPv(0L);
            programAlbum.setLast15DaysPlayPv(0L);
            programAlbum.setLast30DaysPlayPv(0L);
            if (StringUtil.isNotBlank(programAlbum.getVirtualSid()) && virtualProgramMap.containsKey(programAlbum.getVirtualSid())) {
                ProgramAlbumEs virtualProgram = virtualProgramMap.get(programAlbum.getVirtualSid());
                if (!programAlbum.getSid().equals(virtualProgram.getSid())) {
                    continue;
                }
            }
            setProgramAlbumEs(programAlbum, nowYear);
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(programAlbum);
            indexQueries.add(indexQuery);
            setActorEs(programAlbum, actorMap);
            if ((i != 0 && i % 500 == 0) || i == actorMap.size()) {
                restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(ProgramAlbumEs.class.getAnnotation(Document.class).indexName()));
                indexQueries = new ArrayList<>();
            }
        }
        if(CollectionUtils.isNotEmpty(indexQueries)){
            restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(ProgramAlbumEs.class.getAnnotation(Document.class).indexName()));
        }
        log.info("initAlbum end");
        IndexOperations indexOperations = restTemplate.indexOps(ProgramAlbumEs.class);
        indexOperations.refresh();
        batchInsertActorEs(actorMap);
        log.info("initActor end");
        this.minorsRefreshToEs();
        log.info("minorsRefreshToEs end");
    }

    private Map<String, ProgramAlbumEs> buildVirtualProgramMap() {
        Map<String, Map<String, ProgramAlbumEs>> allProgramMap = virtualProgramService.getAllCache();
        Map<String, ProgramAlbumEs> virtualProgramMap = new HashMap<>();
        List<String> priorityList = searchConfig.getFilterPriority();
        for (String virtualId : allProgramMap.keySet()) {
            Map<String, ProgramAlbumEs> programMap = allProgramMap.get(virtualId);
            for (String source : priorityList) {
                if (programMap.get(source) != null) {
                    virtualProgramMap.put(virtualId, programMap.get(source));
                    break;
                }
            }
        }
        return virtualProgramMap;
    }

    public void batchInsertActorEs(Map<String, Integer> actorMap) {
        elasticsearchService.deleteIndex(HotActorEs.class);
        elasticsearchService.createIndexAndMapping(HotActorEs.class);
        List<IndexQuery> indexQueries = new ArrayList<>();
        int i = 0;
        for (String s : actorMap.keySet()) {
            i++;
            HotActorEs hotActorEs = new HotActorEs();
            String[] suggest = {s};
            Completion suggestCompletion = new Completion(suggest);
            suggestCompletion.setWeight(actorMap.get(s));
            hotActorEs.setActorName(suggestCompletion);
            hotActorEs.setName(s);
            hotActorEs.setActorNamePinyin(suggestCompletion);
            hotActorEs.setIsHot(actorMap.get(s));
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(hotActorEs);
            indexQueries.add(indexQuery);
            if ((i != 0 && i % 500 == 0) || i == actorMap.size()) {
                restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(HotActorEs.class.getAnnotation(Document.class).indexName()));
                indexQueries = new ArrayList<>();
            }
        }
        IndexOperations indexOperations = restTemplate.indexOps(HotActorEs.class);
        indexOperations.refresh();
    }


    public void setActorEs(ProgramAlbumEs programAlbum, Map<String, Integer> actorMap) {
        String contentType = programAlbum.getContentType();
        if (!ContentTypeEnum.MOVIE.getCode().equals(contentType) && !ContentTypeEnum.TV.getCode().equals(contentType)
                && !ContentTypeEnum.SHOW.getCode().equals(contentType)) {
            return;
        }
        Set<String> actorAndDirector = new HashSet<>();
        this.addAllActor(actorAndDirector, programAlbum.getActor());
        this.addAllActor(actorAndDirector, programAlbum.getDirector());
        // 移除未知，无，明星数据
        actorAndDirector.removeAll(CommonConstant.errorActorName);
        if (CollectionUtils.isEmpty(actorAndDirector)) {
            return;
        }

        Integer isHot = programAlbum.getIsHot();
        for (String s : actorAndDirector) {
            if (s.length() == 0 || s.contains("/")) {
                continue;
            }
            Integer score = hotVideoProperties.getHotActor().contains(s) ? 4 : 1;
            score += isHot;
            if (actorMap.containsKey(s)) {
                int preScore = actorMap.get(s);
                if (preScore < score) {
                    actorMap.put(s, score);
                }
            } else {
                actorMap.put(s, score);
            }
        }

    }

    /**
     * 将 director进行切分。过滤掉空字符串，将结果加入  actorAndDirector中
     * @param actorAndDirector
     * @param director
     */
    private void addAllActor(Set<String> actorAndDirector, String director) {
        if(StringUtil.isEmpty(director)) {
            return;
        }
        actorAndDirector.addAll(Arrays.stream(director.split("\\|"))
                .map(String::trim)
                .filter(dir -> !dir.isEmpty())
                .collect(Collectors.toList()));
    }

    private List<ProgramAlbumEs>  selectProgramAlbumEsByVirtualSid(String virtualSid){
        List<ProgramAlbumEs> programAlbumEsList =new ArrayList<>();
        List<MisVirtualProgramRelation> virtualProgramRelationList =virtualProgramMapper.selectVirtualProgramByVirtualSid(virtualSid,CommonConstant.allowSource);
        if(CollectionUtils.isNotEmpty(virtualProgramRelationList)){
            for (MisVirtualProgramRelation virtualProgramRelation : virtualProgramRelationList) {
                String sid =virtualProgramRelation.getSid();
                int database =Math.abs(sid.hashCode() / (tableEnd+1)) % (datebaseEnd+1);
                int table =Math.abs(sid.hashCode() % (tableEnd+1));
                StandardAlbumEs standardAlbumEs =standardAlbumMapper.selectStandardAlbumBySid(database,table,sid,1);
                if(standardAlbumEs !=null){
                    ProgramAlbumEs programAlbumEs = standardAlbumToProgramAlbumEs(standardAlbumEs);
                    programAlbumEs.setVirtualSid(virtualSid);
                    programAlbumEsList.add(programAlbumEs);
                }
            }
        }
        return programAlbumEsList;
    }

    private ProgramAlbumEs standardAlbumToProgramAlbumEs(StandardAlbum standardAlbum){
        ProgramAlbumEs programAlbumEs =new ProgramAlbumEs();
        BeanUtils.copyProperties(standardAlbum,programAlbumEs);
        programAlbumEs.setEpstitle(standardAlbum.getSubTitle());
        programAlbumEs.setContentType(standardAlbum.getProgramType());
        programAlbumEs.setSourceScore(StringUtil.isBlank(standardAlbum.getSourceScore()) ? 0.0F : Float.parseFloat(standardAlbum.getSourceScore()));
        setDoubanAndOppoScore(standardAlbum, programAlbumEs);
        if (standardAlbum.getFreeStartTime() != null) {
            programAlbumEs.setFreeStartTime(standardAlbum.getFreeStartTime());
        }
        if (standardAlbum.getFreeEndTime() != null) {
            programAlbumEs.setFreeEndTime(standardAlbum.getFreeEndTime());
        }
        return programAlbumEs;
    }


    private void updateVirtualCache(StandardAlbum standardAlbum,String virtualSid,Integer status){
        String virtualValue = jedisCluster.hget(VirtualProgramService.virtualCacheKey,virtualSid);
        Map<String,ProgramAlbumEs> virtualMap =new HashMap<>();
        if(StringUtil.isNotBlank(virtualValue)){
            virtualMap= JSON.parseObject(virtualValue,new TypeReference<Map<String, ProgramAlbumEs>>(){});
        }
        if(standardAlbum.getStatus() == 1 && standardAlbum.getFeatureType() == 1 && Objects.equals(status,1)){
            virtualMap.put(standardAlbum.getSource(),standardAlbumToProgramAlbumEs(standardAlbum));
        }else{
            virtualMap.remove(standardAlbum.getSource());
        }
        jedisCluster.hset(VirtualProgramService.virtualCacheKey,virtualSid,JSON.toJSONString(virtualMap));
    }

    /**
     * 数据更新
     *
     */
    public void update(StandardAlbum standardAlbum, RowData rowData) {
        if (standardAlbum == null ||
                "live".equals(standardAlbum.getProgramType()) ||
                !CommonConstant.allowSource.contains(standardAlbum.getSource())) {
            return;
        }
        String eventType = rowData.getEventType();
        boolean deleteData = true;
        switch (eventType) {
            case "UPDATE":
                StandardAlbum before = JSON.parseObject(JSON.toJSONString(rowData.getBefore()), StandardAlbum.class);
                if (before.getStatus() == standardAlbum.getStatus() && standardAlbum.getStatus() == 1) {
                    deleteData = false;
                }
                break;
            default:
                break;
        }

        MisVirtualProgramRelation misVirtualProgramRelation = virtualProgramMapper.selectVirtualProgramBySid(standardAlbum.getSid());
        List<ProgramAlbumEs> allVirtualAlbumList = handleVirtualProgram(deleteData, misVirtualProgramRelation, standardAlbum);
        ProgramAlbumEs programAlbumEs = buildProgramAlbumEs(misVirtualProgramRelation, standardAlbum, allVirtualAlbumList);

        if (programAlbumEs != null) {
            setDoubanAndOppoScore(standardAlbum, programAlbumEs);
            setProgramAlbumEs(programAlbumEs, Calendar.getInstance().get(Calendar.YEAR));
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery("sid", programAlbumEs.getSid()));
            NativeSearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQuery).build();
            SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
            for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
                ProgramAlbumEs oldProgramAlbumEs = searchHit.getContent();
                programAlbumEs.setOppoHot(oldProgramAlbumEs.getOppoHot());
                programAlbumEs.setDayNo(oldProgramAlbumEs.getDayNo());
                programAlbumEs.setLast7DaysClickPv(oldProgramAlbumEs.getLast7DaysClickPv());
                programAlbumEs.setLast15DaysClickPv(oldProgramAlbumEs.getLast15DaysClickPv());
                programAlbumEs.setLast30DaysClickPv(oldProgramAlbumEs.getLast30DaysClickPv());
                programAlbumEs.setLast7DaysPlayPv(oldProgramAlbumEs.getLast7DaysPlayPv());
                programAlbumEs.setLast15DaysPlayPv(oldProgramAlbumEs.getLast15DaysPlayPv());
                programAlbumEs.setLast30DaysPlayPv(oldProgramAlbumEs.getLast30DaysPlayPv());
            }
            restTemplate.save(programAlbumEs);
            saveActorEs(programAlbumEs);
        }
    }

    private List<ProgramAlbumEs> handleVirtualProgram(boolean deleteData, MisVirtualProgramRelation misVirtualProgramRelation,
                                      StandardAlbum standardAlbum) {
        List<ProgramAlbumEs> allVirtualAlbumList = new ArrayList<>();
        if (deleteData || misVirtualProgramRelation != null) {
            restTemplate.delete(standardAlbum.getSid(), ProgramAlbumEs.class);
            deleteActorEs(standardAlbum);
            if (misVirtualProgramRelation != null) {
                //处理 原来不是虚拟，现在是 + 原来是虚拟，现在失效
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.must(QueryBuilders.termQuery("sid", standardAlbum.getSid()));
                boolQuery.must(QueryBuilders.termQuery("virtualSid",misVirtualProgramRelation.getVirtualSid()));
                restTemplate.delete(new NativeSearchQueryBuilder().withQuery(boolQuery).build(), ProgramAlbumEs.class,IndexCoordinates.of(ProgramAlbumEs.class.getAnnotation(Document.class).indexName()));
                allVirtualAlbumList = selectProgramAlbumEsByVirtualSid(misVirtualProgramRelation.getVirtualSid());
                for (ProgramAlbumEs programAlbumEs : allVirtualAlbumList) {
                    restTemplate.delete(programAlbumEs.getSid(), ProgramAlbumEs.class);
                }
            }
        }
        return allVirtualAlbumList;
    }

    private ProgramAlbumEs buildProgramAlbumEs(MisVirtualProgramRelation misVirtualProgramRelation, StandardAlbum standardAlbum,
                                List<ProgramAlbumEs> allVirtualAlbumList) {
        ProgramAlbumEs programAlbumEs = null;
        if (misVirtualProgramRelation != null){
            updateVirtualCache(standardAlbum,misVirtualProgramRelation.getVirtualSid(),misVirtualProgramRelation.getStatus());
        }
        if (misVirtualProgramRelation != null && CollectionUtils.isNotEmpty(allVirtualAlbumList)) {
            Map<String, ProgramAlbumEs> programAlbumEsMap = allVirtualAlbumList.stream().collect(Collectors.toMap(ProgramAlbumEs::getSource, l -> l, (u1, u2) -> u1));
            List<String> priorityList = searchConfig.getFilterPriority();
            for (String source : priorityList) {
                if (!programAlbumEsMap.containsKey(source)) {
                    continue;
                }
                programAlbumEs = programAlbumEsMap.get(source);
                programAlbumEs.setHasVirtualSid(1);
                break;
            }
        } else if (standardAlbum.getStatus() == 1 && (standardAlbum.getFeatureType() == 1 || standardAlbum.getFeatureType() == 2)) {
            programAlbumEs = standardAlbumToProgramAlbumEs(standardAlbum);
        }
        if (programAlbumEs != null && standardAlbum.getFreeStartTime() != null) {
            programAlbumEs.setFreeStartTime(standardAlbum.getFreeStartTime());
        }
        if (programAlbumEs != null && standardAlbum.getFreeEndTime() != null) {
            programAlbumEs.setFreeEndTime(standardAlbum.getFreeEndTime());
        }
        return programAlbumEs;
    }

    private static void setDoubanAndOppoScore(StandardAlbum standardAlbum, ProgramAlbumEs programAlbumEs) {
        if (StringUtils.isNotEmpty(standardAlbum.getDoubanScore())) {
            programAlbumEs.setDoubanScore(Float.parseFloat(standardAlbum.getDoubanScore()));
        }
        if (StringUtils.isNotEmpty(standardAlbum.getOppoScore())) {
            programAlbumEs.setOppoScore(Float.parseFloat(standardAlbum.getOppoScore()));
        }
    }

    private void deleteActorEs(StandardAlbum standardAlbum) {
        try {
            Set<String> actorAndDirector = new HashSet<>();
            if (StringUtil.isNotEmpty(standardAlbum.getActor())) {
                String[] actors = standardAlbum.getActor().split("\\|");
                for (String actor : actors) {
                    actorAndDirector.add(actor);
                }
            }
            if (StringUtil.isNotEmpty(standardAlbum.getDirector())) {
                String[] directors = standardAlbum.getDirector().split("\\|");
                for (String director : directors) {
                    actorAndDirector.add(director);
                }
            }
            for (String s : actorAndDirector) {
                long n = actorService.getActorProgramCount(s);
                if (n <= 1) {
                    HotActorEs hotActorEs = new HotActorEs();
                    hotActorEs.setName(s);
                    restTemplate.delete(hotActorEs);
                }
            }
        } catch (Exception e) {
            log.error("deleteActorEs error");
        }
    }

    private void saveActorEs(ProgramAlbumEs programAlbum) {
        if(Objects.isNull(programAlbum)) {
            return;
        }
        try {
            Map<String, Integer> actorMap = new HashMap<>();
            this.setActorEs(programAlbum, actorMap);
            for (String s : actorMap.keySet()) {
                HotActorEs oldHotActorEs = restTemplate.get(s, HotActorEs.class);
                boolean b = oldHotActorEs == null ||
                        (Objects.nonNull(oldHotActorEs.getIsHot()) && oldHotActorEs.getIsHot() < actorMap.get(s));
                if (b) {
                    HotActorEs newHotActor = new HotActorEs();
                    String[] suggest = {s};
                    Completion suggestCompletion = new Completion(suggest);
                    suggestCompletion.setWeight(actorMap.get(s));
                    newHotActor.setActorName(suggestCompletion);
                    newHotActor.setName(s);
                    newHotActor.setActorNamePinyin(suggestCompletion);
                    newHotActor.setIsHot(actorMap.get(s));
                    restTemplate.save(newHotActor);
                }
            }
        } catch (Exception e) {
            log.error("insertActorEs error", e);
        }
    }


    /**
     * 数据字段处理
     *
     * @param programAlbum
     * @param nowYear
     */
    public void setProgramAlbumEs(ProgramAlbumEs programAlbum, int nowYear) {
        programAlbum.setTitlePinyin(programAlbum.getTitle());
        if (StringUtil.isBlank(programAlbum.getVirtualSid())) {
            programAlbum.setVirtualSid(programAlbum.getSid());
        }
        if (StringUtil.isNotBlank(programAlbum.getTags())) {
            programAlbum.setTags(programAlbum.getTags().replace("|", ","));
        }
        if (StringUtil.isBlank(programAlbum.getShowTime())) {
            programAlbum.setShowTime(programAlbum.getYear() + "01");
        }
        handleActorAndDirector(programAlbum);

        BigDecimal functionScore = handleFunctionScore(programAlbum, nowYear);

        int weight = functionScore.multiply(new BigDecimal(100)).intValue();
        String[] titleSuggest = {programAlbum.getTitle()};
        Completion suggestTitleCompletion = new Completion(titleSuggest);
        suggestTitleCompletion.setWeight(weight);
        programAlbum.setSuggestTitlePinyin(suggestTitleCompletion);

        String seriesTitle = programAlbum.getTitle();
        Map<String, String> seriesAlbumMap = seriesAlbumService.getCache();
        if (seriesAlbumMap.containsKey(programAlbum.getTitle())) {
            seriesTitle = seriesAlbumMap.get(programAlbum.getTitle());
            programAlbum.setIsSeries(1);
        }
        String[] titleSeriesSuggest = {seriesTitle};
        Completion seriesTitleCompletion = new Completion(titleSeriesSuggest);
        seriesTitleCompletion.setWeight(weight);
        programAlbum.setSeriesTitle(seriesTitleCompletion);
        programAlbum.setSeriesTitlePinyin(seriesTitleCompletion);

    }

    private void handleActorAndDirector(ProgramAlbumEs programAlbum) {
        String oldActor = programAlbum.getActor();
        if (StringUtil.isNotBlank(oldActor)) {
            String[] oldActors = oldActor.split("\\|");
            List<String> newActors = new ArrayList<>();
            for (String act : oldActors) {
                if (!CommonConstant.errorActorName.contains(act)) {
                    newActors.add(act);
                }
            }
            programAlbum.setActor(String.join("|", newActors));
            programAlbum.setActorPinyin(programAlbum.getActor());
        }
        String oldDirector = programAlbum.getDirector();
        if (StringUtil.isNotBlank(oldDirector)) {
            String[] oldDirectors = oldDirector.split("\\|");
            List<String> newDirectors = new ArrayList<>();
            for (String dir : oldDirectors) {
                if (!CommonConstant.errorActorName.contains(dir)) {
                    newDirectors.add(dir);
                }
            }

            programAlbum.setDirector(String.join("|", newDirectors));
            programAlbum.setDirectorPinyin(programAlbum.getDirector());
        }
    }

    /**
     * 处理排序分，影响后续ES查询时排序
     */
    private BigDecimal handleFunctionScore(ProgramAlbumEs programAlbum, int nowYear) {
        //根据评分增加0 ~0.2倍的推荐值
        float sourceScore = programAlbum.getSourceScore() >= 9 ? 9 : programAlbum.getSourceScore();
        BigDecimal functionScore = sourceScore <= 5 ? BigDecimal.ZERO : new BigDecimal(sourceScore - 5).setScale(2, BigDecimal.ROUND_HALF_UP);
        functionScore = new BigDecimal(1).add(new BigDecimal(0.03).multiply(functionScore)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if(programAlbum.getFeatureType()==1){
            //最近一年内的增加0.1的推荐值
            int yearArrang = nowYear - programAlbum.getYear();
            Integer showTime = Integer.parseInt(programAlbum.getShowTime());
            Integer nowShowTime = Integer.parseInt(sdf.format(new Date()));
            if (nowShowTime.equals(showTime) || nowShowTime <= showTime + 1) {
                functionScore = functionScore.add(new BigDecimal(0.16).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else if (nowShowTime <= showTime + 3) {
                functionScore = functionScore.add(new BigDecimal(0.13).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else if (nowShowTime <= showTime + 6) {
                functionScore = functionScore.add(new BigDecimal(0.11).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else if (yearArrang <= 10) {
                functionScore = functionScore.add(new BigDecimal((0.1 - 0.01 * yearArrang)).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
//        产品下线配置热词库加权逻辑
//        if (isConfigHotKey(programAlbum)) {
//            functionScore = functionScore.add(new BigDecimal(0.8)).setScale(2, BigDecimal.ROUND_HALF_UP);
//            programAlbum.setIsHot(2);
//        } else
        if (isRankHotKey(programAlbum)) {
            functionScore = functionScore.add(new BigDecimal(0.3)).setScale(2, BigDecimal.ROUND_HALF_UP);
            programAlbum.setIsHot(1);
        }
        if (programAlbum.getIsSeries() == 1) {
            functionScore = functionScore.add(new BigDecimal(0.05)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        programAlbum.setFunctionScore(functionScore.floatValue());
        return functionScore;
    }

    private boolean isConfigHotKey(ProgramAlbumEs es) {
        try {
            Set<String> hotKeySet = getConfig(es);
            if (CollectionUtils.isEmpty(hotKeySet)) {
                return false;
            }
            for (String s : hotKeySet) {
                if (es.getTitle().startsWith(s)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("isConfigHotKey error", e);
        }
        return false;
    }

    private Set<String> getConfig(ProgramAlbumEs es) {
        ContentTypeEnum contentTypeEnum = ContentTypeEnum.getByCode(es.getContentType());
        if (contentTypeEnum == null) {
            return null;
        }
        switch (contentTypeEnum) {
            case COMIC:
                return hotVideoProperties.getComicHotTitle();
            case TV:
                return hotVideoProperties.getTvHotTitle();
            case SHOW:
                return hotVideoProperties.getShowHotTitle();
            case KIDS:
                return hotVideoProperties.getKidsHotTitle();
            case MOVIE:
                return hotVideoProperties.getMovieHotTitle();
            default:
                return null;
        }
    }


    private boolean isRankHotKey(ProgramAlbumEs es) {
        try {
            ContentTypeEnum contentTypeEnum = ContentTypeEnum.getByCode(es.getContentType());
            if (contentTypeEnum == null) {
                return false;
            }
            ConcurrentHashMap concurrentHashMap = hotVideoService.getHashMap(contentTypeEnum);
            if (concurrentHashMap == null) {
                return false;
            }
            boolean isHotKey = SensitiveWordUtil.isContaintSensitiveWord(es.getTitle(), concurrentHashMap);
            return isHotKey;
        } catch (Exception e) {
            log.error("isHotKey error", e);
        }
        return false;
    }

    public void minorsRefreshToEs(){
        try {
            List<MinorPoolCodeRecord> minorPoolCodeRecords = this.lvContentPoolRpcApiProxy.getMinorsPoolCode(MinorsPoolCodeEnum.AGE_16_TO_18.getValue())
                    .get(2, TimeUnit.SECONDS);
            if (CollectionUtils.isEmpty(minorPoolCodeRecords)) {
                log.error("minorsRefreshToEs error poolCode is null Records{}", minorPoolCodeRecords);
                return;
            }
            minorPoolCodeRecords
                    .stream()
                    .map(MinorPoolCodeRecord::getPoolCode)
                    .forEach(poolCode->{
                        AlbumSearchParameterModifier param = new AlbumSearchParameterModifier();
                        param.setPoolCode(poolCode);
                        this.adjustApiSearchAlbumService.collateSinglePoolCodeApiSearchAlbum(param);
                });

        } catch (Exception e) {
            log.error("minorsRefreshToEs error ", e);
        }
    }

    public void unofficialAlbumCreateIndex(){
        elasticsearchService.deleteIndex(UnofficialAlbumEsForCreateIndex.class);
        elasticsearchService.createIndexAndMapping(UnofficialAlbumEsForCreateIndex.class);
    }
}
