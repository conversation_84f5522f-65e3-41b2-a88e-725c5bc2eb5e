package com.heytap.longvideo.search.service.standard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardPersonRpcApi;
import com.heytap.longvideo.client.media.entity.StandardPerson;
import com.heytap.longvideo.client.media.query.PageRequest;
import com.heytap.longvideo.client.media.query.StandardPersonExample;
import com.heytap.longvideo.search.model.param.standard.ListStandardPersonRequest;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * @Description: 标准影人RPC接口调用
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/5/13
 */
@Slf4j
@Service
public class StandardPersonService {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardPersonRpcApi standardPersonRpcApi;

    public CompletableFuture<RpcResult<IPage<StandardPerson>>> listMediaPerson(ListStandardPersonRequest request) {
        try {
            StandardPersonExample queryCondition = new StandardPersonExample();
            copyProperties(queryCondition, request);
            PageRequest pageRequest = new PageRequest(request.getPageIndex(), request.getPageSize());
            return standardPersonRpcApi.pageList(queryCondition, pageRequest);
        } catch (Exception e) {
            log.error("listStandardPerson error, request:{}", request, e);
            return CompletableFuture.completedFuture(null);
        }
    }

    private void copyProperties(StandardPersonExample queryCondition, ListStandardPersonRequest request) {
        queryCondition.setSid(request.getSid());
        queryCondition.setSource(request.getSource());
        queryCondition.setId(request.getId());
        queryCondition.setSourcePersonId(request.getSourcePersonId());
        queryCondition.setStatus(request.getStatus());
        queryCondition.setCnName(request.getCnName());
        queryCondition.setForeName(request.getForeName());
        queryCondition.setSortType(request.getSortType());
    }
}
