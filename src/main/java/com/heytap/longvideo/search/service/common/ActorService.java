package com.heytap.longvideo.search.service.common;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.HotActorEs;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Cache;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/*
 * 影人及导演
 * Date 18:58 2021/12/21
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class ActorService {

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    //影人及导演缓存
    Cache<String, Set<String>> actorAndDirectorCache = Caffeine.newBuilder()
            .maximumSize(2)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build();

    private static String actorAndDirectorCacheKey = "actorAndDirector";

    /**
     * 判断是否影人
     *
     * @param keyword
     * @return
     */
    public boolean checkIsActorAndDirector(String keyword) {
        Set<String> actorAndDirectorSet = actorAndDirectorCache.getIfPresent(actorAndDirectorCacheKey);
        if (CollectionUtils.isNotEmpty(actorAndDirectorSet)) {
            return actorAndDirectorSet.contains(keyword);
        }
        CompletableFuture.runAsync(() -> {
            synchronized (actorAndDirectorCacheKey) {
                if (CollectionUtils.isNotEmpty(actorAndDirectorCache.getIfPresent(actorAndDirectorCacheKey))) {
                    return;
                }
                Set<String> actorAndDirector = selectAllActorAndDirector();
                actorAndDirector.removeAll(CommonConstant.errorActorName);
                actorAndDirectorCache.put(actorAndDirectorCacheKey, actorAndDirector);
            }
        });
        return false;
    }


    public Set<String> selectAllActorAndDirector() {
        Set<String> returnSet = new HashSet<>();
        try {
            // 分页查询总上限index.max_result_window=30000，全量查询需使用滚动查询
            SearchSourceBuilder builder = new SearchSourceBuilder()
                    .query(QueryBuilders.matchAllQuery())
                    .trackTotalHits(true)
                    .size(5000);
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.API_HOT_ACTOR)
                    .source(builder);
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1));
            searchRequest.scroll(scroll);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            SearchHit[] hits = searchResponse.getHits().getHits();
            while (ArrayUtils.isNotEmpty(hits)) {
                for (SearchHit hit : hits) {
                    // 该对象包含没有无参构造函数的外部类，使用Jackson需要自定义反序列化器，太麻烦，仍用FastJSON
                    HotActorEs es = JSON.parseObject(hit.getSourceAsString(), HotActorEs.class);
                    returnSet.add(es.getName());
                }
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(scroll);
                SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
                scrollId = searchScrollResponse.getScrollId();
                hits = searchScrollResponse.getHits().getHits();
            }
            log.info("selectAllActorAndDirector success. totalCounts={}", returnSet.size());
        } catch (Exception e) {
            log.error("selectAllActorAndDirector error", e);
        }
        return returnSet;
    }

    public long getActorProgramCount(String name) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();

        shouldQuery.should(QueryBuilders.matchQuery("actor", name));
        shouldQuery.should(QueryBuilders.matchQuery("director", name));
        boolQueryBuilder.must(shouldQuery);

        CountRequest countRequest = new CountRequest(ProgramAlbumEs.class.getAnnotation(Document.class).indexName());

        countRequest.query(boolQueryBuilder);
        try {
            CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            RestStatus status = countResponse.status(); // ES请求状态
            if (status != RestStatus.OK) {
                log.error("count status not ok:{}", JSON.toJSONString(status));
                return 0;
            }
            long count = countResponse.getCount();
            return count;
        } catch (Exception e) {
            log.error("count", e);
        }
        return 0;
    }

}
