package com.heytap.longvideo.search.service.app;

import com.google.common.collect.Lists;
import com.heytap.longvideo.search.model.DouBanSearchRequest;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 向域外提交豆瓣爬取任务
 * <AUTHOR>
 * @date 2025/5/13 16:21
 */
@Slf4j
@Component
public class DouBanCommitTaskService {

    @Autowired
    private HttpDataChannel httpDataChannel;
    @Autowired
    private SearchProperties searchProperties;

    public static final int TOP_TASK_TYPE = 3;

    public void commitDouBanTask(List<String> titles) {
        if(CollectionUtils.isEmpty(titles)) {
            return;
        }
        List<String> names = Lists.partition(titles, 20).stream()
                .map(group -> String.join("#", group))
                .collect(Collectors.toList());

        for (String name : names) {
            DouBanSearchRequest douBanSearchRequest = new DouBanSearchRequest();
            douBanSearchRequest.setNames(name);
            douBanSearchRequest.setTaskType(TOP_TASK_TYPE);
            try {
                httpDataChannel.postForObjectWithGenerics(this.getUrl(searchProperties.getDoubanTaskCommitPath()),
                        douBanSearchRequest, String.class, 3000);
            } catch (HttpDataChannelException e) {
                log.error("commitDouBanTask error name:{}", name, e);
            }
        }
    }


    public String getUrl(String doubanTaskCommitPath) {
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(searchProperties.getYunheUrl());
        urlBuilder.append(doubanTaskCommitPath);
        urlBuilder.append("?")
                .append(YunheRankService.NAME).append("=").append(searchProperties.getYunheName())
                .append("&")
                .append(YunheRankService.WORD).append("=").append(searchProperties.getYunhePwd());
        return urlBuilder.toString();
    }
}
