package com.heytap.longvideo.search.service.common;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.UrlCoderUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_SOHU;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_YOUKU_MOBILE;

/**
 * @Description: 搜狐VIP内容处理 - Service
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/6/25
 */
@Slf4j
@Service
public class CommonService {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;

    public boolean isSohuVipContent(StandardAlbum album) {

        // 未找到对应剧头
        if (album == null) {
            return false;
        }

        // 非sohu节目
        if (!COPYRIGHT_SOHU.equals(album.getSource())) {
            return false;
        }

        // 未完结
        if (Objects.equals(album.getCompleted(),0) ) {
            return false;
        }

        // 更新集数==全部集数
        if (Objects.equals(album.getValidEpisode(), album.getTotalEpisode())) {
            return false;
        }

        try {
            // 当前时间
            long currentTimeMillis = System.currentTimeMillis();
            // 节目的最后一次更新时间（已完结的节目可认为是完结时间）
            String completedTimeStr = album.getShowTime();
            // 如果showTime字段为空 那么没有字段标识节目的完结时间
            if (StringUtils.isEmpty(completedTimeStr)) {
                return false;
            }

            Date completedTime = new SimpleDateFormat("yyyyMM").parse(completedTimeStr);
            long completedTimeMillis = completedTime.getTime();
            long diffTimeMillis = currentTimeMillis - completedTimeMillis;
            long diffTimeDays = TimeUnit.MILLISECONDS.toDays(diffTimeMillis);

            // 完结时间<=1年
            return diffTimeDays > 365;
        } catch (Exception e) {
            log.error("calculate end time error:", e);
            // 计算完结时间失败 也直接continue
            return false;
        }
    }

    public Map<String, StandardAlbum> fetchAlbumMap(List<String> sidList) {
        // sid -> standardAlbum
        Map<String, StandardAlbum> albumMap = new HashMap<>();
        if (CollectionUtils.isEmpty(sidList)) {
            return albumMap;
        }

        CompletableFuture<RpcResult<Map<String, StandardAlbum>>> albumFuture = standardAlbumRpcApi.getBySidsFilterInvalid(sidList);
        RpcResult<Map<String, StandardAlbum>> albumRpcResult = FutureUtil.getFutureIgnoreException(albumFuture, 1, TimeUnit.SECONDS);
        if (albumRpcResult != null) {
            albumMap = albumRpcResult.getData();
        }
        return albumMap;
    }

    public void handleSohuVipContents(SearchInterveneCardResponse searchInterveneCardResponse) {
        List<String> sidList = searchInterveneCardResponse.getContents().stream()
                .map(KeyWordSearchResponse::getSid)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(sidList)) {
            Map<String, StandardAlbum> albumMap = fetchAlbumMap(sidList);

            for (KeyWordSearchResponse keyWordSearchResponse : searchInterveneCardResponse.getContents()) {
                // 找到对应剧头 判断是否是搜狐VIP内容
                StandardAlbum album = albumMap.get(keyWordSearchResponse.getSid());
                if (isSohuVipContent(album)) {
                    keyWordSearchResponse.setProgramInfo("");
                }
            }
        }
    }

    public void addAlgorithmTransparentToDeepLink(KeyWordSearchResponse keyWordSearchResponse, String algorithmTransparent) {
        if (Objects.isNull(keyWordSearchResponse) || StringUtils.isEmpty(algorithmTransparent)) {
            return;
        }

        String deepLink = keyWordSearchResponse.getDeepLink();

        if (!deepLink.contains("linkValue")) {
            return;
        }

        String[] splitRes1 = deepLink.split("\\?");
        if (splitRes1.length <= 1) {
            return;
        }

        String[] strArr = splitRes1[1].split("&");
        if (strArr.length == 0) {
            return;
        }

        StringBuilder stringBuilder = new StringBuilder(splitRes1[0]);
        stringBuilder.append("?").append(strArr[0]);

        if (strArr[0].contains("linkValue")) {
            stringBuilder.append("linkValue=").append(addAlgorithmTransparentToDeepLink(algorithmTransparent, strArr[0]));
        } else {
            stringBuilder.append(strArr[0]);
        }

        if (strArr.length > 1) {
            for (int i=1;i<strArr.length;i++) {
                if (strArr[i].contains("linkValue")) {
                    stringBuilder.append("&linkValue=").append(addAlgorithmTransparentToDeepLink(algorithmTransparent, strArr[i]));
                } else {
                    stringBuilder.append("&").append(strArr[i]);
                }
            }
        }

        keyWordSearchResponse.setDeepLink(stringBuilder.toString());
    }

    private String addAlgorithmTransparentToDeepLink(String algorithmTransparent, String str) {
        String[] splitResult = str.split("=");
        if (splitResult.length == 2) {
            String linkValue = splitResult[1];
            String decodedLinkValue = UrlCoderUtil.decode(linkValue, StandardCharsets.UTF_8);
            decodedLinkValue += UrlCoderUtil.encode(("&transparent=" + algorithmTransparent), StandardCharsets.UTF_8);
            return UrlCoderUtil.encode(decodedLinkValue, StandardCharsets.UTF_8);
        }

        return str;
    }

    /**
     * @Description: 算法场景 优酷内容拼接算法透传字段
     */
    public void addAlgorithmTransparentToDeepLink(SearchInterveneCardResponse recommendResponse) {
        if (Objects.isNull(recommendResponse)) {
            return;
        }

        if (StringUtils.isNotEmpty(recommendResponse.getTransparent())) {
            for (KeyWordSearchResponse keyWordSearchResponse : recommendResponse.getContents()) {
                if (Objects.equals(COPYRIGHT_YOUKU_MOBILE, keyWordSearchResponse.getSource())) {
                    addAlgorithmTransparentToDeepLink(keyWordSearchResponse, recommendResponse.getTransparent());
                }
            }
        }
    }
}