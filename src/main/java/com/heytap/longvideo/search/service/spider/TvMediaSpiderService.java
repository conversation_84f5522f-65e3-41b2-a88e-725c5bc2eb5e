package com.heytap.longvideo.search.service.spider;

import com.heytap.longvideo.search.api.model.request.TvMediaScrollSearchRequest;
import com.heytap.longvideo.search.api.model.response.TvMediaScrollSearchResponse;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> ye mengsheng
 * @Description：电视台增量/全量爬虫
 * @Version: 1.0
 * @date 2025/5/14 下午8:47
 */

@Slf4j
@Service
public class TvMediaSpiderService {

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    /**
     * 电视媒资增量爬取
     */
    public TvMediaScrollSearchResponse tvMediaSpiderIncr(TvMediaScrollSearchRequest request) throws IOException {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(tvMediaSpiderQueryBuilder("incr"))
                .size(request.getBatchSize());

        SearchRequest searchRequest = new SearchRequest()
                .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                .source(searchSourceBuilder);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        return buildResponse(searchResponse);
    }

    /**
     * 电视媒资全量爬取
     */
    public TvMediaScrollSearchResponse tvMediaSpiderAll(TvMediaScrollSearchRequest request) throws IOException {
        // 第一次请求创建游标
        if (StringUtils.isEmpty(request.getScrollId())) {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(tvMediaSpiderQueryBuilder("all"))
                    .size(request.getBatchSize());
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(request.getScrollTimeInMinutes()));
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX);
            searchRequest.scroll(scroll);
            searchRequest.source(searchSourceBuilder);
            // 执行初始搜索请求
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            return buildResponse(searchResponse);
        }

        // 使用游标获取后续批次
        SearchScrollRequest scrollRequest = new SearchScrollRequest(request.getScrollId());
        scrollRequest.scroll(TimeValue.timeValueMinutes(request.getScrollTimeInMinutes()));
        SearchResponse searchResponse = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
        return buildResponse(searchResponse);
    }

    private TvMediaScrollSearchResponse buildResponse(SearchResponse searchResponse) {
        TvMediaScrollSearchResponse res = new TvMediaScrollSearchResponse();
        SearchHits hits = searchResponse.getHits();
        HashSet<String> titleSet = new HashSet<>();
        String regex = "[\\(（].*?[\\)）]";
        Pattern pattern = Pattern.compile(regex);
        for (SearchHit hit : hits.getHits()) {
            UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
            // 包含括号爬虫搜索不准确，因此去除括号
            Matcher matcher = pattern.matcher(unofficialAlbumEs.getTitle());
            String title = matcher.replaceAll("");
            titleSet.add(title);
        }
        res.setTitleList(new ArrayList<>(titleSet));
        res.setHasMore(titleSet.size() > 0);
        res.setScrollId(searchResponse.getScrollId());
        return res;
    }

    private BoolQueryBuilder tvMediaSpiderQueryBuilder(String type) {
        BoolQueryBuilder boolQueryBuilder = null;
        if ("all".equals(type)) {
            boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("programType", Arrays.asList("tv", "movie", "comic", "show", "doc", "kids")))
                    .must(QueryBuilders.termsQuery("source", Arrays.asList("iqiyi", "tencent")))
                    .must(QueryBuilders.termQuery("featureType", "1"))
                    .must(QueryBuilders.termQuery("sourceStatus", "1"))
                    .must(QueryBuilders.termQuery("verifyStatus", "1"))
                    .mustNot(QueryBuilders.wildcardQuery("subProgramType", "*预告片*"));
        } else if ("incr".equals(type)) {
            ZonedDateTime yesterdayStart = ZonedDateTime.now(ZoneOffset.UTC)
                    .minusDays(1)
                    .with(LocalTime.MIN);
            ZonedDateTime yesterdayEnd = yesterdayStart.plusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
            String startStr = formatter.format(yesterdayStart);
            String endStr = formatter.format(yesterdayEnd);

            boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("programType", Arrays.asList("tv", "movie", "comic", "show", "doc", "kids")))
                    .must(QueryBuilders.termsQuery("source", Arrays.asList("iqiyi", "tencent")))
                    .must(QueryBuilders.termQuery("featureType", "1"))
                    .must(QueryBuilders.termQuery("sourceStatus", "1"))
                    .must(QueryBuilders.termQuery("verifyStatus", "1"))
                    .must(QueryBuilders.rangeQuery("createTime").gt(startStr))
                    .must(QueryBuilders.rangeQuery("createTime").lt(endStr))
                    .mustNot(QueryBuilders.wildcardQuery("subProgramType", "*预告片*"));
        }
        return boolQueryBuilder;
    }

}
