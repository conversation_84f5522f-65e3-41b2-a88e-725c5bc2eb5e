package com.heytap.longvideo.search.service.sourcefilter;

import com.heytap.longvideo.client.media.enums.SourceEnum;
import org.springframework.stereotype.Component;
import shaded_package.io.swagger.models.auth.In;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2024/11/14 上午10:56
 */
@Component
public class FunshionLongVideoAndWeidiouFilterService {
    private static final Integer FUNSHION_LONG_VIDEO_AND_WEIDIOU_LOW_VERSION = 80100;

    /**
     * 返回true 过滤风行长视频和微迪欧
     */
    public boolean filterItemBySource(String source, Integer version) {
        if (SourceEnum.FUNSHION_LONGVIDEO.getDataSource().equals(source) || SourceEnum.WEIDIOU.getDataSource().equals(source)) {
            if (null == version) {
                return true;
            }
            return FUNSHION_LONG_VIDEO_AND_WEIDIOU_LOW_VERSION > version;
        }
        return false;
    }

    /**
     * 返回true 过滤风行长视频和微迪欧
     */
    public boolean filterItem(Integer version) {
        if (null == version) {
            return true;
        }
        return FUNSHION_LONG_VIDEO_AND_WEIDIOU_LOW_VERSION > version;
    }

}
