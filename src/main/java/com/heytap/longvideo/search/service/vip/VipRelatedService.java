package com.heytap.longvideo.search.service.vip;

import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.common.lib.constants.MobileBrandConstants;
import com.heytap.longvideo.common.media.programsource.SourceVersionService;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.rpc.consumer.VipRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.VirtualProgramRelationRpcApiProxy;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.vip.sdk.model.outer.VipInfo;
import com.heytap.longvideo.vip.sdk.model.outer.VipInfoRequest;
import com.heytap.longvideo.vip.sdk.model.outer.VipResponse;
import com.heytap.longvideo.vip.sdk.service.VipInfoService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.next.executor.NextRequest;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.cpc.video.framework.lib.vip.UserVipInfoRequest;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import com.oppo.trace.async.TraceBiFunction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.heytap.longvideo.search.constants.UserVipConstants.DEFAULT;
import static com.heytap.longvideo.search.constants.UserVipConstants.MONGO_AND_VIDEO_VIP;
import static com.heytap.longvideo.search.constants.UserVipConstants.MONGO_VIDEO_VIP;
import static com.heytap.longvideo.search.constants.UserVipConstants.VIDEO_VIP;

/**
 * @Description: 会员相关 - service
 * @Author: 80398885WT
 * @Date: 2025/6/4
 */
@Slf4j
@Service
public class VipRelatedService {

    @HeraclesDynamicConfig(key = "copyRight.priority", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, List<String>> vipBrandPriority;

    private final VipInfoService vipInfoService;

    private final VipRpcApiProxy vipRpcApiProxy;

    private final VirtualProgramRelationRpcApiProxy virtualProgramRelationRpcApiProxy;

    private final SourceVersionService sourceVersionService;

    public VipRelatedService(VipInfoService vipInfoService,
                             VipRpcApiProxy vipRpcApiProxy,
                             VirtualProgramRelationRpcApiProxy virtualProgramRelationRpcApiProxy,
                             SourceVersionService sourceVersionService) {
        this.vipInfoService = vipInfoService;
        this.vipRpcApiProxy = vipRpcApiProxy;
        this.virtualProgramRelationRpcApiProxy = virtualProgramRelationRpcApiProxy;
        this.sourceVersionService = sourceVersionService;
    }

    /**
     * 获取会员信息
     */
    public CompletableFuture<VipResponse<VipInfo>> getVipInfo(AttributeValues attributeValues, String userId, String vipType, boolean needSignInfo) {
        VipInfoRequest vipInfoRequest = new VipInfoRequest();
        vipInfoRequest.setUserId(userId);
        String brand = attributeValues != null && StringUtils.isNotEmpty(attributeValues.getChannel()) ? attributeValues.getChannel() : MobileBrandConstants.OPPO;
        vipInfoRequest.setBrand(brand);
        vipInfoRequest.setVipType(vipType);
        vipInfoRequest.setNeedSignInfo(needSignInfo);

        return vipInfoService.getVipInfo(vipInfoRequest);
    }

    /**
     * 获取userId
     */
    public String getUserIdFromCookie(NextRequest request) {
        String userId = "";
        Cookie cookie = request.getScookieIgnoreException();
        if (cookie != null && cookie.getInfo() != null) {
            // 未登录返回 ""
            userId = cookie.getInfo().getUid();
        }
        return userId;
    }

    public String getVipType(VideoVipInfo videoVipInfo) {
        return translateVideoVipInfo(videoVipInfo);
    }

    public String getVipType(String ssoid) {
        // 查询会员的rpc内部直接走了缓存
        VideoVipInfo videoVipInfo = FutureUtil.getFutureIgnoreException(getVipInfo(ssoid), 100, TimeUnit.MILLISECONDS);
        return translateVideoVipInfo(videoVipInfo);
    }

    private String translateVideoVipInfo(VideoVipInfo videoVipInfo) {
        if (videoVipInfo == null) {
            return DEFAULT;
        }

        String videoVipStatus = StringUtils.isBlank(videoVipInfo.getVideoVipStatus()) ? "0" : videoVipInfo.getVideoVipStatus();
        String mongoVideoVipStatus = StringUtils.isBlank(videoVipInfo.getMongoVideoVipStatus()) ? "0" : videoVipInfo.getMongoVideoVipStatus();

        // 非会员
        if ("0".equals(videoVipStatus) && "0".equals(mongoVideoVipStatus)) {
            return DEFAULT;
        }

        // 影视会员
        if ("1".equals(videoVipStatus) && "0".equals(mongoVideoVipStatus)) {
            return VIDEO_VIP;
        }

        // 芒果会员
        if ("1".equals(mongoVideoVipStatus) && "0".equals(videoVipStatus)) {
            return MONGO_VIDEO_VIP;
        }

        // 双会员
        return MONGO_AND_VIDEO_VIP;
    }

    public CompletableFuture<VideoVipInfo> getVipInfo(String ssoid) {
        VideoVipInfo videoVipInfo = new VideoVipInfo();
        videoVipInfo.setMongoVideoVipStatus("0");
        videoVipInfo.setVideoVipStatus("0");
        if (StringUtils.isBlank(ssoid)) {
            return CompletableFuture.completedFuture(videoVipInfo);
        }

        UserVipInfoRequest userVipInfoRequest = new UserVipInfoRequest();
        userVipInfoRequest.setUserId(ssoid);
        return vipRpcApiProxy.queryUserVipInfo(userVipInfoRequest).handle(new TraceBiFunction<>((rpcResult, e) -> {
            if (e != null) {
                log.error("vipRpcApiProxy.queryUserVipInfo rpc error, ssoid:{}, e:", ssoid, e);
                return videoVipInfo;
            }

            if (rpcResult == null || rpcResult.getData() == null) {
                log.error("vipRpcApiProxy.queryUserVipInfo result is null, ssoid:{}", ssoid);
                return videoVipInfo;
            }

            BeanUtils.copyProperties(rpcResult.getData(), videoVipInfo);
            return videoVipInfo;
        }));
    }

    /**
     * 会员身份替换
     */
    public void vipReplace(SearchByKeyWordContext context, ProgramAlbumEs programAlbumEs, String appVersion) {
        try {
            String vipType = getVipType(context.getVipInfo());
            StandardAlbum standardAlbum = getReplaceAlbum(vipType, programAlbumEs.getSid());

            if (standardAlbum == null) {
                return;
            }

            // 替换后的节目不适配当前版本，则不替换
            List<String> matchedSourceList = sourceVersionService.getSourceListByVersion(appVersion);
            if (CollectionUtils.isEmpty(matchedSourceList) || !matchedSourceList.contains(standardAlbum.getSource())) {
                return;
            }

            // 使用替换的后的节目信息
            BeanUtils.copyProperties(standardAlbum, programAlbumEs);
        } catch (Exception e) {
            log.error("vipReplace error programAlbumEs={}", programAlbumEs, e);
        }
    }

    /**
     * 得到替换后的剧头
     */
    public StandardAlbum getReplaceAlbum(String vipType, String sid) {
        Map<String, StandardAlbum> standardAlbumMap = virtualProgramRelationRpcApiProxy.getAllRelAlbumsBySid(sid);
        if (MapUtils.isEmpty(standardAlbumMap)) {
            return null;
        }

        return getAlbumByVipType(vipType, standardAlbumMap);
    }

    /**
     * 获取最终的album
     */
    private StandardAlbum getAlbumByVipType(String vipType, Map<String, StandardAlbum> standardAlbumMap) {
        if (StringUtils.isBlank(vipType)) {
            vipType = "default";
        }

        List<String> vipBrandPriorityList = vipBrandPriority.get(vipType);
        if (CollectionUtils.isEmpty(vipBrandPriorityList)) {
            return null;
        }

        for (String brand : vipBrandPriorityList) {
            StandardAlbum standardAlbum = standardAlbumMap.get(brand);

            if (standardAlbum != null) {
                return standardAlbum;
            }
        }

        return null;
    }
}