package com.heytap.longvideo.search.service.spider;

import com.heytap.longvideo.client.media.model.dto.TopWechatDoubanMediaListDto;
import com.heytap.longvideo.client.media.query.TopWechatDoubanMediaRequest;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.LvTopWechatDoubanMediaRpcApiProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/7 9:09
 */
@Slf4j
@Service
public class DouBanTopMediaSpiderService {

    @Autowired
    private LvTopWechatDoubanMediaRpcApiProxy lvTopWechatDoubanMediaRpcApiProxy;

    @Autowired
    private SearchProperties searchProperties;


    public Result<TopWechatDoubanMediaListDto> getDouBanWechatTop(TopWechatDoubanMediaRequest request) {
        if (!searchProperties.getFilterApiKey().contains(request.getApiKey())) {
            log.warn("request:{}", request);
            return Result.success();
        }
        try {
            TopWechatDoubanMediaListDto data =  lvTopWechatDoubanMediaRpcApiProxy.listByCache(request);
            if(Objects.isNull(data)) {
                return Result.fail("数据为空", null);
            }
            return Result.success(data);
        } catch (Exception e) {
            log.error("getDouBanWechatTop error request {}", request, e);
        }
        return Result.fail("服务器返回异常", null);
    }



}
