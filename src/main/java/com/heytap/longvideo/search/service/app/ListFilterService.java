package com.heytap.longvideo.search.service.app;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.CopyrightConstant;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEsV2;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.model.param.app.Urlpack;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.LvContentPoolRpcApiProxy;
import com.heytap.longvideo.search.service.common.VirtualProgramService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.*;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.MINORS_POOL_CODE;

/*
 * 筛选节目列表
 * Date 18:58 2021/12/21
 * Author songjiajia 80350688
 */
@Service("programAlbumService")
@Slf4j
public class ListFilterService {

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private VirtualProgramService virtualProgramService;

    @Autowired
    private SearchProperties searchConfig;

    @Autowired
    private LvContentPoolRpcApiProxy contentPoolRpcApiProxy;

    @Autowired
    private YoukuSourceFilterService youkuSourceFilterService;

    @Autowired
    private DeepLinkUtils deepLinkUtils;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private StandardAlbumRpcApi standardAlbumRpcApi;

    public List<ProgramAlbumEs> listFilter(ListFilterParam param) {
        log.info("ListFilter params:{}", param.toString());
        Map<String, Object> urlpackMap = JacksonUtil.parseObject(param.getUrlpack(), Map.class);
        String json = JacksonUtil.toJSONString(urlpackMap.get("cmd_vod"));
        Urlpack urlpack = JacksonUtil.parseObject(json, Urlpack.class);
        //contentType在匹配标签卡逻辑里可能为多个，","隔开
        if (urlpack == null || StringUtil.isBlank(urlpack.getContentType())) {
            return Collections.emptyList();
        }
        // 设置versionTag
        int versionTag = CommonUtils.getVersionTag(param.getVersion());
        urlpack.setVersion_tag(versionTag);
        int pageNo = param.getOffset() < 27 ? 0 : param.getOffset() / 27;
        //标签卡逻辑调用时，pageNo取参数里的
        if (param.getCallType() == 2) {
            pageNo = param.getOffset() - 1;
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery;
        queryBuilder = queryBuilder.withQuery(buildBoolQuery(urlpack, param.getMinors(), param))
                .withPageable(PageRequest.of(pageNo, param.getNumber()));
        queryBuilder = buildSort(urlpack, queryBuilder);
        searchQuery = queryBuilder.build();
        //4.解析响应
        if (StringUtil.isNotEmpty(urlpack.getSource()) && !"all".equals(urlpack.getSource())) {
            return searchFromStandardAlbumEsV2(searchQuery, param);
        }
        return searchFromProgramAlbumEs(searchQuery, param, urlpack);
    }

    /**
     * 获取符合条件的所有contentType集合（用于下发筛选项）
     * @param param
     * @return
     */
    public List<String> listDistinctContentTypes(ListFilterParam param) {
        Map<String, Object> urlpackMap = JacksonUtil.parseObject(param.getUrlpack(), Map.class);
        String json = JacksonUtil.toJSONString(urlpackMap.get("cmd_vod"));
        Urlpack urlpack = JacksonUtil.parseObject(json, Urlpack.class);
        if (urlpack == null || StringUtil.isBlank(urlpack.getContentType())) {
            return Collections.emptyList();
        }

        // 设置 versionTag
        int versionTag = CommonUtils.getVersionTag(param.getVersion());
        urlpack.setVersion_tag(versionTag);

        // 构建基础查询条件
        BoolQueryBuilder boolQuery = buildBoolQuery(urlpack, param.getMinors(), param);
        String aggregationName = "distinct_content_types";

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                // 只返回单个文档
                .withPageable(PageRequest.of(1, 1))
                // 添加聚合去重逻辑
                .addAggregation(AggregationBuilders.terms(aggregationName).field("contentType"));

        NativeSearchQuery searchQuery = queryBuilder.build();

        // 执行查询
        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        if (searchHits.getAggregations() == null) {
            return Collections.emptyList();
        }

        // 提取聚合结果
        Terms termsAgg = searchHits.getAggregations().get(aggregationName);
        return termsAgg.getBuckets().stream()
                .map(MultiBucketsAggregation.Bucket::getKeyAsString)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .filter(contentType -> searchConfig.getSearchFilterList().contains(contentType))
                .collect(Collectors.toList());
    }

    public NativeSearchQueryBuilder buildSort(Urlpack urlpack, NativeSearchQueryBuilder queryBuilder) {
        String sort = urlpack.getSort();
        if ("hot".equals(sort)) {
            if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(urlpack.getSource()) || "all".equals(urlpack.getSource())) {
                queryBuilder = queryBuilder.withSort(SortBuilders.fieldSort("sourceHot").order(SortOrder.DESC));
            }
            queryBuilder = queryBuilder.withSort(SortBuilders.fieldSort("dayNo").order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("oppoHot").order(SortOrder.DESC));
        } else if ("score".equals(sort)) {
            //  7.8新增按评分排序，评分相同按热度排序
            queryBuilder = queryBuilder.withSort(SortBuilders.fieldSort("sourceScore").order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("dayNo").order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("oppoHot").order(SortOrder.DESC));
        }  else if ("free".equals(sort)) {
            //  8.8新增免费排序，相同则按热度排序
            queryBuilder = queryBuilder.withSort(SortBuilders.fieldSort("payStatus").order(SortOrder.ASC))
                    .withSort(SortBuilders.fieldSort("dayNo").order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("oppoHot").order(SortOrder.DESC));
        } else {
            queryBuilder.withSort(SortBuilders.fieldSort("showTime").order(SortOrder.DESC));
        }
        if (StringUtil.isEmpty(urlpack.getSource())) {
            queryBuilder = queryBuilder.withSort(SortBuilders.fieldSort("sid").order(SortOrder.DESC));
        } else {
            queryBuilder = queryBuilder.withSort(SortBuilders.fieldSort("_id").order(SortOrder.DESC));
        }
        return queryBuilder;
    }


    private List<ProgramAlbumEs> searchFromProgramAlbumEs(NativeSearchQuery searchQuery, ListFilterParam param, Urlpack urlpack) {
        List<ProgramAlbumEs> returnList = new ArrayList<>();

        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        //搜索接口的标签卡逻辑调用时，才会使用到hasMore
        if (param.getCallType() == 2 && searchHits.getTotalHits() > ((long) param.getOffset() * param.getNumber())) {
            param.setHasMore(true);
        }
        // 为了得到sourceWebUrl
        if (searchHits.getTotalHits() <= 0) {
            return returnList;
        }
        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            ProgramAlbumEs programAlbumEs = searchHit.getContent();
            returnList.add(programAlbumEs);
        }
        Map<String, String> poolCodeMap = null;
        if ("1".equals(param.getMinors())) {
            poolCodeMap = returnList.stream().filter(item -> item.getSid() != null && item.getMinorsPoolCode() != null)
                    .collect(Collectors.toMap(ProgramAlbumEs::getSid, ProgramAlbumEs::getMinorsPoolCode, (u1, u2) -> u1));
        }
        returnList = returnList.stream()
                .map(l -> virtualProgramService.getProgramAlbumByVipType(
                        urlpack.getVersion_tag(), l, param.getVipType(), urlpack.getPayment(), param.getQuickEngineVersion(), param.getVersion(), 0, param.getAppId()))
                .collect(Collectors.toList());
        // 虚拟节目会从缓存里取ES结果，取完再做处理
        List<String> sidList = returnList.stream().map(ProgramAlbumEs::getSid).collect(Collectors.toList());
        CompletableFuture<RpcResult<Map<String, StandardAlbum>>> albumFuture = standardAlbumRpcApi.getBySidsFilterInvalid(sidList);
        RpcResult<Map<String, StandardAlbum>> albumRpcResult = FutureUtil.getFutureIgnoreException(albumFuture, 3, TimeUnit.SECONDS);
        if (null == albumRpcResult || albumRpcResult.getCode() != ResultCode.SUCCESS.getCode() || albumRpcResult.getData() == null) {
            log.error("albumRpcApiProxy.getBySid code error,sid:{},result:{}", sidList, JSON.toJSONString(albumRpcResult));
            return returnList;
        }

        handleDeepLink(returnList, param, albumRpcResult.getData());
        handleMinorsPoolCode(returnList, param, poolCodeMap);

        log.info("ListFilter response:{}", returnList.size());

        return returnList;
    }

    private void handleDeepLink(List<ProgramAlbumEs> esList, ListFilterParam param, Map<String, StandardAlbum> albumMap) {
        for (ProgramAlbumEs es : esList) {
            if (youkuSourceFilterService.setDeepLinkFilter(es.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                StandardAlbum standardAlbum = albumMap.get(es.getSid());
                if (standardAlbum != null) {
                    es.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(),
                            standardAlbum.getSourceWebUrl()));
                }
            } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(es.getSource())) {
                es.setDeepLink(youkuSourceFilterService.getUpGradeUrl(es.getSid(), es.getSource(), es.getTitle()));
            } else {
                es.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.ALBUM.getCode(), es.getSid()));
            }
        }
    }

    private void handleMinorsPoolCode(List<ProgramAlbumEs> esList, ListFilterParam param, Map<String, String> poolCodeMap) {
        if ("1".equals(param.getMinors()) && MapUtils.isNotEmpty(poolCodeMap)) {
            // 保证下发minorsPoolCode
            for (ProgramAlbumEs item : esList) {
                if (StringUtils.isEmpty(item.getMinorsPoolCode()) && poolCodeMap.containsKey(item.getSid())) {
                    item.setMinorsPoolCode(poolCodeMap.get(item.getSid()));
                }
            }
        }
    }

    private List<ProgramAlbumEs> searchFromStandardAlbumEsV2(NativeSearchQuery searchQuery, ListFilterParam param) {
        List<ProgramAlbumEs> returnList = new ArrayList<>();
        SearchHits<StandardAlbumEsV2> searchHits = restTemplate.search(searchQuery, StandardAlbumEsV2.class);
        for (SearchHit<StandardAlbumEsV2> searchHit : searchHits) {
            StandardAlbumEsV2 standardAlbumEs = searchHit.getContent();
            ProgramAlbumEs programAlbumEs = new ProgramAlbumEs();
            programAlbumEs.setSid(standardAlbumEs.getSid());
            programAlbumEs.setTitle(standardAlbumEs.getTitle());
            programAlbumEs.setEpstitle(standardAlbumEs.getSubTitle());
            programAlbumEs.setMarkCode(standardAlbumEs.getMarkCode());
            programAlbumEs.setDirector(standardAlbumEs.getDirector());
            programAlbumEs.setActor(standardAlbumEs.getActor());
            programAlbumEs.setTags(standardAlbumEs.getTags());
            programAlbumEs.setArea(standardAlbumEs.getArea());
            programAlbumEs.setSource(standardAlbumEs.getSource());
            programAlbumEs.setVerticalIcon(standardAlbumEs.getVerticalIcon());
            programAlbumEs.setHorizontalIcon(standardAlbumEs.getHorizontalIcon());
            programAlbumEs.setSourceScore(Float.valueOf(standardAlbumEs.getSourceScore()));
            programAlbumEs.setFeatureType(standardAlbumEs.getFeatureType());
            programAlbumEs.setPayStatus(standardAlbumEs.getPayStatus());
            programAlbumEs.setCopyrightCode(standardAlbumEs.getCopyrightCode());
            programAlbumEs.setContentType(standardAlbumEs.getProgramType());
            programAlbumEs.setProgramInfo(standardAlbumEs.getProgramInfo());
            programAlbumEs.setLanguage(standardAlbumEs.getLanguage());
            programAlbumEs.setShowTime(standardAlbumEs.getShowTime());
            programAlbumEs.setVipType(standardAlbumEs.getVipType());
            programAlbumEs.setYear(standardAlbumEs.getYear());
            programAlbumEs.setBrief(standardAlbumEs.getBrief());
            // 筛选页面设置 deepLink
            if (youkuSourceFilterService.setDeepLinkFilter(standardAlbumEs.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                programAlbumEs.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), standardAlbumEs.getSourceWebUrl()));
            } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(standardAlbumEs.getSource())) {
                programAlbumEs.setDeepLink(youkuSourceFilterService.getUpGradeUrl(standardAlbumEs.getSid(), standardAlbumEs.getSource(), standardAlbumEs.getTitle()));
            } else {
                programAlbumEs.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.ALBUM.getCode(), programAlbumEs.getSid()));
            }
            returnList.add(programAlbumEs);
        }
        log.info("ListFilter response:{}", returnList.size());
        return returnList;
    }


    private BoolQueryBuilder buildBoolQuery(Urlpack urlpack, String minors, ListFilterParam param) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        String source = urlpack.getSource();
        boolQuery.filter(QueryBuilders.termQuery("featureType", 1));
        boolQuery.filter(QueryBuilders.termQuery("status", 1));
        if (StringUtil.isEmpty(source) || "all".equals(source)) {
            buildBoolQuery4AllSource(urlpack, boolQuery);
        } else {
            buildBoolQuery4Sources(urlpack, param, source, boolQuery);
        }

        buildBoolQuery4YearAndAge(urlpack, boolQuery);

        if ("vip".equals(urlpack.getPayment())) {
            boolQuery.filter(QueryBuilders.termQuery("payStatus", 1));
        } else if ("single".equals(urlpack.getPayment())) {
            boolQuery.filter(QueryBuilders.termQuery("payStatus", 2));
        } else if ("free".equals(urlpack.getPayment())) {
            boolQuery.filter(QueryBuilders.termQuery("payStatus", 0));
        }

        buildBoolQuery4VersionTag(urlpack, boolQuery);

        // sid黑名单
        if (StringUtils.isNotBlank(urlpack.getSidBlackList())) {
            boolQuery.mustNot(QueryBuilders.termsQuery("sid", Arrays.asList(urlpack.getSidBlackList().split(","))));
        }

        //筛选接口一期先屏蔽咪咕内容
        boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MIGUOLMPIC));

        // 筛选 过滤风行和微迪欧
        if (funshionLongVideoAndWeidiouFilterService.filterItem(param.getVersion())) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_FUNSHION_LONGVIDEO));
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_WEIDIOU));
        }

        // 筛选屏蔽优酷
        if (youkuSourceFilterService.filterItem(param.getVersion())) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YOUKU_MOBILE));
        }


        if ("1".equals(minors) && StringUtils.isNotEmpty(urlpack.getAgeCode())) {
            // 未成年模式  ageCode转未成年内容池code
            Collection<String> poolCodeList = contentPoolRpcApiProxy.getMinorsPoolCodes(Arrays.asList(urlpack.getAgeCode().split(",")));
            if (CollectionUtils.isNotEmpty(poolCodeList)) {
                boolQuery.filter(QueryBuilders.matchQuery(MINORS_POOL_CODE, String.join(",", poolCodeList)));
            }
        }

        if (StringUtil.isNotBlank(urlpack.getActor())) {
            boolQuery.must(QueryBuilders.matchPhraseQuery("actor", urlpack.getActor()));
        }

        return boolQuery;
    }


    private void buildBoolQuery4AllSource(Urlpack urlpack, BoolQueryBuilder boolQuery) {
        if (StringUtil.isNotBlank(urlpack.getArea())) {
            boolQuery.filter(QueryBuilders.matchQuery("area", urlpack.getArea().replace("|", ",")));
        }
        if (StringUtil.isNotBlank(urlpack.getTag())) {
            boolQuery.filter(QueryBuilders.matchQuery("tags", urlpack.getTag().replace("|", ",")));
        }
        if (!"all".equals(urlpack.getContentType())) {
            //搜索接口的标签卡逻辑会传","隔开的contentType
            boolQuery.filter(QueryBuilders.termsQuery("contentType", urlpack.getContentType().split(",")));
        } else {
            boolQuery.filter(QueryBuilders.termsQuery("contentType", "comic", "tv", "show", "doc", "movie", "kids"));
        }
    }

    private void buildBoolQuery4Sources(Urlpack urlpack, ListFilterParam param, String source, BoolQueryBuilder boolQuery) {
        if (source.contains("funshion")) {
            // app端 根据版本过滤 风行和微迪欧
            if (funshionLongVideoAndWeidiouFilterService.filterItem(param.getVersion())) {
                source = source.replace("funshion", "huashi,senyu,letv,yst");
            } else {
                source = source.replace("funshion", "huashi,senyu,letv,yst,weidiou,funshion_lv");
            }
        }
        boolQuery.filter(QueryBuilders.termsQuery("source", source.split(",")));
        if (StringUtil.isNotBlank(urlpack.getArea())) {
            boolQuery.filter(QueryBuilders.matchQuery("area", urlpack.getArea().replace("|", ",")));
        }
        if (StringUtil.isNotBlank(urlpack.getTag())) {
            boolQuery.filter(QueryBuilders.matchQuery("mappingTags", urlpack.getTag().replace("|", ",")));
        }
        if (!"all".equals(urlpack.getContentType())) {
            boolQuery.filter(QueryBuilders.termQuery("programType", urlpack.getContentType()));
        }
        boolQuery.mustNot(QueryBuilders.termQuery("programType", "live"));
    }

    private void buildBoolQuery4VersionTag(Urlpack urlpack, BoolQueryBuilder boolQuery) {
        if (urlpack.getVersion_tag() <= 1) {
            boolQuery.filter(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_SOHU));
        } else {
            if (urlpack.getVersion_tag() < searchConfig.getMgAppVersion()) {
                boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MGMOBILE));
            }
            if (urlpack.getVersion_tag() < searchConfig.getLetvAppVersion()) {
                boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_LETV));
            }
            if (urlpack.getVersion_tag() < searchConfig.getYstAppVersion()) {
                boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YST));
            }
            if (urlpack.getVersion_tag() < searchConfig.getMiguOlympicAppVersion()) {
                boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_MIGUOLMPIC));
            }
        }
        if (urlpack.getVersion_tag() < 3) {
            boolQuery.mustNot(QueryBuilders.termQuery("payStatus", 2));
        }
    }

    private void buildBoolQuery4YearAndAge(Urlpack urlpack, BoolQueryBuilder boolQuery) {
        if (StringUtil.isNotBlank(urlpack.getYear())) {
            if (urlpack.getYear().indexOf("-") > 0) {
                String[] years = urlpack.getYear().split("-");
                boolQuery.filter(QueryBuilders.rangeQuery("year").gte(years[0]).lte(years[1]));
            } else {
                boolQuery.filter(QueryBuilders.termQuery("year", urlpack.getYear()));
            }
        }

        if (StringUtil.isNotBlank(urlpack.getAge()) && urlpack.getAge().indexOf("-") > 0) {
            String[] arr = urlpack.getAge().split("-");
            if (arr.length > 1) {
                boolQuery.must(QueryBuilders.rangeQuery("fitAgeMin").gte(arr[0]));
                boolQuery.must(QueryBuilders.rangeQuery("fitAgeMax").lte(arr[1]));
            }
        }
    }
}