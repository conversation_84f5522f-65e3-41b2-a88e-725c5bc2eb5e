package com.heytap.longvideo.search.service.thirdparty;

import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.config.ThirdPartyCommonConfig;
import com.heytap.longvideo.search.constants.ThirdPartyMediaTypeEnum;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.UrlCoderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.heytap.longvideo.search.constants.ThirdPartyConstant.THIRD_PARTY_DETAIL_PAGE;
import static com.heytap.longvideo.search.constants.ThirdPartyConstant.THIRD_PARTY_DETAIL_PAGE_VERSION;

/**
 * <AUTHOR> Yanping
 * @date 2025/6/5
 */
@Slf4j
@Service
public class ThirdPartyCommonService {
    private static final String DP_DETAIL_PAGE_STYLE_PARAM =  "&yoliDpDetailPageStyle=";

    private static final String MEDIA_TYPE_PARAM = "&yoliMediaType=";

    private static final List<String> specialMediaTypeList = new ArrayList<>();
    static {
        // 浏览器：未传kkua
        specialMediaTypeList.add(ThirdPartyMediaTypeEnum.BROSWER.getMediaType());
        // 小布只传了appVersion和部分id
        specialMediaTypeList.add(ThirdPartyMediaTypeEnum.BREENO.getMediaType());
        // 三方低版本未传kkua，锁屏为新接入，无低版本调用
        // specialMediaTypeList.add(ThirdPartyMediaTypeEnum.MAGAZINE.getMediaType());
        // 三方低版本未传kkua
        specialMediaTypeList.add(ThirdPartyMediaTypeEnum.QUAN_SOU.getMediaType());
    }

    // 匹配升级页地址，及接口逻辑拼接的参数后缀
    private static final Pattern pattern = Pattern.compile("url=([^&]*)&?(.*)");

    private static final Pattern linkValuePattern = Pattern.compile("^(.*linkValue=)(.*)$", Pattern.CASE_INSENSITIVE);

    @Autowired
    private ThirdPartyCommonConfig thirdPartyCommonConfig;

    @Autowired
    private DeepLinkUtils deepLinkUtils;

    @Autowired
    private SourceFilterConfig sourceFilterConfig;

    public String getThirdPartyMediaParams(String mediaType, int detailPageStyle, Integer version, String source, boolean isTransparentKkua) {
        log.info("mediaType:{}, detailPageStyle:{}, version:{}, source:{}, isTransparentKkua:{}", mediaType, detailPageStyle, version, source, isTransparentKkua);
        String dpParams = "";
        // 视频低版本不支持跳转新详情页
        if (version == null || version < THIRD_PARTY_DETAIL_PAGE_VERSION) {
            return dpParams;
        }
        // 快应用不需要添加跳转新详情页参数
        if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            return dpParams;
        }
        if (THIRD_PARTY_DETAIL_PAGE == detailPageStyle) {
            dpParams += DP_DETAIL_PAGE_STYLE_PARAM + detailPageStyle + MEDIA_TYPE_PARAM + mediaType;
        }
        //部分三方媒体，由于没有完整策略信息，策略定投可能不生效，故再按照服务端配置进行兜底判断
        if (!isTransparentKkua && specialMediaTypeList.contains(mediaType) && !dpParams.contains(DP_DETAIL_PAGE_STYLE_PARAM)
                && THIRD_PARTY_DETAIL_PAGE == thirdPartyCommonConfig.getDefaultDpDetailStyleMap().get(mediaType)) {
            dpParams += DP_DETAIL_PAGE_STYLE_PARAM + thirdPartyCommonConfig.getDefaultDpDetailStyleMap().get(mediaType) + MEDIA_TYPE_PARAM + mediaType;
        }
        return dpParams;
    }

    /**
     * 在升级页dp后面拼接正常dp，前端在快应用升级后，使用该dp地址进行跳转
     * @param deepLink
     * @param dpLinkType
     * @param quickEngineVersion
     * @param source
     * @param sid
     * @param sourceWebUrl
     * @return
     */
    public String handleUpgradePageDp(String deepLink, int dpLinkType, Integer quickEngineVersion, String source, String sid, String sourceWebUrl) {
        if (TemplateLinkTypeEnum.UPGRADE_PAGE.getCode() != dpLinkType || !SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            return deepLink;
        }

        if (quickEngineVersion == null || quickEngineVersion >= sourceFilterConfig.getMinWebQuickVersion()) {
            // 不是由于快应用版本不符合版本要求而下发升级页的不处理；快应用版本未传不处理
            return deepLink;
        }
        String replaceDeepLink = deepLink;
        try {
            // 解析原有dp
            Matcher matcher = pattern.matcher(deepLink.substring(deepLink.indexOf('?') + 1));
            if (matcher.find()) {
                // 获取url参数值并解码
                String url = UrlCoderUtil.decode(matcher.group(1));
                // matcher.group(2)匹配的为各接口拼接的其他参数后缀
                String normalDpValue = deepLinkUtils.getAlbumDeepLink(source, sid, sourceWebUrl) + "&" + matcher.group(2);
                Matcher linkValueMatcher = linkValuePattern.matcher(normalDpValue);
                if (linkValueMatcher.find()) {
                    String bae64EncodeNormalDpValue = new String(Base64Utils.encode(linkValueMatcher.group(2).getBytes(StandardCharsets.UTF_8)));
                    String linkValue = url + "&normalDp=" + UrlCoderUtil.encode(linkValueMatcher.group(1) + bae64EncodeNormalDpValue);
                    replaceDeepLink = deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.H5.getCode(), linkValue);
                }
            }
            return replaceDeepLink;
        } catch (Exception e) {
            log.error("handleUpgradePageDp error, error:", e);
            return deepLink;
        }
    }
}
