package com.heytap.longvideo.search.service.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.constants.RelationTypeEnum;
import com.heytap.longvideo.search.model.param.app.RelationListResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.cpc.video.framework.lib.utils.MD5SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 封装一些调用relation的方法
 */
@Service
@Slf4j
public class RelationListService {
    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private HttpDataChannel httpDataChannel;

    public Map<String, Integer> requestRelationList(String userId, List<String> sidList) {
        HashMap<String, Object> requestParam = initRequestParam(userId, sidList);
        String urlParams = urlParams(requestParam);
        String requestUrl = searchProperties.getRelationGetListUrl() + urlParams;
        log.info("[RelationListService.requestRelationList],request: {},url:{}", requestParam, requestUrl);
        try {
            String relationGetStatusResponse = httpDataChannel.postForObject(searchProperties.getRelationGetListUrl(), requestParam, String.class, null, new HashMap<>(), searchProperties.getRelationTimeOut());
            if (log.isDebugEnabled()) {
                log.debug("[RelationListService.requestRelationList] request:{},response={}", requestParam, relationGetStatusResponse);
            }
            if (relationGetStatusResponse == null) {
                return new HashMap<>();
            }
            JSONObject responseJson = JSONObject.parseObject(relationGetStatusResponse);
            JSONObject resultJson = responseJson.getJSONObject("result");
            if (resultJson == null) {
                return new HashMap<>();
            }
            return new HashMap<String, Integer>() {
                {
                    Set<Entry<String, Object>> entrySet = resultJson.entrySet();
                    for (Entry<String, Object> entry : entrySet) {
                        put(entry.getKey(), (Boolean) entry.getValue() ? 1 : 0);
                    }

                }
            };
        } catch (Exception e) {
            log.error("[RelationListService.requestRelationList] error,requestParam: {}", requestParam, e);
        }
        return new HashMap<>();
    }

    private String urlParams(HashMap<String, Object> request) {
        return "?appId=" + request.get("appId")
                + "&fromApp=" + request.get("fromApp");
    }

    private HashMap<String, Object> initRequestParam(String userId, List<String> sidList) {
        //预约请求参数
        JSONObject subscribeQueryInfo = new JSONObject();
        subscribeQueryInfo.put("type", CommonConstant.ACTION_SUBSCRIBE);
        subscribeQueryInfo.put("targetList", getTargetInfoList(sidList, false));
        //追剧请求参数
        JSONObject chaseAlbumQueryInfo = new JSONObject();
        chaseAlbumQueryInfo.put("type", CommonConstant.FAVORITE);
        chaseAlbumQueryInfo.put("targetList", getTargetInfoList(sidList, false));
        List<JSONObject> queryInfos = new ArrayList<>();
        queryInfos.add(subscribeQueryInfo);
        queryInfos.add(chaseAlbumQueryInfo);
        return new HashMap<String, Object>() {
            {
                put("appId", "longvideo");
                put("fromApp", "longvideo");
                put("fid", userId);
                put("queryInfoList", queryInfos);
            }
        };
    }

    private List<JSONObject> getTargetInfoList(List<String> sids, boolean needSource) {
        List<JSONObject> targetInfoList = new ArrayList<>();
        sids.forEach(sid -> {
            JSONObject targetInfo = getTargetInfo(sid, needSource);
            targetInfoList.add(targetInfo);
        });
        return targetInfoList;
    }

    private JSONObject getTargetInfo(String sid, boolean needSource) {
        JSONObject targetInfo = new JSONObject();
        targetInfo.put("tid", sid);
        if (sid.startsWith("h")) {
            targetInfo.put("tsource", "H5");
        } else if (sid.startsWith("d")) {
            targetInfo.put("tsource", "deepLink");
        } else {
            targetInfo.put("tsource", needSource ? "album" : "0");
        }
        return targetInfo;
    }

    private HashMap<String, Object> initReqParam(String userId, RelationTypeEnum relationTypeEnum) {
        return new HashMap<String, Object>() {
            {
                put("appId", CommonConstant.LONGVIDEO);
                put("fromApp", CommonConstant.LONGVIDEO);
                put("fid", userId);
                put("type", relationTypeEnum.getMsg());
                put("timestamp", System.currentTimeMillis());
            }
        };
    }
}
