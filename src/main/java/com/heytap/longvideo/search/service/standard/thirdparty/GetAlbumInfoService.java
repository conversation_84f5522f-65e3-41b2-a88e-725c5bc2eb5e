package com.heytap.longvideo.search.service.standard.thirdparty;

import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.thirdparty.standard.GetAlbumInfoRequest;
import com.heytap.longvideo.search.model.response.thirdparty.GetAlbumInfoResponse;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.ProgramConstant.CONTENT_CHANNEL_ALL_WEB;
import static com.heytap.longvideo.search.constants.ProgramConstant.CONTENT_CHANNEL_OPPO_VIDEO;

/**
 * <AUTHOR> Yanping
 * @date 2025/6/19
 */
@Slf4j
@Service
public class GetAlbumInfoService {

    private String nonInStockDeepLink = "yoli://yoli.com/detail/longvideo/outofstockvideodetail?linkValue=%s&title=%s";

    private String outDeepLinkSuffix = "&showSplashAd=0&openMainActivity=1&openFrom=screenoff_alg_CT_%s_DP&showWhenLocked=1&swl=1";

    @Autowired
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    @Autowired
    private UnofficialAlbumService unofficialAlbumService;

    @Autowired
    private DeepLinkUtils deepLinkUtils;

    public StandardResult<PageResponse<GetAlbumInfoResponse>> getAlbumInfo(GetAlbumInfoRequest request) {
        if (!checkRequest(request)) {
            return StandardResult.fail(-1001, "param is invalid");
        }
        StandardResult<PageResponse<GetAlbumInfoResponse>> result = new StandardResult<>();
        PageResponse<GetAlbumInfoResponse> pageResponse = new PageResponse<>();
        result.setData(pageResponse);
        List<GetAlbumInfoResponse> itemList = new ArrayList<>();
        // 单个sid请求时，三方不知道是自建还是全网内容；sid不为空时，只返回单个内容详情
        if (StringUtils.isNotBlank(request.getSid())) {
            GetAlbumInfoResponse getAlbumInfoResponse = handleBySid(request.getSid());
            itemList.add(getAlbumInfoResponse);
            pageResponse.setCurrentPage(request.getPageIndex());
            pageResponse.setPageSize(request.getPageSize());
            pageResponse.setTotalCount(1);
            pageResponse.setMaxPage(1);
            pageResponse.setItemList(itemList);
            pageResponse.setItemListSize(1);
            return StandardResult.success(pageResponse);
        }

        // 批量sid请求
        if (CollectionUtils.isNotEmpty(request.getSids())) {
            List<GetAlbumInfoResponse> albumList = handleBySids(request.getSids());
            if (CollectionUtils.isNotEmpty(albumList)) {
                itemList.addAll(albumList);
            }
        }

        // 处理全网节目列表详情
        if (CollectionUtils.isNotEmpty(request.getAllWebSids())) {
            List<GetAlbumInfoResponse> allWebAlbumList = handleByWebSids(request.getAllWebSids());
            if (CollectionUtils.isNotEmpty(allWebAlbumList)) {
                itemList.addAll(allWebAlbumList);
            }
        }
        pageResponse.setCurrentPage(request.getPageIndex());
        pageResponse.setPageSize(request.getPageSize());
        pageResponse.setTotalCount(1);
        pageResponse.setMaxPage(1);
        pageResponse.setItemList(itemList);
        pageResponse.setItemListSize(itemList.size());
        return StandardResult.success(pageResponse);
    }

    /**
     * 处理全网搜节目列表
     * @param allWebSids
     * @return
     */
    private List<GetAlbumInfoResponse> handleByWebSids(List<String> allWebSids) {
        List<GetAlbumInfoResponse> unOfficialAlbumList = new ArrayList<>();
        List<UnofficialAlbumEs> unofficialAlbumEsList = unofficialAlbumService.getAlbumList(allWebSids);
        if (CollectionUtils.isEmpty(unofficialAlbumEsList)) {
            log.warn("unofficialAlbumEsList is empty, allWebSids:{}", allWebSids);
            return new ArrayList<>();
        }
        Map<String, UnofficialAlbumEs> unofficialAlbumEsMap = unofficialAlbumEsList.stream()
                .collect(Collectors.toMap(UnofficialAlbumEs::getSid, obj -> obj));
        for (String allWebSid : allWebSids) {
            UnofficialAlbumEs currentUnofficialAlbumEs = unofficialAlbumEsMap.get(allWebSid);
            if (currentUnofficialAlbumEs == null) {
                log.warn("currentUnofficialAlbumEs is null, webSid:{}", allWebSid);
                continue;
            }
            GetAlbumInfoResponse getAlbumInfoResponse = new GetAlbumInfoResponse();
            BeanUtils.copyProperties(currentUnofficialAlbumEs, getAlbumInfoResponse);
            getAlbumInfoResponse.setDeepLink(getNonInStockDeepLink(allWebSid, currentUnofficialAlbumEs.getTitle()));
            getAlbumInfoResponse.setContentChannel(CONTENT_CHANNEL_ALL_WEB);
            unOfficialAlbumList.add(getAlbumInfoResponse);
        }
        return unOfficialAlbumList;
    }

    /**
     * 处理自建内容节目列表
     * @param sids
     * @return
     */
    private List<GetAlbumInfoResponse> handleBySids(List<String> sids) {
        List<GetAlbumInfoResponse> albumList = new ArrayList<>();
        Map<String, StandardAlbum> albumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sids);
        for (String sid : sids) {
            StandardAlbum standardAlbum = albumMap.get(sid);
            if (standardAlbum == null) {
                log.error("standardAlbum is null, sid:{}", sid);
                continue;
            }
            GetAlbumInfoResponse albumInfoResponse = new GetAlbumInfoResponse();
            BeanUtils.copyProperties(standardAlbum, albumInfoResponse);
            albumInfoResponse.setContentChannel(CONTENT_CHANNEL_OPPO_VIDEO);
            albumInfoResponse.setDeepLink(getAlbumDeepLink(standardAlbum));
            albumList.add(albumInfoResponse);
        }
        return albumList;
    }


    private GetAlbumInfoResponse handleBySid(String sid) {
        GetAlbumInfoResponse albumInfo = new GetAlbumInfoResponse();
        // 单sid个请求
        StandardAlbum standardAlbum = standardAlbumRpcApiProxy.getBySid(sid);
        // 因为无法判断sid是自建还是全网内容，先查询合作方媒资表里是否有内容
        if (standardAlbum != null) {
            BeanUtils.copyProperties(standardAlbum, albumInfo);
            albumInfo.setContentChannel(CONTENT_CHANNEL_OPPO_VIDEO);
            albumInfo.setDeepLink(getAlbumDeepLink(standardAlbum));
            return albumInfo;
        } else {
            UnofficialAlbumEs unofficialAlbumEs = null;
            try {
                // 再查询全网内容
                unofficialAlbumEs = unofficialAlbumService.searchBySid(sid, UnofficialAlbumEs.class);
                if (unofficialAlbumEs == null) {
                    log.warn("unofficialAlbumEs is null, sid:{}", sid);
                    return null;
                }
                BeanUtils.copyProperties(unofficialAlbumEs, albumInfo);
                albumInfo.setContentChannel(CONTENT_CHANNEL_ALL_WEB);
                albumInfo.setDeepLink(getNonInStockDeepLink(sid, unofficialAlbumEs.getTitle()));
            } catch (Exception e) {
                log.error("get unOfficialAlbum error, sid:{}, error:{}", sid, e);
            }
            return albumInfo;
        }
    }

    private String getAlbumDeepLink(StandardAlbum album) {
        String deepLink;
        SourceEnum sourceEnum = SourceEnum.getBySpiSource(album.getSource());
        switch (sourceEnum) {
            case YOUKU_MOBILE:
                deepLink = deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), album.getSourceWebUrl() + "&_SWL_=1");
                break;
            default:
                deepLink = deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.ALBUM.getCode(), album.getSid());
        }
        if (StringUtils.isBlank(deepLink)) {
            return null;
        }
        return deepLink + String.format(outDeepLinkSuffix, album.getSid());
    }

    private String getNonInStockDeepLink(String sid, String title) {
        return String.format(nonInStockDeepLink, sid, title) + String.format(outDeepLinkSuffix, sid);
    }

    private boolean checkRequest(GetAlbumInfoRequest request) {
        if (StringUtils.isBlank(request.getSid()) && CollectionUtils.isEmpty(request.getSids()) && CollectionUtils.isEmpty(request.getAllWebSids())) {
            log.error("sid, sids，allWebSids can not all empty!, request:{}", request);
            return false;
        }
        return true;
    }
}
