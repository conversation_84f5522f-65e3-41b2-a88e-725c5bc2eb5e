package com.heytap.longvideo.search.service.standard;

import com.heytap.longvideo.search.model.entity.es.UgcStandardAuthorEs;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Yanping
 * @date 2022/10/24 11:55
 */
@Service
@Slf4j
public class UgcStandardAuthorService {
    private static final Logger UPDATELOG = LoggerFactory.getLogger("updatelog");

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    /**
     * 数据更新
     *
     * @param es
     */
    public void insertOrUpdate(UgcStandardAuthorEs es) {
        try {
            restTemplate.save(es);
        }catch (Exception e){
            UPDATELOG.error("insertOrUpdate ugcStandardAuthorEs", e);
            throw e;
        }
    }

    /**
     * 数据更新
     *
     * @param es
     */
    public void delete(UgcStandardAuthorEs es) {
        try {
            restTemplate.delete(es.getSid(), IndexCoordinates.of(UgcStandardAuthorEs.class
                    .getAnnotation(Document.class).indexName()));
        }catch (Exception e){
            UPDATELOG.error("insertOrUpdate ugcStandardVideo", e);
            throw e;
        }
    }
}