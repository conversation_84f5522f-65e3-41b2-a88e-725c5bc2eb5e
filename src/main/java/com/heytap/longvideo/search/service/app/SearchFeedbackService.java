package com.heytap.longvideo.search.service.app;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.search.api.LvSearchFeedbackRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchFeedback;
import com.heytap.longvideo.client.arrange.search.request.SearchFeedbackRequest;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.model.param.app.SearchFeedbackOption;
import com.heytap.longvideo.search.model.param.app.SearchFeedbackSubmitParam;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import com.heytap.longvideo.search.utils.AESUtil;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SearchFeedbackService {
    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1)
    private LvSearchFeedbackRpcApi lvSearchFeedbackRpcApi;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private UploadFileToOcsComponent fileToOcsComponent;

    @Autowired
    private CsvExportConfig csvExportConfig;

    @Autowired
    private HttpDataChannel httpClient;

    @Autowired
    @Qualifier("imageFileThreadPool")
    private Executor imageFileThreadPool;

    @HeraclesDynamicConfig(key = "search.feedback.webhook", fileName = "search_config.properties")
    private String searchFeedbackWebhook;

    public static final String OCS_PATH = "searchFeedback";

    // 加密密钥，长度为32字节
    public static final String PHONE_AES_KEY = "6162636465666768696a6b6c6d6e6f70";

    public CompletableFuture<List<LvSearchFeedback>> listByCreateTimeAndLimitNum(SearchFeedbackRequest request) {
        return lvSearchFeedbackRpcApi.listByCreateTimeAndLimitNum(request).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("listByCreateTimeAndLimitNum error, ret:{}", ret, e);
                return Collections.emptyList();
            }
            return ret.getData();
        });
    }


    public Boolean submit(SearchFeedbackSubmitParam param) {
        SearchFeedbackRequest request = new SearchFeedbackRequest();
        request.setKeyword(param.getWord());
        request.setType(param.getType());
        request.setNote(param.getNote());
        String phone = param.getPhone();
        if (StringUtil.isNotBlank(param.getIv())) {
            phone = AESUtil.decrypt(param.getPhone(), PHONE_AES_KEY, param.getIv());
        }
        request.setPhone(phone);
        if (CollectionUtils.isNotEmpty(param.getImages())) {
            // 把BASE64图片上传ocs，保存url
            List<CompletableFuture<String>> futures = param.getImages().stream()
                    .map(image -> CompletableFuture.supplyAsync(() -> {
                        String name = MapUtils.getString(image,"name");
                        String base64 = MapUtils.getString(image,"base64");
                        String fileName = base64ToFile(name, base64);
                        return uploadOcs(fileName);
                    }, imageFileThreadPool))
                    .collect(Collectors.toList());

            List<String> urlList = new ArrayList<>();
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenAccept(v -> {
                for (CompletableFuture<String> future : futures) {
                    urlList.add(future.join());
                }
            }).join();
            String image = StringUtil.join(urlList, "\n");
            request.setImage(image);
        }
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        CompletableFuture<RpcResult<Boolean>> saveFuture = lvSearchFeedbackRpcApi.save(request);

        RpcResult<Boolean> rpcResult = FutureUtil.getFutureIgnoreException(saveFuture, 5, TimeUnit.SECONDS);
        if (null == rpcResult || rpcResult.getCode() != ResultCode.SUCCESS.getCode() || rpcResult.getData() == null) {
            log.error("lvSearchFeedbackRpcApi.save error,keyword:{},result:{}", param.getKeyword(), com.alibaba.fastjson.JSON.toJSONString(rpcResult));
            return false;
        }
        return rpcResult.getData();
    }

    public String base64ToFile(String name, String base64) {
        String fileName;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
            fileName = format.format(new Date()) + "_" + name;
            // 去掉MIME类型前缀 "data:image/png;base64,xxx"
            String base64Data = base64.split(",")[1];
            // 解码 BASE64 编码的字符串
            byte[] imageBytes = Base64.getDecoder().decode(base64Data);
            // 将解码后的字节数据写入文件
            Path path = Paths.get(fileName);
            Files.write(path, imageBytes);
        } catch (Exception e) {
            log.error("searchFeedback convert image error", e);
            throw new RuntimeException();
        }
        return fileName;
    }

    public String uploadOcs(String fileName) {
        try {
            boolean success = fileToOcsComponent.uploadFileToOcs(csvExportConfig.getAccessKeyId(),
                    csvExportConfig.getAccessKeySecret(),
                    csvExportConfig.getEndPoint(),
                    csvExportConfig.getRegion(),
                    csvExportConfig.getBucketName(),
                    fileName,
                    csvExportConfig.getLocalPath(),
                    OCS_PATH);
            if (!success) {
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("searchFeedback upload ocs error", e);
            throw e;
        }
        return "http://" + csvExportConfig.getBucketName() + "." + csvExportConfig.getEndPoint() + "/" +
                OCS_PATH + "/" + fileName;
    }

    public List<SearchFeedbackOption> getOptions() {
        Map<String, String> options = searchProperties.getFeedbackOption();
        List<SearchFeedbackOption> feedbackList = new ArrayList<>();
        for (Map.Entry<String, String> entry : options.entrySet()) {
            SearchFeedbackOption option = new SearchFeedbackOption(Integer.valueOf(entry.getKey()), entry.getValue());
            feedbackList.add(option);
        }
        return feedbackList;
    }


    public Boolean collect(Map<String, Object> params) {
        Date start;
        Date end;

        String startTime = MapUtils.getString(params, "startTime");
        String endTime = MapUtils.getString(params, "endTime");
        if (StringUtil.isNotBlank(startTime) && StringUtil.isNotBlank(endTime)) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            try {
                start = formatter.parse(startTime);
                end = formatter.parse(endTime);
            } catch (ParseException e) {
                throw new RuntimeException("params error");
            }
        } else {
            Integer cycleDay = MapUtils.getInteger(params, "cycleDay", 7);
            end = new Date();
            start = DateUtils.addDays(end, -cycleDay);
        }

        // 查询周期内的用户反馈
        SearchFeedbackRequest request = new SearchFeedbackRequest();
        request.setMinCreateTime(start);
        request.setMaxCreateTime(end);
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        CompletableFuture<RpcResult<List<LvSearchFeedback>>> future = lvSearchFeedbackRpcApi.listByCreateTime(request);
        RpcResult<List<LvSearchFeedback>> rpcResult = FutureUtil.getFutureIgnoreException(future, 3, TimeUnit.SECONDS);
        if (null == rpcResult || rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
            log.warn("lvSearchFeedbackRpcApi.listByCreateTime error, request:{}, result:{}", JSON.toJSONString(request), JSON.toJSONString(rpcResult));
            return false;
        }

        String excelUrl;
        if (CollectionUtils.isEmpty(rpcResult.getData())) {
            excelUrl = "无";
        } else {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            String fileName = format.format(start) + "-" + format.format(end) + ".xlsx";
            try (FileOutputStream fileOut = new FileOutputStream(fileName)) {
                // 生成excel
                Workbook workbook = buildExcel(rpcResult.getData());
                workbook.write(fileOut);
                // 上传ocs
                excelUrl = uploadOcs(fileName);
                log.info("searchFeedback upload success. excelUrl={}", excelUrl);
            } catch (Exception e) {
                log.error("searchFeedback upload excel error", e);
                throw new RuntimeException("upload excel error");
            }
        }

        // TT通知
        Map<String, Object> msg = new HashMap<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        msg.put("text", "【视频搜索反馈周期统计】\n" +
                "时间范围：" + formatter.format(start) + "~" + formatter.format(end) + "\n" +
                "反馈条数：" + Optional.ofNullable(rpcResult.getData()).map(List::size).orElse(0) + "\n" +
                "Excel链接：" + excelUrl);
        Map<String, Object> body = new HashMap<>();
        body.put("type", 2);
        body.put("msg",msg);
        try {
            httpClient.postForObjectWithGenerics(searchFeedbackWebhook, body, String.class, null, 3000);
        }catch (Exception e){
            log.error("searchFeedback send TT error",e);
            return false;
        }

        return true;
    }

    public Workbook buildExcel(List<LvSearchFeedback> lvSearchFeedbackList) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");
        int rowNum = 0;
        Row firstRow = sheet.createRow(rowNum++);
        int colNum = 0;
        firstRow.createCell(colNum++).setCellValue("搜索词");
        firstRow.createCell(colNum++).setCellValue("问题类型");
        firstRow.createCell(colNum++).setCellValue("问题说明");
        firstRow.createCell(colNum++).setCellValue("联系方式");
        firstRow.createCell(colNum++).setCellValue("图片地址");
        firstRow.createCell(colNum++).setCellValue("提交时间");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (LvSearchFeedback lvSearchFeedback : lvSearchFeedbackList) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(lvSearchFeedback.getKeyword());
            String questionType = searchProperties.getFeedbackOption().get(lvSearchFeedback.getType().toString());
            row.createCell(1).setCellValue(questionType);
            row.createCell(2).setCellValue(lvSearchFeedback.getNote());
            row.createCell(3).setCellValue(lvSearchFeedback.getPhone());
            row.createCell(4).setCellValue(lvSearchFeedback.getImage());
            row.createCell(5).setCellValue(formatter.format(lvSearchFeedback.getCreateTime()));
        }
        // 自动调整列宽
        for (int i = 0; i < colNum; i++) {
            sheet.autoSizeColumn(i);
        }

        return workbook;
    }
}
