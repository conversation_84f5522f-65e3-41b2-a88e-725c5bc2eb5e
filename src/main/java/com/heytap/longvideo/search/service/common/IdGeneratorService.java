package com.heytap.longvideo.search.service.common;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.model.entity.IdGeneratorResult;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.fundament.org.springframework.http.HttpHeaders;
import com.oppo.browser.dfoob.fundament.org.springframework.http.MediaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 获取标准化id
 */
@Slf4j
@Service
public class IdGeneratorService {

    private final HttpDataChannel httpClient;

    @HeraclesDynamicConfig(fileName = "id_generator.properties", key = "contentcloud.generatorId.url")
    private String generatorIdUrl;

    @HeraclesDynamicConfig(fileName = "id_generator.properties", key = "contentcloud.generatorId.timeout")
    private int generatorIdTimeout;

    @HeraclesDynamicConfig(fileName = "id_generator.properties", key = "contentcloud.generatorId.bizName")
    private String bizName;

    @HeraclesDynamicConfig(fileName = "id_generator.properties", key = "contentcloud.generatorId.token")
    private String token;

    @HeraclesDynamicConfig(fileName = "id_generator.properties", key = "contentcloud.generatorId.cpChannel")
    private String cpChannel;

    @HeraclesDynamicConfig(fileName = "id_generator.properties", key = "contentcloud.generatorId.cpChannelName")
    private String cpChannelName;

    public IdGeneratorService(HttpDataChannel httpClient) {
        this.httpClient = httpClient;
    }

    /**
     * 根据 CP 方内容 id 和 CP 方 channel id 生成得到内容平台唯一的资源 id（resource_id）
     *
     * @param cpId CP方文章资源唯一 id
     * @return 唯一的资源 id
     */
    public String generateId(String cpId) {
        log.debug("resourecIdGenUrl:{},bizName:{},token:{},cpId:{},cpChannel:{},cpChannelName:{}", generatorIdUrl, bizName, token, cpId, cpChannel, cpChannelName);
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        List<NameValuePair> urlParameters = new ArrayList<>();
        urlParameters.add(new BasicNameValuePair("biz_name", bizName));
        urlParameters.add(new BasicNameValuePair("token", token));
        urlParameters.add(new BasicNameValuePair("cp_doc_id", cpId));
        urlParameters.add(new BasicNameValuePair("cp_channel", cpChannel));
        urlParameters.add(new BasicNameValuePair("cp_channel_name", cpChannelName));
        String requestBody = URLEncodedUtils.format(urlParameters, Consts.UTF_8);

        IdGeneratorResult result;
        int loop = 5;
        do {
            try {
                String jsonResult = httpClient.postForObject(generatorIdUrl, requestBody, String.class, null, headers, generatorIdTimeout);
                result = JSON.parseObject(jsonResult, IdGeneratorResult.class);
                if (null == result) {
                    log.info("request result is null,return null");
                    return null;
                }
                log.debug("id result:{}", result);
                if (result.getCode() != 0) {
                    log.info("ip result error:{} and return null", result.getMessage());
                    return null;
                }
                if (null == result.getData()) {
                    log.info("data of ip result is null,return null");
                    return null;
                }
                return result.getId();
            } catch (Exception e) {
                --loop;
                if (loop == 0) {
                    log.error("generatorIdV1 error ", e);
                    break;
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(200);
                } catch (InterruptedException ex) {
                }
            }
        } while (loop > 0);
        return null;
    }
}
