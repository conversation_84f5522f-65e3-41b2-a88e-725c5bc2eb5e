package com.heytap.longvideo.search.service.app;

import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.mapper.tvservice.RankCollectMapper;
import com.heytap.longvideo.search.model.entity.es.HotVideoEs;
import com.heytap.longvideo.search.properties.HotVideoProperties;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.utils.SensitiveWordUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class HotVideoService {

    private static final Logger log = LoggerFactory.getLogger("updatelog");

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private RankCollectMapper rankCollectMapper;

    @Autowired
    private HotVideoProperties hotVideoProperties;

    public static ConcurrentHashMap kidsHotMap =new ConcurrentHashMap(128);

    public static ConcurrentHashMap tvHotMap =new ConcurrentHashMap(128);

    public static ConcurrentHashMap movieHotMap =new ConcurrentHashMap(128);

    public static ConcurrentHashMap showHotMap =new ConcurrentHashMap(128);

    public static ConcurrentHashMap comicHotMap =new ConcurrentHashMap(128);



    public void insertRankCollectVideoToEs() {
        List<HotVideoEs> list = rankCollectMapper.selectRankCollect();
        List<HotVideoEs> esList = new ArrayList<>();
        for (HotVideoEs rankCollectEs : list) {
            String name =rankCollectEs.getTitle().split(" ")[0];
            if(StringUtil.isBlank(name)){
                continue;
            }
            rankCollectEs.setTitle(name);
            rankCollectEs.setKey(rankCollectEs.getContentType()+rankCollectEs.getTitle());
            rankCollectEs.setHotScore(8F);
            esList.add(rankCollectEs);
        }
        if(CollectionUtils.isEmpty(esList)){
            log.info("rankcollectes null");
            return;
        }
        IndexOperations indexOperations = restTemplate.indexOps(HotVideoEs.class);
        if(!indexOperations.exists()){
            elasticsearchService.createIndexAndMapping(HotVideoEs.class);
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (HotVideoEs rankCollectEs : list) {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(rankCollectEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(HotVideoEs.class.getAnnotation(Document.class).indexName()));
        indexOperations.refresh();
    }

    public void insertOutHotVideoToEs(List<String> titles,String contentType) {
        List<HotVideoEs> esList = new ArrayList<>();
        for (int i = 0; i <titles.size() ; i++) {
            String title =titles.get(i);
            if(StringUtil.isBlank(title)){
                continue;
            }
            HotVideoEs HotVideoEs =new HotVideoEs();
            HotVideoEs.setTitle(title);
            HotVideoEs.setContentType(contentType);
            HotVideoEs.setKey(contentType+titles);
            if(i<10){
                HotVideoEs.setHotScore(6f);
            }else{
                HotVideoEs.setHotScore(3f);
            }
            esList.add(HotVideoEs);
        }
        if(CollectionUtils.isEmpty(esList)){
            log.info("rankcollectes null");
            return;
        }
        IndexOperations indexOperations = restTemplate.indexOps(HotVideoEs.class);
        if(!indexOperations.exists()){
            elasticsearchService.createIndexAndMapping(HotVideoEs.class);
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (HotVideoEs rankCollectEs : esList) {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(rankCollectEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(HotVideoEs.class.getAnnotation(Document.class).indexName()));
        indexOperations.refresh();
    }

    public void insertAllHeraclesConfigVideoToEs() {
        IndexOperations indexOperations = restTemplate.indexOps(HotVideoEs.class);
        if(!indexOperations.exists()){
            elasticsearchService.createIndexAndMapping(HotVideoEs.class);
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        setHotVideoEs(indexQueries,hotVideoProperties.getComicHotTitle(),ContentTypeEnum.COMIC);
        setHotVideoEs(indexQueries,hotVideoProperties.getTvHotTitle(),ContentTypeEnum.TV);
        setHotVideoEs(indexQueries,hotVideoProperties.getShowHotTitle(),ContentTypeEnum.SHOW);
        setHotVideoEs(indexQueries,hotVideoProperties.getKidsHotTitle(),ContentTypeEnum.KIDS);
        setHotVideoEs(indexQueries,hotVideoProperties.getMovieHotTitle(),ContentTypeEnum.MOVIE);
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(HotVideoEs.class.getAnnotation(Document.class).indexName()));
        indexOperations.refresh();
    }

    public void updateHeraclesConfigVideoToEs(ContentTypeEnum contentTypeEnum, Set<String> titleSet) {
        IndexOperations indexOperations = restTemplate.indexOps(HotVideoEs.class);
        if(!indexOperations.exists()){
            elasticsearchService.createIndexAndMapping(HotVideoEs.class);
        }
        hotVideoProperties.getComicHotTitle();
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (String s : titleSet) {
            IndexQuery indexQuery = new IndexQuery();
            HotVideoEs rankCollectEs =new HotVideoEs();
            rankCollectEs.setTitle(s);
            rankCollectEs.setContentType(contentTypeEnum.getCode());
            rankCollectEs.setKey(contentTypeEnum.getCode()+s);
            rankCollectEs.setHotScore(10F);
            indexQuery.setObject(rankCollectEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(HotVideoEs.class.getAnnotation(Document.class).indexName()));
        indexOperations.refresh();
    }


    public void initHotVideoSensitiveWordMap(ContentTypeEnum typeEnum){
        if(typeEnum ==null){
            log.warn("typeEnum null");
            return;
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("contentType", typeEnum.getCode()));
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQuery)
                .withPageable(PageRequest.of(0, 1000))
                .build();
        //4.解析响应
        List<HotVideoEs> list = new ArrayList<>();
        SearchHits<HotVideoEs> searchHits = restTemplate.search(searchQuery, HotVideoEs.class);
        for (SearchHit<HotVideoEs> searchHit : searchHits) {
            HotVideoEs es = searchHit.getContent();
            list.add(es);
        }
        if(CollectionUtils.isNotEmpty(list)){
            Set<String> set = list.stream().map(HotVideoEs::getTitle).collect(Collectors.toSet());
            if(getHashMap(typeEnum)!=null){
                ConcurrentHashMap hashMap =getHashMap(typeEnum);
                hashMap.clear();
                SensitiveWordUtil.initSensitiveWordMap(set,hashMap);
            }
        }
    }

    public ConcurrentHashMap getHashMap(ContentTypeEnum typeEnum) {
        switch (typeEnum) {
            case COMIC:
                return comicHotMap;
            case TV:
                return tvHotMap;
            case SHOW:
                return showHotMap;
            case KIDS:
                return kidsHotMap;
            case MOVIE:
                return movieHotMap;
            default:
                return null;
        }
    }

    public static ContentTypeEnum getContentTypeEnumByConfigName(String configName){
        switch (configName) {
            case "movie.hot.title":
                return ContentTypeEnum.MOVIE;
            case "tv.hot.title":
                return ContentTypeEnum.TV;
            case "kids.hot.title":
                return ContentTypeEnum.KIDS;
            case "show.hot.title":
                return ContentTypeEnum.SHOW;
            case "comic.hot.title":
                return ContentTypeEnum.COMIC;
            default:
                return null;
        }
    }

    private void setHotVideoEs(List<IndexQuery> indexQueries,Set<String> titleSet,ContentTypeEnum contentTypeEnum){
        for (String s : titleSet) {
            if(StringUtil.isBlank(s)){
                continue;
            }
            IndexQuery indexQuery = new IndexQuery();
            HotVideoEs hotVideoEs =new HotVideoEs();
            hotVideoEs.setTitle(s);
            hotVideoEs.setContentType(contentTypeEnum.getCode());
            hotVideoEs.setKey(contentTypeEnum.getCode()+s);
            indexQuery.setObject(hotVideoEs);
            hotVideoEs.setHotScore(10f);
            indexQueries.add(indexQuery);
        }
    }
}
