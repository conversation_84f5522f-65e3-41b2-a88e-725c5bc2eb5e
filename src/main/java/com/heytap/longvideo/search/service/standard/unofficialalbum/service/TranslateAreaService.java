package com.heytap.longvideo.search.service.standard.unofficialalbum.service;

import com.heytap.longvideo.client.media.entity.MisArea;
import com.heytap.longvideo.client.media.entity.MisOrgArea;
import com.heytap.longvideo.search.rpc.consumer.TranslateAreaApiProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/4/23 下午12:47
 */

@Service
@Slf4j
public class TranslateAreaService {


    @Autowired
    private TranslateAreaApiProxy translateAreaApiProxy;

    /**
     * 转换地域名称
     */
    public String translateArea(String programType, String orgArea) {
        if (StringUtils.isBlank(orgArea)) {
            return "";
        }
        if ("其他".equals(orgArea)) {
            return orgArea;
        }
        // 原始地域映射
        List<MisOrgArea> misOrgAreaList = null;
        try {
            misOrgAreaList = translateAreaApiProxy.getMisOrgAreasByNameAndProgramType(programType, orgArea).get().getData();
        } catch (Exception e) {
            log.error("call misOrgArea rpc api error!", e);
        }

        if (CollectionUtils.isEmpty(misOrgAreaList)) {
            return "";
        }

        //获取地域列表
        List<MisArea> areaList = null;
        try {
            areaList = translateAreaApiProxy.getAreaListByAreaIdAndProgramType(misOrgAreaList.get(0).getAreaId(), programType).get().getData();
        } catch (Exception e) {
            log.error("call misArea rpc api error!", e);
        }

        if (CollectionUtils.isEmpty(areaList)) {
            return "";
        }
        return areaList.get(0).getTitle();
    }
}
