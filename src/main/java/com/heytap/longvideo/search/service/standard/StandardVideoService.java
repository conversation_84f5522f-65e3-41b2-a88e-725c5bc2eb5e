package com.heytap.longvideo.search.service.standard;

import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.entity.es.UgcStandardVideoEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.SearchExportVo;
import com.heytap.longvideo.search.model.param.standard.SearchStandardVideoParams;
import com.heytap.longvideo.search.model.param.standard.StandardVideoVo;
import com.heytap.longvideo.search.model.param.standard.VideoTypeListVo;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import com.heytap.longvideo.search.utils.CSVExportUtil;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import redis.clients.jedis.JedisCluster;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;

import static com.heytap.longvideo.search.constants.MediaExportFileConstant.*;

/*
 * Description 标准化后的剧头搜索
 * Date 9:50 2022/3/17
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class StandardVideoService {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private CsvExportConfig csvExportConfig;

    @Autowired
    @Qualifier("csvFileThreadPool")
    private Executor csvFileThreadPool;

    @Autowired
    private JedisCluster jedisCluster;

    @Autowired
    UploadFileToOcsComponent fileToOcsComponent;


    public PageResponse<StandardVideoVo> searchVideo(SearchStandardVideoParams request) throws Exception {
        try {
            PageResponse<StandardVideoVo> response = new PageResponse<StandardVideoVo>();
            List<StandardVideoVo> voList = new ArrayList<StandardVideoVo>(request.getPageSize());

            if (StringUtils.isBlank(request.getOrder())) {
                request.setOrder("updateTime");
            }

            if (request.getOrder().contains("_")) {
                request.setOrder(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, request.getOrder()));
            }

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_VIDEO_INDEX)
                    .source(new SearchSourceBuilder()
                            .query(getQueryBuilder(request))
                            .from((request.getPageIndex() - 1) * request.getPageSize())
                            .size(request.getPageSize())
                            .trackTotalHits(true)
                            .sort(SortBuilders.fieldSort(request.getOrder()).order(SortOrder.DESC)));
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                StandardVideoEs standardVideo = JsonUtil.fromStr(hit.getSourceAsString(), StandardVideoEs.class);
                StandardVideoVo videoPageListVo = new StandardVideoVo();
                BeanUtils.copyProperties(standardVideo, videoPageListVo);
                videoPageListVo.setOriginStatus(standardVideo.getSourceStatus());
                videoPageListVo.setSourceVipType(String.valueOf(standardVideo.getPayStatus()));
                videoPageListVo.setSourceVideoType(standardVideo.getVideoType());
                voList.add(videoPageListVo);
            }

            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / request.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchVideo fail", e);
            throw e;
        }
    }


    public SearchExportVo searchAndExport(SearchStandardVideoParams request) throws Exception {
        try {
            if (StringUtils.isBlank(request.getOrder())) {
                request.setOrder("updateTime");
            }

            if (request.getOrder().contains("_")) {
                request.setOrder(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, request.getOrder()));
            }
            String fileName = buildFileName(request);

            String searchExportId = String.valueOf(UUID.randomUUID());
            String redisStatusKey = SEARCH_MEDIA_EXPORT_REDIS_STATUS + searchExportId;
            String redisUrlKey = SEARCH_MEDIA_EXPORT_REDIS_URL + searchExportId;
            jedisCluster.set(redisStatusKey, String.valueOf(0), "NX", "EX", 60 * 60);

            //线程池查询
            csvFileThreadPool.execute(() -> {
                try {
                    int totalCounts = writeDataToCsvFile(request, fileName);
                    File file = new File(csvExportConfig.getLocalPath() + fileName);
                    String newFileName = fileName.replace(".csv", "_" + totalCounts + "条.csv");
                    file.renameTo(new File(csvExportConfig.getLocalPath() + newFileName));
                    boolean uploadRes = fileToOcsComponent.uploadFileToOcs(csvExportConfig.getAccessKeyId(),
                            csvExportConfig.getAccessKeySecret(),
                            csvExportConfig.getEndPoint(),
                            csvExportConfig.getRegion(),
                            csvExportConfig.getBucketName(),
                            newFileName,
                            csvExportConfig.getLocalPath(),
                            csvExportConfig.getOcsPath());
                    //redis状态更新
                    if (uploadRes) {
                        jedisCluster.set(redisStatusKey, String.valueOf(1), "XX", "EX", 60 * 60);
                        jedisCluster.set(redisUrlKey, getUrl(newFileName), "NX", "EX", 60 * 60);
                    } else {
                        jedisCluster.set(redisStatusKey, String.valueOf(-1), "XX", "EX", 60 * 60);
                    }
                } catch (Exception e) {
                    jedisCluster.set(redisStatusKey, String.valueOf(-1), "XX", "EX", 60 * 60);
                    if (StringUtils.isNotBlank(fileName)) {
                        File file = new File(csvExportConfig.getLocalPath() + fileName);
                        file.delete();
                    }
                    throw new RuntimeException(e.getMessage(), e);
                }
            });
            return SearchExportVo.builder().searchExportId(searchExportId).url(getUrl(fileName)).build();
        } catch (Exception e) {
            log.error("search export media Video fail", e);
            throw e;
        }
    }

    private String getUrl(String fileName) {
        String url = "http://" + csvExportConfig.getBucketName() + "." + csvExportConfig.getEndPoint() + "/" + csvExportConfig.getOcsPath() + "/" + fileName;
        return url;
    }

    private int writeDataToCsvFile(SearchStandardVideoParams request, String fileName) throws IOException {
        //创建查询条件
        SearchSourceBuilder builder = new SearchSourceBuilder()
                .query(getQueryBuilder(request))
                .trackTotalHits(true)
                .sort(SortBuilders.fieldSort(request.getOrder()).order(SortOrder.DESC)).
                size(3000);
        SearchRequest searchRequest = new SearchRequest()
                .indices(IndexNameConstant.STANDARD_VIDEO_INDEX)
                .source(builder);
        Scroll scroll = new Scroll(new TimeValue(1800000));
        searchRequest.scroll(scroll);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<String> dataList = new ArrayList<>();
        dataList.add("vid,标题,sid,内容品类,视频类型,分类信息（标签）,时长,付费类型,查询条件：" + request.toString().replace(",", ""));
        String file = csvExportConfig.getLocalPath() + fileName;
        CSVExportUtil.exportCsv(new File(file), dataList);
        int totalCounts = 0;
        while (ArrayUtils.isNotEmpty(hits)) {
            totalCounts += hits.length;
            dataList = new ArrayList<>();
            for (SearchHit hit : hits) {
                StandardVideoEs standardVideoEs = JsonUtil.fromStr(hit.getSourceAsString(), StandardVideoEs.class);
                StringBuffer buffer = new StringBuffer();
                String category;
                if ("mgmobile".equals(request.getSource()) || "letv".equals(request.getSource()) || "yst".equals(request.getSource())) {
                    category = standardVideoEs.getTags();
                } else {
                    category = standardVideoEs.getCategory();
                }
                buffer.append("\"" + standardVideoEs.getVid() + "\t" + "\"" + ",").//vid
                        append("\"" + standardVideoEs.getTitle() + "\"" + ",").//标题
                        append("\"" + standardVideoEs.getSid() + "\t" + "\"" + ",").//sid
                        append("\"" + standardVideoEs.getProgramType() + "\"" + ",").//内容品类
                        append("\"" + MEDIA_TYPE_MAP.get(standardVideoEs.getSource() + "_" + standardVideoEs.getVideoType()) + "\"" + ",").//视频类型
                        append("\"" + category + "\"" + ",").//分类信息（标签）
                        append("\"" + standardVideoEs.getDuration() + "\"" + ",").//时长,秒
                        append("\"" + payStatusMap.get(standardVideoEs.getPayStatus()) + "\"" + ",");//付费类型
                dataList.add(buffer.toString());
            }
            CSVExportUtil.exportCsv(new File(file), dataList);
            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
            searchScrollRequest.scroll(scroll);
            SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
            scrollId = searchScrollResponse.getScrollId();
            hits = searchScrollResponse.getHits().getHits();
        }
        return totalCounts;
    }

    private String buildFileName(SearchStandardVideoParams request) throws IOException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd_hh-mm-ss");
        Date date = new Date();
        String fileName;
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        if (!StringUtils.isEmpty(request.getVideoManageName())) {
            fileName = request.getVideoManageName() + "_" + format.format(date) + "_" + uuid + ".csv";
        } else {
            fileName = "视频管理" + "_" + format.format(date) + "_" + uuid + ".csv";
        }
        return fileName;
    }


    /**
     * 构造剧头查询条件
     *
     * @param nRequest
     * @return
     */
    private QueryBuilder getQueryBuilder(SearchStandardVideoParams nRequest) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                SearchStandardVideoParams.class, EsField.class);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        Integer durationMin = nRequest.getDurationMin();
        Integer durationMax = nRequest.getDurationMax();
        boolQueryBuilder.must(QueryBuilders.rangeQuery("duration").from(durationMin).to(durationMax));

        //屏蔽优酷内容 后台
        if ("1".equals(nRequest.getBlockYoukuMobile())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("source", SourceEnum.YOUKU_MOBILE.getDataSource()));
        }

        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fieldName = field.getAnnotation(EsField.class).name();
            fieldName = StringUtils.isNotEmpty(fieldName) ? fieldName : entry.getKey();
            buildSingleField(fieldName, fieldValue, nRequest.getSource(), boolQueryBuilder);
        }
        return boolQueryBuilder;
    }

    private void buildSingleField(String fieldName, String fieldValue, String source, BoolQueryBuilder boolQueryBuilder) {
        if ("source".equals(fieldName) && "funshion".equals(fieldValue)) {
            // 后台直接添加 风行和微迪欧
            boolQueryBuilder.must(QueryBuilders.termsQuery(fieldName, "huashi", "senyu","weidiou","funshion_lv"));
        } else if ("status".equals(fieldName) && "0".equals(fieldValue)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("status", "1"));
        } else if (("mgmobile".equals(source) || "letv".equals(source) || "yst".equals(source)) &&
                "category".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("tags", fieldValue));
        } else {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fieldName, fieldValue));
        }
    }


    /**
     * 数据更新
     *
     * @param es
     */
    public void insertOrUpdate(StandardVideoEs es) {
        setAlbumInfo(es);
        try {
            es.setUniKey(es.getSid() + ":" + es.getVid());
            restTemplate.save(es);
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardAlbum", e);
            throw e;
        }
    }


    /**
     * 数据更新
     *
     * @param es
     */
    public void delete(StandardVideoEs es) {
        try {
            es.setUniKey(es.getSid() + ":" + es.getVid());
            restTemplate.delete(es.getUniKey(), IndexCoordinates.of(StandardVideoEs.class.getAnnotation(Document.class).indexName()));
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardVideo", e);
            throw e;
        }
    }

    /**
     * 数据初始化
     */
    public void initData(List<StandardVideoEs> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("list null");
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (StandardVideoEs standardVideoEs : list) {
            setAlbumInfo(standardVideoEs);
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(standardVideoEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(StandardVideoEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(StandardVideoEs.class);
        indexOperations.refresh();
    }

    private void setAlbumInfo(StandardVideoEs standardVideoEs) {
        StandardAlbum standardAlbum = null;
        try {
            standardAlbum = standardAlbumRpcApi.getBySid(standardVideoEs.getSid()).get().getData();
        } catch (Exception e) {
            log.warn("call standardAlbum rpc api error!", e);
            return;
        }
        if (standardAlbum == null) {
            return;
        }
        standardVideoEs.setAlbumActor(standardAlbum.getActor());
        standardVideoEs.setAlbumArea(standardAlbum.getArea());
        standardVideoEs.setAlbumBrief(standardAlbum.getBrief());
        standardVideoEs.setAlbumCompleted(standardAlbum.getCompleted());
        standardVideoEs.setAlbumDirector(standardAlbum.getDirector());
        standardVideoEs.setAlbumInformation(standardAlbum.getInformation());
        standardVideoEs.setAlbumPayStatus(standardAlbum.getPayStatus());
        standardVideoEs.setAlbumProgramInfo(standardAlbum.getProgramInfo());
        standardVideoEs.setAlbumShowTime(standardAlbum.getShowTime());
        standardVideoEs.setAlbumStatus(standardAlbum.getStatus());
        standardVideoEs.setAlbumTitle(standardAlbum.getTitle());
        standardVideoEs.setAlbumYear(standardAlbum.getYear());
        standardVideoEs.setAlbumFeatureType(standardAlbum.getFeatureType());
        standardVideoEs.setScore(standardAlbum.getSourceScore());
        standardVideoEs.setAlbumHorizontalIcon(standardAlbum.getHorizontalIcon());
        standardVideoEs.setAlbumVerticalIcon(standardAlbum.getVerticalIcon());
    }

    /**
     * ugc数据初始化
     */
    public void initUgcData(List<UgcStandardVideoEs> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("ugc list null");
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (UgcStandardVideoEs standardVideoEs : list) {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(standardVideoEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(UgcStandardVideoEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(UgcStandardVideoEs.class);
        indexOperations.refresh();
    }

    public List<VideoTypeListVo> getVideoType(String source) {

        if ("funshion".equals(source)) {
            // 后台直接添加 风行和微迪欧
            source = source.replace("funshion", "huashi,senyu,weidiou,funshion_lv");
        }

        SearchRequest searchRequest = new SearchRequest(IndexNameConstant.STANDARD_VIDEO_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(source)) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("source", source.split(","));
            query.filter(termsQueryBuilder);
        }
        sourceBuilder.query(query);
        sourceBuilder.size(0);

        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("videoType").field("videoType");
        aggregationBuilder.size(100);
        sourceBuilder.aggregation(aggregationBuilder);

        searchRequest.source(sourceBuilder);
        sourceBuilder.trackTotalHits(true);

        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("search es error!", e);
        }
        Aggregations aggregations = searchResponse.getAggregations();
        Map<String, Aggregation> aggregationMap = aggregations.asMap();
        Terms programTypeList = (Terms) aggregationMap.get("videoType");
        List<? extends Terms.Bucket> buckets = programTypeList.getBuckets();

        List<VideoTypeListVo> vos = Lists.newArrayList();
        buckets.forEach(bucket -> {
            String value = bucket.getKeyAsString();
            Map<String, Integer> videoTypeMap = StandardConstant.VIDEO_TYPE_MAP;
            Set<String> videoTypeKeys = videoTypeMap.keySet();
            String text = null;
            for (String videoTypeKey : videoTypeKeys) {
                Integer videoType = videoTypeMap.get(videoTypeKey);
                if (value.equals(String.valueOf(videoType))) {
                    text = videoTypeKey;
                    break;
                }
            }
            VideoTypeListVo vo = new VideoTypeListVo();
            vo.setValue(value);
            vo.setText(text);
            vos.add(vo);
        });
        return vos;
    }
}
