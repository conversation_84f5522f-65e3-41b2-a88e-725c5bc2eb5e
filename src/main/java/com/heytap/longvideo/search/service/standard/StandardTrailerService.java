package com.heytap.longvideo.search.service.standard;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.StandardEpisodeRpcApi;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.StandardEpisode;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.StandardTrailerEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class StandardTrailerService {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardEpisodeRpcApi standardEpisodeRpcApi;

    public void initData(List<StandardTrailerEs> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("list null");
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (StandardTrailerEs standardTrailerEs : list) {
            standardTrailerEs.setTidL(Long.valueOf(standardTrailerEs.getTid()));
            setAlbumInfo(standardTrailerEs);
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(standardTrailerEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(StandardTrailerEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(StandardTrailerEs.class);
        indexOperations.refresh();
    }

    private void setAlbumInfo(StandardTrailerEs standardTrailerEs) {
        StandardAlbum standardAlbum = null;
        try {
            standardAlbum = standardAlbumRpcApi.getBySid(standardTrailerEs.getSid()).get().getData();
        } catch (Exception e) {
            log.warn("call standardAlbum rpc api error!", e);
            return;
        }
        if (standardAlbum == null) {
            return;
        }
        standardTrailerEs.setAlbumActor(standardAlbum.getActor());
        standardTrailerEs.setAlbumArea(standardAlbum.getArea());
        standardTrailerEs.setAlbumBrief(standardAlbum.getBrief());
        standardTrailerEs.setAlbumCompleted(standardAlbum.getCompleted());
        standardTrailerEs.setAlbumDirector(standardAlbum.getDirector());
        standardTrailerEs.setAlbumInformation(standardAlbum.getInformation());
        standardTrailerEs.setAlbumPayStatus(standardAlbum.getPayStatus());
        standardTrailerEs.setAlbumProgramInfo(standardAlbum.getProgramInfo());
        standardTrailerEs.setAlbumShowTime(standardAlbum.getShowTime());
        standardTrailerEs.setAlbumStatus(standardAlbum.getStatus());
        standardTrailerEs.setAlbumTitle(standardAlbum.getTitle());
        standardTrailerEs.setAlbumYear(standardAlbum.getYear());
        standardTrailerEs.setAlbumFeatureType(standardAlbum.getFeatureType());
        standardTrailerEs.setScore(standardAlbum.getSourceScore());
        standardTrailerEs.setAlbumHorizontalIcon(standardAlbum.getHorizontalIcon());
        standardTrailerEs.setAlbumVerticalIcon(standardAlbum.getVerticalIcon());
        standardTrailerEs.setAlbumProgramType(standardAlbum.getProgramType());
        standardTrailerEs.setAlbumTags(standardAlbum.getTags());
        standardTrailerEs.setAlbumLanguage(standardAlbum.getLanguage());
        standardTrailerEs.setSourceAlbumId(standardAlbum.getSourceAlbumId());

        if (StringUtils.isNotEmpty(standardTrailerEs.getEid())) {
            try {
                StandardEpisode standardEpisode = standardEpisodeRpcApi.getByEid(standardTrailerEs.getEid(), standardTrailerEs.getSid()).get().getData();
                if (standardEpisode != null) {
                    standardTrailerEs.setEpsiodeTitle(standardEpisode.getTitle());
                }
            } catch (Exception e) {
                log.warn("call StandardEpisode.getByEid rpc api error!", e);
            }
        }
    }

    public PageResponse<StandardTrailerVo> searchTrailer(SearchStandardTrailerParams nRequest) throws Exception {
        try {

            if (StringUtils.isBlank(nRequest.getOrder())) {
                nRequest.setOrder("updateTime");
            }

            PageResponse<StandardTrailerVo> response = new PageResponse<>();
            List<StandardTrailerVo> voList = new ArrayList<>(nRequest.getPageSize());

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getTrailerQueryBuilder(nRequest))
                    .from((nRequest.getPageIndex() - 1) * nRequest.getPageSize())
                    .size(nRequest.getPageSize())
                    .sort(SortBuilders.fieldSort(nRequest.getOrder()).order(SortOrder.DESC))
                    .sort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                    .sort(SortBuilders.fieldSort("tidL").order(SortOrder.DESC))
                    .trackTotalHits(true);
            // .sort(SortBuilders.fieldSort("sid").order(SortOrder.DESC));

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_TRAILER_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                StandardTrailerEs standardTrailer = JsonUtil.fromStr(hit.getSourceAsString(), StandardTrailerEs.class);
                StandardTrailerVo standardTrailerVo = new StandardTrailerVo();
                BeanUtils.copyProperties(standardTrailer, standardTrailerVo);
                standardTrailerVo.setOriginStatus(standardTrailer.getSourceStatus());
                standardTrailerVo.setSourceVipType(String.valueOf(standardTrailer.getPayStatus()));
                standardTrailerVo.setLinkValue(standardTrailer.getVid());
                voList.add(standardTrailerVo);
            }
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(nRequest.getPageIndex());
            response.setPageSize(nRequest.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / nRequest.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchTrailer fail", e);
            throw e;
        }
    }

    public PageResponse<CmsTrailerVo> searchCmsTrailer(CmsSearchTrailerParams nRequest) throws Exception {
        try {
            PageResponse<CmsTrailerVo> response = new PageResponse<>();
            List<CmsTrailerVo> voList = new ArrayList<>(nRequest.getPageSize());

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getCmsTrailerQueryBuilder(nRequest))
                    .from((nRequest.getPageIndex() - 1) * nRequest.getPageSize())
                    .size(nRequest.getPageSize())
                    .trackTotalHits(true);
            // .sort(SortBuilders.fieldSort("sid").order(SortOrder.DESC));

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_TRAILER_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                StandardTrailerEs standardTrailer = JsonUtil.fromStr(hit.getSourceAsString(), StandardTrailerEs.class);
                CmsTrailerVo cmsTrailerVo = new CmsTrailerVo();
                BeanUtils.copyProperties(standardTrailer, cmsTrailerVo);
                cmsTrailerVo.setOriginStatus(standardTrailer.getSourceStatus());
                cmsTrailerVo.setLinkValue(standardTrailer.getVid());
                cmsTrailerVo.setLinkType(95);
                cmsTrailerVo.setId(standardTrailer.getTid());
                voList.add(cmsTrailerVo);
            }
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(nRequest.getPageIndex());
            response.setPageSize(nRequest.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / nRequest.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchTrailer fail", e);
            throw e;
        }
    }

    private QueryBuilder getTrailerQueryBuilder(SearchStandardTrailerParams nRequest) {
        if (StringUtils.isNotEmpty(nRequest.getLinkValue())) {
            nRequest.setTid(nRequest.getLinkValue());
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //周边视频查询剧头 是否屏蔽优酷内容
        if ("1".equals(nRequest.getBlockYoukuMobile())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("source", SourceEnum.YOUKU_MOBILE.getDataSource()));
        }

        if (nRequest.getStartDuration() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("duration").gte(nRequest.getStartDuration()));
        }
        if (nRequest.getEndDuration() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("duration").lte(nRequest.getEndDuration()));
        }
        if (nRequest.getAgeStart() != null || nRequest.getAgeEnd() != null) {
            int ageStart = nRequest.getAgeStart() == null ? 0 : nRequest.getAgeStart();
            int ageEnd = nRequest.getAgeEnd() == null ? 100 : nRequest.getAgeEnd();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fitAgeMin").gte(ageStart));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fitAgeMax").lte(ageEnd));
        }

        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(nRequest.getDurationItems())) {
            for (DurationItem durationItem : nRequest.getDurationItems()) {

                boolQueryBuilder1.should(QueryBuilders.boolQuery()
                        .must(QueryBuilders.termsQuery("source", durationItem.getSource()))
                        .must(getRangeQueryBuilder(durationItem)));

                boolQueryBuilder.must(boolQueryBuilder1);
            }
        }

        buildEsFieldQuery(nRequest, boolQueryBuilder);

        return boolQueryBuilder;
    }

    private void buildEsFieldQuery(SearchStandardTrailerParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                SearchStandardTrailerParams.class, EsField.class);
        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fileName = field.getAnnotation(EsField.class).name();
            fileName = StringUtils.isNotEmpty(fileName) ? fileName : entry.getKey();
            if ("sid".equals(entry.getKey())) {
                buildSidQuery(nRequest, boolQueryBuilder, fieldValue);
            } else if ("source".equals(entry.getKey()) && "funshion".equals(fieldValue)) {
                // 后台直接添加 风行和微迪欧
                boolQueryBuilder.must(QueryBuilders.termsQuery(fileName, "huashi", "senyu", "weidiou", "funshion_lv"));
            } else if ("albumTags".equals(entry.getKey())) {
                boolQueryBuilder.must(QueryBuilders.matchQuery(fileName, fieldValue.replace(",", "|")));
            } else if ("programType".equals(entry.getKey()) && StringUtils.isNotEmpty(fieldValue)) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("albumProgramType", fieldValue.split(",")));
            } else if ("trailerType".equals(entry.getKey()) && StringUtils.isNotEmpty(fieldValue)) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("trailerType", fieldValue.split(",")));
            } else {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fileName, fieldValue));
            }
        }
    }

    private void buildSidQuery(SearchStandardTrailerParams nRequest, BoolQueryBuilder boolQueryBuilder, String fieldValue) {
        if (nRequest.isPreciseFlag()) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sid", fieldValue));
        } else {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("sid", "*" + fieldValue + "*"));
        }
    }

    private RangeQueryBuilder getRangeQueryBuilder(DurationItem durationItem) {
        RangeQueryBuilder duration = QueryBuilders.rangeQuery("duration").gte(0);

        if (durationItem.getStartDuration() == null && durationItem.getEndDuration() == null) {
            duration = QueryBuilders.rangeQuery("duration").lte(20000);
        }

        if (durationItem.getStartDuration() != null) {
            duration = QueryBuilders.rangeQuery("duration").gte(durationItem.getStartDuration());
        }

        if (durationItem.getEndDuration() != null) {
            duration = QueryBuilders.rangeQuery("duration").lte(durationItem.getEndDuration());
        }

        return duration;
    }

    private QueryBuilder getCmsTrailerQueryBuilder(CmsSearchTrailerParams nRequest) {

        if (StringUtils.isNotEmpty(nRequest.getLinkValue())) {
            nRequest.setTid(nRequest.getLinkValue());
        }

        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                CmsSearchTrailerParams.class, EsField.class);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fileName = field.getAnnotation(EsField.class).name();
            fileName = StringUtils.isNotEmpty(fileName) ? fileName : entry.getKey();
            if ("sid".equals(entry.getKey())) {
                boolQueryBuilder.must(QueryBuilders.wildcardQuery(fileName, "*" + fieldValue + "*"));
            } else if ("source".equals(entry.getKey()) && "funshion".equals(fieldValue)) {
                // 后台直接添加 风行和微迪欧
                boolQueryBuilder.must(QueryBuilders.termsQuery(fileName, "huashi", "senyu", SourceEnum.FUNSHION_LONGVIDEO.getDataSource(), SourceEnum.WEIDIOU.getDataSource()));
            } else {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fileName, fieldValue));
            }
        }
        return boolQueryBuilder;
    }

    public void insertOrUpdate(StandardTrailerEs es) {
        setAlbumInfo(es);
        try {
            restTemplate.save(es);
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardTrailer", e);
            throw e;
        }
    }

    public void delete(StandardTrailerEs es) {
        try {
            restTemplate.delete(es.getTid(), IndexCoordinates.of(StandardTrailerEs.class.getAnnotation(Document.class).indexName()));
        } catch (Exception e) {
            updateLog.error("delete standardTrailer", e);
            throw e;
        }
    }

    public List<TrailerTypeListVo> getTrailerType(String sid) {

        List<TrailerTypeListVo> vos = Lists.newArrayList();

        if (StringUtils.isBlank(sid)) {
            return vos;
        }

        SearchRequest searchRequest = new SearchRequest(IndexNameConstant.STANDARD_TRAILER_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder query = QueryBuilders.boolQuery();

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("sid", sid);
        query.filter(termQueryBuilder);
        sourceBuilder.query(query);
        sourceBuilder.size(0);

        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("trailerType").field("trailerType");
        aggregationBuilder.size(100);
        sourceBuilder.aggregation(aggregationBuilder);

        searchRequest.source(sourceBuilder);
        sourceBuilder.trackTotalHits(true);

        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("search es error!", e);
        }
        Aggregations aggregations = searchResponse.getAggregations();
        Map<String, Aggregation> aggregationMap = aggregations.asMap();
        Terms programTypeList = (Terms) aggregationMap.get("trailerType");
        List<? extends Terms.Bucket> buckets = programTypeList.getBuckets();

        buckets.forEach(bucket -> {
            String value = bucket.getKeyAsString();
            Map<String, Integer> videoTypeMap = StandardConstant.VIDEO_TYPE_MAP;
            Set<String> videoTypeKeys = videoTypeMap.keySet();
            String text = null;
            for (String videoTypeKey : videoTypeKeys) {
                Integer videoType = videoTypeMap.get(videoTypeKey);
                if (value.equals(String.valueOf(videoType))) {
                    text = videoTypeKey;
                    break;
                }
            }
            TrailerTypeListVo vo = new TrailerTypeListVo();
            vo.setTrailerType(value);
            vo.setTypeName(text);
            vos.add(vo);
        });
        return vos;
    }
}
