package com.heytap.longvideo.search.service.app;

import com.github.stuxuhai.jpinyin.ChineseHelper;
import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.github.stuxuhai.jpinyin.PinyinHelper;
import com.heytap.longvideo.search.constants.CopyrightConstant;
import com.heytap.longvideo.search.model.entity.es.HotActorEs;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParam;
import com.heytap.longvideo.search.model.param.app.SuggestionResponse;
import com.heytap.longvideo.search.service.search.SearchAlbumService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FieldValueFactorFunctionBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.search.suggest.Suggest;
import org.elasticsearch.search.suggest.SuggestBuilder;
import org.elasticsearch.search.suggest.completion.CompletionSuggestionBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*
 * Description 关键词联想
 * Date 17:30 2022/6/21
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class SuggestService {

    private final ElasticsearchRestTemplate restTemplate;

    private final YoukuSourceFilterService youkuSourceFilterService;

    private final FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    private final SearchAlbumService searchAlbumService;

    static Pattern hanyuPattern;

    static {
        hanyuPattern = Pattern.compile("[\\u4E00-\\u9FA5]+");

    }

    public SuggestService(ElasticsearchRestTemplate restTemplate,
                          YoukuSourceFilterService youkuSourceFilterService,
                          FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService,
                          SearchAlbumService searchAlbumService) {
        this.restTemplate = restTemplate;
        this.youkuSourceFilterService = youkuSourceFilterService;
        this.funshionLongVideoAndWeidiouFilterService = funshionLongVideoAndWeidiouFilterService;
        this.searchAlbumService = searchAlbumService;
    }

    /**
     * 按关键词搜索
     *
     * @param param
     * @return
     */
    public List<SuggestionResponse> suggest(KeyWordSearchParam param) {
        //1.参数校验
        List<String> titleList = new ArrayList<>();
        List<SuggestionResponse> returnList = new ArrayList<>();
        String keyword = param.getKeyword();
        if (StringUtil.isBlank(keyword) || keyword.length() > 20) {
            return returnList;
        }
        keyword = ChineseHelper.convertToSimplifiedChinese(keyword.trim());
        log.info("search params:{}", param.toString());
        //2.构造查询参数
        getTitleList(keyword, titleList, param);

        Set<String> set = new HashSet<>();
        if (CollectionUtils.isNotEmpty(titleList)) {
            int i = 0;
            for (String s : titleList) {
                if (set.contains(s)) {
                    continue;
                }
                set.add(s);
                i++;
                SuggestionResponse suggestionResponse = new SuggestionResponse();
                suggestionResponse.setTitle(s);
                returnList.add(suggestionResponse);
                if (i == 10) {
                    break;
                }
            }
        }
        return returnList;
    }

    private void getTitleList(String keyword, List<String> titleList, KeyWordSearchParam param) {
        int size = 10;
        int actorInsertNum = 0;
        CompletionSuggestionBuilder suggestionBuilder = null;
        if (StringUtil.isNumericOrChar(keyword)) {
            if (keyword.length() <= 5) {
                suggestionBuilder = new CompletionSuggestionBuilder("seriesTitlePinyin").prefix(keyword).size(size).skipDuplicates(true);
                actorInsertNum = getProgramSuggestResponse(suggestionBuilder, titleList);
            } else {
                suggestionBuilder = new CompletionSuggestionBuilder("suggestTitlePinyin").prefix(keyword).size(size);
                actorInsertNum = getProgramSuggestResponse(suggestionBuilder, titleList);
            }
            int actorSize = titleList.size() < 5 ? 5 : 3;
            suggestionBuilder = new CompletionSuggestionBuilder("actorNamePinyin").prefix(keyword).size(actorSize);
            getActorSuggestResponse(suggestionBuilder, keyword, titleList, actorInsertNum);
        } else {
            if (keyword.length() == 1) {
                suggestionBuilder = new CompletionSuggestionBuilder("seriesTitle").prefix(keyword).size(size).skipDuplicates(true);
                actorInsertNum = getProgramSuggestResponse(suggestionBuilder, titleList);
            } else {
                searchForSuggest(keyword, titleList, param);
            }
            int actorSize = titleList.size() < 5 ? 5 : 3;

            suggestionBuilder = new CompletionSuggestionBuilder("actorName").prefix(keyword).size(actorSize);
            getActorSuggestResponse(suggestionBuilder, keyword, titleList, actorInsertNum);
            if (keyword.length() == 1 && titleList.size() < 10) {
                searchForSuggest(keyword, titleList, param);
            }
        }
    }

    private <T> int getProgramSuggestResponse(CompletionSuggestionBuilder suggestionBuilderDistrict, List<String> resultSet) {
        int num = 0;
        SuggestBuilder suggestBuilder = new SuggestBuilder();
        suggestBuilder.addSuggestion("suggest", suggestionBuilderDistrict);//添加suggest
        SearchResponse response = restTemplate.suggest(suggestBuilder, IndexCoordinates.of(ProgramAlbumEs.class.getAnnotation(Document.class).indexName()));
        Suggest suggest = response.getSuggest();

        if (suggest != null) {
            List<? extends Suggest.Suggestion.Entry<? extends Suggest.Suggestion.Entry.Option>> entries = suggest.getSuggestion("suggest").getEntries();
            for (Suggest.Suggestion.Entry<? extends Suggest.Suggestion.Entry.Option> entry : entries) {
                for (Suggest.Suggestion.Entry.Option option : entry.getOptions()) {
                    String result = option.getText().string();
                    if (num < 2 && option.getScore() > 150) {
                        num++;
                    }
                    resultSet.add(result);
                }
            }
        }
        return num;
    }

    private <T> void getActorSuggestResponse(CompletionSuggestionBuilder suggestionBuilderDistrict, String keyword, List<String> allSet, int actorInsertNum) {
        List<String> highSet = new ArrayList<>();
        List<String> middleSet = new ArrayList<>();

        SuggestBuilder suggestBuilder = new SuggestBuilder();
        suggestBuilder.addSuggestion("suggest", suggestionBuilderDistrict);//添加suggest
        SearchResponse response = restTemplate.suggest(suggestBuilder, IndexCoordinates.of(HotActorEs.class.getAnnotation(Document.class).indexName()));
        Suggest suggest = response.getSuggest();

        if (suggest != null) {
            List<? extends Suggest.Suggestion.Entry<? extends Suggest.Suggestion.Entry.Option>> entries = suggest.getSuggestion("suggest").getEntries();
            for (Suggest.Suggestion.Entry<? extends Suggest.Suggestion.Entry.Option> entry : entries) {
                for (Suggest.Suggestion.Entry.Option option : entry.getOptions()) {
                    handleOption(option, keyword, highSet, middleSet);
                }
            }
        }
        allSet.addAll(actorInsertNum, highSet);
        if (allSet.size() >= 10) {
            allSet.addAll(8, middleSet);
        } else {
            allSet.addAll(middleSet);
        }
    }

    private void handleOption(Suggest.Suggestion.Entry.Option option, String keyword, List<String> highSet, List<String> middleSet) {
        String result = option.getText().string();
        if (option.getScore() < 2 && StringUtil.isNumericOrChar(keyword) && !keyword.equals(StringUtil.toPinyin(result))) {
            return;
        }
        if (option.getScore() <= 3) {
            middleSet.add(result);
        } else {
            highSet.add(result);
        }
    }

    public List<String> searchForSuggest(String keyword, List<String> returnSet, KeyWordSearchParam param) {
        List<ProgramAlbumEs> programAlbumEsList = new ArrayList<>();
        BoolQueryBuilder boolQuery = buildQuery(keyword);
        FieldValueFactorFunctionBuilder fieldQuery = new FieldValueFactorFunctionBuilder("functionScore");
        FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(boolQuery, fieldQuery);
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        PageRequest pageRequest = PageRequest.of(0, 20);
        // 搜索提示词 过滤风行和微迪欧
        if (funshionLongVideoAndWeidiouFilterService.filterItem(param.getVersion())) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_FUNSHION_LONGVIDEO));
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_WEIDIOU));
        }
        // 搜索提示词 根据过滤条件进行更改
        if (youkuSourceFilterService.filterItem(param.getVersion())) {
            boolQuery.mustNot(QueryBuilders.termQuery("source", CopyrightConstant.COPYRIGHT_YOUKU_MOBILE));
        }
        NativeSearchQuery searchQuery = queryBuilder.withQuery(functionScoreQueryBuilder)
                .withPageable(pageRequest)
                .build();
        //4.解析响应
        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            float score = searchHit.getScore();
            ProgramAlbumEs es = searchHit.getContent();
            es.setReleScore(score);
            programAlbumEsList.add(es);
        }
        //5.过滤
        if (CollectionUtils.isEmpty(programAlbumEsList)) {
            return returnSet;
        }
        programAlbumEsList = searchAlbumService.filterByMatch(programAlbumEsList, keyword);
        if (CollectionUtils.isNotEmpty(programAlbumEsList)) {
            programAlbumEsList = searchAlbumService.keywordSearchSort(programAlbumEsList, keyword);
            for (ProgramAlbumEs programAlbumEs : programAlbumEsList) {
                returnSet.add(programAlbumEs.getTitle());
            }
        }
        return returnSet;

    }

    private BoolQueryBuilder buildQuery(String keyword) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (keyword.length() >= 6) {
            boolQuery.should(QueryBuilders.matchQuery("title", keyword).operator(Operator.OR).minimumShouldMatch("85%"));
        } else if (keyword.length() >= 5) {
            boolQuery.should(QueryBuilders.matchQuery("title", keyword).operator(Operator.OR).minimumShouldMatch("80%"));
        } else if (keyword.length() >= 4) {
            boolQuery.should(QueryBuilders.matchQuery("title", keyword).operator(Operator.OR).minimumShouldMatch("75%"));
        } else if (keyword.length() == 3) {
            StringBuilder pinyinBuild = new StringBuilder();
            char[] hanYuArr = keyword.toCharArray();
            for (int i = 0, len = hanYuArr.length; i < len; i++) {
                Matcher matcher = hanyuPattern.matcher(Character.toString(hanYuArr[i]));
                if (matcher.find()) {
                    String[] pys = PinyinHelper.convertToPinyinArray(hanYuArr[i], PinyinFormat.WITHOUT_TONE);
                    pinyinBuild.append(pys[0]);
                } else {
                    pinyinBuild.append(hanYuArr[i]);
                }
            }
            boolQuery.should(QueryBuilders.matchPhraseQuery("titlePinyin", pinyinBuild.toString()));
        }
        boolQuery.should(QueryBuilders.matchPhraseQuery("title", keyword));
        return boolQuery;
    }
}
