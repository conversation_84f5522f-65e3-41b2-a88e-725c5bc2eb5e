package com.heytap.longvideo.search.service.common;

import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.VideoOperationLogEs;
import com.heytap.video.client.search.model.VideoOperationLog;
import com.heytap.video.client.search.model.request.VideoOperationLogQueryRequest;
import com.heytap.video.client.search.model.response.PageResponse;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/*
 * Description 运营操作日志
 * Date 19:27 2024/3/28
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class VideoOperationLogService {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;


    @Autowired
    private ElasticSearchService elasticsearchService;


    public PageResponse<VideoOperationLog> queryPage(VideoOperationLogQueryRequest request) {
        try {
            PageResponse<VideoOperationLog> response = new PageResponse<>();
            List<VideoOperationLog> voList = new ArrayList<VideoOperationLog>();

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getQueryLogBuilder(request))
                    .from((request.getPageIndex() - 1) * request.getPageSize())
                    .size(request.getPageSize())
                    .trackTotalHits(true)
                    .sort("createTime", SortOrder.DESC);

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.VIDEO_OPERATION_LOG)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                VideoOperationLog videoOperationLog = JsonUtil.fromStr(hit.getSourceAsString(), VideoOperationLog.class);
                voList.add(videoOperationLog);
            }

            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / request.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchVideoOperationLog fail", e);
            throw new RuntimeException(e.getMessage());
        }
    }


    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * @return
     */
    private QueryBuilder getQueryLogBuilder(VideoOperationLogQueryRequest request) throws Exception {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        try {
            if (StringUtils.isNotBlank(request.getBusinessMudule())) {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("businessMudule", request.getBusinessMudule()));
            }
            if (StringUtils.isNotBlank(request.getBusinessType())) {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("businessType", request.getBusinessType()));
            }
            if (StringUtils.isNotBlank(request.getCreateUser())) {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("createUser", request.getCreateUser()));
            }
            if (StringUtils.isNotBlank(request.getCreateTimeStart())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").gte(dateFormat.parse(request.getCreateTimeStart()).getTime()));
            }
            if (StringUtils.isNotBlank(request.getCreateTimeEnd())) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").lte(dateFormat.parse(request.getCreateTimeEnd()).getTime()));
            }
            return boolQueryBuilder;
        } catch (Exception e) {
            log.error("getQueryLogBuilder error", e);
            throw e;
        }
    }


    /**
     * 数据初始化
     */
    public void createIndex() {
        elasticsearchService.deleteIndex(VideoOperationLogEs.class);
        elasticsearchService.createIndexAndMapping(VideoOperationLogEs.class);
    }


    /**
     * 数据更新
     *
     * @param es
     */
    public void insert(VideoOperationLogEs es) {
        try {
            restTemplate.save(es);
        } catch (Exception e) {
            log.error("insert VideoOperationLogEs error", e);
            throw e;
        }
    }

}
