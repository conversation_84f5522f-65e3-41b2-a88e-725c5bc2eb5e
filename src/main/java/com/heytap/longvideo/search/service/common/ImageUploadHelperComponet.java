package com.heytap.longvideo.search.service.common;

import com.alibaba.fastjson.JSONObject;
import com.drew.imaging.FileType;
import com.drew.imaging.FileTypeDetector;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/13 14:35
 */
@Slf4j
@Component
public class ImageUploadHelperComponet implements SmartInitializingSingleton {

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.url", fileName = "configure.properties")
    private String url;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.serverId", fileName = "configure.properties")
    private String serverId;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.readTimeOut", fileName = "configure.properties")
    private int readTimeOut = 2000;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.connectTimeOut", fileName = "configure.properties")
    private int connectTimeOut = 2000;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.connect.maxTotal", fileName = "configure.properties")
    private int connectmaxTotal = 20;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.outside.requestUrl", fileName = "configure.properties")
    private String requestUrl;
    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.userOutSide.send", fileName = "configure.properties")
    private Boolean outSideSend;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.outSide.machine.sourceList", fileName = "configure.properties")
    private List<String> outSideSourceList;

    @HeraclesDynamicConfig(key = "unofficial.album.uploadPic.inSide.machine.sourceList", fileName = "configure.properties")
    private List<String> inSideSourceList;

    private CloseableHttpClient httpclient;

    /**
     * 新图片上传，建议使用
     */
    public String uploadPic(String imageUrl, String fileName, String sid, String source) {
        if (StringUtils.isEmpty(imageUrl) || StringUtils.isEmpty(fileName) || StringUtils.isEmpty(sid)) {
            return null;
        }
        log.info("Upload pic file, sid:{}, imageUrl:{}", sid, imageUrl);

        //获取imageUrl字节流信息
        String url = null;
        if (CollectionUtils.isNotEmpty(outSideSourceList) && outSideSourceList.contains(source)) {
            url = String.format(requestUrl, imageUrl);
        }
        if (CollectionUtils.isNotEmpty(inSideSourceList) && inSideSourceList.contains(source)) {
            url = imageUrl;
        }
        if (StringUtils.isEmpty(url)) {
            return null;
        }

        HttpGet httpGet = new HttpGet(url);
        HttpResponse response;
        try {
            response = httpclient.execute(httpGet);
        } catch (Exception e) {
            log.info("httpclient execute error, sid:{}, imageUrl:{}, ", sid, imageUrl, e);
            return null;
        }
        HttpEntity responseEntity = response.getEntity();
        try (InputStream inputStream = responseEntity.getContent(); BufferedInputStream bis = new BufferedInputStream(inputStream)) {
            //获取图片类型
            FileType fileType = FileTypeDetector.detectFileType(bis);
            if (fileType == null || StringUtils.isEmpty(fileType.name()) || "unknown".equalsIgnoreCase(fileType.name())) {
                log.info("Failed to detect file type, detected type: {}, sid: {}, imageUrl:{}", fileType != null ? fileType.name() : "null", sid, imageUrl);
                return null;
            }
            String imageName = fileName + "." + fileType.name().toLowerCase();
            //图片URL转File
            File file = buildFileFfromUrl(imageName, bis);
            //上传图片
            return doUploadPic(imageName, file);
        } catch (Exception e) {
            log.error("upload pic error, sid:{}, imageUrl:{}", sid, imageUrl, e);
        }
        return null;
    }

    /**
     * 上传图片
     */
    public String doUploadPic(String fileName, File file) {
        if (file == null) {
            return "";
        }
        //try(CloseableHttpClient httpClient = HttpClients.createDefault()) {   没有走连接池化创建httpClient，导致客户端响应异常
        try {
            HttpPost httpPost = new HttpPost(new StringBuilder().append(url).append("?serverId=").append(serverId)
                    .append("&optim=1").append("&filename=").append(fileName).toString());
            HttpEntity entity = MultipartEntityBuilder.create().addBinaryBody("a", file, ContentType.DEFAULT_BINARY, fileName).build();

            httpPost.setEntity(entity);

            HttpResponse response = httpclient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();

            String result = "";
            if (responseEntity != null) {  // 将响应内容转换为字符串
                result = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            }
            //判断是否上传成功  返回200
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.debug("doUploadPic result: {}", result);
                if (!StringUtils.isEmpty(result)) {
                    JSONObject jsonResult = JSONObject.parseObject(result);
                    if (jsonResult.getIntValue("ret") == 0) {
                        JSONObject data = jsonResult.getJSONObject("data");
                        return data != null ? data.getString("url") : "";
                    } else {
                        log.warn("doUploadPic failure: result  = {}", jsonResult.toJSONString());
                        return "";
                    }
                } else {
                    log.warn("doUploadPic failure: result is blank!");
                    return "";
                }
            } else {
                log.warn("Upload pic failure: StatusCode = {}", response.getStatusLine().getStatusCode());
                return "";
            }
        } catch (Exception e) {
            log.error("doUploadPic error: " + e);
        } finally {
            if (file.exists()) {
                file.delete();
            }
        }
        return "";
    }

    /**
     * 构建File
     */
    public File buildFileFfromUrl(String imageName, BufferedInputStream bis) {
        File fileDir = new File("./temp");
        if (!fileDir.exists() && !fileDir.isDirectory()) {
            fileDir.mkdirs();
        }
        File file = new File("./temp/" + imageName);

        // Thumbnails.of(new URL(imageUrl)).scale(1).toFile(file);

        int bufferSize = 1024;
        byte[] buf = new byte[bufferSize];
        int size = 0;
        try (FileOutputStream fos = new FileOutputStream(file)) {
            while ((size = bis.read(buf)) != -1) {
                fos.write(buf, 0, size);
            }
            fos.flush();
            return file;
        } catch (Exception ex) {
            log.error("buildFileFfromUrl error: ", ex);
        }
        return null;
    }

    @Override
    public void afterSingletonsInstantiated() {
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        manager.setMaxTotal(connectmaxTotal); //连接池最大并发连接数
        manager.setDefaultMaxPerRoute(connectmaxTotal);//单路由最大并发数,路由是对maxTotal的细分

        RequestConfig config = RequestConfig.copy(RequestConfig.DEFAULT)
                .setSocketTimeout(readTimeOut)
                .setConnectTimeout(connectTimeOut)
                .setConnectionRequestTimeout(300).build();

        httpclient = HttpClients.custom().setConnectionManager(manager).setDefaultRequestConfig(config).build();

    }
}
