package com.heytap.longvideo.search.service.common;

import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.constants.CopyrightConstant;
import com.heytap.longvideo.search.constants.ThirdPartyMediaTypeEnum;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.properties.SearchProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/*
 * 版本过滤
 * Date 18:58 2021/12/21
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class VersionFilterService {

    @Autowired
    private SearchProperties searchConfig;

    public boolean versionFilter(int versionTag, ProgramAlbumEs programAlbumEs, String appId){
        // 浏览器播放场景不做过滤
        if (ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(appId)) {
            return false;
        }
        List<String> allowsource = CommonConstant.versionTagAllowSource.get(versionTag);
        if(!allowsource.contains(programAlbumEs.getSource())){
            return true;
        }

        if (versionTag < searchConfig.getFunshionSingleBuyAppVersion()) {
            if(2 == programAlbumEs.getPayStatus() &&
                    (CopyrightConstant.COPYRIGHT_HUASHI.equalsIgnoreCase(programAlbumEs.getSource())
                            || CopyrightConstant.COPYRIGHT_SENYU.equalsIgnoreCase(programAlbumEs.getSource()))){
                return true;
            }
        }

        return false;
    }
}
