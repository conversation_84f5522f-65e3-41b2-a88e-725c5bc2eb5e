package com.heytap.longvideo.search.service.app;

import com.heytap.longvideo.client.media.entity.StandardEpisodeBO;
import com.heytap.longvideo.common.lib.constants.ActivityTaskUnlockEpisodeConstant;
import com.heytap.longvideo.common.lib.constants.MarkStyleEnum;
import com.heytap.longvideo.search.model.EpisodeVO;
import com.heytap.longvideo.common.lib.model.TaskForEpisodeAttachment;
import com.heytap.longvideo.common.lib.rpc.ActivityRpcApi;
import com.heytap.longvideo.common.lib.rpc.req.MarkCodeRpcRequest;
import com.heytap.longvideo.common.lib.rpc.resp.activity.UnlockEpisodeInfo;
import com.heytap.longvideo.search.constants.SortEnum;
import com.heytap.longvideo.search.model.BaseRequest;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.service.common.StrategyService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.heytap.longvideo.search.utils.VersionUtil;
import com.heytap.video.ad.common.entity.req.BuriedCommonReqProperty;
import com.heytap.video.client.entity.video.ButtonVO;
import com.heytap.video.client.enums.ButtonStatusEnum;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.cookie.UserInfo;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TaskUnlockEpisodeService {

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private StrategyService strategyService;

    @Autowired
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Reference(providerAppId = "longvideo-activity-rest", protocol = "dubbo")
    private ActivityRpcApi activityRpcApi;

    @HeraclesDynamicConfig(key = "taskUnlockEpisode.version", fileName = "configure.properties", defaultValue = {"7.12.0"})
    private String taskUnlockEpisodeVersion = "7.12.0";

    public CompletableFuture<List<UnlockEpisodeInfo>> getUnlockEpisodeMarkCode(BuriedCommonReqProperty nRequest,
                                                                               String uid,
                                                                               String userVipType,
                                                                               List<UnlockEpisodeInfo> episodeInfos,
                                                                               Map<String, MatchStrategyResponseItem> strategyMap,
                                                                               Predicate<TaskForEpisodeAttachment> sceneSwitch) {
        if (StringUtils.isEmpty(uid)) {
            uid = Optional.ofNullable(nRequest.getScookieIgnoreException())
                    .map(Cookie::getInfo)
                    .map(UserInfo::getUid)
                    .orElse(null);
        }

        MatchStrategyResponseItem strategy = strategyMap.get(ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE);
        MatchStrategyResponseItem mongoStrategy = strategyMap.get(ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE_MONGO);
        boolean unlockSwitch = showTaskMarkStrategyCheck(strategy, sceneSwitch);
        boolean unlockMongoSwitch = showTaskMarkStrategyCheck(mongoStrategy, sceneSwitch);
        if (!unlockSwitch && !unlockMongoSwitch) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        MarkCodeRpcRequest request = new MarkCodeRpcRequest();
        request.setNRequest(nRequest);
        request.setUnlockSwitch(unlockSwitch);
        request.setUnlockMongoSwitch(unlockMongoSwitch);
        request.setUid(uid);
        request.setUserVipType(userVipType);
        request.setEpisodeInfos(episodeInfos);
        request.setStrategy(strategy);
        request.setMongoStrategy(mongoStrategy);

        return activityRpcApi.getUnlockEpisodeMarkCode(request).handle((ret,e) -> {
            if (ret == null || e!= null || !ret.isSuccess()) {
                log.error("activityRpcApi.getUnlockEpisodeMarkCode error. request:{} ret:{}", request, ret, e);
                return Collections.emptyList();
            }
            if (ret.getData() == null || CollectionUtils.isEmpty(ret.getData().getTaskUnlockEpisodes())) {
                return Collections.emptyList();
            }
            return ret.getData().getTaskUnlockEpisodes();
        });
    }

    public static boolean showTaskMarkStrategyCheck(MatchStrategyResponseItem strategy,Predicate<TaskForEpisodeAttachment> predicate) {
        if (strategy == null) {
            return false;
        }
        String attachment = strategy.getAttachment();
        TaskForEpisodeAttachment episodeActivity = JacksonUtil.parseObject(attachment, TaskForEpisodeAttachment.class);
        //外层提供断言方案,不同的地方传递的开关不一样
        return episodeActivity != null && predicate.test(episodeActivity);
    }

    /**
     * 聚合卡二级页接口处理任务解锁角标
     */
    public void handleMarkCode4InterveneCard(InterveneCardParam request, SearchInterveneCardResponse interveneCardResponse) {
        try {
            if (request.getVersion() == null || request.getVersion() < searchProperties.getTaskUnlockEpisodeExpVersion()) {
                return;
            }
            if (StringUtils.isBlank(request.getAttributeValues().getFullBrowserVersion())) {
                request.getAttributeValues().setFullBrowserVersion(VersionUtil.getVersionName(request.getVersion()));
            }
            List<UnlockEpisodeInfo> unlockEpisodeInfos = convert2UnlockInfos(interveneCardResponse);
            if (CollectionUtils.isEmpty(unlockEpisodeInfos)) {
                return;
            }

            CompletableFuture<List<UnlockEpisodeInfo>> markCodeInfosCF = strategyService.matchSearchStrategy(
                    request, new String[]{ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE,
                                    ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE_MONGO})
                    .thenComposeAsync(strategyMap -> getUnlockEpisodeMarkCode(
                            request, null, request.getVipType(), unlockEpisodeInfos, strategyMap,
                            (attachment) -> BooleanUtils.isTrue(attachment.getSearchPageOpen())));
            List<UnlockEpisodeInfo> markCodeInfos = FutureUtil.getFutureIgnoreException(markCodeInfosCF);
            if (CollectionUtils.isEmpty(markCodeInfos)) {
                return;
            }

            Map<String, UnlockEpisodeInfo> sid2UnlockInfoMap = markCodeInfos.stream()
                    .collect(Collectors.toMap(UnlockEpisodeInfo::getSid,
                            Function.identity(),
                            (oldValue, newValue) -> oldValue));

            setListFreeUnlockedMarkCode(sid2UnlockInfoMap, request.getAttributeValues().getFullBrowserVersion());
            for (KeyWordSearchResponse searchResponse : interveneCardResponse.getContents()) {
                UnlockEpisodeInfo unlockEpisodeInfo = sid2UnlockInfoMap.get(searchResponse.getSid());
                if (unlockEpisodeInfo != null) {
                    searchResponse.setMarkCode(unlockEpisodeInfo.getMarkCode());
                    if(StringUtils.isNotBlank(unlockEpisodeInfo.getMarkCodeUrl())){
                        searchResponse.setMarkCodeUrl(unlockEpisodeInfo.getMarkCodeUrl());
                    }
                }
            }

            // 免费优先排序：免费节目>可免费解锁>会员付费节目
            if (SortEnum.FREE.getType().equals(request.getSortType())) {
                interveneCardResponse.getContents().sort(
                        Comparator.comparing(KeyWordSearchResponse::getPayStatus)
                                .thenComparing(resp -> sid2UnlockInfoMap.get(resp.getSid()) == null)
                );
            }
        } catch (Exception e) {
            log.error("handleMarkCode4SearchPage error", e);
        }
    }

    private List<UnlockEpisodeInfo> convert2UnlockInfos(SearchInterveneCardResponse interveneCardResponse) {
        return interveneCardResponse.getContents().stream().map(item -> {
            UnlockEpisodeInfo unlockEpisodeInfo = new UnlockEpisodeInfo();
            unlockEpisodeInfo.setSid(item.getSid());
            unlockEpisodeInfo.setPayStatus(item.getPayStatus());
            unlockEpisodeInfo.setFeatureType(item.getFeatureType());
            unlockEpisodeInfo.setSource(item.getSource());
            unlockEpisodeInfo.setContentType(item.getContentType());
            unlockEpisodeInfo.setMarkStyle(MarkStyleEnum.LIST.getCode());
            return unlockEpisodeInfo;
        }).collect(Collectors.toList());
    }


    /**
     * 搜索结果页处理任务解锁角标
     */
    public void handleMarkCode4SearchPage(KeyWordSearchParamV2 request, SearchResponse searchResponse) {
        try {
            if (request.getVersion() == null || request.getVersion() < searchProperties.getTaskUnlockEpisodeExpVersion()) {
                return;
            }
            if (StringUtils.isBlank(request.getAttributeValues().getFullBrowserVersion())) {
                request.getAttributeValues().setFullBrowserVersion(VersionUtil.getVersionName(request.getVersion()));
            }

            List<UnlockEpisodeInfo> unlockEpisodeInfos = convert2AlbumInfos(searchResponse);
            if (CollectionUtils.isEmpty(unlockEpisodeInfos)) {
                return;
            }

            CompletableFuture<List<UnlockEpisodeInfo>> markCodeInfosCF = strategyService.matchSearchStrategy(
                            request, new String[]{ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE,
                                    ActivityTaskUnlockEpisodeConstant.TASK_FOR_EPISODE_MONGO})
                    .thenComposeAsync(strategyMap -> getUnlockEpisodeMarkCode(
                            request, null, request.getVipType(), unlockEpisodeInfos, strategyMap,
                            (attachment) -> BooleanUtils.isTrue(attachment.getSearchPageOpen())));
            List<UnlockEpisodeInfo> markCodeInfos = FutureUtil.getFutureIgnoreException(markCodeInfosCF);
            if (CollectionUtils.isEmpty(markCodeInfos)) {
                return;
            }
            // 统一将解锁角标修改为免费解锁
            setListFreeUnlockedMarkCode(markCodeInfos, request.getAttributeValues().getFullBrowserVersion());
            //sid剧集解锁map
            Map<String, UnlockEpisodeInfo> markCodeInfoMap = markCodeInfos.stream()
                    .filter(info -> info.getVid() == null)
                    .collect(Collectors.toMap(UnlockEpisodeInfo::getSid, Function.identity(), (oldValue, newValue) -> oldValue));
            //vid剧集解锁map
            Map<String, UnlockEpisodeInfo> vidUnlockInfoMap = markCodeInfos.stream()
                    .filter(info -> info.getVid() != null)
                    .collect(Collectors.toMap(UnlockEpisodeInfo::getVid,
                            Function.identity(),
                            (oldValue, newValue) -> oldValue));
            // 长视频结果卡
            setMarkCodeUrl(Optional.ofNullable(searchResponse.getLongVideoSearchResult()).orElse(Collections.emptyList()), markCodeInfoMap,vidUnlockInfoMap);
            // 推荐卡
            setMarkCodeUrl(Optional.ofNullable(searchResponse.getLongVideoRecommend()).map(SearchInterveneCardResponse::getContents)
                    .orElse(Collections.emptyList()), markCodeInfoMap,null);
            // 标签卡
            setMarkCodeUrl(Optional.ofNullable(searchResponse.getLongVideoTag()).map(SearchInterveneCardResponse::getContents)
                    .orElse(Collections.emptyList()), markCodeInfoMap,null);
            // 系列卡
            setMarkCodeUrl(Optional.ofNullable(searchResponse.getLongVideoSeries()).map(SearchInterveneCardResponse::getContents)
                    .orElse(Collections.emptyList()), markCodeInfoMap,null);
            // 影人卡
            setMarkCodeUrl(Optional.ofNullable(searchResponse.getLongVideoActor()).map(SearchInterveneCardResponse::getContents)
                    .orElse(Collections.emptyList()), markCodeInfoMap,null);
        } catch (Exception e) {
            log.error("handleMarkCode4SearchPage error", e);
        }
    }

    /**
     * 外部搜索处理任务解锁角标
     */
    public void handleMarkCode4OutSearch(KeyWordSearchParamV2 request, String uid, String vipType,
                                         Map<String, MatchStrategyResponseItem> strategyMap,
                                         SearchResponse response) {
        List<UnlockEpisodeInfo> unlockEpisodeInfos = convert2AlbumInfos(response);
        if (CollectionUtils.isEmpty(unlockEpisodeInfos)) {
            return;
        }

        CompletableFuture<List<UnlockEpisodeInfo>> markCodeInfosCF = getUnlockEpisodeMarkCode(
                request, uid, vipType, unlockEpisodeInfos, strategyMap,
                (attachment) -> BooleanUtils.isTrue(attachment.getSearchPageOpen()));
        List<UnlockEpisodeInfo> markCodeInfos = FutureUtil.getFutureIgnoreException(markCodeInfosCF);
        if (CollectionUtils.isEmpty(markCodeInfos)) {
            return;
        }
        // 统一将解锁角标修改为免费解锁
        setListFreeUnlockedMarkCode(markCodeInfos, request.getAttributeValues().getFullBrowserVersion());
        //sid解锁map
        Map<String, UnlockEpisodeInfo> markCodeInfoMap = markCodeInfos.stream()
                .filter(info -> info.getVid() == null)
                .collect(Collectors.toMap(UnlockEpisodeInfo::getSid, Function.identity(), (oldValue, newValue) -> oldValue));
        //vid解锁map
        Map<String, UnlockEpisodeInfo> vidUnlockInfoMap = markCodeInfos.stream()
                .filter(info -> info.getVid() != null)
                .collect(Collectors.toMap(UnlockEpisodeInfo::getVid,
                        Function.identity(),
                        (oldValue, newValue) -> oldValue));
        // 长视频结果卡
        setMarkCodeUrl(Optional.ofNullable(response.getLongVideoSearchResult()).orElse(Collections.emptyList()), markCodeInfoMap, vidUnlockInfoMap);
    }

    private List<UnlockEpisodeInfo> convertEpisodeUnlockInfos(List<EpisodeVO> episodeVOList, KeyWordSearchResponse keyWordSearchResponse){
        if(CollectionUtils.isEmpty(episodeVOList)){
            return null;
        }
        return episodeVOList.stream().map(episode -> {
            UnlockEpisodeInfo unlockEpisodeInfo = new UnlockEpisodeInfo();
            unlockEpisodeInfo.setSid(episode.getSid());
            unlockEpisodeInfo.setVid(episode.getVid());
            unlockEpisodeInfo.setPayStatus(episode.getPayStatus());
            unlockEpisodeInfo.setFeatureType(episode.getFeatureType());
            unlockEpisodeInfo.setSource(episode.getSource());
            unlockEpisodeInfo.setContentType(keyWordSearchResponse.getContentType());
            unlockEpisodeInfo.setMarkStyle(MarkStyleEnum.NUMBER.getCode());
            return unlockEpisodeInfo ;
        }).collect(Collectors.toList());
    }


    private List<UnlockEpisodeInfo> convert2AlbumInfos(SearchResponse searchResponse) {
        List<UnlockEpisodeInfo> result = Stream.of(
                        Optional.ofNullable(searchResponse.getLongVideoSearchResult()).orElse(Collections.emptyList()),
                        Optional.ofNullable(searchResponse.getLongVideoRecommend()).map(SearchInterveneCardResponse::getContents).orElse(Collections.emptyList()),
                        Optional.ofNullable(searchResponse.getLongVideoTag()).map(SearchInterveneCardResponse::getContents).orElse(Collections.emptyList()),
                        Optional.ofNullable(searchResponse.getLongVideoSeries()).map(SearchInterveneCardResponse::getContents).orElse(Collections.emptyList()),
                        Optional.ofNullable(searchResponse.getLongVideoActor()).map(SearchInterveneCardResponse::getContents).orElse(Collections.emptyList())
                )
                .flatMap(List::stream)
                .map(this::convert2AlbumInfo)
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(searchResponse.getLongVideoSearchResult()) ){
            KeyWordSearchResponse firstKeyWordSearchResponse = searchResponse.getLongVideoSearchResult().get(0);
            if(CollectionUtils.isNotEmpty(firstKeyWordSearchResponse.getEpisodes())){
                List<UnlockEpisodeInfo> firstUnlockEpisodeInfos = convertEpisodeUnlockInfos(firstKeyWordSearchResponse.getEpisodes(), firstKeyWordSearchResponse);
                if(CollectionUtils.isNotEmpty(firstUnlockEpisodeInfos)){
                    result.addAll(firstUnlockEpisodeInfos);
                }
            }
        }

        return result;
    }

    private UnlockEpisodeInfo convert2AlbumInfo(KeyWordSearchResponse searchResponse) {
        UnlockEpisodeInfo unlockEpisodeIno = new UnlockEpisodeInfo();
        unlockEpisodeIno.setSid(searchResponse.getSid());
        unlockEpisodeIno.setPayStatus(searchResponse.getPayStatus());
        unlockEpisodeIno.setFeatureType(searchResponse.getFeatureType());
        unlockEpisodeIno.setSource(searchResponse.getCopyrightCode());
        unlockEpisodeIno.setContentType(searchResponse.getContentType());
        unlockEpisodeIno.setMarkStyle("list");
        return unlockEpisodeIno;
    }

    private void setMarkCodeUrl(List<KeyWordSearchResponse> searchResponseList, Map<String, UnlockEpisodeInfo> markCodeInfoMap,Map<String, UnlockEpisodeInfo> vidMarkCodeInfoMap) {
        for (KeyWordSearchResponse searchResponse : searchResponseList) {
            if(CollectionUtils.isNotEmpty(searchResponse.getEpisodes()) && vidMarkCodeInfoMap != null){
                setEpisodeMarkCodeUrl(searchResponse.getEpisodes(),vidMarkCodeInfoMap);
            }

            UnlockEpisodeInfo markCodeInfo = markCodeInfoMap.get(searchResponse.getSid());
            if (markCodeInfo != null) {
                searchResponse.setMarkCode(markCodeInfo.getMarkCode());
                searchResponse.setMarkCodeUrl(markCodeInfo.getMarkCodeUrl());
                //如果当前buttons不为空,则证明是结果卡v2版本,需要将第一个按钮置为免费解锁
                if(CollectionUtils.isNotEmpty(searchResponse.getButtons())){
                    ButtonVO firstButton = searchResponse.getButtons().get(0);
                    firstButton.setStatus(ButtonStatusEnum.FREE_UNLOCK.getCode());
                    firstButton.setText(ButtonStatusEnum.FREE_UNLOCK.getDesc());
                }
            }
        }
    }

    private void setEpisodeMarkCodeUrl(List<EpisodeVO> episodeVOList,Map<String, UnlockEpisodeInfo> markCodeInfoMap){
        if(CollectionUtils.isEmpty(episodeVOList) || markCodeInfoMap == null){
            return ;
        }
        for(EpisodeVO episodeVO: episodeVOList){
            UnlockEpisodeInfo unlockEpisodeInfo = markCodeInfoMap.get(episodeVO.getVid());
            if(unlockEpisodeInfo != null){
                episodeVO.setMarkCode(unlockEpisodeInfo.getMarkCode());
                episodeVO.setMarkCodeUrl(unlockEpisodeInfo.getMarkCodeUrl());
            }
        }
    }

    /**
     * 获取支持免费解锁的节目列表
     */
    public List<String> getUnlockAlbumList(BaseRequest request,
                                           String userVipType,
                                           String uid,
                                           Map<String, MatchStrategyResponseItem> strategyMap,
                                           List<KeyWordSearchResponse> albumList) {
        try {
            if (StringUtils.isEmpty(request.getAppVersion())) {
                return Collections.emptyList();
            }

            // 7.12 详情页影片列表+系列剧列表
            boolean supportUnlock = CommonUtils.compareVersion(request.getAppVersion(), taskUnlockEpisodeVersion) >= 0;
            if (!supportUnlock) {
                return Collections.emptyList();
            }
            List<UnlockEpisodeInfo> unlockEpisodeInfos = convertAlbum2UnlockInfos(albumList);
            if (CollectionUtils.isEmpty(unlockEpisodeInfos)) {
                return Collections.emptyList();
            }

            CompletableFuture<List<UnlockEpisodeInfo>> markCodeInfosCF = getUnlockEpisodeMarkCode(
                    request, uid, userVipType, unlockEpisodeInfos, strategyMap,
                    (attachment) -> BooleanUtils.isTrue(attachment.getDetailPageOpen()));
            List<UnlockEpisodeInfo> markCodeInfos = FutureUtil.getFutureIgnoreException(markCodeInfosCF);
            if (CollectionUtils.isEmpty(markCodeInfos)) {
                return Collections.emptyList();
            }
            return markCodeInfos.stream().map(UnlockEpisodeInfo::getSid).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getUnlockAlbumList error", e);
            return Collections.emptyList();
        }
    }

    private List<UnlockEpisodeInfo> convertAlbum2UnlockInfos(List<KeyWordSearchResponse> albumList) {
        if (CollectionUtils.isEmpty(albumList)) {
            return Collections.emptyList();
        }
        return albumList.stream().map(album -> {
            UnlockEpisodeInfo unlockEpisodeInfo = new UnlockEpisodeInfo();
            unlockEpisodeInfo.setSid(album.getSid());
            unlockEpisodeInfo.setPayStatus(album.getPayStatus());
            unlockEpisodeInfo.setFeatureType(album.getFeatureType());
            unlockEpisodeInfo.setSource(album.getSource());
            unlockEpisodeInfo.setContentType(album.getContentType());
            unlockEpisodeInfo.setMarkStyle(MarkStyleEnum.LIST.getCode());
            return unlockEpisodeInfo;
        }).collect(Collectors.toList());
    }

    /**
     * s8.9 列表页标签统一修改为免费解锁
     * @param sid2UnlockInfoMap
     */
    private void setListFreeUnlockedMarkCode(Map<String, UnlockEpisodeInfo> sid2UnlockInfoMap, String appVersion) {
        String imageUrl = imageTagRpcApiProxy.getImageUrl(ActivityTaskUnlockEpisodeConstant.LIST_FREE_UNLOCKED, appVersion);
        for (String sid : sid2UnlockInfoMap.keySet()) {
            sid2UnlockInfoMap.get(sid).setMarkCodeUrl(imageUrl);
            sid2UnlockInfoMap.get(sid).setMarkCode(ActivityTaskUnlockEpisodeConstant.LIST_FREE_UNLOCKED);
        }
    }

    /**
     * s8.9 列表页标签统一修改为免费解锁
     * @param markCodeInfos
     */
    private void setListFreeUnlockedMarkCode(List<UnlockEpisodeInfo> markCodeInfos, String appVersion) {
        String imageUrl = imageTagRpcApiProxy.getImageUrl(ActivityTaskUnlockEpisodeConstant.LIST_FREE_UNLOCKED, appVersion);
        for (UnlockEpisodeInfo unlockEpisodeInfo : markCodeInfos) {
            unlockEpisodeInfo.setMarkCodeUrl(imageUrl);
            unlockEpisodeInfo.setMarkCode(ActivityTaskUnlockEpisodeConstant.LIST_FREE_UNLOCKED);
        }
    }
}