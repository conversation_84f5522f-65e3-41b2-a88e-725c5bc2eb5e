package com.heytap.longvideo.search.service.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.*;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.BaseRequest;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.outside.OutSideSearchButtonConfig;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.AlbumRankRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.StandardAlbumRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.StandardEpisodeRpcApiProxy;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.service.vip.VipRelatedService;
import com.heytap.longvideo.search.utils.DeepLinkUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.video.client.entity.video.ButtonVO;
import com.heytap.video.client.entity.video.HighLightVO;
import com.heytap.video.client.enums.ButtonStatusEnum;
import com.heytap.video.client.enums.HighLightTypeEnum;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.OutSideSearchConstant.*;
import static com.heytap.longvideo.search.constants.SearchConstant.*;


@Service
@Slf4j
public class ConvertResponseService {

    @HeraclesDynamicConfig(key = "album.detail.deepLink", fileName = "search_config.properties")
    private String detailDeepLink;

    @Autowired
    private StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    @Autowired
    private ImageTagRpcApiProxy imageTagRpcApiProxy;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private YoukuSourceFilterService youkuSourceFilterService;

    @Autowired
    private DeepLinkUtils deepLinkUtils;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Autowired
    private AlbumRankRpcApiProxy albumRankRpcApiProxy;

    @Autowired
    private StandardEpisodeRpcApiProxy standardEpisodeRpcApiProxy;

    @Autowired
    private VipRelatedService vipRelatedService;

    @Autowired
    private  TaskUnlockEpisodeService taskUnlockEpisodeService;

    public List<KeyWordSearchResponse> getResponseBySidList(
            List<String> sidList, Integer showMarkCode, KeyWordSearchParamV2 requestParam, Integer sortType) {
        List<KeyWordSearchResponse> list = new ArrayList<>();
        Map<String, StandardAlbum> standardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);
        if (MapUtils.isEmpty(standardAlbumMap)) {
            return list;
        }

        for (String sid : sidList) {
            KeyWordSearchResponse keyWordSearchResponse = handleSingleSid(standardAlbumMap.get(sid), requestParam, showMarkCode);
            if (keyWordSearchResponse != null) {
                list.add(keyWordSearchResponse);
            }
        }

        handleSubTitle(list, requestParam.getVersion());
        sort(list, sortType, standardAlbumMap, requestParam.getVersion());
        return list;
    }

    public KeyWordSearchResponse handleSingleSid(StandardAlbum standardAlbum, KeyWordSearchParamV2 requestParam,
                                                 Integer showMarkCode) {
        if (standardAlbum == null) {
            return null;
        }
        KeyWordSearchResponse keyWordSearchResponse = standardAlbumToSearchResponse(standardAlbum, requestParam);
        if (keyWordSearchResponse == null) {
            return null;
        }
        // 二级页 风行和微迪欧屏蔽
        if (funshionLongVideoAndWeidiouFilterService.filterItemBySource(keyWordSearchResponse.getSource(), requestParam.getVersion())) {
            return null;
        }
        // 二级页 根据开关屏蔽优酷
        if (youkuSourceFilterService.filterItemBySource(keyWordSearchResponse.getSource(), requestParam.getVersion())) {
            return null;
        }
        // 二级页 设置deepLink
        if (youkuSourceFilterService.setDeepLinkFilter(keyWordSearchResponse.getSource(), requestParam.getQuickEngineVersion(), requestParam.getVersion())) {
            keyWordSearchResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), standardAlbum.getSourceWebUrl()));
        } else if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(keyWordSearchResponse.getSource())) {
            keyWordSearchResponse.setDeepLink(youkuSourceFilterService.getUpGradeUrl(keyWordSearchResponse.getSid(), keyWordSearchResponse.getSource(), keyWordSearchResponse.getTitle()));
        }
        if ((showMarkCode != null && showMarkCode == 1) || FEATURE_TYPE_YG == keyWordSearchResponse.getFeatureType()) {
            if (FEATURE_TYPE_YG == keyWordSearchResponse.getFeatureType()) {
                keyWordSearchResponse.setMarkCode(MARK_CODE_YG);
            }
            keyWordSearchResponse.setMarkCodeUrl(imageTagRpcApiProxy.getImageUrl(keyWordSearchResponse.getMarkCode()));
        } else {
            keyWordSearchResponse.setMarkCode("");
            keyWordSearchResponse.setMarkCodeUrl("");
        }
        handleBackgroundColor(keyWordSearchResponse, standardAlbum.getBackGroundColorJson());
        handleShowMsg(keyWordSearchResponse, false, requestParam.getVersion());
        return keyWordSearchResponse;
    }

    public void sort(List<KeyWordSearchResponse> list, Integer sortType, Map<String, StandardAlbum> standardAlbumMap,
                     Integer version) {
        if (version == null || version < searchProperties.getSearchCardOptVersion()) {
            return;
        }
        if (sortType == null) {
            return;
        }
        if (SortEnum.HOT.getType().equals(sortType)) {
            list.sort((a, b) -> {
                StandardAlbum album1 = standardAlbumMap.get(a.getSid());
                StandardAlbum album2 = standardAlbumMap.get(b.getSid());
                return compareSourceHot(album1, album2);
            });
        } else if (SortEnum.NEW.getType().equals(sortType)) {
            list.sort((a, b) -> {
                StandardAlbum album1 = standardAlbumMap.get(a.getSid());
                StandardAlbum album2 = standardAlbumMap.get(b.getSid());
                return compareShowTime(album1, album2);
            });
        } else if (SortEnum.FREE.getType().equals(sortType)) {
            list.sort(Comparator.comparing(KeyWordSearchResponse::getPayStatus));
        }
    }

    private int compareSourceHot(StandardAlbum album1, StandardAlbum album2) {
        if (album1.getSourceHot() == null && album2.getSourceHot() == null) {
            return 0;
        } else if (album1.getSourceHot() == null) {
            return 1;
        } else if (album2.getSourceHot() == null) {
            return -1;
        }
        return Double.compare(album2.getSourceHot(), album1.getSourceHot());
    }

    private int compareShowTime(StandardAlbum album1, StandardAlbum album2) {
        if (StringUtil.isBlank(album1.getShowTime()) && StringUtil.isBlank(album2.getShowTime())) {
            return 0;
        } else if (StringUtil.isBlank(album1.getShowTime())) {
            return 1;
        } else if (StringUtil.isBlank(album2.getShowTime())) {
            return -1;
        }
        return album2.getShowTime().compareTo(album1.getShowTime());
    }

    public KeyWordSearchResponse standardAlbumToSearchResponse(StandardAlbum standardAlbum, KeyWordSearchParamV2 param) {
        if (standardAlbum == null) {
            return null;
        }
        if ("ztv".equals(standardAlbum.getSource())) {
            return null;
        }
        KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
        BeanUtils.copyProperties(standardAlbum, keyWordSearchResponse);
        keyWordSearchResponse.setReleScore(10F);
        keyWordSearchResponse.setContentType(standardAlbum.getProgramType());
        keyWordSearchResponse.setRecommendInfo(standardAlbum.getBrief());
        keyWordSearchResponse.setMultipleSourceCode(new ArrayList<>(Lists.newArrayList(standardAlbum.getSource())));
        keyWordSearchResponse.setSourceScore(StringUtil.isBlank(standardAlbum.getSourceScore()) ? 0.0F : Float.parseFloat(standardAlbum.getSourceScore()));
        keyWordSearchResponse.setStars(standardAlbum.getActor().replace("|", ","));
        keyWordSearchResponse.setLanguages(standardAlbum.getLanguage());
        keyWordSearchResponse.setAlbumFeatureType(keyWordSearchResponse.getFeatureType());
        keyWordSearchResponse.setDirectors(standardAlbum.getDirector().replace("|", ","));
        if (StringUtil.isEmpty(keyWordSearchResponse.getWebUrl()) && StringUtil.isEmpty(keyWordSearchResponse.getDeepLink())) {
            keyWordSearchResponse.setDeepLink(String.format(detailDeepLink, standardAlbum.getSid()));
        }
        handleSourceKind(keyWordSearchResponse);
        keyWordSearchResponse = handleYoukuDeepLink(standardAlbum, param, keyWordSearchResponse);
        return keyWordSearchResponse;
    }

    private KeyWordSearchResponse handleYoukuDeepLink(StandardAlbum standardAlbum, KeyWordSearchParamV2 param, KeyWordSearchResponse keyWordSearchResponse) {
        if (!SourceEnum.YOUKU_MOBILE.getDataSource().equals(standardAlbum.getSource())) {
            return keyWordSearchResponse;
        }
        if (1 == param.getIsOut()) {
            Map<String, StandardAlbum> standardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(
                    Lists.newArrayList(standardAlbum.getSid()));
            if (standardAlbumMap.isEmpty()) {
                return null;
            }
            keyWordSearchResponse.setSourceWebUrl(standardAlbum.getSourceWebUrl());
            if (youkuSourceFilterService.thirdPartyFastAppFilter(standardAlbum.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                keyWordSearchResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(
                        TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), standardAlbumMap.get(standardAlbum.getSid()).getSourceWebUrl()));
            } else {
                keyWordSearchResponse.setDeepLink(youkuSourceFilterService.getUpGradeUrl(
                        standardAlbum.getSid(), standardAlbum.getSource(), standardAlbum.getTitle()));
                keyWordSearchResponse.setDpLinkType(TemplateLinkTypeEnum.UPGRADE_PAGE.getCode());
            }
        } else if (0 == param.getIsOut()) {
            Map<String, StandardAlbum> standardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(
                    Lists.newArrayList(standardAlbum.getSid()));
            if (standardAlbumMap.isEmpty()) {
                return null;
            }
            if (youkuSourceFilterService.setDeepLinkFilter(standardAlbum.getSource(), param.getQuickEngineVersion(), param.getVersion())) {
                keyWordSearchResponse.setDeepLink(deepLinkUtils.getDeeplinkByType(
                        TemplateLinkTypeEnum.WEB_FAST_APP.getCode(), standardAlbumMap.get(standardAlbum.getSid()).getSourceWebUrl()));
            } else {
                keyWordSearchResponse.setDeepLink(youkuSourceFilterService.getUpGradeUrl(
                        standardAlbum.getSid(), standardAlbum.getSource(), standardAlbum.getTitle()));
            }
        }
        return keyWordSearchResponse;
    }

    public KeyWordSearchResponse programAlbumEsConvertResponse(ProgramAlbumEs programAlbumEs, SearchByKeyWordContext context, List<String> unlockAlbumList) {
        KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
        BeanUtils.copyProperties(programAlbumEs, keyWordSearchResponse);

        if (FEATURE_TYPE_YG == programAlbumEs.getFeatureType()) {
            keyWordSearchResponse.setMarkCode(MARK_CODE_YG);
            keyWordSearchResponse.setMarkCodeUrl(imageTagRpcApiProxy.getImageUrl(keyWordSearchResponse.getMarkCode()));
        }

        keyWordSearchResponse.setStars(programAlbumEs.getActor().replace("|", ","));
        keyWordSearchResponse.setTags(programAlbumEs.getMappingTags());
        keyWordSearchResponse.setLanguages(programAlbumEs.getLanguage());
        keyWordSearchResponse.setDirectors(programAlbumEs.getDirector().replace("|", ","));
        if (StringUtil.isEmpty(keyWordSearchResponse.getWebUrl()) && StringUtil.isEmpty(keyWordSearchResponse.getDeepLink())) {
            keyWordSearchResponse.setDeepLink(String.format(detailDeepLink, programAlbumEs.getSid()));
        }
        keyWordSearchResponse.setAlbumFeatureType(keyWordSearchResponse.getFeatureType());

        // 对外搜索设置按钮文案, 目前只对全搜作处理
        if (context.getRequestParam() != null && SEARCH_FROM_QUANSOU.equals(context.getRequestParam().getAppId())) {
            handleButtonWord(programAlbumEs, keyWordSearchResponse, context, unlockAlbumList);
        }
        handleSourceKind(keyWordSearchResponse);
        return keyWordSearchResponse;
    }

    public List<String> getUnlockAlbumList(SearchByKeyWordContext context, List<ProgramAlbumEs> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        BaseRequest baseRequest = new BaseRequest();
        if (context != null && context.getRequestParam() != null) {
            baseRequest.setAttributeValues(context.getRequestParam().getAttributeValues());
            baseRequest.setAppVersion(context.getRequestParam().getAppVersion());
        }
        String vipType = vipRelatedService.getVipType(context.getVipInfo());
        ArrayList<KeyWordSearchResponse> albumList = new ArrayList<>();
        for (ProgramAlbumEs programAlbumEs : list) {
            KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
            keyWordSearchResponse.setSid(programAlbumEs.getSid());
            keyWordSearchResponse.setPayStatus(programAlbumEs.getPayStatus());
            keyWordSearchResponse.setFeatureType(programAlbumEs.getFeatureType());
            keyWordSearchResponse.setSource(programAlbumEs.getSource());
            keyWordSearchResponse.setContentType(programAlbumEs.getContentType());
            albumList.add(keyWordSearchResponse);
        }
        return taskUnlockEpisodeService.getUnlockAlbumList(baseRequest, vipType, context.getUserId(),
                context.getStrategyMatchResult(), albumList);
    }

    /**
     * 对外搜索设置按钮文案
     */
    public void handleButtonWord(ProgramAlbumEs programAlbumEs, KeyWordSearchResponse response, SearchByKeyWordContext context, List<String> unlockAlbumList) {
        OutSideSearchButtonConfig outSideSearchButtonConfig = context.getOutSideSearchButtonConfig();
        VideoVipInfo vipInfo = context.getVipInfo();
        // 策略为空的默认值:播放
        response.setButtonWord("2");

        if (outSideSearchButtonConfig == null) {
            log.warn("warn: outSideSearchButtonConfig is null");
            return;
        }
        //默认值
        response.setButtonWord(outSideSearchButtonConfig.getDefaultText());
        // 限时免费节目
        Date currentTime = new Date(System.currentTimeMillis());
        if (programAlbumEs.getFreeStartTime() != null && programAlbumEs.getFreeEndTime() != null &&
                currentTime.compareTo(programAlbumEs.getFreeStartTime()) >= 0 && currentTime.compareTo(programAlbumEs.getFreeEndTime()) <=0 ) {
            response.setButtonWord(outSideSearchButtonConfig.getTimeLimitedFreeText());
            return;
        }
        // 免费节目
        if (0 == response.getPayStatus()) {
            response.setButtonWord(outSideSearchButtonConfig.getFreeText());
            return;
        }
        // 免费解锁节目--影视会员
        if (1 == response.getPayStatus() && context.getRequestParam().getVersion() >= TASK_UNLOCK_EPISODE_VERSION_FOR_VIDEO_VIP &&
                VIDEO_VIP_SOURCE_LIST.contains(programAlbumEs.getSource()) && unlockAlbumList.contains(response.getSid()) &&
                (vipInfo == null || !"1".equals(vipInfo.getVideoVipStatus()))) {
            response.setButtonWord(outSideSearchButtonConfig.getTaskForEpisodeText());
            return;
        }
        // 免费解锁节目--芒果会员
        if (1 == response.getPayStatus() && context.getRequestParam().getVersion() >= TASK_UNLOCK_EPISODE_VERSION_FOR_MONGO_VIP &&
                SourceEnum.MG_MOBILE.getDataSource().equals(programAlbumEs.getSource()) && unlockAlbumList.contains(response.getSid()) &&
                (vipInfo == null || !"1".equals(vipInfo.getMongoVideoVipStatus()))) {
            response.setButtonWord(outSideSearchButtonConfig.getTaskForEpisodeText());
            return;
        }
        // 会员免费节目
        if (1 == response.getPayStatus()) {
            response.setButtonWord(outSideSearchButtonConfig.getVipText());
            return;
        }
        // 单片付费节目
        if (2 == response.getPayStatus()) {
            response.setButtonWord(outSideSearchButtonConfig.getPayText());
        }
    }

    /**
     * 处理节目副标题
     * 优先级：榜单名次＞推荐语
     */
    public void handleSubTitle(List<KeyWordSearchResponse> searchResponseList, Integer version) {
        if (version == null || version < searchProperties.getSearchCardOptVersion()) {
            return;
        }

        Set<String> sidList = searchResponseList.stream().map(KeyWordSearchResponse::getSid).collect(Collectors.toSet());
        Map<String, AlbumRecommendInfo> sid2AlbumRecommendInfo = FutureUtil.getFutureIgnoreException(
                albumRankRpcApiProxy.getAlbumRecommendInfo(sidList, true));

        searchResponseList.forEach(searchResponse -> {
            // 默认类型
            searchResponse.setRecommendInfoType("default");
            if (StringUtil.isBlank(searchResponse.getRecommendInfo())) {
                searchResponse.setRecommendInfo(searchResponse.getBrief());
            }

            if (sid2AlbumRecommendInfo == null || !sid2AlbumRecommendInfo.containsKey(searchResponse.getSid())) {
                return;
            }
            AlbumRecommendInfo albumRecommendInfo = sid2AlbumRecommendInfo.get(searchResponse.getSid());
            searchResponse.setRecommendInfo(albumRecommendInfo.getRecommendInfo());
            searchResponse.setRecommendInfoDp(albumRecommendInfo.getRecommendInfoDp());
            searchResponse.setRecommendInfoType("auto");
        });
    }

    /**
     * 处理搜索聚合卡的背景色
     */
    public void handleBackgroundColor(KeyWordSearchResponse keyWordSearchResponse, String backGroundColorJson) {
        if (StringUtil.isNotBlank(backGroundColorJson)) {
            Map<String, String> colorMap = JsonUtil.fromStr(backGroundColorJson, new TypeReference<Map<String, String>>() {});
            String baseImgColor = MapUtils.getString(colorMap, "searchCardLightColor", null);
            String darkImgColor = MapUtils.getString(colorMap, "searchCardDarkColor", null);
            keyWordSearchResponse.setBaseImgColor(baseImgColor);
            keyWordSearchResponse.setDarkImgColor(darkImgColor);
        }
    }

    /**
     * 构建右下角文案
     * @param keyWordSearchResponse
     * @param isSearchCard  是否按结果卡展示
     */
    public void handleShowMsg(KeyWordSearchResponse keyWordSearchResponse, boolean isSearchCard, Integer version) {
        if (version == null || version < searchProperties.getSearchCardOptVersion()) {
            // 8.9之前版本，在com.oppo.browser.video.common.utils.LongvideoUtils.convertFrom中处理showMsg
            return;
        }

        if (ContentTypeEnum.MOVIE.getCode().equals(keyWordSearchResponse.getContentType()) ) {
            if (keyWordSearchResponse.getSourceScore() < 6) {
                // 低分电影不显示评分
                keyWordSearchResponse.setShowMsg("");
            } else if (isSearchCard && keyWordSearchResponse.getSourceScore() > 9) {
                // 高分但按结果卡展示的电影不显示评分(因为要展示在推荐理由中)
                keyWordSearchResponse.setShowMsg("");
            } else {
                keyWordSearchResponse.setShowMsg(String.format("%.1f", keyWordSearchResponse.getSourceScore()));
            }
            return;
        }
        if (StringUtil.isBlank(keyWordSearchResponse.getProgramInfo())) {
            keyWordSearchResponse.setShowMsg("");
            return;
        }
        if (keyWordSearchResponse.getProgramInfo().startsWith("更新至")) {
            keyWordSearchResponse.setShowMsg("更新中");
            return;
        }
        keyWordSearchResponse.setShowMsg(keyWordSearchResponse.getProgramInfo());
    }


    /**
     * 构建新结果卡基础信息
     */
    public void buildNewResultCardBaseInfo(KeyWordSearchResponse keyWordSearchResponse) {
        StringBuilder tempStringBuilder = new StringBuilder();
        if (StringUtil.isNotBlank(keyWordSearchResponse.getArea())) {
            tempStringBuilder.append(keyWordSearchResponse.getArea()
                            .replace(",", " ")
                            .replace("|", " "))
                    .append(" / ");
        }
        if (keyWordSearchResponse.getYear() != null) {
            tempStringBuilder.append(keyWordSearchResponse.getYear()).append(" / ");
        }
        if (StringUtil.isNotBlank(keyWordSearchResponse.getDirectors())) {
            tempStringBuilder.append(keyWordSearchResponse.getDirectors()
                    .replace(",", " ")
                    .replace("|", " "))
                    .append(" ");
        }
        if (StringUtil.isNotBlank(keyWordSearchResponse.getStars())) {
            tempStringBuilder.append(keyWordSearchResponse.getStars()
                    .replace(",", " ")
                    .replace("|", " "));
        }
        if (tempStringBuilder.length() > 3 && '/' == tempStringBuilder.charAt(tempStringBuilder.length() - 2)) {
            tempStringBuilder.delete(tempStringBuilder.length() - 3, tempStringBuilder.length());
        }
        keyWordSearchResponse.setBaseInfo(tempStringBuilder.toString());
    }

    /**
     * 构建新结果卡的推荐理由
     */
    public void buildNewResultHighLights(KeyWordSearchResponse keyWordSearchResponse, Map<String, AlbumRecommendInfo> resultMap) {
        List<HighLightVO> highLights = new ArrayList<>();
        if (keyWordSearchResponse.getSourceScore() > 9) {
            HighLightVO scoreHighLight = new HighLightVO();
            scoreHighLight.setType(HighLightTypeEnum.SCORE.getCode());
            scoreHighLight.setText(String.valueOf(keyWordSearchResponse.getSourceScore()));
            highLights.add(scoreHighLight);
        }
        if (MapUtils.isNotEmpty(resultMap) && resultMap.containsKey(keyWordSearchResponse.getSid())) {
            AlbumRecommendInfo albumRecommendInfo = resultMap.get(keyWordSearchResponse.getSid());
            HighLightVO rankHighLightVo = new HighLightVO();
            rankHighLightVo.setType(HighLightTypeEnum.RANK.getCode());
            rankHighLightVo.setText(albumRecommendInfo.getRecommendInfo());
            rankHighLightVo.setDeepLink(albumRecommendInfo.getRecommendInfoDp());
            highLights.add(rankHighLightVo);
        }
        if (StringUtil.isNotBlank(keyWordSearchResponse.getTags())) {
            //品类中文和英文都需要参与过滤
            List<String> tagList = Arrays.stream(ContentTypeEnum.values()).map(ContentTypeEnum::getDesc).collect(Collectors.toList());
            tagList.addAll(Arrays.stream(ContentTypeEnum.values()).map(ContentTypeEnum::getCode).collect(Collectors.toList()));
            //所有的推荐理由中,过滤掉品类和其他
            //构建过滤品类的正则表达式
            String regex = String.join("|", tagList.stream().map(Pattern::quote).toArray(String[]::new));
            String tagsText = keyWordSearchResponse.getTags().replaceAll(regex, "").replaceAll("其他", "");
            if(tagsText.contains(",")){
                highLights.addAll(initTagHighLights(keyWordSearchResponse.getTags().replaceAll(regex, "").replaceAll("其他", ""),keyWordSearchResponse,","));
            }else{
                highLights.addAll(initTagHighLights(keyWordSearchResponse.getTags().replaceAll(regex, "").replaceAll("其他", ""),keyWordSearchResponse,"\\|"));
            }
        }
        keyWordSearchResponse.setHighlights(highLights);
    }

    private List<HighLightVO> initTagHighLights(String tagsText,KeyWordSearchResponse keyWordSearchResponse,String splitChar){
        if(StringUtil.isBlank(tagsText)){
            return new ArrayList<>();
        }
        List<HighLightVO> highLightVOList = new ArrayList<>();
        String[] tagArray = tagsText.split(splitChar);
        for (String tag : tagArray) {
            if (StringUtils.isBlank(tag)) {
                continue;
            }
            HighLightVO highLightVO = new HighLightVO();
            highLightVO.setType(HighLightTypeEnum.TAG.getCode());
            highLightVO.setText(tag);
            highLightVO.setDeepLink(searchProperties.getTagDeepLinkPrefix() + "?tag=" + tag + "&contentType=" + keyWordSearchResponse.getContentType());
            highLightVOList.add(highLightVO);
        }
        return highLightVOList ;
    }

    /**
     * 构建新结果卡的按钮列表
     */
    public void buildNewResultButtons(KeyWordSearchResponse keyWordSearchResponse, Map<String, Integer> favoriteMap, List<String> canSubscribeSidList,List<KeyWordSearchResponse> removeBaseSearchList) {
        List<ButtonVO> buttons = new ArrayList<>();
        //判断是否为非合作方内容
        if (searchProperties.getThirdSearchMap().containsKey(keyWordSearchResponse.getSource())) {
            //如果是非合作方内容
            ButtonVO buttonVo = new ButtonVO();
            buttonVo.setStatus(ButtonStatusEnum.WEB_PAGE_PLAY.getCode());
            buttonVo.setText(ButtonStatusEnum.WEB_PAGE_PLAY.getDesc());
            buttons.add(buttonVo);
            keyWordSearchResponse.setButtons(buttons);
            return;
        }
        //如果是合作方内容(正片)
        if (FeatureTypeEnum.FEATURE_FILM.getCode() == keyWordSearchResponse.getFeatureType()) {
            ButtonVO buttonVo = getButtonVo(keyWordSearchResponse);
            buttons.add(buttonVo);
            //检查配置,是否展示第二个按钮(收藏和预约按钮)
            if (searchProperties.getSearchCardButtonNum() == 2) {
                ButtonVO favoriteButtonVo = new ButtonVO();
                favoriteButtonVo.setStatus(ButtonStatusEnum.COLLECT.getCode());
                favoriteButtonVo.setText(ButtonStatusEnum.COLLECT.getDesc());
                if (favoriteMap != null && !favoriteMap.isEmpty()) {
                    Integer favorite = favoriteMap.get(keyWordSearchResponse.getSid() + "_" + CommonConstant.FAVORITE +"_0");
                    //favorite为1 代表已收藏
                    if (favorite == 1) {
                        favoriteButtonVo.setStatus(ButtonStatusEnum.ALREADY_COLLECT.getCode());
                        favoriteButtonVo.setText(ButtonStatusEnum.ALREADY_COLLECT.getDesc());
                    }
                }
                buttons.add(favoriteButtonVo);
            }
        }
        //合作方内容(预告片)
        if (FeatureTypeEnum.TRAILER.getCode() == keyWordSearchResponse.getFeatureType()) {
            //获取到该预告片下有没有标准剧集,如果没有或者调用rpc失败,则不展示观看预告,否则下发观看预告的按钮
            Integer episodeCount = standardEpisodeRpcApiProxy.countStandardEpisodeBySid(keyWordSearchResponse.getSid());
            if(episodeCount != null && episodeCount != 0){
                ButtonVO buttonVo = new ButtonVO();
                buttonVo.setStatus(ButtonStatusEnum.VIEW_PREVIEW.getCode());
                buttonVo.setText(ButtonStatusEnum.VIEW_PREVIEW.getDesc());
                buttons.add(buttonVo);
            }else{
                //如果没有剧集信息,又不可能订阅的预告,直接过滤掉
                if(removeBaseSearchList != null && (CollectionUtils.isEmpty(canSubscribeSidList) ||
                        CollectionUtils.isNotEmpty(canSubscribeSidList) && !canSubscribeSidList.contains(keyWordSearchResponse.getSid()))){
                    removeBaseSearchList.add(keyWordSearchResponse);
                }
            }
            //如果可预约的sid列表不为空,并且包含当前搜索结果的sid,就下发预约按钮,否则不下发
            if (CollectionUtils.isNotEmpty(canSubscribeSidList) && canSubscribeSidList.contains(keyWordSearchResponse.getSid())
                && searchProperties.getSearchCardButtonNum() == 2) {
                ButtonVO chaseButtonVO = new ButtonVO();
                chaseButtonVO.setStatus(ButtonStatusEnum.RESERVATION.getCode());
                chaseButtonVO.setText(ButtonStatusEnum.RESERVATION.getDesc());
                if (favoriteMap != null && !favoriteMap.isEmpty()) {
                    Integer chase = favoriteMap.get(keyWordSearchResponse.getSid() + "_" + CommonConstant.ACTION_SUBSCRIBE +"_0");
                    //chase == 1 表示已预约过该预告片
                    if (chase == 1) {
                        chaseButtonVO.setStatus(ButtonStatusEnum.ALREADY_RESERVATION.getCode());
                        chaseButtonVO.setStatus(ButtonStatusEnum.ALREADY_RESERVATION.getDesc());
                    }
                }
                buttons.add(chaseButtonVO);
            }
        }
        //其他的feature_type实际上并没有,暂时不做考虑
        keyWordSearchResponse.setButtons(buttons);
    }

    /**
     *  是否需要过滤(满足既不可以预约又没有预告片的预告需要过滤掉)
     */
    public boolean isNeedFilter(KeyWordSearchResponse keyWordSearchResponse,List<String> canSubscribeSidList){
        //如果可订阅的列表不为空,并且列表中包含当前该剧的sid,则证明当前该剧是可预约的直接返回false
        if(CollectionUtils.isNotEmpty(canSubscribeSidList) && canSubscribeSidList.contains(keyWordSearchResponse.getSid())){
            return true;
        }
        Integer episodeCount = standardEpisodeRpcApiProxy.countStandardEpisodeBySid(keyWordSearchResponse.getSid());
        //如果该剧的剧集信息为空或者等于0,则证明该剧
        return episodeCount != null && episodeCount != 0;
    }


    private static @NotNull ButtonVO getButtonVo(KeyWordSearchResponse keyWordSearchResponse) {
        ButtonVO buttonVo = new ButtonVO();
        if (PayStatusEnum.FREE.getCode() == keyWordSearchResponse.getPayStatus()) {
            //免费内容直接展示免费观看
            buttonVo.setStatus(ButtonStatusEnum.FREE_VIEW.getCode());
            buttonVo.setText(ButtonStatusEnum.FREE_VIEW.getDesc());
        }
        if (PayStatusEnum.MEMBER_FREE.getCode() == keyWordSearchResponse.getPayStatus()) {
            //vip内容展示立即观看
            buttonVo.setStatus(ButtonStatusEnum.NOW_VIEW.getCode());
            buttonVo.setText(ButtonStatusEnum.NOW_VIEW.getDesc());
        }
        return buttonVo;
    }

    public List<String> getCanSubscribeSid(List<KeyWordSearchResponse> list) {
        List<String> sidList = list.stream().filter(l -> Objects.equals(l.getFeatureType(), 2)).map(KeyWordSearchResponse::getSid).collect(Collectors.toList());
        List<String> returnList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sidList)) {
            Map<String, StandardAlbum> standardAlbumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(sidList);
            long currentTime = System.currentTimeMillis();
            for (StandardAlbum standardAlbum : standardAlbumMap.values()) {
                if (standardAlbum.getFeatureType() == 2 && currentTime - standardAlbum.getCreateTime().getTime() < searchProperties.getSubscribeTimeLimit()) {
                    returnList.add(standardAlbum.getSid());
                }
            }
        }
        return returnList;
    }

    public void handleFilterList(SearchInterveneCardResponse interveneCardResponse, List<String> contentTypes) {
        // 没有品类时，不下发筛选项
        if (CollectionUtils.isEmpty(contentTypes)) {
            return;
        }
        contentTypes = contentTypes.stream().sorted(Comparator.comparingInt(s -> {
            int index = searchProperties.getSearchFilterList().indexOf(s);
            return index == -1 ? Integer.MAX_VALUE : index;
        })).collect(Collectors.toList());
        // 添加"全部"选项
        contentTypes.add(0, "all");
        interveneCardResponse.setFilterList(contentTypes);
    }

    public void handleSourceKind(KeyWordSearchResponse response) {
        if (UnofficialMediaConstant.SOURCE_DOUBAN.equals(response.getSource())
                || UnofficialMediaConstant.SOURCE_TENCENT.equals(response.getSource())
                || UnofficialMediaConstant.SOURCE_IQIYI.equals(response.getSource())
                || UnofficialMediaConstant.SOURCE_YOUKU.equals(response.getSource())) {
            // 非合作官方源：豆瓣源、电视源
            response.setSourceKind(2);
        } else if (UnofficialMediaConstant.SOURCE_KEKE.equals(response.getSource())
                || UnofficialMediaConstant.SOURCE_CUPFOX.equals(response.getSource())) {
            // 非官方源：可可影视、茶杯狐
            response.setSourceKind(3);
        } else {
            response.setSourceKind(1);
        }
    }

}
