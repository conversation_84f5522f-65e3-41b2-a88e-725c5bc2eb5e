package com.heytap.longvideo.search.service.common;

import com.heytap.longvideo.search.mapper.media.SeriesAlbumMapper;
import com.heytap.longvideo.search.model.entity.db.SeriesAlbum;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Cache;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/*
 * Description 序列剧
 * Date 9:28 2022/6/17
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class SeriesAlbumService {

    @Autowired
    private SeriesAlbumMapper seriesAlbumMapper;

    //虚拟节目缓存
    Cache<String, Map<String, String>> seriesCache = Caffeine.newBuilder()
            .maximumSize(2)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build();

    private static String seriesCacheKey = "seriesCache";


    public void createCache() {
        int totalCount = seriesAlbumMapper.selectAllSeriesProgramCount();
        int pageSize = 500;
        Map<String, String> seriesMap = new HashMap<>(totalCount);
        for (int i = 0; i <= totalCount; i += pageSize) {
            List<SeriesAlbum> seriesProgramList = seriesAlbumMapper.selectSeriesAlbumList(i, pageSize);
            for (SeriesAlbum seriesItem : seriesProgramList) {
                if(StringUtils.isNotBlank(seriesItem.getSeriesName())){
                    seriesMap.put(seriesItem.getAlbumTitle(), seriesItem.getSeriesName());
                }
            }
        }
        seriesCache.put(seriesCacheKey, seriesMap);
    }


    public Map<String, String> getCache() {
        Map<String, String> seriesAllMap = seriesCache.getIfPresent(seriesCacheKey);
        if (MapUtils.isEmpty(seriesAllMap)) {
            synchronized (seriesCacheKey) {
                if (MapUtils.isNotEmpty(seriesCache.getIfPresent(seriesCacheKey))) {
                    return seriesCache.getIfPresent(seriesCacheKey);
                }
                createCache();
            }
        }
        return seriesCache.getIfPresent(seriesCacheKey);
    }
}
