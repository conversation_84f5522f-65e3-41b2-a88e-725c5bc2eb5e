package com.heytap.longvideo.search.service.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.stereotype.Service;

/*
 * Description es基本操作
 * Date 15:18 2021/11/23
 * Author songjiajia 80350688
 */
@Service
public class ElasticSearchService {

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    public void createIndex(Class<?> clazz) {
        IndexOperations indexOperations = restTemplate.indexOps(clazz);
        indexOperations.create();
    }


    public void createIndexAndMapping(Class<?> clazz) {
        IndexOperations indexOperations = restTemplate.indexOps(clazz);
        indexOperations.create();
        Document mapping = indexOperations.createMapping();
        indexOperations.putMapping(mapping);
    }

    public void deleteIndex(Class<?> clazz) {
        IndexOperations indexOperations = restTemplate.indexOps(clazz);
        indexOperations.delete();
    }

}
