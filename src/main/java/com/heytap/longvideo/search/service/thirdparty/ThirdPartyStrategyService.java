package com.heytap.longvideo.search.service.thirdparty;

import com.heytap.longvideo.search.service.common.StrategyService;
import com.heytap.longvideo.search.utils.StrategyUtil;
import com.heytap.video.ad.common.entity.req.BuriedCommonReqProperty;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.heytap.longvideo.search.constants.ThirdPartyConstant.NORMAL_DETAIL_PAGE;
import static com.heytap.longvideo.search.constants.ThirdPartyConstant.THIRD_PARTY_DETAIL_PAGE_STYLE;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
@Slf4j
@Service
public class ThirdPartyStrategyService {

    @Autowired
    private StrategyService matchStrategyService;


    public CompletableFuture<Map<String, MatchStrategyResponseItem>> getStrategyServerConfig(BuriedCommonReqProperty request, String versionName) {
        String[] serviceIds = new String[]{THIRD_PARTY_DETAIL_PAGE_STYLE};
        StrategyUtil.perfectAttributeValues(request.getAttributeValues(), versionName);
        return matchStrategyService.matchSearchStrategy(request, serviceIds);
    }

    public int getThirdPartyDetailPageStyle(String mediaType, Map<String, MatchStrategyResponseItem> strategyResponse) {
        try {
            if (MapUtils.isEmpty(strategyResponse) || !strategyResponse.containsKey(THIRD_PARTY_DETAIL_PAGE_STYLE)) {
                return NORMAL_DETAIL_PAGE;
            }
            log.info("getDirectPageDpSwitch, strategyMatchResult:{}", strategyResponse);
            MatchStrategyResponseItem strategyResponseItem = strategyResponse.get(THIRD_PARTY_DETAIL_PAGE_STYLE);
            if (strategyResponseItem == null || StringUtils.isBlank(strategyResponseItem.getAttachment())) {
                return NORMAL_DETAIL_PAGE;
            }

            Map<String, Integer> map = JsonTools.toMap(strategyResponseItem.getAttachment(), Map.class);
            if (MapUtils.isNotEmpty(map) && map.get(mediaType) != null) {
                return map.get(mediaType);
            }
        } catch (Exception e) {
            log.error("parse strategyResult error:", e);
        }
        return NORMAL_DETAIL_PAGE;
    }
}
