package com.heytap.longvideo.search.service.standard;

import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.entity.MisPerson;
import com.heytap.longvideo.client.media.entity.StandardRole;
import com.heytap.longvideo.client.media.entity.UnofficialAlbum;
import com.heytap.longvideo.client.media.entity.UnofficialAlbumRoleRel;
import com.heytap.longvideo.client.media.enums.LockedFieldsEnum;
import com.heytap.longvideo.client.media.query.RoleExample;
import com.heytap.longvideo.search.api.entity.UnofficialAlbumEsDTO;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.constants.SortStrategyConstant;
import com.heytap.longvideo.search.constants.UnofficialMediaConstant;
import com.heytap.longvideo.search.model.entity.es.TvAlbumEs;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.model.request.MagazinePoolSuggestRequest;
import com.heytap.longvideo.search.model.request.OcsImageUploadAndEsSyncRequest;
import com.heytap.longvideo.search.model.unofficial.request.SearchUnofficialAlbumRequest;
import com.heytap.longvideo.search.mq.producer.RocketMqProducer;
import com.heytap.longvideo.search.rpc.consumer.LvContentItemRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.UnofficialAlbumRoleRelRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.YunheRankRpcApiProxy;
import com.heytap.longvideo.search.service.spider.UnofficialAlbumImageUrlTransformService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.service.TranslateAreaService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.service.TranslateTagsService;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.IllegalCharacterCheckerUtil;
import com.heytap.longvideo.search.utils.LockedFieldsUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 非合作方剧头索引 - service
 * @Author: 80398885WT
 * @Version: 1.0
 * @Date: 2024/10/28 19:18
 */
@Slf4j
@Service
public class UnofficialAlbumService {

    private static final List<String> actorNameNeedToFilter = Lists.newArrayList("", "无", "其他", "其它", null);

    private final RestHighLevelClient restHighLevelClient;

    private final TranslateAreaService translateAreaService;

    private final TranslateTagsService translateTagsService;

    private final YunheRankRpcApiProxy yunheRankRpcProxy;

    private final LvContentItemRpcApiProxy lvContentItemRpcApiProxy;

    private final RocketMqProducer rocketMqProducer;

    private final MisPersonService misPersonService;

    private final UnofficialAlbumRoleRelRpcApiProxy unofficialAlbumRoleRelRpcApiProxy;

    private final UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService;

    @HeraclesDynamicConfig(key = "unofficial.album.max.number.for.similar", fileName = "search_config.properties")
    private int maxNumberForSimilar = 5;

    @HeraclesDynamicConfig(key = "unofficial.album.sync.batchSize", fileName = "search_config.properties")
    private int pageSize = 1000;

    @HeraclesDynamicConfig(key = "unofficial.album.sync.retryTimes", fileName = "search_config.properties")
    private int maxRetryTimes = 5;

    @HeraclesDynamicConfig(key = "unofficial.album.sync.scrollTime", fileName = "search_config.properties")
    private int scrollTime = 5;

    @HeraclesDynamicConfig(key = "unofficial.album.sync.sources", fileName = "search_config.properties")
    private Set<String> sources = new HashSet<String>() {{
        add("tencent");
        add("iqiyi");
    }};

    @HeraclesDynamicConfig(key = "third.search", fileName = "search_config.properties", textType = TextType.JSON)
    private LinkedHashMap<String, Map<String, Integer>> thirdSearchSourceMap;

    @HeraclesDynamicConfig(key = "third.search.douban", fileName = "search_config.properties", textType = TextType.JSON)
    private List<String> doubanPriority;

    /**
     * 剧头信息对外同步的视频类型
     */
    @HeraclesDynamicConfig(key = "album.sync.programTypes", fileName = "search_config.properties")
    private List<String> syncProgramTypes = new ArrayList<>();

    /**
     * 剧头信息建议对外同步的源
     */
    @HeraclesDynamicConfig(key = "magazine.suggest.sync.source", fileName = "search_config.properties")
    private List<String> magazineSuggestSource = new ArrayList<>();

    @HeraclesDynamicConfig(key = "unofficial.album.change.sync.topic", fileName = "mq.properties")
    private String unofficialAlbumChangeSyncTopic = "unofficial-album-change-sync-topic";

    /**
     * 全网节目--标准化时，是否生存ocs图片
     */
    @HeraclesDynamicConfig(key = "unofficial.album.mq.standard.generate.ocs.image", fileName = "configure.properties")
    private Boolean generateOcsImageSwitch;

    /**
     * 全网节目--生成ocs图片的并发量
     */
    @HeraclesDynamicConfig(key = "unofficial.album.generate.ocs.image.concurrent.numbers", fileName = "configure.properties")
    private Integer concurrentNumbers = 20;

    /**
     * 全网节目--查询es(未生成ocs图片),一次返回的数据量
     */
    @HeraclesDynamicConfig(key = "unofficial.album.query.es.batch.size", fileName = "configure.properties")
    private Integer batchSize = 20;

    private Map<Integer, String> FEATURE_TYPE_MAP = new HashMap<Integer, String>() {
        {
            put(0, "预告片");
            put(1, "正片");
            put(2, "预告片");
            put(15, "正片+预告片");
        }
    };

    private ThreadPoolExecutor ocsImageExecutor = null;
    private Semaphore semaphore = null;

    @PostConstruct
    public void init() {
        semaphore = new Semaphore(concurrentNumbers);
        ThreadFactory threadFactory = new CustomizableThreadFactory("all-web-image-ocs-pool-");
        ocsImageExecutor = new ThreadPoolExecutor(
                concurrentNumbers,
                concurrentNumbers,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(concurrentNumbers * 2),
                threadFactory,
                new ThreadPoolExecutor.AbortPolicy());
    }

    public UnofficialAlbumService(RestHighLevelClient restHighLevelClient,
                                  TranslateAreaService translateAreaService,
                                  TranslateTagsService translateTagsService,
                                  YunheRankRpcApiProxy yunheRankRpcProxy,
                                  LvContentItemRpcApiProxy lvContentItemRpcApiProxy,
                                  RocketMqProducer rocketMqProducer,
                                  MisPersonService misPersonService,
                                  UnofficialAlbumRoleRelRpcApiProxy unofficialAlbumRoleRelRpcApiProxy,
                                  UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService) {
        this.restHighLevelClient = restHighLevelClient;
        this.translateAreaService = translateAreaService;
        this.translateTagsService = translateTagsService;
        this.yunheRankRpcProxy = yunheRankRpcProxy;
        this.lvContentItemRpcApiProxy = lvContentItemRpcApiProxy;
        this.rocketMqProducer = rocketMqProducer;
        this.misPersonService = misPersonService;
        this.unofficialAlbumRoleRelRpcApiProxy = unofficialAlbumRoleRelRpcApiProxy;
        this.unofficialAlbumImageUrlTransformService = unofficialAlbumImageUrlTransformService;
    }


    /**
     * @Description: 根据sid查询剧头
     * @param sid 剧头sid
     * @return 剧头
     * @Description: 根据sid查询es
     */
    public <U> U searchBySid(String sid, Class<U> clazz) {
        if (StringUtils.isEmpty(sid)) {
            return null;
        }

        try {
            GetRequest getRequest = new GetRequest(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX);
            getRequest.id(sid);
            GetResponse getResponse = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
            if (getResponse != null && getResponse.getSourceAsString() != null) {
                return JsonUtil.fromStr(getResponse.getSourceAsString(), clazz);
            }
        } catch (Exception e) {
            log.error("searchBySid sid={} error", sid, e);
        }
        return null;
    }

    /**
     * @Description: 根据sourceAlbumId查询剧头
     * @param sourceAlbumId 源id
     * @return UnofficialAlbumEs
     */
    public UnofficialAlbumEs searchBySourceAlbumId(String sourceAlbumId) {
        if (StringUtils.isEmpty(sourceAlbumId)) {
            return null;
        }

        try {
            SearchRequest searchRequest = new SearchRequest(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(new SearchSourceBuilder().query(QueryBuilders.termQuery("sourceAlbumId", sourceAlbumId)));

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            if (Objects.isNull(searchHits) || Objects.equals(searchHits.getHits().length, 0)) {
                log.error("the album:{} doesn't exist in es", sourceAlbumId);
                return null;
            }

            return JsonUtil.fromStr(searchHits.getHits()[0].getSourceAsString(), UnofficialAlbumEs.class);
        } catch (Exception e) {
            log.error("searchBySourceAlbumId({}) error:", sourceAlbumId, e);
            return null;
        }
    }

    public List<UnofficialAlbumEs> searchListBySourceAlbumId(String sourceAlbumId) {
        if (StringUtils.isEmpty(sourceAlbumId)) {
            return null;
        }

        try {
            SearchRequest searchRequest = new SearchRequest(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(new SearchSourceBuilder().query(QueryBuilders.termQuery("sourceAlbumId", sourceAlbumId)));
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            if (Objects.isNull(searchHits) || Objects.equals(searchHits.getHits().length, 0)) {
                log.error("the album:{} doesn't exist in es", sourceAlbumId);
                return null;
            }
            SearchHit[] hits = searchHits.getHits();
            ArrayList<UnofficialAlbumEs> res = new ArrayList<>();
            for (SearchHit hit : hits) {
                UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
                res.add(unofficialAlbumEs);
            }
            return res;
        } catch (Exception e) {
            log.error("searchBySourceAlbumId({}) error:", sourceAlbumId, e);
            return null;
        }
    }


    public List<UnofficialAlbumEs> getAlbumList(List<String> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return new ArrayList<>();
        }

        try {
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(new SearchSourceBuilder()
                            .from(0)
                            .size(sids.size())
                            .query(QueryBuilders.boolQuery()
                                    .filter(QueryBuilders.termsQuery("sid", sids))));

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            List<UnofficialAlbumEs> esResultData = Lists.newArrayList();

            for (SearchHit hit : searchHits.getHits()) {
                UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
                esResultData.add(unofficialAlbumEs);
            }

            return esResultData;
        } catch (Exception e) {
            log.error("search es error", e);
            return new ArrayList<>();
        }
    }

    public PageResponse<StandardAlbumVo> searchUnofficialAlbum(SearchStandardAlbumParams request) throws Exception {
        try {
            PageResponse<StandardAlbumVo> response = new PageResponse<>();
            List<StandardAlbumVo> voList = new ArrayList<StandardAlbumVo>(request.getPageSize());

            if (StringUtils.isBlank(request.getOrder())) {
                request.setOrder("showTime");
            }
            if (request.getOrder().contains("_")) {
                request.setOrder(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, request.getOrder()));
            } else if ("score".equals(request.getOrder())) {
                request.setOrder("sourceScore");
            }

            if (Objects.equals(request.getSortStrategy(), SortStrategyConstant.YEAR)) {
                request.setOrder("year");
            } else if (Objects.equals(request.getSortStrategy(), SortStrategyConstant.SCORE)) {
                request.setOrder("sourceScore");
            } else if (Objects.equals(request.getSortStrategy(), SortStrategyConstant.PLAY_PV)) {
                request.setOrder("oppoHot");
            }

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getAlbumQueryBuilder(request))
                    .from((request.getPageIndex() - 1) * request.getPageSize())
                    .size(request.getPageSize())
                    .trackTotalHits(true);
            if ("outOfStockContentPool".equals(request.getFromScene())) {
                searchSourceBuilder.sort("title.keyword", SortOrder.ASC);
            } else {
                searchSourceBuilder
                        .sort(SortBuilders.fieldSort(request.getOrder()).order(request.getSortType() != null && request.getSortType() == 1 ? SortOrder.ASC : SortOrder.DESC))
                        .sort("sidL", SortOrder.DESC);
            }
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
                StandardAlbumVo standardAlbumVo = new StandardAlbumVo();
                BeanUtils.copyProperties(unofficialAlbumEs, standardAlbumVo);
                standardAlbumVo.setOriginStatus(unofficialAlbumEs.getSourceStatus());
                standardAlbumVo.setReleScore(hit.getScore());
                standardAlbumVo.setFeatureTypeDesc(getFeatureTypeDesc(unofficialAlbumEs.getFeatureType(),
                        (unofficialAlbumEs.getPreviewInfo() != null && unofficialAlbumEs.getPreviewInfo().length() > 10) ? 1 : 0));
                if (standardAlbumVo.getManualWebUrlPriority() == null) {
                    standardAlbumVo.setManualWebUrlPriority(true);
                }
                voList.add(standardAlbumVo);
            }
            if ("outOfStockContentPool".equals(request.getFromScene())) {
                handleAlbumInPool(request.getContentPoolCode(), voList);
                sortBySource(voList);
            }
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / request.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchUnofficialAlbum fail", e);
            throw e;
        }
    }

    /**
     * 构造剧头查询条件
     *
     * @param nRequest
     * @return
     */
    public QueryBuilder getAlbumQueryBuilder(SearchStandardAlbumParams nRequest) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        buildNonEsFieldCondition(nRequest, boolQueryBuilder);
        buildEsFieldCondition(nRequest, boolQueryBuilder);
        if ("outOfStockContentPool".equals(nRequest.getFromScene())) {
            buildoutOfStockContentPoolCondition(boolQueryBuilder);
        }
        return boolQueryBuilder;
    }

    private void buildoutOfStockContentPoolCondition(BoolQueryBuilder queryBuilder) {
        queryBuilder.must(QueryBuilders.termsQuery("programType", syncProgramTypes));
        queryBuilder.must(QueryBuilders.termsQuery("featureType", "1"));
        queryBuilder.must(QueryBuilders.termsQuery("status", "1"));
    }

    private void buildNonEsFieldCondition(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        if (StringUtils.isNotBlank(nRequest.getYearStart())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("year").gte(nRequest.getYearStart()));
        }
        if (StringUtils.isNotBlank(nRequest.getYearEnd())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("year").lte(nRequest.getYearEnd()));
        }
        buildSourceCondition(nRequest, boolQueryBuilder);

        if (nRequest.getAgeStart() != null || nRequest.getAgeEnd() != null) {
            int ageStart = nRequest.getAgeStart() == null ? 0 : nRequest.getAgeStart();
            int ageEnd = nRequest.getAgeEnd() == null ? 100 : nRequest.getAgeEnd();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fitAgeMin").gte(ageStart));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fitAgeMax").lte(ageEnd));
        }
    }

    private void buildSourceCondition(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        if (nRequest.getSourceScoreStart() != null) {
            if (nRequest.getSourceScoreStart().compareTo(10F) == 0) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("sourceScore", "10.0"));
            } else {
                BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
                scoreBuilderOr.should().add(QueryBuilders.termsQuery("sourceScore", "10.0"));
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("sourceScore").gte(nRequest.getSourceScoreStart()));
                boolQueryBuilder.must(scoreBuilderOr);
            }
        }
        if (nRequest.getSourceScoreEnd() != null) {
            if (nRequest.getSourceScoreEnd().compareTo(10F) == 0) {
                BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
                scoreBuilderOr.should().add(QueryBuilders.termsQuery("sourceScore", "10.0"));
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("sourceScore").lte("9.9"));
                boolQueryBuilder.must(scoreBuilderOr);
            } else {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("sourceScore").lte(nRequest.getSourceScoreEnd()));
                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("sourceScore", "10.0"));
            }
        }
    }

    private void buildEsFieldCondition(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                SearchStandardAlbumParams.class, EsField.class);
        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fieldName = field.getAnnotation(EsField.class).name();
            fieldName = StringUtils.isNotEmpty(fieldName) ? fieldName : entry.getKey();
            buildSingleEsFieldCondition(fieldName, fieldValue, boolQueryBuilder);
        }
    }

    private void buildSingleEsFieldCondition(String fieldName, String fieldValue, BoolQueryBuilder boolQueryBuilder) {
        if ("sid".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery(fieldName, "*" + fieldValue + "*"));
        } else if ("tags".equals(fieldName) || "actor".equals(fieldName) || "director".equals(fieldName) || "area".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery(fieldName, fieldValue.replace(",", "|")));
        } else if ("source".equals(fieldName) || "copyrightCode".equals(fieldName) || "programType".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(fieldName, fieldValue.split(",")));
        } else if ("status".equals(fieldName) && "0".equals(fieldValue)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("status", "1"));
        } else if ("useHighLight".equals(fieldName)) {
            buildHighLight(fieldValue, boolQueryBuilder);
        } else if ("managerStatus".equals(fieldName) && "3".equals(fieldValue)) {
            //表示要筛选部分字段锁定的节目（此类节目的managerStatus值 >=4 ）
            boolQueryBuilder.must(QueryBuilders.rangeQuery("managerStatus").gte(4));
        } else if ("featureType".equalsIgnoreCase(fieldName) && "15".equalsIgnoreCase(fieldValue)) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fieldName, 1));
            boolQueryBuilder.must(QueryBuilders.termQuery("hasPreview", 1));
        } else {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fieldName, fieldValue));
        }
    }

    private void buildHighLight(String fieldValue, BoolQueryBuilder boolQueryBuilder) {
        if ("1".equals(fieldValue)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("highLightVid", "*"));
        } else if ("0".equals(fieldValue)) {
            boolQueryBuilder.mustNot(QueryBuilders.wildcardQuery("highLightVid", "*"));
        }
    }

    private String getFeatureTypeDesc(Integer featureType, Integer hasPreview) {
        Set<String> featureTypeSet = new HashSet<>();

        featureTypeSet.add(FEATURE_TYPE_MAP.get(featureType));

        if (hasPreview != null && hasPreview == 1) {
            featureTypeSet.add("预告片");
        }

        return StringUtils.join(featureTypeSet, "+");
    }

    /**
     * @param unofficialAlbumEs 剧头
     * @Description: 插入/更新
     */
    public void saveOrUpdate(UnofficialAlbumEs unofficialAlbumEs) throws IOException {
        UpdateRequest updateRequest = new UpdateRequest()
                .index(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                .doc(JsonUtil.toJson(unofficialAlbumEs), XContentType.JSON)
                .id(unofficialAlbumEs.getSid());
        updateRequest.docAsUpsert(true);
        restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
    }

    /**
     * @Description: 根据sid删除（单个文档）
     * @param sid 剧头sid
     */
    public void deleteDocumentById(String sid) throws IOException {
        DeleteRequest request = new DeleteRequest(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX, sid);
        DeleteResponse response = restHighLevelClient.delete(request, RequestOptions.DEFAULT);

        // 处理响应
        if (response.getResult() == DocWriteResponse.Result.DELETED) {
            log.info("文档删除成功");
        }
    }

    /**
     * @Description: 根据查询条件做批量删除
     * @param field 字段
     * @param value 值
     * @throws IOException IOException
     */
    public void deleteByQuery(String field, String value) throws IOException {
        DeleteByQueryRequest request = new DeleteByQueryRequest(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX);

        // 设置查询条件（示例：删除字段值为指定值的文档）
        request.setQuery(QueryBuilders.termQuery(field, value));

        // 执行删除
        BulkByScrollResponse response = restHighLevelClient.deleteByQuery(request, RequestOptions.DEFAULT);

        // 处理响应
        long deleted = response.getDeleted();
        log.info("删除文档数量: " + deleted);
    }

    /**
     * @Description: 批量插入或更新
     * @param unofficialAlbumEsList 非合作方剧头列表
     */
    public void batchInsertOrUpdate(List<UnofficialAlbumEs> unofficialAlbumEsList) {
        if (CollectionUtils.isEmpty(unofficialAlbumEsList)) {
            return;
        }

        BulkRequest bulkRequest = new BulkRequest(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX);
        for (UnofficialAlbumEs unofficialAlbumEs : unofficialAlbumEsList) {
            UpdateRequest updateRequest = new UpdateRequest()
                    .index(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .doc(JsonUtil.toJson(unofficialAlbumEs), XContentType.JSON)
                    .id(unofficialAlbumEs.getSid());
            updateRequest.docAsUpsert(true);
            bulkRequest.add(updateRequest);
        }

        int bulkTimes = 0;
        while (bulkTimes < maxRetryTimes) {
            try {
                BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
                if (bulkResponse.hasFailures()) {
                    // 批量写入/更新失败 进行重试
                    log.error("Bulk updating/inserting failed");
                    bulkTimes++;
                } else {
                    // 立即刷新索引
                    restHighLevelClient.indices().refresh(new RefreshRequest().indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX), RequestOptions.DEFAULT);
                    break;
                }
            } catch (Exception e) {
                // 捕捉到错误 进行重试
                log.error("During bulk updating/inserting, error occur:", e);
                bulkTimes++;
            }
        }
    }

    /**
     * @Description: 同步电视端es数据至unofficial_album
     */
    public void syncDataFromTV() {
        try {
            // 构建查询请求
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("source", sources)))
                    .size(pageSize)
                    .trackTotalHits(true);
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.TV_ALBUM_INDEX)
                    .source(searchSourceBuilder)
                    .scroll(TimeValue.timeValueMinutes(scrollTime));

            SearchResponse searchResponse;
            String scrollId = "";
            while (true) {
                // 查询电视媒资
                if (StringUtils.isEmpty(scrollId)) {
                    searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                    scrollId = searchResponse.getScrollId();
                } else {
                    searchResponse = restHighLevelClient.scroll(new SearchScrollRequest(scrollId)
                            .scroll(TimeValue.timeValueMinutes(scrollTime)), RequestOptions.DEFAULT);
                }

                // 没有查询到结果时退出循环
                if (searchResponse.getHits().getHits().length == 0) {
                    break;
                }

                // 处理查询结果并写入UnofficialAlbumES
                SearchHits searchHits = searchResponse.getHits();
                List<UnofficialAlbumEs> unofficialAlbumEsList = Arrays.stream(searchHits.getHits())
                        .map(searchHit -> {
                            UnofficialAlbumEs unofficialAlbumEs = buildUnofficialALbumEs(JsonUtil.fromStr(searchHit.getSourceAsString(), TvAlbumEs.class));

                            // 构建节目与影人关系并写入UnofficialAlbumRoleRel
                            buildMisPersonAndUnofficialAlbumRoleRel(unofficialAlbumEs);

                            // 处理锁定的字段
                            handleLockedFields(unofficialAlbumEs);
                            return unofficialAlbumEs;
                        })
                        .collect(Collectors.toList());
                batchInsertOrUpdate(unofficialAlbumEsList);
            }
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("During sync data from TV, error occur:", e);
        }
    }

    public void handleLockedFields(UnofficialAlbumEs unofficialAlbumEs) {
        UnofficialAlbumEs existUnofficialAlbumEs = searchBySid(unofficialAlbumEs.getSid(), UnofficialAlbumEs.class);
        if (Objects.nonNull(existUnofficialAlbumEs)) {
            // 处理锁定的字段
            LockedFieldsUtil.handleLockedFields(existUnofficialAlbumEs, unofficialAlbumEs, existUnofficialAlbumEs.getManagerStatus(), (field, managerStatus) -> {
                Set<String> lockedFields = LockedFieldsUtil.getLockedFields(managerStatus);
                return lockedFields.contains(field.getName());
            });

            // 锁定状态也不更改 managerStatus
            unofficialAlbumEs.setManagerStatus(existUnofficialAlbumEs.getManagerStatus());
        }
    }

    public UnofficialAlbumEs buildUnofficialALbumEs(TvAlbumEs tvAlbumEs) {
        UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
        BeanUtils.copyProperties(tvAlbumEs, unofficialAlbumEs);

        // 字段映射
        if (StringUtils.isNotEmpty(tvAlbumEs.getSid())) {
            unofficialAlbumEs.setSidL(Long.parseLong(tvAlbumEs.getSid()));
            unofficialAlbumEs.setSourceStatus(tvAlbumEs.getOriginStatus());
        }
        tvAreaAndTagsTrans(unofficialAlbumEs);

        // 处理演员字段中的非法字符
        String newActor = processField(tvAlbumEs.getActor());
        if (StringUtils.isNotEmpty(newActor) && !newActor.equals(tvAlbumEs.getActor())) {
            unofficialAlbumEs.setActor(newActor);
            log.info("handle TvMedia actor filed, before:{}, after:{}", tvAlbumEs.getActor(), newActor);
        }
        // 处理导演字段中的非法字符
        String newDirector = processField(tvAlbumEs.getDirector());
        if (StringUtils.isNotEmpty(newDirector) && !newDirector.equals(tvAlbumEs.getDirector())) {
            unofficialAlbumEs.setDirector(newDirector);
            log.info("handle TvMedia director file    d, before:{}, after:{}", tvAlbumEs.getDirector(), newDirector);
        }
        // s8.10 全网内容同步锁屏，大小横竖图不不能为空
        if (StringUtils.isEmpty(unofficialAlbumEs.getHorizontalIcon()) || StringUtils.isEmpty(unofficialAlbumEs.getHorizontalImage())) {
            String image = StringUtils.isEmpty(unofficialAlbumEs.getHorizontalIcon()) ? unofficialAlbumEs.getHorizontalImage() : unofficialAlbumEs.getHorizontalIcon();
            unofficialAlbumEs.setHorizontalIcon(image);
            unofficialAlbumEs.setHorizontalImage(image);
        }
        if (StringUtils.isEmpty(unofficialAlbumEs.getVerticalIcon()) || StringUtils.isEmpty(unofficialAlbumEs.getVerticalImage())) {
            String image = StringUtils.isEmpty(unofficialAlbumEs.getVerticalIcon()) ? unofficialAlbumEs.getVerticalImage() : unofficialAlbumEs.getVerticalIcon();
            unofficialAlbumEs.setVerticalIcon(image);
            unofficialAlbumEs.setVerticalImage(image);
        }
        if (generateOcsImageSwitch) {
            // 全网图片保存到ocs
            Boolean res = unofficialAlbumImageUrlTransformService.generateImageAndSyncToEs(unofficialAlbumEs, false, false);
            if (!Boolean.TRUE.equals(res)) {
                log.warn("unofficial album handle Ocs Image error, sid:{}", unofficialAlbumEs.getSid());
            }
        }
        return unofficialAlbumEs;
    }

    public void buildMisPersonAndUnofficialAlbumRoleRel(UnofficialAlbumEs unofficialAlbumEs) {
        if (Objects.isNull(unofficialAlbumEs)) {
            return;
        }

        List<StandardRole> actorAndDirectorList = Lists.newArrayList();
        splitStrToList(processField(unofficialAlbumEs.getActor()), actorAndDirectorList, 2);
        splitStrToList(processField(unofficialAlbumEs.getDirector()), actorAndDirectorList, 1);

        if (CollectionUtils.isEmpty(actorAndDirectorList)) {
            return;
        }

        // 演员 导演字段是否锁定
        boolean actorLock = (LockedFieldsEnum.ACTOR.getManagerStatus() & unofficialAlbumEs.getManagerStatus()) == LockedFieldsEnum.ACTOR.getManagerStatus();
        boolean directorLock = (LockedFieldsEnum.DIRECTOR.getManagerStatus() & unofficialAlbumEs.getManagerStatus()) == LockedFieldsEnum.DIRECTOR.getManagerStatus();

        // 导演/演员有一个未锁定即删除响应的影人-剧头关系
        if (!actorLock || !directorLock) {
            unofficialAlbumRoleRelRpcApiProxy.deleteLockedActorAndDirector(unofficialAlbumEs.getSid(), actorLock, directorLock);
        }

        StringBuilder actorStr = new StringBuilder();
        StringBuilder directorStr = new StringBuilder();
        for (StandardRole role : actorAndDirectorList) {
            // 构建影人并入库
            MisPerson misPerson = misPersonService.buildAndSaveMisPerson(role.getCastName(), unofficialAlbumEs);
            if (Objects.isNull(misPerson)) {
                continue;
            }

            // 导演字段锁定，跳过
            if (role.getRoleType() == 1 && directorLock) {
                continue;
            }

            // 演员字段锁定，跳过
            if (role.getRoleType() == 2 && actorLock) {
                continue;
            }

            // 构建非合作方节目与影人关系并入库
            RoleExample queryCondition = RoleExample.builder()
                    .sid(unofficialAlbumEs.getSid())
                    .personSid(misPerson.getSid())
                    .roleType(role.getRoleType())
                    .build();
            UnofficialAlbumRoleRel existUnofficialRoleRel = unofficialAlbumRoleRelRpcApiProxy.queryOne(queryCondition);
            if (Objects.nonNull(existUnofficialRoleRel) && existUnofficialRoleRel.getLocked()) {
                continue;
            }

            UnofficialAlbumRoleRel unofficialAlbumRoleRel = UnofficialAlbumRoleRel.builder()
                    .sid(unofficialAlbumEs.getSid())
                    .source(unofficialAlbumEs.getSource())
                    .personSid(misPerson.getSid())
                    .sourceId(misPerson.getTencentId())
                    .programTitle(unofficialAlbumEs.getTitle())
                    .castName(role.getCastName())
                    .headImg(misPerson.getHeadImg())
                    .squareImg(misPerson.getSquareImg())
                    .orderNum(1)
                    .roleType(role.getRoleType())
                    .relateType(0)
                    .status(1)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .locked(false)
                    .build();
            unofficialAlbumRoleRelRpcApiProxy.saveOrUpdate(unofficialAlbumRoleRel);

            if (role.getRoleType() == 1) {
                if (StringUtils.isNotEmpty(directorStr.toString())) {
                    directorStr.append("|");
                }
                directorStr.append(role.getCastName());
            } else if (role.getRoleType() == 2) {
                if (StringUtils.isNotEmpty(actorStr.toString())) {
                    actorStr.append("|");
                }
                actorStr.append(role.getCastName());
            }
        }

        unofficialAlbumEs.setActor(actorStr.toString());
        unofficialAlbumEs.setDirector(directorStr.toString());
    }

    private void splitStrToList(String inputStr, List<StandardRole> resultList, int roleType) {
        if (StringUtils.isEmpty(inputStr)) {
            return;
        }
        
        // 支持 |、/、、 作为分隔符
        String[] inputArr = inputStr.split("[|/、]");
        Set<String> distinctSet = Sets.newHashSet(inputArr);
        for (String item : distinctSet) {
            if (Objects.isNull(item)) {
                continue;
            }

            String name = item.trim();
            if (StringUtils.isEmpty(name) || actorNameNeedToFilter.contains(name)) {
                continue;
            }

            StandardRole standardRole = new StandardRole();
            standardRole.setCastName(name);
            standardRole.setRoleType(roleType);
            resultList.add(standardRole);
        }
    }

    private String processField(String filed) {
        if (StringUtils.isEmpty(filed)) {
            return null;
        }
        String[] split = filed.split("\\|");
        if (split.length == 0) {
            return null;
        }

        ArrayList<String> list = new ArrayList<>();
        for (String text : split) {
            if (IllegalCharacterCheckerUtil.containsInvalidCharacters(text)) {
                continue;
            }

            if (text.contains("/")) {
                list.addAll(Lists.newArrayList(text.trim().split("/")));
                continue;
            }

            list.add(text);
        }
        return CollectionUtils.isNotEmpty(list) ? String.join("|", list) : null;
    }

    public List<UnofficialAlbum> getSimilarAlbum(SearchUnofficialAlbumRequest request) {
        try {
            ArrayList<UnofficialAlbum> result = new ArrayList<>();
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(getSimilarAlbumQueryBuilder(request.getTitle(), request.getSource()))
                    .sort("_score", SortOrder.DESC)
                    .size(maxNumberForSimilar);
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(sourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            if (Objects.isNull(searchHits) || Objects.equals(searchHits.getHits().length, 0)) {
                log.info("the title:{} doesn't exist in es", request.getTitle());
                return result;
            }
            for (SearchHit hit : searchHits.getHits()) {
                UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
                UnofficialAlbum unofficialAlbum = new UnofficialAlbum();
                BeanUtils.copyProperties(unofficialAlbumEs, unofficialAlbum);
                result.add(unofficialAlbum);
            }
            return result;
        } catch (Exception e) {
            log.error("getSimilarAlbum fail", e);
        }
        return Lists.newArrayList();
    }


    private QueryBuilder getSimilarAlbumQueryBuilder(String title, String source) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("title", title));
        boolQueryBuilder.must(QueryBuilders.termsQuery("source", source));
        boolQueryBuilder.must(QueryBuilders.termsQuery("status", UnofficialMediaConstant.UNOFFICIAL_ALBUM_NORMAL));
        return boolQueryBuilder;
    }

    public void tvAreaAndTagsTrans(UnofficialAlbumEs unofficialAlbumEs) {
        // 更新地域转换
        String area = unofficialAlbumEs.getArea();
        if (StringUtils.isNotEmpty(area)) {
            List<String> areaSplit = Arrays.asList(area.split("\\|"));
            String translateArea;
            if (areaSplit.size() > 1) {
                List<String> list = areaSplit.stream()
                        .map(item -> translateAreaService.translateArea(unofficialAlbumEs.getProgramType(), item))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                // 列表个数为1也可以正确处理
                translateArea = String.join("|", list);
            } else {
                translateArea = translateAreaService.translateArea(unofficialAlbumEs.getProgramType(), unofficialAlbumEs.getArea());
            }
            if (translateArea != null && !translateArea.equals(unofficialAlbumEs.getArea())) {
                log.info("Tv media unofficialAlbumEs translateArea, sourceAlbumId:{}, before:{}, after:{}", unofficialAlbumEs.getSourceAlbumId(), unofficialAlbumEs.getArea(), translateArea);
            }
            unofficialAlbumEs.setArea(translateArea);
        }
        // 标签转换
        String translateTags = translateTagsService.translateTags(unofficialAlbumEs.getTags(), unofficialAlbumEs.getProgramType());
        if (translateTags != null && !translateTags.equals(unofficialAlbumEs.getMappingTags())) {
            log.info("Tv media unofficialAlbumEs translateTags, sourceAlbumId:{}, before:{}, after:{}", unofficialAlbumEs.getSourceAlbumId(), unofficialAlbumEs.getTags(), translateTags);
        }
        unofficialAlbumEs.setMappingTags(translateTags);
    }

    public PageResponse<StandardAlbumVo> magazinePoolSuggest(MagazinePoolSuggestRequest request) {
        try {
            PageResponse<StandardAlbumVo> response = new PageResponse<>();
            String beginDay = formatDateDaysAgo(9);
            String endDay = formatDateDaysAgo(2);
            CompletableFuture<List<String>> distinctTitlesFuture = yunheRankRpcProxy.queryDistinctTitlesByDay(beginDay, endDay);
            List<String> distinctTitles = FutureUtil.getFutureIgnoreException(distinctTitlesFuture);
            if (CollectionUtils.isEmpty(distinctTitles)) {
                return new PageResponse<>();
            }
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            buildYunheBankQuery(distinctTitles, boolQueryBuilder);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .from((request.getPageIndex() - 1) * request.getPageSize())
                    .size(request.getPageSize())
                    .sort("title.keyword", SortOrder.ASC);
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(sourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            ArrayList<StandardAlbumVo> voList = new ArrayList<>();
            for (SearchHit hit : searchHits.getHits()) {
                UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
                StandardAlbumVo standardAlbumVo = new StandardAlbumVo();
                BeanUtils.copyProperties(unofficialAlbumEs, standardAlbumVo);
                if (unofficialAlbumEs.getSourceStatus() != null) {
                    standardAlbumVo.setOriginStatus(unofficialAlbumEs.getSourceStatus());
                }
                standardAlbumVo.setReleScore(hit.getScore());
                standardAlbumVo.setFeatureTypeDesc(getFeatureTypeDesc(unofficialAlbumEs.getFeatureType(),
                        (unofficialAlbumEs.getPreviewInfo() != null && unofficialAlbumEs.getPreviewInfo().length() > 10) ? 1 : 0));
                if (standardAlbumVo.getManualWebUrlPriority() == null) {
                    standardAlbumVo.setManualWebUrlPriority(true);
                }
                voList.add(standardAlbumVo);
            }
            handleAlbumInPool(request.getContentPoolCode(), voList);
            // 按照源优先级排序
            sortBySource(voList);
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / request.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("magazinePoolSuggest error", e);
        }
        return new PageResponse<>();
    }

    public void sortBySource(List<StandardAlbumVo> voList) {
        ArrayList<String> allWebSourcePriority = new ArrayList<>(thirdSearchSourceMap.keySet());
        voList.sort((a, b) -> {
            int title = a.getTitle().compareTo(b.getTitle());
            if (title != 0) {
                return title;
            }
            int programType = a.getProgramType().compareTo(b.getProgramType());
            if (programType != 0) {
                return programType;
            }
            int source = 0;
            if (allWebSourcePriority.contains(a.getSource()) && allWebSourcePriority.contains(b.getSource())) {
                source = allWebSourcePriority.indexOf(a.getSource()) - allWebSourcePriority.indexOf(b.getSource());
            }
            if (source != 0) {
                return source;
            }
            int copyrightCode = 0;
            if (doubanPriority.contains(a.getCopyrightCode()) && doubanPriority.contains(b.getCopyrightCode())) {
                copyrightCode = doubanPriority.indexOf(a.getCopyrightCode()) - doubanPriority.indexOf(b.getCopyrightCode());
            }
            return copyrightCode;
        });
    }

    /**
     * 处理已经存在于内容池中的数据
     */
    private void handleAlbumInPool(String contentPoolCode, List<StandardAlbumVo> voList) {
        List<String> sids = voList.stream().map(StandardAlbumVo::getSid).collect(Collectors.toList());
        CompletableFuture<RpcResult<List<String>>> searchExistSidFuture = lvContentItemRpcApiProxy.searchExistSid(sids, contentPoolCode);
        RpcResult<List<String>> existSidList = FutureUtil.getFutureIgnoreException(searchExistSidFuture);
        if (existSidList != null && existSidList.getData() != null) {
            voList.stream().filter(x -> existSidList.getData().contains(x.getSid())).forEach(x -> x.setIsInContentPool(true));
        }
    }

    /**
     * 根据标题精确匹配，并限制品类、状态
     */
    private void buildYunheBankQuery(List<String> distinctTitles, BoolQueryBuilder boolQueryBuilder) {
        boolQueryBuilder.must(QueryBuilders.termsQuery("title.keyword", distinctTitles));
        boolQueryBuilder.must(QueryBuilders.termsQuery("programType", syncProgramTypes));
        boolQueryBuilder.must(QueryBuilders.termsQuery("source", magazineSuggestSource));
        boolQueryBuilder.must(QueryBuilders.termQuery("featureType", "1"));
        boolQueryBuilder.must(QueryBuilders.termQuery("status", "1"));

    }

    private String formatDateDaysAgo(Integer daysAgo) {
        return LocalDate.now()
                .minusDays(daysAgo)
                .format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public void mqSendMsg(UnofficialAlbumEs unofficialAlbumEs, Integer delayLevel) {
        UnofficialAlbumEsDTO unofficialAlbumEsDTO = new UnofficialAlbumEsDTO();
        BeanUtils.copyProperties(unofficialAlbumEs, unofficialAlbumEsDTO);
        rocketMqProducer.mqSendMsg(unofficialAlbumChangeSyncTopic, "edit", unofficialAlbumEs.getSid(), JsonUtil.toJson(unofficialAlbumEsDTO), delayLevel);
    }


    public void ocsImageUploadAndEsSync(OcsImageUploadAndEsSyncRequest request) {

        String sid = "1";

        for (int i = 0; i < request.getCount(); i++) {
            try {
                SearchHits searchHits = getUnprocessedUnofficialAlbum(request.getSourceList(), sid);
                if (searchHits == null || searchHits.getHits().length == 0) {
                    log.warn("No more unprocessed unofficial albums found, processed {} albums", i);
                    break;
                }

                for (SearchHit hit : searchHits.getHits()) {
                    // 等待可用许可(阻塞)
                    semaphore.acquire();
                    UnofficialAlbumEs unofficialAlbumEs = JsonUtil.fromStr(hit.getSourceAsString(), UnofficialAlbumEs.class);
                    sid = unofficialAlbumEs.getSid();

                    ocsImageExecutor.submit(() -> {
                        try {
                            long startTime = System.currentTimeMillis();
                            unofficialAlbumImageUrlTransformService.generateImageAndSyncToEs(unofficialAlbumEs, false, true);
                            log.info("Processed album {} in {}ms", unofficialAlbumEs.getSid(), System.currentTimeMillis() - startTime);
                        } catch (Exception e) {
                            log.error("Failed to process album {}: {}", unofficialAlbumEs.getSid(), e.getMessage(), e);
                        } finally {
                            semaphore.release();
                        }
                    });
                }
            } catch (Exception e) {
                Thread.currentThread().interrupt();
                log.error("Processing ocs image error", e);
            }
        }
    }

    public SearchHits getUnprocessedUnofficialAlbum(List<String> sourceList, String sid) throws IOException {
        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
        BoolQueryBuilder condition1 = QueryBuilders.boolQuery()
                .must(QueryBuilders.existsQuery("verticalIcon"))
                .mustNot(QueryBuilders.existsQuery("verticalIconOcs"));
        BoolQueryBuilder condition2 = QueryBuilders.boolQuery()
                .must(QueryBuilders.existsQuery("verticalImage"))
                .mustNot(QueryBuilders.existsQuery("verticalImageOcs"));
        BoolQueryBuilder condition3 = QueryBuilders.boolQuery()
                .must(QueryBuilders.existsQuery("horizontalIcon"))
                .mustNot(QueryBuilders.existsQuery("horizontalIconOcs"));
        BoolQueryBuilder condition4 = QueryBuilders.boolQuery()
                .must(QueryBuilders.existsQuery("horizontalImage"))
                .mustNot(QueryBuilders.existsQuery("horizontalImageOcs"));
        shouldQuery.should(condition1).should(condition2).should(condition3).should(condition4)
                .minimumShouldMatch(1);

        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("status", 1))
                .must(QueryBuilders.termsQuery("source", sourceList))
                .must(QueryBuilders.rangeQuery("sid.keyword").gt(sid));

        BoolQueryBuilder finalQuery = QueryBuilders.boolQuery()
                .must(shouldQuery)
                .must(mustQuery);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(finalQuery)
                .sort("sid.keyword", SortOrder.ASC)
                .size(batchSize);

        SearchRequest searchRequest = new SearchRequest()
                .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                .source(sourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        return searchResponse.getHits();
    }
}