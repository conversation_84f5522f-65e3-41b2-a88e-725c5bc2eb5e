package com.heytap.longvideo.search.service.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.heytap.longvideo.search.constants.HandleTypeEnum;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.properties.MqProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchScrollHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 封装端内端外同步搜索媒资数据的逻辑
 */
@Service
@Slf4j
public class AlbumSyncToSearchService {

    private static final int PAGE_SIZE = 100;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private MqProperties mqProperties;

    @Autowired
    private Producer<String, String> kafkaProducer;


    /**
     * 存量数据同步(合作方数据)
     */
    public void stockSyncWithStandardsAlbum() {
        String scrollId = null;
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("programType",mqProperties.getThirdParyExternalSearchStandardFilterList()))
                    .must(QueryBuilders.termQuery("status",1));

            SearchSourceBuilder builder = new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .trackTotalHits(true)
                    .size(PAGE_SIZE);

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_ALBUM_INDEX)
                    .source(builder);
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1));
            searchRequest.scroll(scroll);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            SearchHit[] hits = searchResponse.getHits().getHits();
            while (ArrayUtils.isNotEmpty(hits)) {
                for (SearchHit hit : hits) {
                    StandardAlbumEs standardAlbumEs = JSON.parseObject(hit.getSourceAsString(), StandardAlbumEs.class);
//                    //合作方只同步电影、电视剧、综艺
//                    if (isFilterProgramType(standardAlbumEs.getProgramType())) {
//                        continue;
//                    }
                    sendKafka(formatMessage(standardAlbumEs.getSid(), standardAlbumEs.getTitle(),
                            standardAlbumEs.getStatus(), standardAlbumEs.getSource()));
                }
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(scroll);
                SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
                scrollId = searchScrollResponse.getScrollId();
                hits = searchScrollResponse.getHits().getHits();
            }
        } catch (IOException e) {
            log.error("[AlbumSyncToSearchService.stockSyncWithStandardsAlbum] error", e);
        } finally {
            if (scrollId != null) {
                ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                clearScrollRequest.addScrollId(scrollId);
                try {
                    restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    log.error("[AlbumSyncToSearchService.stockSyncWithStandardsAlbum]restHighLevelClient.clearScroll error", e);
                }
            }
        }
    }

    /**
     * 存量数据同步(全网搜数据)
     */
    public void stockSyncWithUnofficialAlbum() {
        String scrollId = null ;
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("source",mqProperties.getThirdPartyExternalSearchFilterList()))
                    .must(QueryBuilders.termsQuery("programType",mqProperties.getThirdParyExternalSearchStandardFilterList()))
                    .must(QueryBuilders.termQuery("status",1));

            SearchSourceBuilder builder = new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .trackTotalHits(true)
                    .size(PAGE_SIZE);
            //查询条件前置
            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
                    .source(builder);
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1));
            searchRequest.scroll(scroll);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            SearchHit[] hits = searchResponse.getHits().getHits();
            while (ArrayUtils.isNotEmpty(hits)) {
                for (SearchHit hit : hits) {
                    UnofficialAlbumEs unofficialAlbumEs = JSON.parseObject(hit.getSourceAsString(), UnofficialAlbumEs.class);
//                    if (isFilterNotCooperative(unofficialAlbumEs.getSource())) {
//                        continue;
//                    }
//                    if (isFilterProgramType(unofficialAlbumEs.getProgramType())) {
//                        continue;
//                    }
                    sendKafka(formatMessage(unofficialAlbumEs.getSid(), unofficialAlbumEs.getTitle(),
                            unofficialAlbumEs.getStatus(), unofficialAlbumEs.getSource()));
                }
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(scroll);
                SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
                scrollId = searchScrollResponse.getScrollId();
                hits = searchScrollResponse.getHits().getHits();
            }
        } catch (IOException e) {
            log.error("[AlbumSyncToSearchService.stockSyncWithUnofficialAlbum] error", e);
        } finally {
            if (scrollId != null) {
                ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                clearScrollRequest.addScrollId(scrollId);
                try {
                    restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                } catch (IOException e) {
                    log.error("[AlbumSyncToSearchService.stockSyncWithUnofficialAlbum]restHighLevelClient.clearScroll error", e);
                }
            }
        }
    }

    /**
     * 增量同步(合作方数据)
     */
    public void incrementSyncWithStandardsAlbum(StandardAlbumEs standardAlbumEs, String handleType) {
        try {
            //如果对象为null或者是非版权方就直接return
            if (standardAlbumEs == null) {
                return;
            }

            //合作方只同步电影、电视剧、综艺
            if (isFilterProgramType(standardAlbumEs.getProgramType())) {
                return;
            }

            if (HandleTypeEnum.INSERT.getValue().equals(handleType) || HandleTypeEnum.UPDATE.getValue().equals(handleType)) {
                sendKafka(formatMessage(standardAlbumEs.getSid(), standardAlbumEs.getTitle(), standardAlbumEs.getStatus(), standardAlbumEs.getSource()));
                return;
            }
            // 如果是删除的话,直接将该条记录的status置为下线的状态
            if (HandleTypeEnum.DELETE.getValue().equals(handleType)) {
                sendKafka(formatMessage(standardAlbumEs.getSid(), standardAlbumEs.getTitle(), 0, standardAlbumEs.getSource()));
                return;
            }
            log.warn("[AlbumSyncToSearchService.incrementSyncWithStandardsAlbum] handle type not match,handleType: {}", handleType);
        } catch (Exception e) {
            log.error("[AlbumSyncToSearchService.incrementSyncWithStandardsAlbum] error", e);
            //如果该方法出现,只catch并打印日志,不影响上层调用方的逻辑
        }
    }

    /**
     * 增量同步(全网搜数据)
     */
    public void incrementSyncWithUnofficialAlbum(UnofficialAlbumEs unofficialAlbumEs) {
        try {
            //如果对象为null或者是非版权方就直接return
            if (unofficialAlbumEs == null
                    || isFilterNotCooperative(unofficialAlbumEs.getSource())
                    || isFilterProgramType(unofficialAlbumEs.getProgramType())) {
                return;
            }
            sendKafka(formatMessage(unofficialAlbumEs.getSid(), unofficialAlbumEs.getTitle(), unofficialAlbumEs.getStatus(), unofficialAlbumEs.getSource()));
        } catch (Exception e) {
            log.error("[AlbumSyncToSearchService.incrementSyncWithUnofficialAlbum] error", e);
            //如果该方法出现,只catch并打印日志,不影响上层调用方的逻辑
        }
    }

    private String formatMessage(String sid, String title, Integer status, String source) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sid", sid);
        jsonObject.put("title", title);
        jsonObject.put("status", status);
        jsonObject.put("source", source);
        return jsonObject.toJSONString();
    }

    private boolean isFilterProgramType(String programType) {
        if (StringUtils.isBlank(programType)) {
            return true;
        }
        if (CollectionUtils.isEmpty(mqProperties.getThirdParyExternalSearchStandardFilterList())) {
            return false;
        }
        return !mqProperties.getThirdParyExternalSearchStandardFilterList().contains(programType);
    }

    //过滤非合作方部分源数据
    public boolean isFilterNotCooperative(String source) {
        if (CollectionUtils.isEmpty(mqProperties.getThirdPartyExternalSearchFilterList())) {
            return false;
        }
        return !mqProperties.getThirdPartyExternalSearchFilterList().contains(source);
    }


    public void sendKafka(String sendMessage) {
        if (StringUtils.isBlank(sendMessage)) {
            return;
        }
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(mqProperties.getThirdPartyExternalSearchTopic(), mqProperties.getThirdPartyExternalSearchKey(), sendMessage);
            kafkaProducer.send(producerRecord).get(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[AlbumSyncToSearchService.sendKafka] send kafka failed", e);
        }
    }

}
