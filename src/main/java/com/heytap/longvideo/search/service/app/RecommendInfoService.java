package com.heytap.longvideo.search.service.app;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.model.param.app.Urlpack;
import com.heytap.longvideo.search.rpc.consumer.AlbumRankRpcApiProxy;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> ZhangXu
 * @Description
 * @date 2023-09-12 10:00
 */
@Slf4j
@Service
public class RecommendInfoService {
    @Autowired
    private AlbumRankRpcApiProxy albumRankRpcApiProxy;

    /**
     * 榜单排名推荐语 组装
     *
     * @param
     */
    public void handleAlbumRankRecommendInfo(List<ProgramAlbumEs> programAlbumEsList, List<KeyWordSearchResponse> result, ListFilterParam param) {
        if (CollectionUtils.isEmpty(programAlbumEsList)) {
            return;
        }

        Boolean isHomeScreen = Boolean.FALSE;
        try {
            Map<String, Object> urlpackMap = JSON.parseObject(param.getUrlpack(), Map.class);
            Urlpack urlpack = JSON.parseObject(urlpackMap.get("cmd_vod").toString(), Urlpack.class);
            isHomeScreen = "all".equals(urlpack.getContentType());
        } catch (Exception e) {
            log.warn("handleAlbumRankRecommendInfo getUrlPack from req wrong,param:{}", param);
            return;
        }

        Set<String> sids = programAlbumEsList.stream().map(r -> r.getSid()).collect(Collectors.toSet());
        Map<String, AlbumRecommendInfo> sid2AlbumRecommendInfo = FutureUtil.getFutureIgnoreException(albumRankRpcApiProxy.getAlbumRecommendInfo(sids, isHomeScreen));
        if (MapUtils.isEmpty(sid2AlbumRecommendInfo)) {
            log.info("getAlbumRecommendInfo result is empty,sids:{}", sids);
            return;
        }

        if (log.isDebugEnabled()) {
            log.debug("getAlbumRecommendInfo sid2AlbumRecommendInfo:{}", JsonTools.toJsonString(sid2AlbumRecommendInfo));
        }

        for (KeyWordSearchResponse keyWordSearchResponse : result) {
            if (!sid2AlbumRecommendInfo.containsKey(keyWordSearchResponse.getSid())) {
                continue;
            }

            AlbumRecommendInfo albumRecommendInfo = sid2AlbumRecommendInfo.get(keyWordSearchResponse.getSid());
            keyWordSearchResponse.setRecommendInfo(albumRecommendInfo.getRecommendInfo());
            keyWordSearchResponse.setRecommendInfoDp(albumRecommendInfo.getRecommendInfoDp());
            keyWordSearchResponse.setRecommendInfoType("auto");
        }
    }
}