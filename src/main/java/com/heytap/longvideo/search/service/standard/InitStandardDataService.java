package com.heytap.longvideo.search.service.standard;

import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.mapper.media.StandardEpisodeMapper;
import com.heytap.longvideo.search.mapper.media.StandardTrailerMapper;
import com.heytap.longvideo.search.mapper.media.StandardVideoMapper;
import com.heytap.longvideo.search.mapper.media.UgcStandardVideoMapper;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardEpisodeEs;
import com.heytap.longvideo.search.model.entity.es.StandardTrailerEs;
import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.entity.es.UgcStandardVideoEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/*
 * Description 媒资数据初始化
 * Date 9:50 2022/3/17
 * Author songjiajia 80350688
 */
@Service
public class InitStandardDataService {

    private static final Logger log = LoggerFactory.getLogger("updatelog");

    private final StandardAlbumMapper standardAlbumMapper;

    private final StandardVideoMapper standardVideoMapper;

    private final UgcStandardVideoMapper ugcStandardVideoMapper;

    private final StandardEpisodeMapper standardEpisodeMapper;

    private final StandardTrailerMapper standardTrailerMapper;

    private final StandardVideoService standardVideoService;

    private final StandardAlbumService standardAlbumService;

    private final StandardEpisodeService standardEpisodeService;

    private final StandardTrailerService standardTrailerService;

    private final UnofficialAlbumService unofficialAlbumService;

    public InitStandardDataService(StandardAlbumMapper standardAlbumMapper,
                                   StandardVideoMapper standardVideoMapper,
                                   UgcStandardVideoMapper ugcStandardVideoMapper,
                                   StandardEpisodeMapper standardEpisodeMapper,
                                   StandardTrailerMapper standardTrailerMapper,
                                   StandardVideoService standardVideoService,
                                   StandardAlbumService standardAlbumService,
                                   StandardEpisodeService standardEpisodeService,
                                   StandardTrailerService standardTrailerService,
                                   UnofficialAlbumService unofficialAlbumService) {
        this.standardAlbumMapper = standardAlbumMapper;
        this.standardVideoMapper = standardVideoMapper;
        this.ugcStandardVideoMapper = ugcStandardVideoMapper;
        this.standardEpisodeMapper = standardEpisodeMapper;
        this.standardTrailerMapper = standardTrailerMapper;
        this.standardVideoService = standardVideoService;
        this.standardAlbumService = standardAlbumService;
        this.standardEpisodeService = standardEpisodeService;
        this.standardTrailerService = standardTrailerService;
        this.unofficialAlbumService = unofficialAlbumService;
    }

    public CompletableFuture<String> initUnofficialAlbum(InitDataParam request) {
        long start = System.currentTimeMillis();
        log.info("start initUnofficialAlbum, {}, {}", start, request.toString());

        // 将电视端媒资es的数据全量同步至UnofficialAlbumES
        unofficialAlbumService.syncDataFromTV();
        log.info("end initUnofficialAlbum, cost:{}, {}", System.currentTimeMillis() - start, request);
        return CompletableFuture.completedFuture("success");
    }

    public String initData(InitDataParam request) {
        log.info("start initUgcStandardData,{},{}", System.currentTimeMillis(), request.toString());
        for (int i = request.getDataBaseStart(); i <= request.getDataBaseEnd(); i++) {
            for (int j = request.getTableStart(); j <= request.getTableEnd(); j++) {
                if ("album".equals(request.getType())) {
                    initAlbum(request, i, j);
                } else if ("video".equals(request.getType())) {
                    initVideo(request, i, j);
                } else if ("episode".equals(request.getType())) {
                    initEpisode(request, i, j);
                } else if ("trailer".equals(request.getType())) {
                    initTrailer(request, i, j);
                } else if ("ugcVideo".equals(request.getType())) {
                    initUgcVideo(request, i, j);
                }
            }
        }
        log.info("end initStandardData,{},{}", System.currentTimeMillis(), request);
        return "success";
    }

    private void initAlbum(InitDataParam request, int databaseIndex, int tableIndex) {
        int totalCount = standardAlbumMapper.selectStandardAlbumCount(databaseIndex, tableIndex, request.getUpdateTime());
        log.info("initAlbum totalCount:{}", totalCount);
        int pageSize = 500;
        if (totalCount == 0) {
            return;
        }
        for (int i = 0; i <= totalCount; i += pageSize) {
            try {
                List<StandardAlbumEs> list = standardAlbumMapper.selectStandardAlbumList(databaseIndex, tableIndex, request.getUpdateTime(), i, pageSize);
                list.forEach(item -> {
                    StandardAlbumEs standardAlbumEs = standardAlbumService.searchBySid(item.getSid());
                    item.setSidL(Long.valueOf(item.getSid()));
                    item.setHasPreview((item.getPreviewInfo() != null && item.getPreviewInfo().length() > 10) ? 1 : 0);
                    if (standardAlbumEs == null) {
                        item.setDayNo(0);
                        item.setOppoHot(0L);
                        item.setLast7DaysPlayPv(0L);
                        item.setLast15DaysPlayPv(0L);
                        item.setLast30DaysPlayPv(0L);
                        item.setLast7DaysClickPv(0L);
                        item.setLast15DaysClickPv(0L);
                        item.setLast30DaysClickPv(0L);
                    } else {
                        item.setDayNo(standardAlbumEs.getDayNo());
                        item.setOppoHot(standardAlbumEs.getOppoHot());
                        item.setLast7DaysPlayPv(standardAlbumEs.getLast7DaysPlayPv());
                        item.setLast15DaysPlayPv(standardAlbumEs.getLast15DaysPlayPv());
                        item.setLast30DaysPlayPv(standardAlbumEs.getLast30DaysPlayPv());
                        item.setLast7DaysClickPv(standardAlbumEs.getLast7DaysClickPv());
                        item.setLast15DaysClickPv(standardAlbumEs.getLast15DaysClickPv());
                        item.setLast30DaysClickPv(standardAlbumEs.getLast30DaysClickPv());
                    }
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    standardAlbumService.initData(list);
                }
            } catch (Exception e) {
                log.error("initAlbum error,databaseIndex:{},tableIndex:{},e:{}", databaseIndex, tableIndex, e);
            }
        }
    }

    private void initVideo(InitDataParam request, int databaseIndex, int tableIndex) {
        int totalCount = standardVideoMapper.selectStandardVideoCount(databaseIndex, tableIndex, request.getUpdateTime());
        log.info("initVideo totalCount:{}", totalCount);
        int pageSize = 500;
        if (totalCount == 0) {
            return;
        }
        Long lastId = 0L;
        for (int i = 0; i <= totalCount; i += pageSize) {
            try {
                List<StandardVideoEs> list = standardVideoMapper.selectStandardVideoList(databaseIndex, tableIndex, request.getUpdateTime(), lastId, pageSize);
                if (CollectionUtils.isNotEmpty(list)) {
                    lastId = list.get(list.size() - 1).getIdOffSet();
                    standardVideoService.initData(list);
                } else {
                    break;
                }
            } catch (Exception e) {
                log.error("initVideo error,databaseIndex:{},tableIndex:{},e:{}", databaseIndex, tableIndex, e);
            }
        }
    }

    private void initEpisode(InitDataParam request, int databaseIndex, int tableIndex) {
        int totalCount = standardEpisodeMapper.selectStandardEpisodeCount(databaseIndex, tableIndex, request.getUpdateTime());
        log.info("initEpisode totalCount:{}", totalCount);
        int pageSize = 500;
        if (totalCount == 0) {
            return;
        }
        String lastEid = "0";
        for (int i = 0; i <= totalCount; i += pageSize) {
            try {
                List<StandardEpisodeEs> list = standardEpisodeMapper.selectStandardEpisodeList(databaseIndex, tableIndex, request.getUpdateTime(), lastEid, pageSize);
                if (CollectionUtils.isNotEmpty(list)) {
                    lastEid = list.get(list.size() - 1).getEid();
                    standardEpisodeService.initData(list);
                }
            } catch (Exception e) {
                log.error("initEpisode error,databaseIndex:{},tableIndex:{},e:{}", databaseIndex, tableIndex, e);
            }
        }
    }

    private void initTrailer(InitDataParam request, int databaseIndex, int tableIndex) {
        int totalCount = standardTrailerMapper.selectStandardTrailerCount(databaseIndex, tableIndex, request.getUpdateTime());
        log.info("initTrailer totalCount:{}", totalCount);
        int pageSize = 500;
        if (totalCount == 0) {
            return;
        }
        String lastTId = "0";
        for (int i = 0; i <= totalCount; i += pageSize) {
            try {
                List<StandardTrailerEs> list = standardTrailerMapper.selectStandardTrailerList(databaseIndex, tableIndex, request.getUpdateTime(), lastTId, pageSize);
                if (CollectionUtils.isNotEmpty(list)) {
                    lastTId = list.get(list.size() - 1).getTid();
                    standardTrailerService.initData(list);
                }
            } catch (Exception e) {
                log.error("initTrailer error,databaseIndex:{},tableIndex:{},e:{}", databaseIndex, tableIndex, e);
            }
        }
    }

    private void initUgcVideo(InitDataParam request, int databaseIndex, int tableIndex) {
        int totalCount = ugcStandardVideoMapper.selectUgcStandardVideoCount(databaseIndex, tableIndex, request.getUpdateTime());
        log.info("initUgcVideo totalCount:{}", totalCount);
        int pageSize = 500;
        if (totalCount == 0) {
            return;
        }
        Long lastId = 0L;
        for (int i = 0; i <= totalCount; i += pageSize) {
            try {
                List<UgcStandardVideoEs> list = ugcStandardVideoMapper.selectUgcStandardVideoList(databaseIndex, tableIndex, request.getUpdateTime(), lastId, pageSize);
                if (CollectionUtils.isNotEmpty(list)) {
                    lastId = list.get(list.size() - 1).getIdOffSet();
                    standardVideoService.initUgcData(list);
                } else {
                    break;
                }
            } catch (Exception e) {
                log.error("initUgcVideo error,databaseIndex:{},tableIndex:{},e:{}", databaseIndex, tableIndex, e);
            }
        }
    }

}
