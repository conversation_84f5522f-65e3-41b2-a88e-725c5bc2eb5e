package com.heytap.longvideo.search.service.app;

import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.RecommendParam;
import com.heytap.longvideo.search.model.param.app.RelationListResponse;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.cpc.video.framework.lib.utils.MD5SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FieldValueFactorFunctionBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/*
 * Description 相关推荐
 * Date 9:45 2023/2/1
 * Author songjiajia 80350688
 */
@Slf4j
@Service
public class RelationRecommendService {

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    private static final String APP_ID = "longvideo";

    @HeraclesDynamicConfig(key = "subscribe.getList.url", fileName = "search_config.properties")
    private String subscribeGetListUrl;

    @HeraclesDynamicConfig(key = "user.middle.relation.secretKey", fileName = "search_config.properties")
    private String middleRelationSecretKey;

    @Autowired
    HttpDataChannel httpDataChannel;

    @Autowired
    private StandardAlbumService standardAlbumService;

    public Set<String> getRecommend(RecommendParam param) {
        StandardAlbumEs standardAlbumEs = standardAlbumService.searchBySid(param.getSid());
        if (standardAlbumEs == null) {
            return new HashSet<>();
        }
        CompletableFuture<Set<String>> getByPlay = CompletableFuture.supplyAsync(() -> {
            return getByPlay(param, standardAlbumEs);
        });
        CompletableFuture<Set<String>> getByScore = CompletableFuture.supplyAsync(() -> {
            return getByFunctionScore(param, standardAlbumEs);
        });
        CompletableFuture<Set<String>> getHistory = CompletableFuture.supplyAsync(() -> {
            return getHistory(param);
        });
        CompletableFuture<Set<String>> returnFuture = CompletableFuture.allOf(getByPlay, getByScore, getHistory).handleAsync((aVoid, throwable) -> {
            try {
                Set<String> playList = getByPlay.get(1, TimeUnit.SECONDS);
                Set<String> scoreList = getByScore.get(1, TimeUnit.SECONDS);
                Set<String> historyList = getHistory.get(1, TimeUnit.SECONDS);
                Set<String> returnSet = new HashSet<>();
                returnSet.addAll(playList);
                returnSet.addAll(scoreList);
                returnSet.remove(param.getSid());
                List<String> intersection = (List<String>) CollectionUtils.intersection(returnSet, historyList);
                returnSet.removeAll(historyList);
                returnSet = createRandoms(returnSet, param.getCount());
                int lessSize = param.getCount() - returnSet.size();
                if (lessSize > 0 && intersection.size() >= lessSize) {
                    for (int i = 0; i < lessSize; i++) {
                        returnSet.add(intersection.get(i));
                    }
                }
                return returnSet;
            } catch (Exception e) {
                log.error("getRecommend error", e);
            }
            return new HashSet<>();
        });
        try {
            Set<String> returnSet = returnFuture.get(500, TimeUnit.MILLISECONDS);
            if (returnSet.size() >= param.getCount()) {
                return returnSet;
            } else {
                return new HashSet<>();
            }
        } catch (Exception e) {
            log.error("getRecommend error", e);
        }
        return new HashSet<>();
    }

    private Set<String> getByPlay(RecommendParam param, StandardAlbumEs standardAlbum) {
        Set<String> returnSet = new HashSet<>();
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery;
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtil.isBlank(standardAlbum.getTags())) {
            return returnSet;
        }
        boolQuery.filter(QueryBuilders.matchQuery("tags", standardAlbum.getTags().replace("|", ",")));
        boolQuery.filter(QueryBuilders.matchQuery("contentType", standardAlbum.getProgramType()));
        queryBuilder = queryBuilder.withQuery(boolQuery)
                .withPageable(PageRequest.of(0, param.getCount()))
                .withSort(SortBuilders.fieldSort("dayNo").order(SortOrder.DESC))
                .withSort(SortBuilders.fieldSort("oppoHot").order(SortOrder.DESC))
                .withSort(SortBuilders.fieldSort("showTime").order(SortOrder.DESC));

        searchQuery = queryBuilder.build();
        //4.解析响应

        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            ProgramAlbumEs programAlbumEs = searchHit.getContent();
            log.info("getByPlay hit,sid:{},title:{},content:{},tag:{}", programAlbumEs.getSid(), programAlbumEs.getTitle(), programAlbumEs.getContentType(), programAlbumEs.getTags());
            if (programAlbumEs.getSid().equals(param.getSid()) || programAlbumEs.getTitle().equals(standardAlbum.getTitle())) {
                continue;
            }
            returnSet.add(programAlbumEs.getSid());
        }
        return returnSet;
    }

    private Set<String> getByFunctionScore(RecommendParam param, StandardAlbumEs standardAlbum) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        FieldValueFactorFunctionBuilder fieldQuery = new FieldValueFactorFunctionBuilder("functionScore");
        FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(buildBoolQuery(standardAlbum), fieldQuery);
        PageRequest pageRequest = PageRequest.of(0, param.getCount());
        NativeSearchQuery searchQuery = queryBuilder.withQuery(functionScoreQueryBuilder)
                .withPageable(pageRequest)
                .build();
        //4.解析响应
        Set<String> returnSet = new HashSet<>();
        SearchHits<ProgramAlbumEs> searchHits = restTemplate.search(searchQuery, ProgramAlbumEs.class);
        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            ProgramAlbumEs programAlbumEs = searchHit.getContent();
            log.info("getByFunctionScore hit,sid:{},title:{},content:{},tag:{}", programAlbumEs.getSid(), programAlbumEs.getTitle(), programAlbumEs.getContentType(), programAlbumEs.getTags());
            if (programAlbumEs.getSid().equals(param.getSid()) || programAlbumEs.getTitle().equals(standardAlbum.getTitle())) {
                continue;
            }
            returnSet.add(programAlbumEs.getSid());
        }
        return returnSet;
    }

    private BoolQueryBuilder buildBoolQuery(StandardAlbumEs standardAlbum) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtil.isNotBlank(standardAlbum.getTags())) {
            boolQuery.should(QueryBuilders.matchQuery("tags", standardAlbum.getTags().replace("|", ",")));
        }
        if (StringUtil.isNotBlank(standardAlbum.getLanguage()) && ("英语".equals(standardAlbum.getLanguage()) || "粤语".equals(standardAlbum.getLanguage()) || "日语".equals(standardAlbum.getLanguage()) || "韩语".equals(standardAlbum.getLanguage()))) {
            boolQuery.should(QueryBuilders.matchQuery("language", standardAlbum.getLanguage()));
        }
        String contentType = standardAlbum.getProgramType();
        boolQuery.should(QueryBuilders.matchQuery("contentType", contentType));

        buildActorDirectorQuery(contentType, standardAlbum, boolQuery);

        return boolQuery;
    }

    private void buildActorDirectorQuery(String contentType, StandardAlbumEs standardAlbum, BoolQueryBuilder boolQuery) {
        if (ContentTypeEnum.MOVIE.getCode().equals(contentType) || ContentTypeEnum.TV.getCode().equals(contentType) || ContentTypeEnum.SHOW.getCode().equals(contentType)) {
            if (StringUtil.isNotBlank(standardAlbum.getActor())) {
                Set<String> actorSet = new HashSet<>();
                String[] actors = standardAlbum.getActor().split("\\|");
                for (int i = 0; i < actors.length; i++) {
                    if (i > 2) {
                        continue;
                    }
                    actorSet.add(actors[i]);
                }
                actorSet.removeAll(CommonConstant.errorActorName);
                boolQuery.should(QueryBuilders.matchPhraseQuery("actor", org.apache.commons.lang3.StringUtils.join(actorSet, "|")));
            }
            if (StringUtil.isNotBlank(standardAlbum.getDirector())) {
                boolQuery.should(QueryBuilders.matchPhraseQuery("director", standardAlbum.getDirector()));
            }
        }
    }

    public Set<String> getHistory(RecommendParam param) {
        Set<String> returnSet = new HashSet<>();
        try {
            if (StringUtil.isEmpty(param.getUid())) {
                return returnSet;
            }
            HashMap<String, Object> request = initReqParam(param.getUid());
            request.put("offset", 0L);
            request.put("ext", 0);
            request.put("size", 5);
            request.put("sign", MD5SignUtils.getSignature(request, middleRelationSecretKey, false));
            String url = subscribeGetListUrl
                    + "?appId=" + request.get("appId")
                    + "&fromApp=" + request.get("fromApp")
                    + "&type=" + request.get("type");

            log.info("RelationServiceInvoker.query, request={}", request);
            RelationListResponse response = httpDataChannel.postForObject(url, request, RelationListResponse.class, null, new HashMap<>(),
                    250);

            if (response != null && response.getResult() != null && response.getResult().getTargetInfoList() != null) {
                response.getResult().getTargetInfoList().forEach(v -> {
                    returnSet.add(v.getTid());
                });
            }
        } catch (Exception e) {
            log.error("getHistory error,uid:{},e:{}", param.getUid(), e.getMessage());
        }
        return returnSet;
    }

    private HashMap<String, Object> initReqParam(String accountId) {
        return new HashMap<String, Object>() {
            {
                put("appId", APP_ID);
                put("fromApp", APP_ID);
                put("fid", accountId);
                put("type", "watchHistory");
                put("timestamp", System.currentTimeMillis());
            }
        };
    }


    private Set<String> createRandoms(Set<String> set, int n) {
        if (set.size() <= n) {
            return set;
        }
        List<String> list = new ArrayList<String>(set);
        Collections.shuffle(list);
        list = list.subList(0, n);
        return new HashSet<>(list);
    }

}