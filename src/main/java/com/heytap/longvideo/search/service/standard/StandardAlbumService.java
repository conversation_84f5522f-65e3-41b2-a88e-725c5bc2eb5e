package com.heytap.longvideo.search.service.standard;

import com.alibaba.fastjson.JSON;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.constants.ScoreLevelEnum;
import com.heytap.longvideo.search.constants.ScoreRange;
import com.heytap.longvideo.search.constants.SortStrategyConstant;
import com.heytap.longvideo.search.mapper.media.ProgramTypeMapper;
import com.heytap.longvideo.search.model.entity.db.MisProgramType;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.ProgramTypeListVo;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.mq.MediaJinsConsumerService;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/*
 * Description 标准化后的剧头搜索
 * Date 9:50 2022/3/17
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class StandardAlbumService {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private HotVideoService hotVideoService;

    @Autowired
    private SearchProperties searchConfig;

    @Autowired
    private ProgramTypeMapper programTypeMapper;

    @Autowired
    private MediaJinsConsumerService consumerService;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @HeraclesDynamicConfig(key = "support.scene.switch", fileName = "search_config.properties")
    private int supportSceneSwitch = 1;

    @HeraclesDynamicConfig(key = "support.scene.list", fileName = "search_config.properties", textType = TextType.JSON)
    private List<String> supportSceneList = Lists.newArrayList("2*N","3*N","loopBanner","material", "meizi", "landingPage");

    public PageResponse<StandardAlbumVo> searchAlbum(SearchStandardAlbumParams request) throws Exception {
        try {
            PageResponse<StandardAlbumVo> response = new PageResponse<>();
            List<StandardAlbumVo> voList = new ArrayList<StandardAlbumVo>(request.getPageSize());
            buildRequest(request);

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getAlbumQueryBuilder(request))
                    .from((request.getPageIndex() - 1) * request.getPageSize())
                    .size(request.getPageSize())
                    .trackTotalHits(true)
                    .sort(SortBuilders.fieldSort(request.getOrder())
                            .order(request.getSortType() != null && request.getSortType() == 1 ? SortOrder.ASC : SortOrder.DESC))
                    .sort("sidL", SortOrder.DESC);

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_ALBUM_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                StandardAlbum standardAlbum = JsonUtil.fromStr(hit.getSourceAsString(), StandardAlbum.class);
                //过滤咪咕奥运内容
                if (SourceEnum.MIGU_OLYMPIC.getDataSource().equals(standardAlbum.getSource())
                        && supportSceneSwitch == 1
                        && (StringUtils.isBlank(request.getUsageScene()) || !supportSceneList.contains(request.getUsageScene()))) {
                    continue;
                }
                StandardAlbumVo standardAlbumVo = new StandardAlbumVo();
                BeanUtils.copyProperties(standardAlbum, standardAlbumVo);
                standardAlbumVo.setOriginStatus(standardAlbum.getSourceStatus());
                standardAlbumVo.setReleScore(hit.getScore());
                setSourceType(standardAlbumVo);
                standardAlbumVo.setFeatureTypeDesc(getFeatureTypeDesc(standardAlbum.getFeatureType(), (standardAlbum.getPreviewInfo() != null && standardAlbum.getPreviewInfo().length() > 10) ? 1 : 0));
                voList.add(standardAlbumVo);
            }
//            voList = keywordSearchSort(voList, request);
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / request.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchAlbum fail", e);
            throw e;
        }
    }

    private void buildRequest(SearchStandardAlbumParams request  ) {
        if (StringUtils.isBlank(request.getOrder())) {
            request.setOrder("showTime");
        }
        if (request.getOrder().contains("_")) {
            request.setOrder(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, request.getOrder()));
        } else if ("score".equals(request.getOrder())) {
            request.setOrder("sourceScore");
        }

        if (request.getSortStrategy() == SortStrategyConstant.YEAR) {
            request.setOrder("year");
        } else if (request.getSortStrategy() == SortStrategyConstant.SCORE) {
            request.setOrder("sourceScore");
        } else if (request.getSortStrategy() == SortStrategyConstant.PLAY_PV) {
            request.setOrder("oppoHot");
        }
    }

    /**
     * 无用字段，仅为兼容旧逻辑
     *
     * @return
     */
    private void setSourceType(StandardAlbumVo standardAlbumVo) {
        String source = standardAlbumVo.getSource();
        int sourceType = standardAlbumVo.getSourceType();
        if ("mgmobile".equals(source)) {
            sourceType = 13;
        } else if ("senyu".equals(source)) {
            sourceType = 12;
        } else if ("huashi".equals(source)) {
            sourceType = 11;
        } else if ("sohu".equals(source)) {
            sourceType = 6;
        }
        standardAlbumVo.setSourceType(sourceType);
    }


    /**
     * 构造剧头查询条件
     *
     * @param nRequest
     * @return
     */
    private QueryBuilder getAlbumQueryBuilder(SearchStandardAlbumParams nRequest) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        buildNonEsFieldCondition(nRequest, boolQueryBuilder);
        buildEsFieldCondition(nRequest, boolQueryBuilder);

        return boolQueryBuilder;
    }

    /**
     * 处理非EsField查询条件
     */
    private void buildNonEsFieldCondition(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        //编排后台查询剧头 是否屏蔽优酷内容
        if ("1".equals(nRequest.getBlockYoukuMobile())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("copyrightCode", SourceEnum.YOUKU_MOBILE.getDataSource()));
        }

        if (StringUtils.isNotBlank(nRequest.getYearStart())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("year").gte(nRequest.getYearStart()));
        }
        if (StringUtils.isNotBlank(nRequest.getYearEnd())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("year").lte(nRequest.getYearEnd()));
        }

        buildSource(nRequest, boolQueryBuilder);

        if (nRequest.getAgeStart() != null || nRequest.getAgeEnd() != null) {
            int ageStart = nRequest.getAgeStart() == null ? 0 : nRequest.getAgeStart();
            int ageEnd = nRequest.getAgeEnd() == null ? 100 : nRequest.getAgeEnd();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fitAgeMin").gte(ageStart));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fitAgeMax").lte(ageEnd));
        }

        if (nRequest.getUpdateTime() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("updateTime").gte(nRequest.getUpdateTime()));
        }

        buildOppoScore(nRequest, boolQueryBuilder);
    }

    private void buildSource(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        if (nRequest.getSourceScoreStart() != null) {
            if (nRequest.getSourceScoreStart().compareTo(10F) == 0) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("sourceScore", "10.0"));
            } else {
                BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
                scoreBuilderOr.should().add(QueryBuilders.termsQuery("sourceScore", "10.0"));
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("sourceScore").gte(nRequest.getSourceScoreStart()));
                boolQueryBuilder.must(scoreBuilderOr);
            }
        }
        if (nRequest.getSourceScoreEnd() != null) {
            if (nRequest.getSourceScoreEnd().compareTo(10F) == 0) {
                BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
                scoreBuilderOr.should().add(QueryBuilders.termsQuery("sourceScore", "10.0"));
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("sourceScore").lte("9.9"));
                boolQueryBuilder.must(scoreBuilderOr);
            } else {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("sourceScore").lte(nRequest.getSourceScoreEnd()));
                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("sourceScore", "10.0"));
            }
        }
    }

    private void buildOppoScore(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        if (nRequest.getOppoScoreArray() != null && nRequest.getOppoScoreArray().length > 0) {
            Integer[] oppoScoreLevelArray = nRequest.getOppoScoreArray();
            BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
            for (Integer level : oppoScoreLevelArray) {
                ScoreRange scoreRange = ScoreLevelEnum.getByLevel(level);
                if (scoreRange == null) {
                    continue;
                }
                if (ScoreLevelEnum.SCORE_LEVEL_5.getLevel().equals(level)) {
                    scoreBuilderOr.should().add(QueryBuilders.termQuery("oppoScore", "10.0"));
                }
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("oppoScore").gte(scoreRange.getStart()).lte(scoreRange.getEnd()));
            }
            boolQueryBuilder.filter(scoreBuilderOr);
        }
    }

    /**
     * 处理EsField查询条件
     */
    private void buildEsFieldCondition(SearchStandardAlbumParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                SearchStandardAlbumParams.class, EsField.class);
        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fieldName = field.getAnnotation(EsField.class).name();
            fieldName = StringUtils.isNotEmpty(fieldName) ? fieldName : entry.getKey();
            buildSingleField(fieldName, fieldValue, boolQueryBuilder);
        }
    }

    private void buildSingleField(String fieldName, String fieldValue, BoolQueryBuilder boolQueryBuilder) {
        if ("sid".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery(fieldName, "*" + fieldValue + "*"));
        } else if ("tags".equals(fieldName) || "actor".equals(fieldName)
                || "director".equals(fieldName) || "area".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery(fieldName, fieldValue.replace(",", "|")));
        } else if ("source".equals(fieldName) || "copyrightCode".equals(fieldName)) {
            fieldValue = replaceSource(fieldValue);
            boolQueryBuilder.must(QueryBuilders.termsQuery(fieldName, fieldValue.split(",")));
        } else if ("programType".equals(fieldName)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery(fieldName, fieldValue.split(",")));
        } else if ("status".equals(fieldName) && "0".equals(fieldValue)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("status", "1"));
        } else if ("useHighLight".equals(fieldName)) {
            buildHighLight(fieldValue, boolQueryBuilder);
        } else if ("managerStatus".equals(fieldName) && "3".equals(fieldValue)) {
            //表示要筛选部分字段锁定的节目（此类节目的managerStatus值 >=4 ）
            boolQueryBuilder.must(QueryBuilders.rangeQuery("managerStatus").gte(4));
        } else if ("featureType".equalsIgnoreCase(fieldName) && "15".equalsIgnoreCase(fieldValue)) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fieldName, 1));
            boolQueryBuilder.must(QueryBuilders.termQuery("hasPreview", 1));
        } else {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fieldName, fieldValue));
        }
    }

    private String replaceSource(String fieldValue) {
        if (fieldValue.contains("funshion") && !fieldValue.contains("funshion_lv")) {
            // 后台直接添加 风行和微迪欧
            return fieldValue.replace("funshion", "huashi,senyu,weidiou,funshion_lv");
        }
        return fieldValue;
    }

    private void buildHighLight(String fieldValue, BoolQueryBuilder boolQueryBuilder) {
        if ("1".equals(fieldValue)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("highLightVid", "*"));
        } else if ("0".equals(fieldValue)) {
            boolQueryBuilder.mustNot(QueryBuilders.wildcardQuery("highLightVid", "*"));
        }
    }

    /**
     * 数据初始化
     */
    public void initData(List<StandardAlbumEs> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("albumlist null");
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (StandardAlbumEs standardAlbumEs : list) {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(standardAlbumEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(StandardAlbumEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(StandardAlbumEs.class);
        indexOperations.refresh();
    }


    /**
     * 数据更新
     *
     * @param es
     */
    public void insertOrUpdate(StandardAlbumEs es) {
        try {
            restTemplate.save(es);
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardAlbum", e);
            throw e;
        }
    }

    /**
     * 数据更新
     *
     * @param es
     */
    public void delete(StandardAlbumEs es) {
        try {
            restTemplate.delete(es.getSid(), IndexCoordinates.of(StandardAlbumEs.class.getAnnotation(Document.class).indexName()));
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardAlbum", e);
            throw e;
        }
    }

    public StandardAlbumEs searchBySid(String keyWord) {
        try {
            GetRequest getRequest = new GetRequest(IndexNameConstant.STANDARD_ALBUM_INDEX);
            getRequest.id(keyWord);
            GetResponse getResponse = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
            if (getResponse != null && getResponse.getSourceAsString() != null) {
                return JsonUtil.fromStr(getResponse.getSourceAsString(), StandardAlbumEs.class);
            }
        } catch (Exception e) {
            log.error("searchBySid error", e);
        }
        return null;
    }

    public List<ProgramTypeListVo> getProgramType(String source, Integer version) {

        if ("funshion".equals(source)) {
            // 后台直接添加 风行和微迪欧
            source = source.replace("funshion", "huashi,senyu,weidiou,funshion_lv");
        }
        SearchRequest searchRequest = new SearchRequest(IndexNameConstant.STANDARD_ALBUM_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(source) && !"all".equals(source)) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("source", source.split(","));
            query.filter(termsQueryBuilder);
        }
        sourceBuilder.query(query);
        sourceBuilder.size(0);

        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("programType").field("programType");
        aggregationBuilder.size(100);
        sourceBuilder.aggregation(aggregationBuilder);

        searchRequest.source(sourceBuilder);
        sourceBuilder.trackTotalHits(true);

        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("search es error!", e);
        }
        Aggregations aggregations = searchResponse.getAggregations();
        Map<String, Aggregation> aggregationMap = aggregations.asMap();
        Terms programTypeList = (Terms) aggregationMap.get("programType");
        List<? extends Terms.Bucket> buckets = programTypeList.getBuckets();

        List<ProgramTypeListVo> vos = Lists.newArrayList();
        buckets.forEach(bucket -> {
            String value = bucket.getKeyAsString();
            MisProgramType misProgramType = programTypeMapper.selectNameByCode(value);
            String text = misProgramType.getName();
            ProgramTypeListVo vo = new ProgramTypeListVo();
            vo.setValue(value);
            vo.setCode(value);
            vo.setText(text);
            vo.setName(text);
            vos.add(vo);
        });

        if (StringUtils.isBlank(source)) {
            ProgramTypeListVo vo = new ProgramTypeListVo();
            vo.setValue("targetProgram");
            vo.setCode("targetProgram");
            vo.setText("定向下发");
            vo.setName("定向下发");
            vos.add(vo);
        }

        ProgramTypeListVo vo = new ProgramTypeListVo();
        vo.setValue("funny");
        vo.setCode("funny");
        vo.setText("搞笑");
        vo.setName("搞笑");
        vos.add(vo);

        return vos;
    }

    private Map<Integer, String> FEATURE_TYPE_MAP = new HashMap<Integer, String>() {
        {
            put(0, "预告片");
            put(1, "正片");
            put(2, "预告片");
            put(15, "正片+预告片");
        }
    };

    /**
     * 正片类型展示字段
     *
     * @param featureType
     * @param hasPreview
     * @return
     */
    private String getFeatureTypeDesc(Integer featureType, Integer hasPreview) {
        Set<String> featureTypeSet = new HashSet<>();

        featureTypeSet.add(FEATURE_TYPE_MAP.get(featureType));

        if (hasPreview != null && hasPreview == 1) {
            featureTypeSet.add("预告片");
        }

        return StringUtils.join(featureTypeSet, "+");
    }

    /**
     * 主动更新ES，立即刷新
     *
     * @param standardAlbum
     */
    public void updateStandardAlbum(StandardAlbum standardAlbum) {
        StandardAlbumEs standardAlbumEs = new StandardAlbumEs();
        BeanUtils.copyProperties(standardAlbum, standardAlbumEs);
        try {
            consumerService.setStandardAlbumEs(standardAlbumEs, standardAlbum);
            UpdateRequest request = new UpdateRequest(IndexNameConstant.STANDARD_ALBUM_INDEX, standardAlbumEs.getSid());
            request.doc(JSON.toJSONString(standardAlbumEs), XContentType.JSON);
            // 手动将缓存区中的数据刷新到磁盘中
            request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            // 发送更新请求
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("failed to update document to elasticsearch,the doc is:{},the exception is {}", standardAlbumEs, e);
        }
        log.info("update standardAlbum es success, sid: {}", standardAlbum.getSid());
    }

    public List<String> getSourceAlbumIds(String mediaId, Integer version) {
        List<String> result = Lists.newArrayList();
        if (StringUtils.isBlank(mediaId)) {
            return result;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("extraInfo", mediaId));
        if (funshionLongVideoAndWeidiouFilterService.filterItem(version)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("source", "huashi,senyu".split(",")));
        } else {
            boolQueryBuilder.must(QueryBuilders.termsQuery("source", "huashi,senyu,weidiou,funshion_lv".split(",")));
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .trackTotalHits(true);
        SearchRequest searchRequest = new SearchRequest()
                .indices(IndexNameConstant.STANDARD_ALBUM_INDEX)
                .source(searchSourceBuilder);
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            for (SearchHit hit : searchHits.getHits()) {
                StandardAlbum standardAlbum = JsonUtil.fromStr(hit.getSourceAsString(), StandardAlbum.class);
                result.add(standardAlbum.getSourceAlbumId());
            }
        } catch (Exception e) {
            log.error("query es error!", e);
        }
        return result;
    }
}
