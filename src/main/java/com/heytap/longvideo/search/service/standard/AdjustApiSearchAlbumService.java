package com.heytap.longvideo.search.service.standard;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.entity.MinorPoolCodeRecord;
import com.heytap.longvideo.client.arrange.enums.MinorsPoolCodeEnum;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.AlbumSearchResponse;
import com.heytap.longvideo.client.media.query.AlbumSearchParameterModifier;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.ProgramAlbumUpdateEntity;
import com.heytap.longvideo.search.rpc.consumer.LvContentItemRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.LvContentPoolRpcApiProxy;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.constants.SearchConstant.MINORS_POOL_CODE;
import static com.heytap.longvideo.search.constants.SearchConstant.SID;

@Slf4j
@Service
public class AdjustApiSearchAlbumService {

    @HeraclesDynamicConfig(key = "album.max.count", fileName = "search_config.properties")
    private Integer ALBUM_MAX_COUNT = 2100;

    @HeraclesDynamicConfig(key = "album.max.limit", fileName = "search_config.properties")
    private Integer LIMIT = 500;

    @Autowired
    private LvContentPoolRpcApiProxy lvContentPoolRpcApiProxy;
    @Autowired
    private ElasticsearchRestTemplate restTemplate;
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    @Autowired
    private LvContentItemRpcApiProxy lvContentItemRpcApiProxy;

    public Boolean adjustSingleApiSearchAlbum(AlbumSearchParameterModifier param) {
        log.info("adjustSingleApiSearchAlbum - param: {}", param);
        if (Objects.isNull(param) || StringUtil.isEmpty(param.getSid())
                || StringUtil.isEmpty(param.getPoolCode())) {
            log.warn("adjustSingleApiSearchAlbum - Invalid parameter: {}", param);
            return false;
        }

        String sid = param.getSid();
        String poolCode = param.getPoolCode();
        Boolean result = this.checkPoolCode(poolCode);

        if (Boolean.FALSE.equals(result)) {
            return false;
        }

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery(SID, sid));

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery = queryBuilder
                .withQuery(boolQuery)
                .withFields(SID, MINORS_POOL_CODE)
                .build();

        // 执行查询
        List<ProgramAlbumUpdateEntity> returnList = new ArrayList<>();
        SearchHits<ProgramAlbumEs> searchHits = this.restTemplate.search(searchQuery, ProgramAlbumEs.class);

        boolean alreadyUpdated = false;

        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            String id = searchHit.getId();
            ProgramAlbumEs programAlbumEs = searchHit.getContent();
            String minorsPoolCode = programAlbumEs.getMinorsPoolCode();
            // 可以提前结束
            if (StringUtil.isNotEmpty(minorsPoolCode) && minorsPoolCode.contains(poolCode)) {
                log.info("already in minorsPoolCode:{} poolCode{}", minorsPoolCode, poolCode);
                alreadyUpdated = true;
                continue;
            }
            ProgramAlbumUpdateEntity entity = new ProgramAlbumUpdateEntity(id, programAlbumEs.getSid(),
                    minorsPoolCode);

            returnList.add(entity);
        }

        if (alreadyUpdated) {
            return alreadyUpdated;
        }
        if (CollectionUtils.isEmpty(returnList)) {
            return false;
        }

        Map<String, ProgramAlbumUpdateEntity> sidAndPoolCodeMap = returnList.stream()
                .collect(Collectors.toMap(ProgramAlbumUpdateEntity::getSid, a -> a, (o, n) -> n));

        ProgramAlbumUpdateEntity programAlbumUpdateEntity = sidAndPoolCodeMap.get(sid);
        String minorsPoolCode = programAlbumUpdateEntity.getMinorsPoolCode();

        if (StringUtil.isNotEmpty(minorsPoolCode) && poolCode.contains(minorsPoolCode)) {
            return Boolean.TRUE;
        }

        return this.updateAlbumInEs(programAlbumUpdateEntity, poolCode);
    }

    /**
     * 批量更新 es 中的 minorsPoolCode
     * @param param
     * @return
     */
    public List<AlbumSearchResponse> adjustApiSearchAlbum(AlbumSearchParameterModifier param) {

        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getSidList()) || StringUtil.isEmpty(param.getPoolCode())) {
            log.warn("adjustApiSearchAlbum - Invalid parameter: {}", param);
            return Collections.emptyList();
        }

        log.info("adjustApiSearchAlbum -poolCode {} sidSize: {}", param.getPoolCode(), param.getSidList().size());
        String poolCode = param.getPoolCode();

        Boolean result = this.checkPoolCode(poolCode);
        if (Boolean.FALSE.equals(result)) {
            return Collections.emptyList();
        }

        List<String> sidList = param.getSidList();
        SearchRequest searchRequest = this.buildSearchRequest(sidList, poolCode);

        List<ProgramAlbumUpdateEntity> albumUpdateEntities = this.executeSearch(searchRequest, poolCode);
        if (CollectionUtils.isEmpty(albumUpdateEntities)) {
            log.error("adjustApiSearchAlbum error- not found for sids: {} poolCode {}", sidList, poolCode);
            return Collections.emptyList();
        }

        List<ProgramAlbumUpdateEntity> succeedResults = this.bulkUpdate(albumUpdateEntities);

        if (CollectionUtils.isEmpty(succeedResults)) {
            log.error("adjustApiSearchAlbum error- No success updates for sids: {} poolCode {}", sidList, poolCode);
            return Collections.emptyList();
        }

        List<AlbumSearchResponse> responses = succeedResults.stream()
                .map(entity -> new AlbumSearchResponse(entity.getSid(), entity.getMinorsPoolCode()))
                .collect(Collectors.toList());

        log.info("adjustApiSearchAlbum - Successfully updated {} albums", responses.size());
        return responses;

    }

    public List<AlbumSearchResponse> delApiSearchAlbumPoolCode(AlbumSearchParameterModifier param) {
        log.info("delApiSearchAlbumPoolCode - param: {}", param);
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getSidList()) || StringUtil.isEmpty(param.getPoolCode())) {
            log.warn("delApiSearchAlbumPoolCode - Invalid parameter: {}", param);
            return Collections.emptyList();
        }
        log.info("delApiSearchAlbumPoolCode -:poolCode {} sidSize: {}", param.getPoolCode(), param.getSidList().size());

        String poolCode = param.getPoolCode();
        Boolean result = this.checkPoolCode(poolCode);
        if (Boolean.FALSE.equals(result)) {
            return Collections.emptyList();
        }

        List<String> sidList = param.getSidList();

        SearchRequest searchRequest = new SearchRequest(IndexNameConstant.APP_ALBUM_INDEX);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery(SID, sidList));
        boolQuery.must(QueryBuilders.termQuery(MINORS_POOL_CODE, poolCode));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(sidList.size());

        FetchSourceContext fetchSourceContext = new FetchSourceContext(true, new String[]{SID, MINORS_POOL_CODE}, null);
        searchSourceBuilder.fetchSource(fetchSourceContext);

        searchRequest.source(searchSourceBuilder);


        List<ProgramAlbumUpdateEntity> albumUpdateEntities = new ArrayList<>();

        try {
            SearchResponse searchResponse = this.restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            org.elasticsearch.search.SearchHits searchHits = searchResponse.getHits();

            if (searchHits.getHits() != null && searchHits.getHits().length > 0) {
                albumUpdateEntities = Arrays.stream(searchHits.getHits())
                        .map(hit -> {
                            ProgramAlbumUpdateEntity entity = JSON.parseObject(hit.getSourceAsString(), ProgramAlbumUpdateEntity.class);
                            if(Objects.isNull(entity) || StringUtil.isEmpty(entity.getMinorsPoolCode())) {
                                return null;
                            }
                            entity.setId(hit.getId());
                            extractedByDel(poolCode, entity);
                            return entity;

                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (IOException e) {
            log.error("delApiSearchAlbumPoolCode - error executing search request: {}", searchRequest, e);
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(albumUpdateEntities)) {
            log.error("delApiSearchAlbumPoolCode -not need update for param {}", param);
            return Collections.emptyList();
        }

        List<ProgramAlbumUpdateEntity> succeedResults = this.bulkUpdate(albumUpdateEntities);

        if (CollectionUtils.isEmpty(succeedResults)) {
            log.error("delApiSearchAlbumPoolCode error- No success updates for poolCode: {} param {}", poolCode, param);
            return Collections.emptyList();
        }

        List<AlbumSearchResponse> responses = succeedResults.stream()
                .map(entity -> new AlbumSearchResponse(entity.getSid(), entity.getMinorsPoolCode()))
                .collect(Collectors.toList());

        log.info("delApiSearchAlbumPoolCode - Successfully updated {} albums", responses.size());
        return responses;

    }

    /**
     * 根据poolcode 整理内容池。
     * @param param
     * @return
     */
    public Boolean collateSinglePoolCodeApiSearchAlbum(AlbumSearchParameterModifier param) {
        if (Objects.isNull(param) || StringUtil.isEmpty(param.getPoolCode())) {
            log.warn("collateSinglePoolCode - Invalid parameter: {}", param);
            return false;
        }

        log.info("collateSinglePoolCode -poolCode {}", param.getPoolCode());

        String poolCode = param.getPoolCode();
        // 获取所有的内容池
        Boolean result = this.checkPoolCode(poolCode);
        if (Boolean.FALSE.equals(result)) {
            return false;
        }

        // 查询到池中的所有的 数据 生产有 2000
        try {
            List<String> sidList = this.getItems(poolCode);
            log.info("collate poolCode get sid size :{} poolCode:{}", sidList.size(), poolCode);

            if(!CollectionUtils.isEmpty(sidList)) {

                AlbumSearchParameterModifier parameterModifier = new AlbumSearchParameterModifier();
                parameterModifier.setPoolCode(poolCode);
                parameterModifier.setSidList(sidList);

                // 给有效的数据打标记
                this.adjustApiSearchAlbum(parameterModifier);
            }

            // 移除失效的数据
            return this.removeEsFlag(poolCode, sidList);
        } catch (Exception e) {
            log.error("collateSinglePoolCode error  poolCode:{}", poolCode, e);
        }
        return true;
    }

    /**
     *  校验是否属于配置的内容池
     * @param poolCode
     * @return
     */
    private Boolean checkPoolCode(String poolCode) {
        List<MinorPoolCodeRecord> minorPoolCodeRecords = this.getMinorPoolCodeRecords(MinorsPoolCodeEnum.AGE_16_TO_18.getValue());
        if (CollectionUtils.isEmpty(minorPoolCodeRecords)) {
            log.error("checkPoolCode error - No_minorPoolCode records found {}", poolCode);
            return false;
        }

        // 判断是否属于配置的池
        List<String> allPoolCode = minorPoolCodeRecords.stream()
                .map(MinorPoolCodeRecord::getPoolCode)
                .collect(Collectors.toList());

        if (!allPoolCode.contains(poolCode)) {
            log.info("collate poolCode is not in minorsPool: poolCode {} minorPoolCodeRecords{}", poolCode, minorPoolCodeRecords);
            return false;
        }
        return true;
    }

    private Boolean removeEsFlag(String poolCode, List<String> sidList) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(!CollectionUtils.isEmpty(sidList)) {
            boolQuery.mustNot(QueryBuilders.termsQuery(SID, sidList));
        }
        boolQuery.filter(QueryBuilders.termQuery(MINORS_POOL_CODE, poolCode));

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery = queryBuilder
                .withQuery(boolQuery)
                .withFields(SID, MINORS_POOL_CODE)
                .withPageable(PageRequest.of(0, ALBUM_MAX_COUNT))
                .build();

        List<ProgramAlbumUpdateEntity> returnList = new ArrayList<>();
        SearchHits<ProgramAlbumEs> searchHits = this.restTemplate.search(searchQuery, ProgramAlbumEs.class);

        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            String id = searchHit.getId();
            ProgramAlbumEs programAlbumEs = searchHit.getContent();
            String minorsPoolCode = programAlbumEs.getMinorsPoolCode();
            ProgramAlbumUpdateEntity entity = new ProgramAlbumUpdateEntity(id, programAlbumEs.getSid(),
                    minorsPoolCode);
            extractedByDel(poolCode, entity);
            returnList.add(entity);
        }

        if (CollectionUtils.isEmpty(returnList)) {
            return false;
        }

        List<ProgramAlbumUpdateEntity> succeedResults = this.bulkUpdate(returnList);

        if (CollectionUtils.isEmpty(succeedResults)) {
            log.error("removeEsFlag error - No success updates for sids.size: {} poolCode {}", sidList.size(), poolCode);
            return Boolean.FALSE;
        }
        return true;
    }

    /**
     * 更新单个minorPoolCode
     * @param entity
     * @param poolCode
     * @return
     */
    private Boolean updateAlbumInEs(ProgramAlbumUpdateEntity entity, String poolCode) {
        extracted(poolCode, entity);

        log.info("star update param {}", entity);

        Script script = new Script(ScriptType.INLINE, "painless", "ctx._source.minorsPoolCode = params.minorsPoolCode",
                Collections.singletonMap(MINORS_POOL_CODE, entity.getMinorsPoolCode()));

        UpdateRequest updateRequest = new UpdateRequest(IndexNameConstant.APP_ALBUM_INDEX, entity.getId())
                .script(script);

        try {
            UpdateResponse updateResponse = this.restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
            return "UPDATED".equalsIgnoreCase(updateResponse.getResult().name()) ||
                    "CREATED".equalsIgnoreCase(updateResponse.getResult().name());
        } catch (IOException e) {
            log.error("adjustSingleApiSearchAlbum - error update album in es: {}", entity, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用sid 查询 "sid", "minorsPoolCode"
     * @param sidList
     * @return
     */
    private SearchRequest buildSearchRequest(List<String> sidList, String poolCode) {
        SearchRequest searchRequest = new SearchRequest(IndexNameConstant.APP_ALBUM_INDEX);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery(SID, sidList));
        boolQuery.mustNot(QueryBuilders.termQuery(MINORS_POOL_CODE, poolCode));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(ALBUM_MAX_COUNT);

        FetchSourceContext fetchSourceContext = new FetchSourceContext(true, new String[]{SID, MINORS_POOL_CODE}, null);
        searchSourceBuilder.fetchSource(fetchSourceContext);

        searchRequest.source(searchSourceBuilder);
        return searchRequest;
    }

    /**
     *
     * @param searchRequest
     * @param poolCode
     * @return
     */
    private List<ProgramAlbumUpdateEntity> executeSearch(SearchRequest searchRequest, String poolCode) {
        try {
            SearchResponse searchResponse = this.restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            org.elasticsearch.search.SearchHits searchHits = searchResponse.getHits();

            if (searchHits.getHits() != null && searchHits.getHits().length > 0) {
                return Arrays.stream(searchHits.getHits())
                        .map(hit -> {
                            ProgramAlbumUpdateEntity entity = JSON.parseObject(hit.getSourceAsString(), ProgramAlbumUpdateEntity.class);
                            if(Objects.isNull(entity)) {
                                return null;
                            }
                            entity.setId(hit.getId());
                            extracted(poolCode, entity);
                            return entity;
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        } catch (IOException e) {
            log.error("adjustApiSearchAlbum - error executing search request: {}", searchRequest, e);
        }
        return Collections.emptyList();
    }

    /**
     * 批量更新 minorsPoolCode。 返回结果为成功的数据
     *
     * @param albumUpdateEntities
     * @return
     */
    public List<ProgramAlbumUpdateEntity> bulkUpdate(List<ProgramAlbumUpdateEntity> albumUpdateEntities) {
        if (CollectionUtils.isEmpty(albumUpdateEntities)) {
            return Collections.emptyList();
        }

        Map<String, String> updates = albumUpdateEntities.stream()
                .collect(Collectors.toMap(ProgramAlbumUpdateEntity::getId, ProgramAlbumUpdateEntity::getMinorsPoolCode, (o, n) -> o));

        BulkRequest bulkRequest = new BulkRequest();

        for (Map.Entry<String, String> entry : updates.entrySet()) {
            String id = entry.getKey();
            String newMinorPoolCode = entry.getValue();

            UpdateRequest updateRequest = new UpdateRequest(IndexNameConstant.APP_ALBUM_INDEX, id)
                    .doc(XContentType.JSON, MINORS_POOL_CODE, newMinorPoolCode);
            bulkRequest.add(updateRequest);
        }

        try {
            // 执行批量更新
            BulkResponse bulkResponse = this.restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);

            BulkItemResponse[] items = bulkResponse.getItems();

            List<ProgramAlbumUpdateEntity> succeedResults = Arrays.stream(items)
                    .filter(item -> !item.isFailed())
                    .map(BulkItemResponse::getId)
                    .map(id -> albumUpdateEntities.stream()
                            .filter(a -> id.equals(a.getId()))
                            .findFirst()
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return succeedResults;

        } catch (IOException e) {
            log.error(" -minorsBulkUpdate error param: {} ", albumUpdateEntities, e);
            return Collections.emptyList();
        }
    }


    /**
     * 拼接多个 poolCode 的逻辑
     *
     * @param poolCode
     * @param album
     */
    private static void extracted(String poolCode, ProgramAlbumUpdateEntity album) {
        String minorsPoolCode = album.getMinorsPoolCode();
        if (StringUtil.isEmpty(minorsPoolCode)) {
            minorsPoolCode = poolCode;
        } else if(!minorsPoolCode.contains(poolCode)){
            minorsPoolCode += "," + poolCode;
        }
        album.setMinorsPoolCode(minorsPoolCode);
    }
    /**
     * 从 0个 或多个 移除 poolCode 的逻辑
     *
     * @param poolCode
     * @param album
     */
    private static void extractedByDel(String poolCode, ProgramAlbumUpdateEntity album) {
        String minorsPoolCode = album.getMinorsPoolCode();
        if (StringUtil.isNotEmpty(minorsPoolCode) && StringUtil.isNotEmpty(poolCode)) {
            StringBuilder sb = new StringBuilder(minorsPoolCode);

            // 移除所有的 poolCode
            int index;
            while ((index = sb.indexOf(poolCode)) != -1) {
                sb.delete(index, index + poolCode.length());
            }

            // 移除连续的逗号
            while (sb.indexOf(",,") != -1) {
                sb.replace(sb.indexOf(",,"), sb.indexOf(",,") + 2, ",");
            }

            // 去掉开头的逗号
            while (sb.length() > 0 && sb.charAt(0) == ',') {
                sb.deleteCharAt(0);
            }

            // 去掉结尾的逗号
            while (sb.length() > 0 && sb.charAt(sb.length() - 1) == ',') {
                sb.deleteCharAt(sb.length() - 1);
            }
            album.setMinorsPoolCode(sb.toString());
        }
    }


    /**
     * 正确的取值范围：[0,5]；
     * 0：未知 ;  获取 0——3 和兜底的池
     * 1 ：0<=x<3; 获取 0——3 和兜底的池
     * 2： 3<=x<8 ; 获取 0——8 和兜底的池
     * 3 ：8<=x<12 ; 获取 0——12 和兜底的池
     * 4 ：12<=x<16 ; 获取 0——16 和兜底的池
     * 5 ：16<=x<18 ; 获取 0——18 和兜底的池(全部)
     * >5   <0 获取兜底的池
     * 可使用 MinorsPoolCodeEnum 枚举进行传参
     *
     * @param ageCode 年龄类型
     * @return
     */
    private List<MinorPoolCodeRecord> getMinorPoolCodeRecords(Integer ageCode) {
        List<MinorPoolCodeRecord> minorPoolCodeRecords = null;
        try {
            // 获取所有配置的 青少年内容池
            minorPoolCodeRecords = this.lvContentPoolRpcApiProxy.getMinorsPoolCode(ageCode)
                    .get(2, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("lvContentPoolRpcApiProxy getMinorsPoolCode error,", e);
            throw new RuntimeException(e.getMessage());
        }
        return minorPoolCodeRecords;
    }

    /**
     * 分批获取数据
     * @param contentPoolCode
     * @return
     */
    private List<String> getItems(String contentPoolCode) {
        if (StringUtils.isEmpty(contentPoolCode)) {
            return Collections.emptyList();
        }

        int mun = 0;

        RpcResult<Integer> munRpcResult = FutureUtil.getFutureIgnoreException(this.lvContentItemRpcApiProxy.getItemCount(contentPoolCode));
        if (munRpcResult == null || munRpcResult.getData() == null || munRpcResult.getData() < 1) {
            mun = ALBUM_MAX_COUNT;
        }

        mun = munRpcResult.getData();
        int times = mun  / LIMIT + 1;
        List<String> records = new ArrayList<>(mun);
        for (int i = 1; i <= times; i++) {
            RpcResult<List<String>> rpcResult = FutureUtil.getFutureIgnoreException(
                    this.lvContentItemRpcApiProxy.getItemsByPoolCode(contentPoolCode, i, LIMIT));
            Optional.ofNullable(rpcResult)
                    .map(RpcResult::getData)
                    .ifPresent(stringList -> {
                        if (!CollectionUtils.isEmpty(stringList)) {
                            records.addAll(stringList);
                        }
                    });
        }
        return records;
    }


}
