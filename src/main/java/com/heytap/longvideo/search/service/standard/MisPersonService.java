package com.heytap.longvideo.search.service.standard;

import com.heytap.longvideo.client.media.entity.MisPerson;
import com.heytap.longvideo.client.media.entity.SaveOrUpdateResult;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.rpc.consumer.MisPersonRpcApiProxy;
import com.heytap.longvideo.search.service.common.IdGeneratorProxyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * @Description: 腾讯库 影人 - service
 * @Author: 80398885WT
 * @Date: 2025/6/25
 */
@Slf4j
@Service
public class MisPersonService {

    private final MisPersonRpcApiProxy misPersonRpcApiProxy;

    private final IdGeneratorProxyService idGeneratorProxyService;

    public MisPersonService(MisPersonRpcApiProxy misPersonRpcApiProxy,
                            IdGeneratorProxyService idGeneratorProxyService) {
        this.misPersonRpcApiProxy = misPersonRpcApiProxy;
        this.idGeneratorProxyService = idGeneratorProxyService;
    }

    public MisPerson buildAndSaveMisPerson(String castName, UnofficialAlbumEs unofficialAlbumEs) {
        if (StringUtils.isEmpty(castName) || Objects.isNull(unofficialAlbumEs)) {
            return null;
        }

        String source = unofficialAlbumEs.getSource();
        try {
            MisPerson misPerson;
            misPerson = misPersonRpcApiProxy.queryByCnName(castName);
            if (Objects.nonNull(misPerson)) {
                log.info("{} is already exist", castName);
                return misPerson;
            }

            String copyrightCode = unofficialAlbumEs.getCopyrightCode();
            String area = unofficialAlbumEs.getArea();

            misPerson = new MisPerson();
            String personSid = idGeneratorProxyService.generateSId(source, castName + "-" + System.currentTimeMillis() + "-" + source + "-" + copyrightCode + "-" + area);
            misPerson.setSid(personSid);
            misPerson.setStatus(1);
            misPerson.setSource(source);
            misPerson.setLocked(1);
            misPerson.setCnName(castName);
            misPerson.setSex("u");
            misPerson.setCreateTime(new Date());
            misPerson.setUpdateTime(misPerson.getCreateTime());
            SaveOrUpdateResult saveOrUpdateResult = misPersonRpcApiProxy.saveOrUpdateSafely(misPerson);
            if (Objects.isNull(saveOrUpdateResult)) {
                log.error("misPersonRpcApiProxy.saveOrUpdateSafely({}) return null", misPerson);
                return null;
            }

            if (!saveOrUpdateResult.isSuccess() && !saveOrUpdateResult.isAlreadyExists()) {
                log.error(saveOrUpdateResult.getMessage());
                return null;
            }

            return saveOrUpdateResult.getPerson();
        } catch (Exception e) {
            log.error("buildAndSaveMisPerson({}, {}) error:", castName, source, e);
            return null;
        }
    }
}