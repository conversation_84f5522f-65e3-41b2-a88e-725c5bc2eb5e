package com.heytap.longvideo.search.service.common;

import com.heytap.video.ad.common.entity.req.BuriedCommonReqProperty;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyRequest;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponse;
import com.oppo.browser.common.app.lib.strategy.MatchStrategyResponseItem;
import com.oppo.cpc.video.framework.lib.strategy.StrategyMatchService;
import com.oppo.cpc.video.framework.lib.utils.VipInfoUtil;
import com.oppo.cpc.video.framework.lib.vip.UserVipInfoRpcApi;
import com.oppo.trace.async.TraceFunction;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

@Service
@Slf4j
public class StrategyService {
    @Reference(providerAppId = "oppomobile-vip-rest", protocol = "dubbo", timeout = 500)
    private UserVipInfoRpcApi vipInfoRpcApi;
    @Autowired
    private StrategyMatchService strategyMatchService;

    /**
     * 获取搜索页命中策略
     * @param request
     * @return
     */
    public CompletableFuture<Map<String, MatchStrategyResponseItem>> matchSearchStrategy(
            BuriedCommonReqProperty request, String[] serviceIds){
        MatchStrategyRequest matchStrategyRequest = new MatchStrategyRequest();
        matchStrategyRequest.setSystemId(MatchStrategyRequest.VIDEO);
        matchStrategyRequest.setServiceIds(serviceIds != null ? serviceIds : new String[0]);
        matchStrategyRequest.setAttributeValues(request.getAttributeValues());
        if (request.getAttributeValues() != null && request.getAttributeValues().getDeviceType() == 5) {
            // 5表示find n2 flip，当成直板机
            matchStrategyRequest.getAttributeValues().setDeviceType(0);
        }
        return VipInfoUtil.buildVipInfoCf(request, matchStrategyRequest, vipInfoRpcApi)
                .thenComposeAsync(new TraceFunction<>(strategyRequest -> strategyMatchService.matchStrategy(strategyRequest)))
                .handle((response, e) -> {
                    if (null != e) {
                        log.error("matchStrategy error,req:{}", request, e);
                        return Collections.emptyMap();
                    }

                    if (response == null || response.getRet() != 0 || response.getResult() == null) {
                        return Collections.emptyMap();
                    }

                    //put策略结果
                    setStrategyResult(response, request, serviceIds);

                    return response.getResult();
                });
    }

    private void setStrategyResult(MatchStrategyResponse matchStrategyResponse, BuriedCommonReqProperty reqProperty, String[] serviceIds) {
        //用户条件策略的结果也put到StrategyResult中
        Map<String, MatchStrategyResponseItem> strategyMatchMap = new HashMap<>();
        if (ArrayUtils.isNotEmpty(serviceIds)) {
            //固定资源位策略的结果也put到StrategyResult中
            Stream.of(serviceIds).forEach(serviceId -> {
                MatchStrategyResponseItem item = matchStrategyResponse.getResult().get(serviceId);
                if (item != null) {
                    strategyMatchMap.put(serviceId, item);
                }
            });
        }
        reqProperty.setStrategyResult(strategyMatchMap);
    }
}
