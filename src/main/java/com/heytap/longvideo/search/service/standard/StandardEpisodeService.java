package com.heytap.longvideo.search.service.standard;

import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.StandardEpisodeEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.CmsEpisodeVo;
import com.heytap.longvideo.search.model.param.standard.CmsSearchEpisodeParams;
import com.heytap.longvideo.search.model.param.standard.SearchStandardEpisodeParams;
import com.heytap.longvideo.search.model.param.standard.StandardEpisodeVo;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class StandardEpisodeService {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    public void initData(List<StandardEpisodeEs> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("list null");
            return;
        }
        List<IndexQuery> indexQueries = new ArrayList<>();
        for (StandardEpisodeEs standardEpisodeEs : list) {
            setAlbumInfo(standardEpisodeEs);
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setObject(standardEpisodeEs);
            indexQueries.add(indexQuery);
        }
        restTemplate.bulkIndex(indexQueries, IndexCoordinates.of(StandardEpisodeEs.class.getAnnotation(Document.class).indexName()));
        IndexOperations indexOperations = restTemplate.indexOps(StandardEpisodeEs.class);
        indexOperations.refresh();
    }

    private void setAlbumInfo(StandardEpisodeEs standardEpisodeEs) {
        StandardAlbum standardAlbum = null;
        try {
            standardAlbum = standardAlbumRpcApi.getBySid(standardEpisodeEs.getSid()).get().getData();
        } catch (Exception e) {
            log.warn("call standardAlbum rpc api error!", e);
            return;
        }
        if (standardAlbum == null) {
            return;
        }
        standardEpisodeEs.setAlbumActor(standardAlbum.getActor());
        standardEpisodeEs.setAlbumArea(standardAlbum.getArea());
        standardEpisodeEs.setAlbumBrief(standardAlbum.getBrief());
        standardEpisodeEs.setAlbumCompleted(standardAlbum.getCompleted());
        standardEpisodeEs.setAlbumDirector(standardAlbum.getDirector());
        standardEpisodeEs.setAlbumInformation(standardAlbum.getInformation());
        standardEpisodeEs.setAlbumPayStatus(standardAlbum.getPayStatus());
        standardEpisodeEs.setAlbumProgramInfo(standardAlbum.getProgramInfo());
        standardEpisodeEs.setAlbumShowTime(standardAlbum.getShowTime());
        standardEpisodeEs.setAlbumStatus(standardAlbum.getStatus());
        standardEpisodeEs.setAlbumTitle(standardAlbum.getTitle());
        standardEpisodeEs.setAlbumYear(standardAlbum.getYear());
        standardEpisodeEs.setAlbumFeatureType(standardAlbum.getFeatureType());
        standardEpisodeEs.setScore(standardAlbum.getSourceScore());
        standardEpisodeEs.setAlbumHorizontalIcon(standardAlbum.getHorizontalIcon());
        standardEpisodeEs.setAlbumVerticalIcon(standardAlbum.getVerticalIcon());
        standardEpisodeEs.setAlbumProgramType(standardAlbum.getProgramType());
        standardEpisodeEs.setAlbumTags(standardAlbum.getTags());
        standardEpisodeEs.setAlbumLanguage(standardAlbum.getLanguage());
        standardEpisodeEs.setSourceAlbumId(standardAlbum.getSourceAlbumId());
    }

    public PageResponse<StandardEpisodeVo> searchEpisode(SearchStandardEpisodeParams nRequest) throws Exception {
        try {
            PageResponse<StandardEpisodeVo> response = new PageResponse<>();
            List<StandardEpisodeVo> voList = new ArrayList<StandardEpisodeVo>(nRequest.getPageSize());

            nRequest.setOrder("orderNum");

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getEpisodeQueryBuilder(nRequest))
                    .from((nRequest.getPageIndex() - 1) * nRequest.getPageSize())
                    .size(nRequest.getPageSize())
                    .trackTotalHits(true)
                    .sort(SortBuilders.fieldSort(nRequest.getOrder()).order(SortOrder.ASC));
            // .sort(SortBuilders.fieldSort("sid").order(SortOrder.DESC));

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_EPISODE_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                StandardEpisodeEs standardEpisode = JsonUtil.fromStr(hit.getSourceAsString(), StandardEpisodeEs.class);
                StandardEpisodeVo standardEpisodeVo = new StandardEpisodeVo();
                BeanUtils.copyProperties(standardEpisode, standardEpisodeVo);
                standardEpisodeVo.setOriginStatus(standardEpisode.getSourceStatus());
                standardEpisodeVo.setEpisode(standardEpisode.getEpisodeTerm().contains("-") ? standardEpisode.getEpisodeTerm() : String.valueOf(standardEpisode.getEpisode()));
                voList.add(standardEpisodeVo);
            }
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(nRequest.getPageIndex());
            response.setPageSize(nRequest.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / nRequest.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchEpisode fail", e);
            throw e;
        }
    }

    public PageResponse<CmsEpisodeVo> searchCmsEpisode(CmsSearchEpisodeParams nRequest) throws Exception {
        try {
            PageResponse<CmsEpisodeVo> response = new PageResponse<>();
            List<CmsEpisodeVo> voList = new ArrayList<>(nRequest.getPageSize());

            nRequest.setOrder("orderNum");

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getCmsEpisodeQueryBuilder(nRequest))
                    .from((nRequest.getPageIndex() - 1) * nRequest.getPageSize())
                    .size(nRequest.getPageSize())
                    .trackTotalHits(true)
                    .sort(SortBuilders.fieldSort(nRequest.getOrder()).order(SortOrder.ASC));
            // .sort(SortBuilders.fieldSort("sid").order(SortOrder.DESC));

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_EPISODE_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                StandardEpisodeEs standardEpisode = JsonUtil.fromStr(hit.getSourceAsString(), StandardEpisodeEs.class);
                CmsEpisodeVo cmsEpisodeVo = new CmsEpisodeVo();
                BeanUtils.copyProperties(standardEpisode, cmsEpisodeVo);
                cmsEpisodeVo.setOriginStatus(standardEpisode.getSourceStatus());
                cmsEpisodeVo.setEpisode(standardEpisode.getEpisodeTerm().contains("-") ? standardEpisode.getEpisodeTerm() : String.valueOf(standardEpisode.getEpisode()));
                cmsEpisodeVo.setId(standardEpisode.getEid());
                cmsEpisodeVo.setBrief(standardEpisode.getAlbumBrief());
                cmsEpisodeVo.setVerticalImage(standardEpisode.getVerticalIcon());
                cmsEpisodeVo.setHorizontalImage(standardEpisode.getHorizontalIcon());
                cmsEpisodeVo.setContentType(standardEpisode.getAlbumProgramType());
                voList.add(cmsEpisodeVo);
            }
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(nRequest.getPageIndex());
            response.setPageSize(nRequest.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / nRequest.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("searchEpisode fail", e);
            throw e;
        }
    }

    private QueryBuilder getEpisodeQueryBuilder(SearchStandardEpisodeParams nRequest) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                SearchStandardEpisodeParams.class, EsField.class);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fileName = field.getAnnotation(EsField.class).name();
            fileName = StringUtils.isNotEmpty(fileName) ? fileName : entry.getKey();
            if ("sid".equals(entry.getKey())) {
                if (nRequest.isPreciseFlag()) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(fileName, fieldValue));
                } else {
                    boolQueryBuilder.must(QueryBuilders.wildcardQuery(fileName, "*" + fieldValue + "*"));
                }
            } else if ("source".equals(entry.getKey()) && "funshion".equals(fieldValue)) {
                // 媒资后台直接添加 风行和微迪欧
                boolQueryBuilder.must(QueryBuilders.termsQuery(fileName, "huashi", "senyu", SourceEnum.FUNSHION_LONGVIDEO.getDataSource(), SourceEnum.WEIDIOU.getDataSource()));
            } else {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fileName, fieldValue));
            }
        }
        return boolQueryBuilder;
    }

    private QueryBuilder getCmsEpisodeQueryBuilder(CmsSearchEpisodeParams nRequest) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                CmsSearchEpisodeParams.class, EsField.class);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fileName = field.getAnnotation(EsField.class).name();
            fileName = StringUtils.isNotEmpty(fileName) ? fileName : entry.getKey();
            if ("sid".equals(entry.getKey())) {
                boolQueryBuilder.must(QueryBuilders.wildcardQuery(fileName, "*" + fieldValue + "*"));
            } else if("albumTitle".equals(entry.getKey())) {
                boolQueryBuilder.must(QueryBuilders.wildcardQuery("albumTitle.keyword", "*" + fieldValue + "*"));
            }else if ("source".equals(entry.getKey()) && "funshion".equals(fieldValue)) {
                // 媒资后台直接添加 风行和微迪欧
                boolQueryBuilder.must(QueryBuilders.termsQuery(fileName, "huashi", "senyu", SourceEnum.FUNSHION_LONGVIDEO.getDataSource(), SourceEnum.WEIDIOU.getDataSource()));
            } else if ("moreSource".equals(entry.getKey())) {
                Set<String> sources = Stream.of(fieldValue.split(",")).filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toSet());
                if(sources.contains("funshion")) {
                    sources.remove("funshion");
                    //  "funshion" 替换成其他多个源
                    sources.addAll(Stream.of("huashi", "senyu",
                                    SourceEnum.FUNSHION_LONGVIDEO.getDataSource(),
                                    SourceEnum.WEIDIOU.getDataSource())
                            .collect(Collectors.toList()));
                }
                boolQueryBuilder.must(QueryBuilders.termsQuery("source", sources));
            }else {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fileName, fieldValue));
            }
        }
        return boolQueryBuilder;
    }

    public void insertOrUpdate(StandardEpisodeEs es) {
        setAlbumInfo(es);
        try {
            restTemplate.save(es);
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardEpisode", e);
            throw e;
        }
    }

    public void delete(StandardEpisodeEs es) {
        try {
            restTemplate.delete(es.getEid(), IndexCoordinates.of(StandardEpisodeEs.class.getAnnotation(Document.class).indexName()));
        } catch (Exception e) {
            updateLog.error("insertOrUpdate standardEpisode", e);
            throw e;
        }
    }
}
