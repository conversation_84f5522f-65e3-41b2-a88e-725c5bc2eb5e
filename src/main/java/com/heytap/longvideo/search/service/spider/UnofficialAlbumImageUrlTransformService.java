package com.heytap.longvideo.search.service.spider;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.entity.UnofficialAlbumOriginalImageMappingOcs;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.rpc.consumer.UnofficialAlbumImageMappingRpcApiProxy;
import com.heytap.longvideo.search.service.common.ImageUploadHelperComponet;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import esa.commons.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.utils.FutureUtil.getFutureIgnoreException;

/**
 * <AUTHOR> ye mengsheng
 * @Description：全网图片转换处理
 * @Version: 1.0
 * @date 2025/7/1 上午11:15
 */

@Slf4j
@Service
public class UnofficialAlbumImageUrlTransformService implements SmartInitializingSingleton {

    private ThreadPoolExecutor threadPoolExecutor = null;

    private DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MMdd");

    @Autowired
    private UnofficialAlbumImageMappingRpcApiProxy unofficialAlbumImageMappingRpcApiProxy;

    @Autowired
    private ImageUploadHelperComponet imageUploadHelperComponet;

    @Autowired
    @Lazy
    private UnofficialAlbumService unofficialAlbumService;

    @HeraclesDynamicConfig(key = "unofficial.album.ocs.image.whitelist", fileName = "configure.properties")
    private List<String> supportedSources;

    /**
     * 小竖图
     */
    public static final String H_ICON = "hIcon";
    /**
     * 大竖图
     */
    public static final String H_IMG = "hImg";
    /**
     * 小横图
     */
    public static final String V_ICON = "vIcon";
    /**
     * 大横图
     */
    public static final String V_IMG = "vImg";


    /**
     * 生成ocs图片并同步到es
     */
    public Boolean generateImageAndSyncToEs(UnofficialAlbumEs item, boolean force, boolean updateEs) {
        try {
            if (filterBySourceAndStatus(item)) {
                return true;
            }
            String source = item.getSource();
            LocalDateTime now = LocalDateTime.now();
            String mmdd = now.format(dtf);
            CompletableFuture<RpcResult<List<UnofficialAlbumOriginalImageMappingOcs>>> originalUrlMappingOcsUrlCf = unofficialAlbumImageMappingRpcApiProxy.findByOriginalUrl(item.getSid());
            RpcResult<List<UnofficialAlbumOriginalImageMappingOcs>> rpcResult = getFutureIgnoreException(originalUrlMappingOcsUrlCf);
            if (rpcResult == null || rpcResult.getData() == null) {
                log.error("unofficialAlbumImageMappingRpcApiProxy.findByOriginalUrl() error");
                return false;
            }
            List<UnofficialAlbumOriginalImageMappingOcs> originUrlMappingOcsUrlList = rpcResult.getData();
            ArrayList<UnofficialAlbumOriginalImageMappingOcs> batchAddItems = new ArrayList<>();
            // 处理横图
            generateHImgOcs(item, force, source, mmdd, originUrlMappingOcsUrlList, batchAddItems, H_IMG);
            generateHIconOcs(item, force, source, mmdd, originUrlMappingOcsUrlList, batchAddItems, H_ICON);

            // 处理竖图
            generateVImgOcs(item, force, source, mmdd, originUrlMappingOcsUrlList, batchAddItems, V_IMG);
            generateVIconOcs(item, force, source, mmdd, originUrlMappingOcsUrlList, batchAddItems, V_ICON);
            unofficialAlbumImageMappingRpcApiProxy.batchAdd(batchAddItems);

            if (updateEs) {
                UnofficialAlbumEs updateImageOrIconEs = generateUpdateImageOrIconEs(item);
                unofficialAlbumService.saveOrUpdate(updateImageOrIconEs);
            }
            return true;
        } catch (Exception e) {
            log.error("generateImageAndSyncToEs error, sid:{}", item.getSid(), e);
            return false;
        }
    }

    private boolean filterBySourceAndStatus(UnofficialAlbumEs item) {
        return item.getSource() == null ||
                !supportedSources.contains(item.getSource()) ||
                item.getStatus() != 1;
    }

    private static UnofficialAlbumEs generateUpdateImageOrIconEs(UnofficialAlbumEs item) {
        UnofficialAlbumEs updateImageOrIconEs = new UnofficialAlbumEs();
        updateImageOrIconEs.setSid(item.getSid());

        updateImageOrIconEs.setHorizontalImage(item.getHorizontalImage());
        updateImageOrIconEs.setHorizontalImageOcs(item.getHorizontalImageOcs());
        updateImageOrIconEs.setHorizontalIcon(item.getHorizontalIcon());
        updateImageOrIconEs.setHorizontalIconOcs(item.getHorizontalIconOcs());

        updateImageOrIconEs.setVerticalImage(item.getVerticalImage());
        updateImageOrIconEs.setVerticalImageOcs(item.getVerticalImageOcs());
        updateImageOrIconEs.setVerticalIcon(item.getVerticalIcon());
        updateImageOrIconEs.setVerticalIconOcs(item.getVerticalIconOcs());
        return updateImageOrIconEs;
    }

    private void generateVIconOcs(UnofficialAlbumEs item, boolean force, String source, String mmdd, List<UnofficialAlbumOriginalImageMappingOcs> imageMappingOcsList, ArrayList<UnofficialAlbumOriginalImageMappingOcs> batchAddItems, String imageType) {
        if (StringUtils.isEmpty(item.getVerticalIcon())) {
            item.setVerticalIconOcs("");
            return;
        }
        List<UnofficialAlbumOriginalImageMappingOcs> list = imageMappingOcsList.stream()
                .filter(x -> x.getOriginalUrl().equals(item.getVerticalIcon()) && imageType.equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list) || force) {
            // 大小竖图相同，直接复用
            if (item.getVerticalIcon().equals(item.getVerticalImage()) && StringUtils.isNotEmpty(item.getVerticalImageOcs())) {
                item.setVerticalIconOcs(item.getVerticalImageOcs());
                UnofficialAlbumOriginalImageMappingOcs originalImageUrlMappingOcsUrl = buildOriginalImageUrlMappingOcsUrl(item.getVerticalIcon(), item.getVerticalImageOcs(), source, item.getSid(), imageType);
                batchAddItems.add(originalImageUrlMappingOcsUrl);
                return;
            }
            String verticalIconOcsUrl = imageUploadHelperComponet.uploadPic(item.getVerticalIcon(), item.getSid() + "_" + mmdd + imageType, item.getSid(), item.getSource());
            if (StringUtils.isNotBlank(verticalIconOcsUrl)) {
                item.setVerticalIconOcs(verticalIconOcsUrl);
                UnofficialAlbumOriginalImageMappingOcs originalImageUrlMappingOcsUrl = buildOriginalImageUrlMappingOcsUrl(item.getVerticalIcon(), verticalIconOcsUrl, source, item.getSid(), imageType);
                batchAddItems.add(originalImageUrlMappingOcsUrl);
            }
        } else {
            item.setVerticalIconOcs(list.get(0).getOcsUrl());
        }
    }

    private void generateHIconOcs(UnofficialAlbumEs item, boolean force, String source, String mmdd, List<UnofficialAlbumOriginalImageMappingOcs> imageMappingOcsList, ArrayList<UnofficialAlbumOriginalImageMappingOcs> batchAddItems, String imageType) {
        if (StringUtils.isEmpty(item.getHorizontalIcon())) {
            item.setHorizontalIconOcs("");
            return;
        }
        List<UnofficialAlbumOriginalImageMappingOcs> list = imageMappingOcsList.stream()
                .filter(x -> x.getOriginalUrl().equals(item.getHorizontalIcon()) && imageType.equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list) || force) {
            // 大小横图相同，直接复用
            if (item.getHorizontalIcon().equals(item.getHorizontalImage()) && StringUtils.isNotEmpty(item.getHorizontalImageOcs())) {
                item.setHorizontalIconOcs(item.getHorizontalImageOcs());
                UnofficialAlbumOriginalImageMappingOcs originalImageUrlMappingOcsUrl = buildOriginalImageUrlMappingOcsUrl(item.getHorizontalIcon(), item.getHorizontalImageOcs(), source, item.getSid(), imageType);
                batchAddItems.add(originalImageUrlMappingOcsUrl);
                return;
            }
            String horizontalIconOcsUrl = imageUploadHelperComponet.uploadPic(item.getHorizontalIcon(), item.getSid() + "_" + mmdd + imageType, item.getSid(), item.getSource());
            if (StringUtils.isNotBlank(horizontalIconOcsUrl)) {
                item.setHorizontalIconOcs(horizontalIconOcsUrl);
                UnofficialAlbumOriginalImageMappingOcs originalImageUrlMappingOcsUrl = buildOriginalImageUrlMappingOcsUrl(item.getHorizontalIcon(), horizontalIconOcsUrl, source, item.getSid(), imageType);
                batchAddItems.add(originalImageUrlMappingOcsUrl);
            }
        } else {
            item.setHorizontalIconOcs(list.get(0).getOcsUrl());
        }
    }

    private void generateVImgOcs(UnofficialAlbumEs item, boolean force, String source, String mmdd, List<UnofficialAlbumOriginalImageMappingOcs> imageMappingOcsList, ArrayList<UnofficialAlbumOriginalImageMappingOcs> batchAddItems, String imageType) {
        if (StringUtils.isEmpty(item.getVerticalImage())) {
            item.setVerticalImageOcs("");
            return;
        }
        List<UnofficialAlbumOriginalImageMappingOcs> list = imageMappingOcsList.stream()
                .filter(x -> x.getOriginalUrl().equals(item.getVerticalImage()) && imageType.equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list) || force) {
            String verticalImageOcsUrl = imageUploadHelperComponet.uploadPic(item.getVerticalImage(), item.getSid() + "_" + mmdd + imageType, item.getSid(), item.getSource());
            if (StringUtils.isNotBlank(verticalImageOcsUrl)) {
                item.setVerticalImageOcs(verticalImageOcsUrl);
                UnofficialAlbumOriginalImageMappingOcs originalImageUrlMappingOcsUrl = buildOriginalImageUrlMappingOcsUrl(item.getVerticalImage(), verticalImageOcsUrl, source, item.getSid(), imageType);
                batchAddItems.add(originalImageUrlMappingOcsUrl);
            }
        } else {
            item.setVerticalImageOcs(list.get(0).getOcsUrl());
        }
    }

    private void generateHImgOcs(UnofficialAlbumEs item, boolean force, String source, String mmdd, List<UnofficialAlbumOriginalImageMappingOcs> imageMappingOcsList, ArrayList<UnofficialAlbumOriginalImageMappingOcs> batchAddItems, String imageType) {
        if (StringUtils.isEmpty(item.getHorizontalImage())) {
            item.setHorizontalImageOcs("");
            return;
        }
        List<UnofficialAlbumOriginalImageMappingOcs> list = imageMappingOcsList.stream()
                .filter(x -> x.getOriginalUrl().equals(item.getHorizontalImage()) && imageType.equals(x.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list) || force) {
            String horizontalImageOcsUrl = imageUploadHelperComponet.uploadPic(item.getHorizontalImage(), item.getSid() + "_" + mmdd + imageType, item.getSid(), item.getSource());
            if (StringUtils.isNotBlank(horizontalImageOcsUrl)) {
                item.setHorizontalImageOcs(horizontalImageOcsUrl);
                UnofficialAlbumOriginalImageMappingOcs originalImageUrlMappingOcsUrl = buildOriginalImageUrlMappingOcsUrl(item.getHorizontalImage(), horizontalImageOcsUrl, source, item.getSid(), imageType);
                batchAddItems.add(originalImageUrlMappingOcsUrl);
            }
        } else {
            item.setHorizontalImageOcs(list.get(0).getOcsUrl());
        }
    }

    public UnofficialAlbumOriginalImageMappingOcs buildOriginalImageUrlMappingOcsUrl(String originalUrl, String ocsUrl, String source, String sid, String type) {
        UnofficialAlbumOriginalImageMappingOcs unofficialAlbumOriginalImageMappingOcs = new UnofficialAlbumOriginalImageMappingOcs();
        unofficialAlbumOriginalImageMappingOcs.setCreateTime(new Date());
        unofficialAlbumOriginalImageMappingOcs.setOriginalUrl(originalUrl);
        unofficialAlbumOriginalImageMappingOcs.setOcsUrl(ocsUrl);
        unofficialAlbumOriginalImageMappingOcs.setSource(source);
        unofficialAlbumOriginalImageMappingOcs.setStatus(1);
        unofficialAlbumOriginalImageMappingOcs.setSid(sid);
        unofficialAlbumOriginalImageMappingOcs.setType(type);
        return unofficialAlbumOriginalImageMappingOcs;
    }

    @Override
    public void afterSingletonsInstantiated() {
        ThreadFactory threadFactory = new CustomizableThreadFactory("all-web-image-ocs-pool-");
        threadPoolExecutor = new ThreadPoolExecutor(5, 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue(100), threadFactory);
    }
}