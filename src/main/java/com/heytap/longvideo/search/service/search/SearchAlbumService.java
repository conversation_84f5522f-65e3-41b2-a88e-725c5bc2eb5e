package com.heytap.longvideo.search.service.search;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.config.SourceFilterConfig;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.constants.CopyrightConstant;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.heytap.longvideo.search.service.app.ProgramMatchService;
import com.heytap.longvideo.search.service.common.VersionFilterService;
import com.heytap.longvideo.search.service.common.VirtualProgramService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.SensitiveWordUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.heytap.longvideo.search.liteflow.cmp.SearchAlbumCmp.SERIES_PATTERN;

/**
 *
 */
@Slf4j
@Service
public class SearchAlbumService {

    private final SourceFilterConfig sourceFilterConfig;

    private final VersionFilterService versionFilterService;

    private final VirtualProgramService virtualProgramService;

    private final YoukuSourceFilterService youkuSourceFilterService;

    private final ProgramMatchService programMatchService;

    private final HotVideoService hotVideoService;

    private final SearchProperties searchProperties;

    public SearchAlbumService(SourceFilterConfig sourceFilterConfig,
                              VersionFilterService versionFilterService,
                              VirtualProgramService virtualProgramService,
                              YoukuSourceFilterService youkuSourceFilterService,
                              ProgramMatchService programMatchService,
                              HotVideoService hotVideoService,
                              SearchProperties searchProperties) {
        this.sourceFilterConfig = sourceFilterConfig;
        this.versionFilterService = versionFilterService;
        this.virtualProgramService = virtualProgramService;
        this.youkuSourceFilterService = youkuSourceFilterService;
        this.programMatchService = programMatchService;
        this.hotVideoService = hotVideoService;
        this.searchProperties = searchProperties;
    }


    /**
     * 搜索结果过滤
     */
    public List<ProgramAlbumEs> filterByMatch(List<ProgramAlbumEs> list, String keyWord) {
        List<ProgramAlbumEs> returnList = new ArrayList<>();
        for (ProgramAlbumEs programAlbumEs : list) {
            if (keyWord.length() == 3 && programAlbumEs.getTitle().length() == 3) {
                char[] keyWordChar = keyWord.toCharArray();
                char[] titleChar = programAlbumEs.getTitle().toCharArray();
                int matchNum = 0;
                for (int i = 0; i < keyWordChar.length; i++) {
                    if (keyWordChar[i] == titleChar[i]) {
                        matchNum++;
                    }
                }
                if (matchNum < 2) {
                    continue;
                }
            }
            returnList.add(programAlbumEs);
        }
        return returnList;
    }

    /**
     * 优先级去重及版本过滤逻辑
     */
    public List<ProgramAlbumEs> filterVirtualProgram(List<ProgramAlbumEs> list, KeyWordSearchParamV2 param) {
        List<ProgramAlbumEs> returnList = new ArrayList<>();
        int versionTag = param.getVersionTag();
        for (ProgramAlbumEs programAlbumEs : list) {
            List<String> multipleSourceCode = new ArrayList<>();
            multipleSourceCode.add(programAlbumEs.getSource());
            programAlbumEs.setMultipleSourceCode(multipleSourceCode);
            if (programAlbumEs.getSid().equals(programAlbumEs.getVirtualSid()) && programAlbumEs.getHasVirtualSid() == 0) {
                //优酷内容,开关全开,下发升级页面,不会过滤
                if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(programAlbumEs.getSource())) {
                    if (1 == param.getIsOut() && 1 == sourceFilterConfig.getYoukuSwitch() && 1 == sourceFilterConfig.getYoukuthirdPartySwitch()) {
                        returnList.add(programAlbumEs);
                        continue;
                    } else if (0 == param.getIsOut() && 1 == sourceFilterConfig.getYoukuSwitch() && 1 == sourceFilterConfig.getYoukuVersionSwitch()) {
                        returnList.add(programAlbumEs);
                        continue;
                    }
                }
                if (!versionFilterService.versionFilter(versionTag, programAlbumEs, param.getAppId())) {
                    returnList.add(programAlbumEs);
                }
            } else {
                programAlbumEs = virtualProgramService.getProgramAlbumByVipType(versionTag, programAlbumEs, param.getVipType(),
                        null, param.getQuickEngineVersion(), param.getVersion(), param.getIsOut(), param.getAppId());
                //可能会再次查询出优酷，过滤
                if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(programAlbumEs.getSource())) {
                    if (1 == param.getIsOut() && youkuSourceFilterService.thirdPartyFilter(
                            param.getQuickEngineVersion(), param.getVersion(), param.getAppId())) {
                        continue;
                    } else if (0 == param.getIsOut() && youkuSourceFilterService.filterItem(param.getVersion())) {
                        continue;
                    }
                }
                multipleSourceCode = new ArrayList<>();
                String sourceCode = getSourceCode(programAlbumEs.getSource());
                multipleSourceCode.add(sourceCode);
                for (String s : programAlbumEs.getMultipleSourceCode()) {
                    List<String> allowSource = CommonConstant.versionTagAllowSource.get(versionTag);
                    if (!allowSource.contains(s)) {
                        continue;
                    }
                    s = getSourceCode(s);
                    if (!multipleSourceCode.contains(s)) {
                        multipleSourceCode.add(s);
                    }
                }

                programAlbumEs.setMultipleSourceCode(multipleSourceCode);
                returnList.add(programAlbumEs);
            }
        }

        // 虚拟节目并没有统计预告片，此处加上：同时有同内容的预告片和正片时，过滤预告片逻辑
        filterYGBySameProgram(returnList);
        log.info("search response:{}", JSON.toJSONString(returnList));
        return returnList;
    }

    private void filterYGBySameProgram(List<ProgramAlbumEs> returnList) {
        if (CollectionUtils.isEmpty(returnList) || returnList.size() <= 1 || returnList.size() > 100) {
            return;
        }
        List<ProgramAlbumEs> copyReturnList = new ArrayList<>(returnList);
        for (int i = 0; i < copyReturnList.size() - 1; i++) {
            ProgramAlbumEs firstAlbum = copyReturnList.get(i);
            for (int j = i + 1; j < copyReturnList.size(); j++) {
                ProgramAlbumEs secondAlbum = copyReturnList.get(j);
                if (firstAlbum.getFeatureType() == 1 && secondAlbum.getFeatureType() == 2 && programMatchService.matchProgramScore(firstAlbum, secondAlbum) >= 60) {
                    // 移除同内容的预告片
                    returnList.removeIf(albumEs -> secondAlbum.getSid().equals(albumEs.getSid()));
                }
                if (firstAlbum.getFeatureType() == 2 && secondAlbum.getFeatureType() == 1 && programMatchService.matchProgramScore(firstAlbum, secondAlbum) >= 60) {
                    // 移除同内容的预告片
                    returnList.removeIf(albumEs -> firstAlbum.getSid().equals(albumEs.getSid()));
                }
            }
        }
    }

    private String getSourceCode(String source) {
        if (CopyrightConstant.COPYRIGHT_SENYU.equals(source) || CopyrightConstant.COPYRIGHT_LETV.equals(source) || CopyrightConstant.COPYRIGHT_YST.equals(source)
                || CopyrightConstant.COPYRIGHT_FUNSHION_LONGVIDEO.equals(source) || CopyrightConstant.COPYRIGHT_WEIDIOU.equals(source)) {
            source = CopyrightConstant.COPYRIGHT_HUASHI;
        }
        return source;
    }

    /**
     * 结果排序
     */
    public List<ProgramAlbumEs> keywordSearchSort(List<ProgramAlbumEs> list, String keyword) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<ProgramAlbumEs> seriesAlbumList = new ArrayList<>();
        List<ProgramAlbumEs> termMatchList = new ArrayList<>();
        int seriesAlbumStartIndex = -1;
        try {
            for (ProgramAlbumEs programAlbumEs : list) {
                programAlbumEs.setSortDefine(programAlbumEs.getReleScore());
                if (keyword.equals(programAlbumEs.getTitle())) {
                    termMatchList.add(programAlbumEs);
                    programAlbumEs.setSortDefine(programAlbumEs.getSortDefine() * 100);
                } else if (programAlbumEs.getTitle().startsWith(keyword)) {
                    programAlbumEs.setSortDefine(programAlbumEs.getSortDefine() * 2.5);
                }
                if ("sohu".equals(programAlbumEs.getSource())) {
                    programAlbumEs.setSortDefine(programAlbumEs.getSortDefine() * 0.8);
                }
                if (searchProperties.getHotKeyWordSwitch() == 1) {
                    addPriorityByHotVideo(programAlbumEs);
                }
            }
        } catch (Exception e) {
            log.error("keywordSearchSort error");
        }
        list = list.stream().sorted(Comparator.comparing(ProgramAlbumEs::getSortDefine).reversed().thenComparing(ProgramAlbumEs::getSid)).collect(Collectors.toList());
        for (int i = 0; i < list.size(); i++) {
            ProgramAlbumEs programAlbumEs = list.get(i);
            if (programAlbumEs.getIsSeries() == 1 && !CopyrightConstant.COPYRIGHT_SOHU.equals(programAlbumEs.getSource())
                    && !programAlbumEs.getTitle().contains("英文版")
                    && (programAlbumEs.getTitle().contains("第") || SERIES_PATTERN.matcher(programAlbumEs.getTitle()).find())) {
                if (seriesAlbumStartIndex == -1) {
                    seriesAlbumStartIndex = i;
                }
                seriesAlbumList.add(programAlbumEs);
            }
        }
        if (seriesAlbumList.size() > 1) {
            list.removeAll(seriesAlbumList);
            seriesAlbumList = seriesAlbumList.stream().sorted(Comparator.comparing(ProgramAlbumEs::getShowTime).reversed()).collect(Collectors.toList());
            list.addAll(seriesAlbumStartIndex, seriesAlbumList);
        }
        if (!termMatchList.isEmpty() && !keyword.equals(list.get(0).getTitle())) {
            list.removeAll(termMatchList);
            list.addAll(0, termMatchList);
        }
        return list;
    }

    private void addPriorityByHotVideo(ProgramAlbumEs es) {
        try {
            ContentTypeEnum contentTypeEnum = ContentTypeEnum.getByCode(es.getContentType());
            if (contentTypeEnum == null) {
                return;
            }
            ConcurrentHashMap concurrentHashMap = hotVideoService.getHashMap(contentTypeEnum);
            if (concurrentHashMap == null) {
                return;
            }
            boolean isHotKey = SensitiveWordUtil.isContaintSensitiveWord(es.getTitle(), concurrentHashMap);
            if (isHotKey) {
                es.setSortDefine(es.getSortDefine() * 3);
            }
        } catch (Exception e) {
            log.error("addPriorityByHotVideo error", e);
        }
    }
}