package com.heytap.longvideo.search.service.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.heytap.longvideo.client.arrange.entity.LvContentItem;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.enums.TemplateDateTypeEnum;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.model.LongVideoSearchCard;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.ArrangeRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.ImageTagRpcApiProxy;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.video.client.entity.draw.VideoDrawer;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.heytap.video.client.entity.drawitem.SourceInfo;
import com.heytap.video.client.entity.drawitem.VideoDrawerItem;
import com.heytap.video.client.entity.list.VideoPageListV3;
import com.heytap.video.client.entity.video.AlbumDetailVO;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideo;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yanping
 * @date 2023/10/23
 */
@Slf4j
@Service
public class SearchIntentService {

    @Autowired
    private ConvertResponseService convertResponseService;

    @Autowired
    private ArrangeRpcApiProxy arrangeRpcApiProxy;

    @Autowired
    private SearchProperties searchProperties;


    public CompletableFuture<Pair<List<KeyWordSearchResponse>, Boolean>> getItemByContentPool(String code, Integer pageIndex, Integer pageSize) {
        List<KeyWordSearchResponse> returnList = new ArrayList<>();
        return arrangeRpcApiProxy.getLvContentItems(code, 1, pageIndex, pageSize).handleAsync((result, e) -> {
            if (result == null || CollectionUtils.isEmpty(result.getRecords())) {
                return new ImmutablePair<>(returnList, false);
            }
            for (LvContentItem lvContentItem : result.getRecords()) {
                KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
                keyWordSearchResponse.setSid(lvContentItem.getLinkValue());
                if (Objects.equals(lvContentItem.getContentType(), TemplateLinkTypeEnum.TRIAL.getCode())) {
                    keyWordSearchResponse.setLinkValue(lvContentItem.getLinkSid());
                }
                returnList.add(keyWordSearchResponse);
            }
            return new ImmutablePair<>(returnList, result.hasNext());
        });
    }

    public List<KeyWordSearchResponse> handleContentPoolPageResult(Page<LvContentItem> page, LvSearchIntervene lvSearchIntervene, KeyWordSearchParamV2 requestParam,Integer sortType) {
        List<KeyWordSearchResponse> returnList = new ArrayList<>();
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return returnList;
        }
        for (LvContentItem lvContentItem : page.getRecords()) {
            KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
            keyWordSearchResponse.setSid(lvContentItem.getLinkValue());
            if (Objects.equals(lvContentItem.getContentType(), TemplateLinkTypeEnum.TRIAL.getCode())) {
                keyWordSearchResponse.setLinkValue(lvContentItem.getLinkSid());
            }
            returnList.add(keyWordSearchResponse);
        }
        return handleItemList(returnList, lvSearchIntervene, requestParam,sortType);
    }


    private List<KeyWordSearchResponse> handleItemList(List<KeyWordSearchResponse> itemList, LvSearchIntervene lvSearchIntervene, KeyWordSearchParamV2 requestParam,Integer sortType) {
        List<String> sidList = itemList.stream().map(s -> s.getSid()).collect(Collectors.toList());
        return convertResponseService.getResponseBySidList(sidList, lvSearchIntervene.getIsShowMarkCode(), requestParam, sortType);
    }

    /**
     * 聚合卡二级页v1接口转化出参（已废弃）
     */
    @Deprecated
    public LongVideoSearchCard searchToLongVideo(SearchInterveneCardResponse searchCardResponse) {
        if (searchCardResponse == null) {
            log.error("searchCardResponse is null");
            return null;
        }
        LongVideoSearchCard response = new LongVideoSearchCard();
        BeanUtils.copyProperties(searchCardResponse, response, "contents");
        List<LongVideo> contents = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(searchCardResponse.getContents())) {
            for (KeyWordSearchResponse content : searchCardResponse.getContents()) {
                LongVideo longVideo = new LongVideo();
                BeanUtils.copyProperties(content, longVideo);
                longVideo.setId(content.getSid());
                longVideo.setHorizontalIcon(Collections.singletonList(content.getHorizontalIcon()));
                longVideo.setVerticalIcon(Collections.singletonList(content.getVerticalIcon()));
                BigDecimal bigDecimal = BigDecimal.valueOf(content.getSourceScore());
                longVideo.setSourceScore(bigDecimal.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue());
                contents.add(longVideo);
            }
        }
        response.setContents(contents);
        return response;
    }

    /**
     * 聚合卡二级页v2接口转化出参
     */
    public VideoPageListV3 searchToPageListV3(SearchInterveneCardResponse searchCardResponse,
                                              TemplateDateTypeEnum dataTypeEnum, Integer version) {
        if (searchCardResponse == null) {
            log.error("searchCardResponse is null");
            return null;
        }
        VideoPageListV3 response = new VideoPageListV3();
        response.setHasMore(searchCardResponse.getHasMore());

        VideoDrawer videoDrawer = new VideoDrawer();
        BeanUtils.copyProperties(searchCardResponse, videoDrawer, "contents");
        videoDrawer.setDataType(dataTypeEnum.getCode());

        List<VideoDrawerItem> contents = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(searchCardResponse.getContents())) {
            for (KeyWordSearchResponse content : searchCardResponse.getContents()) {
                LvDrawerItemVO lvDrawerItemVO = new LvDrawerItemVO();
                BeanUtils.copyProperties(content, lvDrawerItemVO);
                lvDrawerItemVO.setLinkType(1);
                lvDrawerItemVO.setLinkValue(content.getSid());
                lvDrawerItemVO.setRecommendInfo(StringUtils.defaultIfEmpty(content.getRecommendInfo(), content.getBrief()));
                lvDrawerItemVO.setAllowChaseAlbum(1);

                if (StringUtils.isNotEmpty(content.getTags())) {
                    AlbumDetailVO albumDetailVO = new AlbumDetailVO();
                    albumDetailVO.setTags(Collections.singletonList(content.getTags()));
                    lvDrawerItemVO.setAlbumDetail(albumDetailVO);
                }

                List<String> multipleSourceCode = content.getMultipleSourceCode();
                if (CollectionUtils.isNotEmpty(multipleSourceCode)) {
                    List<SourceInfo> sourceInfoList = multipleSourceCode.stream().map(source -> {
                        SourceInfo sourceInfo = new SourceInfo();
                        sourceInfo.setSource(source);
                        return sourceInfo;
                    }).collect(Collectors.toList());
                    lvDrawerItemVO.setSourceList(sourceInfoList);
                }

                //根据showSwitch赋值更新信息或评分
                Integer showSwitch = 0;
                //角标展示逻辑
                if (StringUtils.isNotEmpty(content.getMarkCodeUrl())) {
                    lvDrawerItemVO.setMarkCodeUrl(content.getMarkCodeUrl());
                    showSwitch = showSwitch + 4;
                }
                showSwitch = handleShowMsg(content, lvDrawerItemVO, showSwitch, version);
                lvDrawerItemVO.setShowSwitch(showSwitch);

                VideoDrawerItem videoDrawerItem = new VideoDrawerItem(lvDrawerItemVO);
                contents.add(videoDrawerItem);
            }
        }
        videoDrawer.setContents(contents);
        response.setElements(Collections.singletonList(videoDrawer));
        return response;
    }

    /**
     * 处理封面右下角文案
     */
    public Integer handleShowMsg(KeyWordSearchResponse content, LvDrawerItemVO lvDrawerItemVO, Integer showSwitch, Integer version) {
        //电影展示评分, 过滤掉低于6分的评分
        if (ContentTypeEnum.MOVIE.getCode().equals(content.getContentType())) {
            if (content.getSourceScore() >= 6) {
                lvDrawerItemVO.setShowMsg(String.format("%.1f", content.getSourceScore()));
            } else {
                lvDrawerItemVO.setShowMsg(null);
            }
            return showSwitch + 1;
        } else {
            // 8.9之前版本直接取programInfo
            if (version == null || version < searchProperties.getSearchCardOptVersion()) {
                lvDrawerItemVO.setShowMsg(content.getProgramInfo());
            } else if (StringUtils.isNotBlank(content.getProgramInfo())) {
                lvDrawerItemVO.setShowMsg(content.getProgramInfo().startsWith("更新至") ? "更新中" : content.getProgramInfo());
            }
            return showSwitch + 2;
        }
    }
}
