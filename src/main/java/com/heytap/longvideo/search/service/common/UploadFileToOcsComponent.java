package com.heytap.longvideo.search.service.common;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.heytap.longvideo.search.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR> ye mengsheng
 * @date 2024/9/6 上午11:32
 */
@Slf4j
@Component
public class UploadFileToOcsComponent {

    @Autowired
    @Qualifier("upLoadToOcsThreadPool")
    private Executor upLoadToOcsThreadPool;

    /**
     * 不管上传成功与否，本地文件会被删除
     */
    public boolean uploadFileToOcs(String accessKeyId, String accessKeySecret, String endPoint, String region
            , String bucketName, String fileName, String localPath, String ocsPath) {

        String localFile = StringUtil.isEmpty(localPath) ? fileName : localPath + "/" + fileName;
        String ocsFile = StringUtil.isEmpty(ocsPath) ? fileName : ocsPath + "/" + fileName;

        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKeyId, accessKeySecret);
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        ClientConfiguration clientConfiguration = new ClientConfiguration()
                .withProtocol(Protocol.HTTP);

        AmazonS3 s3 = AmazonS3ClientBuilder.standard().withCredentials(awsCredentialsProvider)
                // .withPathStyleAccessEnabled(true)  // 这里是设置path方式访问，一般不需要设置
                .withClientConfiguration(clientConfiguration)
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endPoint, region))
                .build();

        long partSize = 16 * 1024 * 1024; //分片大小，超大文件需要调大该参数
        TransferManagerBuilder builder = TransferManagerBuilder.standard()
                .withS3Client(s3)
                .withMultipartUploadThreshold(3 * partSize)
                .withMinimumUploadPartSize(partSize)
                .withShutDownThreadPools(false) //若不同文件上传用的同一个线程池需加上此项
                .withExecutorFactory(() -> (ExecutorService) upLoadToOcsThreadPool);
        TransferManager manager = builder.build();
        //put object to bucket
        try {
            PutObjectRequest input = new PutObjectRequest(bucketName, ocsFile, new File(localFile));
            Upload upload = manager.upload(input);
            upload.waitForUploadResult();
            return true;
        } catch (AmazonS3Exception exception) {//AmazonS3Exception
            int statusCode = (exception.getStatusCode());
            if (statusCode / 100 == 4) {
                log.error("an error occurred in client,{}", exception.getErrorResponseXml());
            } else if (statusCode / 100 == 5) {
                log.error("an error occurred in ocs,{}", exception.getErrorResponseXml());
            }
            return false;
        } catch (SdkClientException exception) {
            log.error("Caught an AmazonClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with S3, "
                    + "such as not being able to access the network.Error Message: {}", exception.getMessage());
            return false;
        } catch (InterruptedException e) {
            log.error("an error occurred in client");
            return false;
        } finally {
            new File(localFile).delete();
        }
    }
}

