package com.heytap.longvideo.search.service.app;

import com.google.common.base.Function;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/8/26
 */
@Slf4j
@Component
public class ProgramMatchService {

    public static List<String> needCheckTitleColumnStr = Stream.of("DVD版", "3D", "4K", "4k", "上", "下", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "前", "后", "HDR", "hdr", "atmos", "Atmos", "续集").collect(Collectors.toList());

    public static final Map<String, Integer> NEED_REPLACE_TITLE_COLUMN_MAP = Collections.unmodifiableMap(
            new HashMap<String, Integer>() {{
                put("一", 1);
                put("二", 2);
                put("三", 3);
                put("四", 4);
                put("五", 5);
                put("六", 6);
                put("七", 7);
                put("八", 8);
                put("九", 9);
                put("十", 10);
                put("廿", 20);
            }});

    private static final Set<String> LANGUAGE = new HashSet<String>() {
        {
            add("国语");
            add("普通话");
        }
    };

    /**
     * 计算两个节目的相似分数
     * @param firstAlbum
     * @param secondAlbum
     * @return
     */
    public int matchProgramScore(ProgramAlbumEs firstAlbum, ProgramAlbumEs secondAlbum) {

        // 1. 节目类型--影片分类为空，或者类型不同，直接返回0
        if (StringUtil.isBlank(firstAlbum.getContentType()) || !firstAlbum.getContentType().equalsIgnoreCase(secondAlbum.getContentType())) {
            return 0;
        }
        int score = 0;

        // 1.标题
        score += calcTitleScore(firstAlbum, secondAlbum);

        // 2. 年代---不存在  默认为数据缺失---数据缺失给3分，都存子 ，再进行对比给分
        if (firstAlbum.getYear() == null || secondAlbum.getYear() == null) {
            score += 3;
        } else if (firstAlbum.getYear() > 0 && String.valueOf(secondAlbum.getYear()).matches("\\d+")) {
            score += judgeYear(firstAlbum.getYear(), secondAlbum.getYear());
        }

        // 3. 导演字段匹配 ---全部命中，20分，相同值有一半10分，以此类推
        score += calcActorOrDirectorScore(firstAlbum.getDirector(), secondAlbum.getDirector());

        // 4. 完全匹配actor字段--全部命中，20分，相同值有一半10分，以此类推
        score += calcActorOrDirectorScore(firstAlbum.getActor(), secondAlbum.getActor());

        //5. 匹配area字段
        if (StringUtil.isNotEmpty(firstAlbum.getArea())
                && judgeArea(firstAlbum.getArea(), secondAlbum.getArea())) {
            score += 10;
        }

        // 6.匹配language字段
        if (StringUtil.isNotEmpty(firstAlbum.getLanguage())
                && (judgeLanguage(firstAlbum.getLanguage(), secondAlbum.getLanguage())
                || (LANGUAGE.contains(firstAlbum.getLanguage()) && LANGUAGE.contains(secondAlbum.getLanguage())))) {
            score += 10;
        } else if (StringUtils.isBlank(firstAlbum.getLanguage()) || StringUtils.isBlank(secondAlbum.getLanguage())
                || firstAlbum.getLanguage().contains("其他") || secondAlbum.getLanguage().contains("其他")) {
            score += 5;
        } else {
            score -= 30;
        }

        log.info("program match score : {}, info : {}, misprogram sid : {}, title : {}, director : {}, actor : {}, area : {}, " +
                        "language : {}, year : {}",
                score, firstAlbum.toString(), secondAlbum.getSid(), secondAlbum.getTitle(), secondAlbum.getDirector(), secondAlbum.getActor(),
                secondAlbum.getArea(), secondAlbum.getLanguage(), secondAlbum.getYear());
        return score;
    }

    private int calcTitleScore(ProgramAlbumEs firstAlbum, ProgramAlbumEs secondAlbum) {
        int titleScore = 0;
        if (StringUtil.isNotEmpty(firstAlbum.getTitle()) && StringUtil.isNotEmpty(secondAlbum.getTitle())) {
            // 一个包含书名号，一个不包含，则标题匹配分值为0
            if ((firstAlbum.getTitle().indexOf("》") > 0 && !secondAlbum.getTitle().contains("》"))
                    || (secondAlbum.getTitle().indexOf("》") > 0 && !firstAlbum.getTitle().contains("》"))) {
                titleScore = -10;
            } else {
                // 满分30分
                titleScore = judgeTitle(firstAlbum.getTitle(), secondAlbum.getTitle(), firstAlbum.getEpstitle(), secondAlbum.getEpstitle());
            }

        }
        return titleScore;
    }

    public int calcActorOrDirectorScore(String first, String second) {
        int score = 0;
        if (StringUtil.isNotBlank(first) && StringUtil.isNotBlank(second)) {
            List<String> firstList = Stream.of(first.trim().split("\\|"))
                    .map(ProgramMatchService::removeSpecialCharacter).collect(Collectors.toList());
            List<String> secondList = Stream.of(second.trim().split("\\|"))
                    .map(ProgramMatchService::removeSpecialCharacter).collect(Collectors.toList());
            if (firstList.size() >= secondList.size()) {
                int sameSize = (int) secondList.parallelStream().filter(firstList::contains).count();
                score = (int) (20 * ((double) sameSize / firstList.size()));
            } else {
                int sameSize = (int) firstList.parallelStream().filter(secondList::contains).count();
                score = (int) (20 * ((double) sameSize / secondList.size()));
            }
        }
        return score;
    }

    private int judgeTitle(String titleA,String titleB, String titleSubA, String titleSubB) {
        // 移除括号内和特殊字符，保留汉字，字母，数字，书名号
        titleA = getTitle(fixTitle(titleA));
        titleB = getTitle(fixTitle(titleB));
        titleSubA = getTitle(fixTitle(titleSubA));
        titleSubB = getTitle(fixTitle(titleB));
        if (titleA.equalsIgnoreCase(titleSubB)) {
            return 30;
        }
        if (matchRandomCharacter(titleB, titleA) && StringUtil.isNotEmpty(titleSubB)
                && (titleA.equalsIgnoreCase(titleSubB) || titleSubB.equalsIgnoreCase(titleA))) {
            return 20;
        }
        if (matchRandomCharacter(titleB, titleA) && (StringUtil.isNotEmpty(titleSubA)
                && (titleB.equalsIgnoreCase(titleSubA) || titleSubA.equalsIgnoreCase(titleB)))) {
            return 20;
        }
        if (matchRandomCharacter(titleB, titleA) && (titleA.contains(titleB) || titleB.contains(titleA))) {
            return 15;
        }
        return 0;
    }

    /**
     * 判断年份是否相同
     * @param yearA
     * @param yearB
     * @return
     */
    private int judgeYear(int yearA, int yearB) {
        // 完全相等10分   相差1年 3分，原因，不同CP方有1年的时间差
        return yearA == yearB ? 10 : yearA - yearB < 2 || yearA - yearB > -2 ? 3 : 0;
    }

    /**
     * 判断地区是否相同
     * @param areaA
     * @param areaB
     * @return
     */
    private boolean judgeArea(String areaA, String areaB) {
        if (StringUtil.isEmpty(areaA) || StringUtil.isEmpty(areaB)) {
            return false;
        }
        Function<String, String[]> areaFunction = (String area) -> {
            if (area.indexOf(",") > 0) {
                return area.trim().split(",");
            } else if (area.indexOf("|") > 0) {
                return area.trim().split("\\|");
            }
            return new String[]{area};
        };
        Set<String> areaAset = Stream.of(areaFunction.apply(areaA)).collect(Collectors.toSet());
        Set<String> areaBset = Stream.of(areaFunction.apply(areaB)).collect(Collectors.toSet());
        boolean flag = false;
        for (String str : areaAset) {
            if (areaBset.contains(str)) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    private boolean judgeLanguage(String languageA, String languageB) {
        if (StringUtil.isEmpty(languageA) || StringUtil.isEmpty(languageB)) {
            return false;
        }
        Function<String, String[]> areaFunction = (String language) -> {
            if (language.indexOf(",") > 0) {
                return language.trim().split(",");
            } else if (language.indexOf("|") > 0) {
                return language.trim().split("\\|");
            }
            return new String[]{language};
        };
        Set<String> languageAset = Stream.of(areaFunction.apply(languageA)).collect(Collectors.toSet());
        Set<String> languageBset = Stream.of(areaFunction.apply(languageB)).collect(Collectors.toSet());
        for (String str : languageAset) {
            if (languageBset.contains(str)) {
                return true;
            }
        }
        return false;
    }


    public static boolean matchRandomCharacter(String stringA,String stringB) {
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(stringA);
        String aRandom = m.replaceAll("").trim();
        m = p.matcher(stringB);
        String bRandom = m.replaceAll("").trim();
        return aRandom.equalsIgnoreCase(bRandom);
    }


    private static String fixTitle(String title) {
        if (org.apache.commons.lang.StringUtils.isBlank(title) || title.equalsIgnoreCase("\\N")) {
            return "";
        }
        String patternBracketA = "\\[.*?\\]";
        String patternBracketB = "\\（.*?\\）";
        String patternBracketC = "\\(.*?\\)";
        String patternBracketABC = "\\[.*?\\]|\\(.*?\\)|\\（.*?\\）";
        Pattern patten = Pattern.compile(patternBracketABC);//编译正则表达式
        Matcher matcher = patten.matcher(title);// 指定要匹配的字符串
        Set<String> needStr = new HashSet<>();
        while (matcher.find()) {
            String insideTitleStr = matcher.group();
            needStr.addAll(needCheckTitleColumnStr.stream().filter(a -> insideTitleStr.indexOf(a) >= 0).collect(Collectors.toList()));
        }
        title = title.replaceAll(patternBracketA, "");
        title = title.replaceAll(patternBracketB, "");
        title = title.replaceAll(patternBracketC, "");
        // 替换 1 2 3 4 5为一二三。。。。为阿拉伯数字
        for (String str : NEED_REPLACE_TITLE_COLUMN_MAP.keySet()) {
            title = title.replaceAll(str, String.valueOf(NEED_REPLACE_TITLE_COLUMN_MAP.get(str)));
        }

        // 补充意义的字段
        if (!CollectionUtils.isEmpty(needStr)) {
            for (String str : needStr) {
                title = title + str;
            }
        }

        return title;
    }

    public static String getTitle(String title) {
        if(StringUtils.isEmpty(title)){
            return "";
        }
        // 去除括号中的内容
        title = title.replaceAll("\\(.*?\\)|\\{.*?}|\\[.*?]|（.*?）", "");
        // 移除title中特殊符号
        title = removeSpecialCharacter(title);
        return title;
    }


    public static String removeSpecialCharacter(String string) {
        String regEx = "[^0-9a-zA-Z_\\u4e00-\\u9fa5\\u2160-\\u217B\\《\\》]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(string);
        return m.replaceAll("").trim();
    }
}
