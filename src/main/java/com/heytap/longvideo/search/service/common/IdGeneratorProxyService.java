package com.heytap.longvideo.search.service.common;

import com.heytap.longvideo.client.media.enums.SourceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Author: 80339123 liu ying
 * @Date: 2021/10/18 15:24
 */
@Service
public class IdGeneratorProxyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IdGeneratorProxyService.class);

    final IdGeneratorService idGeneratorService;

    public IdGeneratorProxyService(IdGeneratorService idGeneratorService) {
        this.idGeneratorService = idGeneratorService;
    }

    /**
     * 生产Sid
     */
    public String generateSId(String source, String seed) {
        return idGeneratorService.generateId(source + seed);
    }

    /**
     * 生产Vid
     *
     * @param source
     * @param sourceVideoId
     * @return
     */
    public String generateVId(String source, String sourceVideoId) {
        if(SourceEnum.YST.getSpiSource().equals(source)){
            sourceVideoId =SourceEnum.YST.getSpiSource() + sourceVideoId;
        }
        return idGeneratorService.generateId(source + sourceVideoId);
    }

    /**
     * 生成EID
     *
     * @param source
     * @param sid
     * @param vid
     * @return
     */
    public String generateEid(String source, String sid, String vid) {
        return idGeneratorService.generateId(sid + "_" + vid);
    }

    /**
     * 生成TID
     *
     * @param source
     * @param sid
     * @param vid
     * @return
     */
    public String generateTid(String source, String sid, String vid) {
        return idGeneratorService.generateId(sid + "_" + vid);
    }

}
