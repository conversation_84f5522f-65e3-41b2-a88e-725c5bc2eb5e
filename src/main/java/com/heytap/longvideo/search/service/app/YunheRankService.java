package com.heytap.longvideo.search.service.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heytap.longvideo.client.media.enums.YunheRankKindEnum;
import com.heytap.longvideo.client.media.enums.YunheRankTypeEnum;
import com.heytap.longvideo.client.media.yunhe.YunheRank;
import com.heytap.longvideo.client.media.yunhe.YunheRankRequest;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.model.unofficial.response.YunheDetailInfo;
import com.heytap.longvideo.search.model.unofficial.response.YunheHotRankResp;
import com.heytap.longvideo.search.model.unofficial.response.YunheReboTop;
import com.heytap.longvideo.search.model.unofficial.response.YunheTopRankResp;
import com.heytap.longvideo.search.mq.YunheRankProviderService;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.YunheRankRpcApiProxy;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.JacksonUtil;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class YunheRankService {
    @Autowired
    private YunheRankRpcApiProxy yunheRankRpcApiProxy;

    @Autowired
    private HttpDataChannel httpDataChannel;

    @Autowired
    private UploadFileToOcsComponent fileToOcsComponent;

    @Autowired
    private CsvExportConfig csvExportConfig;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private YunheRankProviderService yunheRankProviderService;
    @Autowired
    private DouBanCommitTaskService douBanCommitTaskService;

    public static final String NAME = "u-s303-name";
    public static final String WORD = "pw-2001";

    public static final String OCS_PATH = "yunhe";

    public static final String REBO_API = "/yunhe/postForReboRank";
    public static final String TOP_API = "/yunhe/postForTopRank";
    public static final String HOT_API = "/yunhe/postForHotRank";
    public static final String DETAIL_API = "/yunhe/postForDetail";

    public static final List<String> channelTypes = Arrays.stream(YunheRankTypeEnum.values())
            .map(YunheRankTypeEnum::getCode)
            .collect(Collectors.toList());

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 获取云合排行榜
     */
    public void collect() {
        channelTypes.forEach(channelType -> {
            collectTopRank(channelType);
            collectHotRank(channelType);
        });
        // 云合榜单爬取结束。查询爬取的结果去豆瓣爬取
        searchMedia4Spider();
    }

    /**
     * 发送云合榜单的搜索词到域外进行爬取
     */
    public void searchMedia4Spider() {
        YunheRankRequest request = new YunheRankRequest();
        request.setLimitNum(searchProperties.getYunheDoubanTaskCommitNum());
        CompletableFuture<List<YunheRank>> yunHeRankListCF = yunheRankRpcApiProxy.queryListByDayAndLimitNum(request);
        List<YunheRank> yunHeRankList = FutureUtil.getFutureIgnoreException(yunHeRankListCF, 2, TimeUnit.SECONDS);
        if(CollectionUtils.isEmpty(yunHeRankList)) {
            log.warn("searchMedia4Spider yunHeRank data is null");
            return;
        }

        List<String> titles = yunHeRankList.stream()
                .map(YunheRank::getTitle)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        douBanCommitTaskService.commitDouBanTask(titles);
    }

    /**
     * 获取霸屏榜
     */
    public void collectTopRank(String channelType) {
        try {
            // 连续剧查单独的热播接口
            if (YunheRankTypeEnum.TV.getCode().equals(channelType)) {
                collectTopRank4Tv();
                return;
            }

            Map<String, String> param = new HashMap<>();
            param.put("channelType", channelType);
            YunheTopRankResp resp = post2Yunhe(TOP_API, param, new TypeReference<YunheTopRankResp>() {});
            if (resp == null || CollectionUtils.isEmpty(resp.getContent())) {
                return;
            }

            String day = Instant.ofEpochMilli(resp.getDate())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .format(FORMATTER);
            // 动漫榜没有返回排名，只能按数组顺序排
            AtomicInteger index = new AtomicInteger(1);
            List<YunheRank> rankList = resp.getContent().stream().map(topRank -> {
                Map<String, String> detailParam = new HashMap<>();
                detailParam.put("nameId", topRank.getNameID().toString());
                YunheDetailInfo detail = post2Yunhe(DETAIL_API, detailParam, new TypeReference<YunheDetailInfo>() {});
                return convertTopRank(topRank, detail, day, channelType, index.getAndIncrement());
            }).collect(Collectors.toList());

            yunheRankRpcApiProxy.batchUpsert(rankList);
            yunheRankProviderService.sendMQ(rankList);
        } catch (Exception e) {
            log.error("collectTopRank error", e);
        }
    }

    /**
     * 获取霸屏榜-连续剧
     */
    public void collectTopRank4Tv() {
        List<YunheReboTop> reboList = post2Yunhe(REBO_API, new HashMap<>(), new TypeReference<List<YunheReboTop>>() {});
        if (CollectionUtils.isEmpty(reboList)) {
            return;
        }
        List<YunheRank> rankList = reboList.stream().map(rebo -> {
            Map<String, String> param = new HashMap<>();
            param.put("nameId", rebo.getNameID().toString());
            YunheDetailInfo detail = post2Yunhe(DETAIL_API, param, new TypeReference<YunheDetailInfo>() {});
            return convertReboTop(rebo, detail);
        }).collect(Collectors.toList());

        yunheRankRpcApiProxy.batchUpsert(rankList);
        yunheRankProviderService.sendMQ(rankList);
    }

    /**
     * 获取热度榜
     */
    public void collectHotRank(String channelType) {
        try {
            // 动漫没有热度榜
            if (YunheRankTypeEnum.ANIMATION.getCode().equals(channelType)) {
                return;
            }

            Map<String, String> param = new HashMap<>();
            param.put("channelType", channelType);
            YunheHotRankResp resp = post2Yunhe(HOT_API, param, new TypeReference<YunheHotRankResp>() {});
            if (resp == null || CollectionUtils.isEmpty(resp.getContent())) {
                return;
            }

            String day = Instant.ofEpochMilli(resp.getDate())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .format(FORMATTER);
            // 动漫榜没有返回排名，只能按数组顺序排
            AtomicInteger index = new AtomicInteger(1);
            List<YunheRank> rankList = resp.getContent().stream().map(hotRank -> {
                Map<String, String> detailParam = new HashMap<>();
                detailParam.put("nameId", hotRank.getNameId().toString());
                YunheDetailInfo detail = post2Yunhe(DETAIL_API, detailParam, new TypeReference<YunheDetailInfo>() {});
                return convertHotRank(hotRank, detail, day, channelType, index.getAndIncrement());
            }).collect(Collectors.toList());

            yunheRankRpcApiProxy.batchUpsert(rankList);
            yunheRankProviderService.sendMQ(rankList);

        } catch (Exception e) {
            log.error("collectHotRank error", e);
        }
    }


    /**
     * 调域外接口请求云合数据
     */
    public <T> T post2Yunhe(String api, Map<String, String> param, TypeReference<T> typeReference) {
        try {
            String url = searchProperties.getYunheUrl() + api;
            param.put(NAME, searchProperties.getYunheName());
            param.put(WORD, searchProperties.getYunhePwd());
            String response = httpDataChannel.postForObjectWithGenerics(url, null, String.class, param, 3000);
            if (StringUtils.isNotBlank(response)) {
                return JacksonUtil.parseObject(response, typeReference);
            }
        } catch (Exception e) {
            log.error("post2Yunhe error", e);
        }
        return null;
    }

    public YunheRank convertReboTop(YunheReboTop reboTop, YunheDetailInfo detailInfo) {
        YunheRank yunheRank = new YunheRank();
        yunheRank.setDay(reboTop.getDate().replaceAll("-", ""));
        yunheRank.setKind(YunheRankKindEnum.TOP.getCode());
        yunheRank.setNameId(reboTop.getNameID());
        yunheRank.setTitle(reboTop.getName());
        yunheRank.setRank(reboTop.getReboRank());
        yunheRank.setStatus(reboTop.getStatus());
        yunheRank.setValue(reboTop.getMarketShare());
        yunheRank.setType(YunheRankTypeEnum.TV.getCode());
        yunheRank.setLevel(CollectionUtils.isNotEmpty(reboTop.getYunheLevel()) ?
                reboTop.getYunheLevel().get(0).getLevel() : null);
        yunheRank.setOccurDays(reboTop.getOccurDays());
        yunheRank.setPlatform(reboTop.getDayPlatform());
        convertDetailInfo(yunheRank, detailInfo);

        return yunheRank;
    }

    public YunheRank convertTopRank(YunheTopRankResp.YunheTopRank topRank, YunheDetailInfo detailInfo,
                                    String day, String channelType, int index) {
        YunheRank yunheRank = new YunheRank();
        yunheRank.setDay(day);
        yunheRank.setKind(YunheRankKindEnum.TOP.getCode());
        yunheRank.setNameId(topRank.getNameID());
        yunheRank.setTitle(topRank.getName());
        yunheRank.setRank(Optional.ofNullable(topRank.getRankPredictedAbs()).orElse(index));
        yunheRank.setStatus(topRank.getStatus());
        yunheRank.setValue(topRank.getMarketShare());
        yunheRank.setType(channelType);
        yunheRank.setLevel(CollectionUtils.isNotEmpty(topRank.getYunheLevel()) ?
                topRank.getYunheLevel().get(0).getLevel() : null);
        yunheRank.setOccurDays(topRank.getOccurDays());
        yunheRank.setPlatform(Optional.ofNullable(topRank.getChannel())
                .map(s -> s.replaceAll("/", ",")).orElse(null));
        convertDetailInfo(yunheRank, detailInfo);

        return yunheRank;
    }

    public YunheRank convertHotRank(YunheHotRankResp.YunheHotRank hotRank, YunheDetailInfo detailInfo,
                                    String day, String channelType, int index) {
        YunheRank yunheRank = new YunheRank();
        yunheRank.setDay(day);
        yunheRank.setKind(YunheRankKindEnum.HOT.getCode());
        yunheRank.setNameId(hotRank.getNameId());
        yunheRank.setTitle(hotRank.getName());
        yunheRank.setRank(index);
        yunheRank.setStatus(hotRank.getStatus());
        yunheRank.setValue(hotRank.getAllHot());
        yunheRank.setType(channelType);
        yunheRank.setOccurDays(hotRank.getOccurDays());
        yunheRank.setPlatform(Optional.ofNullable(hotRank.getChannel())
                .map(s -> s.replaceAll("/", ",")).orElse(null));
        convertDetailInfo(yunheRank, detailInfo);

        return yunheRank;
    }

    public void convertDetailInfo(YunheRank yunheRank, YunheDetailInfo detailInfo) {
        if (detailInfo != null) {
            yunheRank.setPosterUrl(uploadImage2Ocs(yunheRank.getNameId(), detailInfo.getHpURL()));
            // 剧集详情信息，用于后续节目匹配
            yunheRank.setDirector(detailInfo.getDirector());
            yunheRank.setActor(retainTen(detailInfo.getActor()));
            yunheRank.setArea(detailInfo.getArea());
            yunheRank.setTag(detailInfo.getTags());
            yunheRank.setRating(detailInfo.getRating());
            yunheRank.setEpisodeNum(detailInfo.getTotalEpisodeNum());
            yunheRank.setBrief(Optional.ofNullable(detailInfo.getBriefStory())
                    .map(s -> s.substring(0, Math.min(450, s.length())))
                    .orElse(null)
            );
            yunheRank.setReleaseTime(Optional.ofNullable(detailInfo.getReleaseTime())
                    .map(time -> Instant.ofEpochMilli(time)
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate()
                            .format(FORMATTER))
                    .orElse(null));
        }
    }

    /**
     * 仅保留前十个演员
     */
    public String retainTen(String input) {
        if (StringUtils.isNotBlank(input)) {
            int count = 0;
            for (int i = 0; i < input.length(); i++) {
                if (i == 450) {
                    return input.substring(0, i);
                }
                if (input.charAt(i) == ',') {
                    count++;
                    if (count == 10) {
                        return input.substring(0, i);
                    }
                }
            }
        }
        return input;
    }

    public String uploadImage2Ocs(Integer nameId, String url) {
        String fileName = "yunhe_poster_" + nameId + ".jpg";
        Path path = Paths.get(fileName);
        try {
            // 获取图片
            byte[] response = httpDataChannel.getForObject(url, byte[].class, 5000);
            // 生成本地文件
            Files.write(path, response);
            // 上传ocs
            fileToOcsComponent.uploadFileToOcs(csvExportConfig.getAccessKeyId(),
                    csvExportConfig.getAccessKeySecret(),
                    csvExportConfig.getEndPoint(),
                    csvExportConfig.getRegion(),
                    csvExportConfig.getBucketName(),
                    fileName,
                    csvExportConfig.getLocalPath(),
                    OCS_PATH);
            return "http://" + csvExportConfig.getBucketName() + "." + csvExportConfig.getEndPoint() + "/" +
                    OCS_PATH + "/" + fileName;
        } catch (Exception e) {
            log.error("uploadImage2Ocs error", e);
            return url;
        } finally {
            try {
                Files.deleteIfExists(path);
            } catch (IOException e) {
                log.error("Failed to delete temporary file", e);
            }
        }
    }
}
