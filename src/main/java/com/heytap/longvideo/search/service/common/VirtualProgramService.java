package com.heytap.longvideo.search.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.longvideo.search.constants.CommonConstant;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.mapper.media.VirtualProgramMapper;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.longvideo.search.service.sourcefilter.YoukuSourceFilterService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;

import java.util.*;
import java.util.stream.Collectors;

/*
 * 虚拟 节目
 * Date 18:58 2021/12/21
 * Author songjiajia 80350688
 */
@Service
@Slf4j
public class VirtualProgramService {

    @Autowired
    private VirtualProgramMapper virtualProgramMapper;

    @Autowired
    private SearchProperties searchConfig;

    @Autowired
    private VersionFilterService versionFilterService;

    @Autowired
    private StandardAlbumMapper standardAlbumMapper;

    @Autowired
    private InitService initService;

    @Autowired
    protected JedisCluster jedisCluster;

    @Autowired
    private YoukuSourceFilterService youkuSourceFilterService;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @HeraclesDynamicConfig(key = "media.datebaseEnd", fileName = "search_config.properties")
    private static int datebaseEnd;

    @HeraclesDynamicConfig(key = "media.tableEnd", fileName = "search_config.properties")
    private static int tableEnd;

    public static final String virtualCacheKey = "search-rest_virtualCache";

    /**
     * 获取某个虚拟节目下优先级最高的视频
     *
     * @return
     */
    public ProgramAlbumEs getProgramAlbumByVipType(int versionTag, ProgramAlbumEs originalData, String vipType,
            String payMent, Integer quickEngineVersion, Integer version, int isOut, String appId) {
        try {
            if (originalData.getSid().equals(originalData.getVirtualSid()) && originalData.getHasVirtualSid() == 0) {
                return originalData;
            }
            if (StringUtil.isBlank(vipType)) {
                vipType = "default";
            }
            String virtualValue = jedisCluster.hget(virtualCacheKey, originalData.getVirtualSid());
            if (StringUtil.isBlank(virtualValue)) {
                return originalData;
            }
            Map<String, ProgramAlbumEs> virtualMap = JSON.parseObject(virtualValue, new TypeReference<Map<String, ProgramAlbumEs>>() {
            });
            if (MapUtils.isEmpty(virtualMap)) {
                return originalData;
            }
            List<String> multipleSourceCode = new ArrayList<>();
            virtualMap.forEach((key, value) -> multipleSourceCode.add(value.getSource()));
            originalData.setMultipleSourceCode(multipleSourceCode);
            List<String> priorityList = searchConfig.getCopyRightPriorityMap().get(vipType);
            if (CollectionUtils.isEmpty(priorityList)) {
                return originalData;
            }
            List<String> priorityListCopy = buildPriorityListCopy(priorityList, quickEngineVersion, version, isOut, appId);

            List<String> multipleSourceCodeSorted = priorityListCopy.stream().filter(multipleSourceCode::contains).collect(Collectors.toList());
            for (String s : priorityListCopy) {
                ProgramAlbumEs es = virtualMap.get(s);
                if (es != null && !versionFilterService.versionFilter(versionTag, es, appId) && paymentFilter(es.getPayStatus(), payMent)) {
                    es.setReleScore(originalData.getReleScore());
                    ProgramAlbumEs programAlbumEs = virtualMap.get(s);
                    programAlbumEs.setMultipleSourceCode(multipleSourceCodeSorted);
                    return programAlbumEs;
                }
            }
        } catch (Exception e) {
            log.error("getProgramAlbumByVipType error", e);
        }
        return originalData;
    }

    private List<String> buildPriorityListCopy(List<String> priorityList, Integer quickEngineVersion, Integer version,
                                               int isOut, String appId) {
        List<String> priorityListCopy = new ArrayList<>(priorityList.size());
        priorityListCopy.addAll(priorityList);
        //多源节目包含优酷，过滤
        if (priorityListCopy.contains(SourceEnum.YOUKU_MOBILE.getDataSource())) {
            if (1 == isOut && youkuSourceFilterService.thirdPartyFilter(quickEngineVersion, version, appId)) {
                priorityListCopy.remove(SourceEnum.YOUKU_MOBILE.getDataSource());
            } else if (0 == isOut && youkuSourceFilterService.filterItem(version)) {
                priorityListCopy.remove(SourceEnum.YOUKU_MOBILE.getDataSource());
            }
        }
        //多源节目包含风行和微迪欧，过滤
        if (funshionLongVideoAndWeidiouFilterService.filterItem(version)) {
            priorityListCopy.remove(SourceEnum.FUNSHION_LONGVIDEO.getDataSource());
            priorityListCopy.remove(SourceEnum.WEIDIOU.getDataSource());
        }
        return priorityListCopy;
    }

    private boolean paymentFilter(int payStatus,String payMent){
        if(StringUtil.isEmpty(payMent)){
            return true;
        }
        if ("vip".equals(payMent)) {
            if(payStatus ==1){
                return true;
            }
        }else if("single".equals(payMent)){
            if(payStatus ==2){
                return true;
            }
        }else if("free".equals(payMent)){
            if(payStatus ==0){
                return true;
            }
        }else{
            return true;
        }
        return false;
    }

    public List<ProgramAlbumEs> createCacheAndReturnAllAlbum() {
        List<ProgramAlbumEs> standardAlbumEsList = new ArrayList<>();
        try {
            int totalCount = virtualProgramMapper.selectAllVirtualProgramCount(CommonConstant.allowSource);
            int pageSize = 500;
            Map<String, Map<String, ProgramAlbumEs>> virtualProgramMap = new HashMap<>();
            int nowYear = Calendar.getInstance().get(Calendar.YEAR);
            for (int i = 0; i <= datebaseEnd; i++) {
                for (int j = 0; j <= tableEnd; j++) {
                    List<ProgramAlbumEs> subList = standardAlbumMapper.selectStandardAlbumListForApp(i, j, CommonConstant.allowSource);
                    standardAlbumEsList.addAll(subList);
                }
            }
            Map<String, ProgramAlbumEs> standardAlbumEsMap = standardAlbumEsList.stream().collect(Collectors.toMap(ProgramAlbumEs::getSid, l -> l, (u1, u2) -> u1));
            for (int i = 0; i <= totalCount; i += pageSize) {
                List<MisVirtualProgramRelation> virtualProgramList = virtualProgramMapper.selectAllVirtualProgram(i, pageSize, CommonConstant.allowSource);
                for (MisVirtualProgramRelation misVirtualProgramRelation : virtualProgramList) {
                    ProgramAlbumEs programAlbumEs = standardAlbumEsMap.get(misVirtualProgramRelation.getSid());
                    if (programAlbumEs == null) {
                        continue;
                    }
                    programAlbumEs.setVirtualSid(misVirtualProgramRelation.getVirtualSid());
                    programAlbumEs.setHasVirtualSid(1);
                    initService.setProgramAlbumEs(programAlbumEs, nowYear);
                    String virtualSid = misVirtualProgramRelation.getVirtualSid();
                    if (virtualProgramMap.containsKey(programAlbumEs.getVirtualSid())) {
                        //虚拟sid -> <source, programAlbumEs>
                        virtualProgramMap.get(programAlbumEs.getVirtualSid()).put(programAlbumEs.getSource(), programAlbumEs);
                    } else {
                        Map<String, ProgramAlbumEs> map = new HashMap<>();
                        map.put(programAlbumEs.getSource(), programAlbumEs);
                        virtualProgramMap.put(virtualSid, map);
                    }
                }
            }
            Map<String, String> redisMap = new HashMap<>();
            virtualProgramMap.forEach((k, v) -> redisMap.put(k, JSON.toJSONString(v)));
            jedisCluster.del(virtualCacheKey);
            jedisCluster.hmset(virtualCacheKey, redisMap);
        }catch (Exception e){
            log.error("createCacheAndReturnAllAlbum error",e);
        }
        return standardAlbumEsList;
    }


    public Map<String, Map<String, ProgramAlbumEs>> getAllCache() {
        Map<String, String> redisValueMap=jedisCluster.hgetAll(virtualCacheKey);
        Map<String, Map<String, ProgramAlbumEs>> virtualAllMap = new HashMap<>();
        redisValueMap.forEach((k,v) -> virtualAllMap.put(k,JSON.parseObject(v, new TypeReference<Map<String, ProgramAlbumEs>>(){
        })));
        return virtualAllMap;
    }
}
