package com.heytap.longvideo.search.service.standard;

import com.google.common.base.CaseFormat;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.entity.UgcStandardVideo;
import com.heytap.longvideo.search.config.CsvExportConfig;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.entity.es.UgcStandardVideoEs;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchExportVo;
import com.heytap.longvideo.search.model.param.standard.UgcSearchExportStatusVo;
import com.heytap.longvideo.search.model.param.standard.UgcSearchVideoParams;
import com.heytap.longvideo.search.model.param.standard.UgcStandardVideoVo;
import com.heytap.longvideo.search.service.common.UploadFileToOcsComponent;
import com.heytap.longvideo.search.utils.CSVExportUtil;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import esa.rpc.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import redis.clients.jedis.JedisCluster;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;

import static com.heytap.longvideo.search.constants.MediaExportFileConstant.SEARCH_MEDIA_EXPORT_REDIS_STATUS;
import static com.heytap.longvideo.search.constants.MediaExportFileConstant.SEARCH_MEDIA_EXPORT_REDIS_URL;

/**
 * <AUTHOR> Yanping
 * @date 2022/10/24 11:54
 */
@Service
@Slf4j
public class UgcStandardVideoService {
    private static final Logger UPDATELOG = LoggerFactory.getLogger("UPDATELOG");

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Autowired
    private CsvExportConfig csvExportConfig;

    @Autowired
    @Qualifier("csvFileThreadPool")
    private Executor csvFileThreadPool;

    @Autowired
    private JedisCluster jedisCluster;

    @Autowired
    UploadFileToOcsComponent fileToOcsComponent;


    /**
     * 查询视频信息
     *
     * @param request
     * @return
     * @throws Exception
     */
    public PageResponse<UgcStandardVideoVo> searchVideo(UgcSearchVideoParams request) throws Exception {
        try {
            PageResponse<UgcStandardVideoVo> response = new PageResponse<>();
            List<UgcStandardVideoVo> voList = new ArrayList<>(request.getPageSize());

            if (StringUtils.isBlank(request.getOrder())) {
                request.setOrder("updateTime");
            }
            if (request.getOrder().contains("_")) {
                request.setOrder(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, request.getOrder()));
            }

            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.UGC_STANDARD_VIDEO_INDEX)
                    .source(new SearchSourceBuilder()
                            .query(getQueryBuilder(request))
                            .from((request.getPageIndex() - 1) * request.getPageSize())
                            .size(request.getPageSize())
                            .trackTotalHits(true)
                            .sort(SortBuilders.fieldSort(request.getOrder()).order(SortOrder.DESC)));
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            for (SearchHit hit : searchHits.getHits()) {
                UgcStandardVideo ugcStandardVideo = JsonUtil.fromStr(hit.getSourceAsString(), UgcStandardVideo.class);
                UgcStandardVideoVo videoPageListVo = new UgcStandardVideoVo();
                BeanUtils.copyProperties(ugcStandardVideo, videoPageListVo);
                voList.add(videoPageListVo);
            }
            long totalCount = searchHits.getTotalHits().value;
            response.setCurrentPage(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setTotalCount(totalCount);
            response.setMaxPage((int) Math.ceil(totalCount / request.getPageSize()));
            response.setItemList(voList);
            response.setItemListSize(voList.size());
            return response;
        } catch (Exception e) {
            log.error("search ugc Video fail", e);
            throw e;
        }
    }


    /**
     * 芒果UGC视频和风行短视频筛选导出
     *
     * @param request
     * @return
     * @throws Exception
     */
    public SearchExportVo searchAndExportVideo(UgcSearchVideoParams request) throws Exception {
        try {
            if (StringUtils.isBlank(request.getOrder())) {
                request.setOrder("updateTime");
            }
            if (request.getOrder().contains("_")) {
                request.setOrder(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, request.getOrder()));
            }
            String fileName = buildFileName(request);
            String searchExportId = String.valueOf(UUID.randomUUID());
            String redisStatusKey = SEARCH_MEDIA_EXPORT_REDIS_STATUS + searchExportId;
            String redisUrlKey = SEARCH_MEDIA_EXPORT_REDIS_URL + searchExportId;
            jedisCluster.set(redisStatusKey, String.valueOf(0), "NX", "EX", 60 * 60);
            //线程池查询
            csvFileThreadPool.execute(() -> {
                try {
                    //导出数据并保存
                    int totalCounts = writeDataToCsvFile(request, fileName);
                    File file = new File(csvExportConfig.getLocalPath() + fileName);
                    String newFileName = fileName.replace(".csv", "_" + totalCounts + "条.csv");
                    file.renameTo(new File(csvExportConfig.getLocalPath() + newFileName));
                    //上传文件到ocs
                    boolean uploadRes = fileToOcsComponent.uploadFileToOcs(csvExportConfig.getAccessKeyId(),
                            csvExportConfig.getAccessKeySecret(),
                            csvExportConfig.getEndPoint(),
                            csvExportConfig.getRegion(),
                            csvExportConfig.getBucketName(),
                            newFileName,
                            csvExportConfig.getLocalPath(),
                            csvExportConfig.getOcsPath());
                    // redis状态更新
                    if (uploadRes) {
                        jedisCluster.set(redisStatusKey, String.valueOf(1), "XX", "EX", 60 * 60);
                        jedisCluster.set(redisUrlKey, getUrl(newFileName), "NX", "EX", 60 * 60);
                    } else {
                        jedisCluster.set(redisStatusKey, String.valueOf(-1), "XX", "EX", 60 * 60);
                    }
                } catch (Exception e) {
                    jedisCluster.set(redisStatusKey, String.valueOf(-1), "XX", "EX", 60 * 60);
                    if (StringUtils.isNotBlank(fileName)) {
                        File file = new File(csvExportConfig.getLocalPath() + fileName);
                        file.delete();
                    }
                    throw new RuntimeException(e.getMessage(), e);
                }
            });
            return SearchExportVo.builder().searchExportId(searchExportId).url(getUrl(fileName)).build();
        } catch (Exception e) {
            log.error("search export ugc Video fail", e);
            throw e;
        }
    }

    private String getUrl(String fileName) {
        String url = "http://" + csvExportConfig.getBucketName() + "." + csvExportConfig.getEndPoint() + "/" + csvExportConfig.getOcsPath() + "/" + fileName;
        return url;
    }

    private int writeDataToCsvFile(UgcSearchVideoParams request, String fileName) throws IOException {
        SearchSourceBuilder builder = new SearchSourceBuilder()
                .query(getQueryBuilder(request))
                .trackTotalHits(true)
                .sort(SortBuilders.fieldSort(request.getOrder()).order(SortOrder.DESC)).
                size(3000);
        SearchRequest searchRequest = new SearchRequest()
                .indices(IndexNameConstant.UGC_STANDARD_VIDEO_INDEX)
                .source(builder);
        Scroll scroll = new Scroll(new TimeValue(1800000));
        searchRequest.scroll(scroll);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<String> dataList = new ArrayList<>();
        dataList.add("vid,标题,sid,内容品类,视频类型,分类信息（标签）,时长,付费类型,查询条件：" + request.toString().replace(",", ""));
        String file = csvExportConfig.getLocalPath() + fileName;
        CSVExportUtil.exportCsv(new File(file), dataList);
        int totalCounts = 0;
        while (ArrayUtils.isNotEmpty(hits)) {
            totalCounts += hits.length;
            dataList = new ArrayList<>();
            for (SearchHit hit : hits) {
                UgcStandardVideo ugcStandardVideo = JsonUtil.fromStr(hit.getSourceAsString(), UgcStandardVideo.class);
                StringBuffer buffer = new StringBuffer();
                buffer.append("\"" + ugcStandardVideo.getVid() + "\t" + "\"" + ",").//vid
                        append("\"" + ugcStandardVideo.getTitle() + "\"" + ",").//标题
                        append("\"" + ugcStandardVideo.getSid() + "\t" + "\"" + ",").//sid
                        append("\"" + ugcStandardVideo.getProgramType() + "\"" + ",").//内容品类
                        append(",").//视频类型
                        append("\"" + ugcStandardVideo.getTag() + "\"" + ",").//分类信息（标签）
                        append("\"" + ugcStandardVideo.getDuration() + "\"" + ",").//时长,秒
                        append(",");//付费类型
                dataList.add(buffer.toString());
            }
            CSVExportUtil.exportCsv(new File(file), dataList);
            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
            searchScrollRequest.scroll(scroll);
            SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
            scrollId = searchScrollResponse.getScrollId();
            hits = searchScrollResponse.getHits().getHits();
        }
        return totalCounts;
    }

    private String buildFileName(UgcSearchVideoParams request) throws IOException {
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String fileName;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd_hh-mm-ss");
        if (!StringUtils.isEmpty(request.getVideoManageName())) {
            fileName = request.getVideoManageName() + "_" + format.format(new Date()) + "_" + uuid + ".csv";
        } else {
            fileName = "视频管理" + "_" + format.format(new Date()) + "_" + uuid + ".csv";
        }
        return fileName;
    }


    /**
     * 查询文件导出状态
     *
     * @param searchExportId
     * @return
     */
    public StandardResult<UgcSearchExportStatusVo> searchExportStatus(String searchExportId) {
        try {
            String value = jedisCluster.get(SEARCH_MEDIA_EXPORT_REDIS_STATUS + searchExportId);
            UgcSearchExportStatusVo ugcSearchExportStatusVo = new UgcSearchExportStatusVo();
            if ("1".equals(value)) {
                ugcSearchExportStatusVo.setStatus("SUCCESS");
                String url = jedisCluster.get(SEARCH_MEDIA_EXPORT_REDIS_URL + searchExportId);
                ugcSearchExportStatusVo.setUrl(url);
                return StandardResult.success(ugcSearchExportStatusVo);
            } else if ("-1".equals(value) || StringUtil.isEmpty(value)) {
                ugcSearchExportStatusVo.setStatus("ERROR");
                return StandardResult.success(ugcSearchExportStatusVo);
            } else {
                ugcSearchExportStatusVo.setStatus("RUNING");
                return StandardResult.success(ugcSearchExportStatusVo);
            }
        } catch (Exception e) {
            return StandardResult.fail(400, e.getMessage());
        }
    }


    /**
     * 构造ES的查询条件
     *
     * @param nRequest
     * @return
     */
    private QueryBuilder getQueryBuilder(UgcSearchVideoParams nRequest) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        Integer durationMin = nRequest.getDurationMin();
        Integer durationMax = nRequest.getDurationMax();
        boolQueryBuilder.must(QueryBuilders.rangeQuery("duration").from(durationMin).to(durationMax));

        handleEsField(nRequest, boolQueryBuilder);

        if (nRequest.getStartDuration() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("duration")
                    .gte(nRequest.getStartDuration()));
        }

        if (nRequest.getEndDuration() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("duration")
                    .lte(nRequest.getEndDuration()));
        }

        if (CollectionUtils.isNotEmpty(nRequest.getSourceVideoIdList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("sourceVideoId.keyword", nRequest.getSourceVideoIdList()));
        }

        if (CollectionUtils.isNotEmpty(nRequest.getVidList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("vid", nRequest.getVidList()));
        }

        /*
         * 如果关联正片 那么linkSid须有值
         * 反之 linkSid为空字符串
         */

        if (StringUtils.isNotEmpty(nRequest.getLinkSid())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("linkSid", nRequest.getLinkSid()));
        } else {
            if (nRequest.getIsRelatedAlbum() != null) {
                // 关联正片
                if (Objects.equals(nRequest.getIsRelatedAlbum(), 0)) {
                    // 字段存在且值不为""
                    boolQueryBuilder
                            .must(QueryBuilders.existsQuery("linkSid"))
                            .must(QueryBuilders.wildcardQuery("linkSid", "*"));
                } else if (Objects.equals(nRequest.getIsRelatedAlbum(), 1)) {
                    // 字段不存在 || 字段存在且为""
                    boolQueryBuilder
                            .mustNot(QueryBuilders.wildcardQuery("linkSid", "*"));
                }
            }
        }

        if (StringUtils.isNotEmpty(nRequest.getTag())) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("tag", "*" + nRequest.getTag().toLowerCase() + "*"));
        }

        return boolQueryBuilder;
    }

    private void handleEsField(UgcSearchVideoParams nRequest, BoolQueryBuilder boolQueryBuilder) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                UgcSearchVideoParams.class, EsField.class);
        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);

            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            if ("tag".equals(entry.getKey())) {
                fieldValue = fieldValue.toLowerCase();
            }

            String fileName = field.getAnnotation(EsField.class).name();

            if ("source".equals(entry.getKey())) {
                String[] sources = fieldValue.split(",");
                boolQueryBuilder.filter(QueryBuilders.termsQuery("source", sources));
            } else if ("subTag".equals(entry.getKey())) {
                String[] subTagList = fieldValue.replace("，", ",").split(",");
                boolQueryBuilder.filter(QueryBuilders.termsQuery("subTag.keyword", subTagList));
            } else {
                boolQueryBuilder.filter(QueryBuilders.matchPhraseQuery(StringUtils.isNotEmpty(fileName)
                        ? fileName : entry.getKey(), fieldValue));
            }
        }
    }

    /**
     * 数据更新
     *
     * @param es
     */
    public void insertOrUpdate(UgcStandardVideoEs es) {
        try {
            es.setUniKey(es.getSid() + ":" + es.getVid());
            restTemplate.save(es);
        } catch (Exception e) {
            UPDATELOG.error("insertOrUpdate ugcStandardAuthor", e);
            throw e;
        }
    }

    /**
     * 数据更新
     *
     * @param es
     */
    public void delete(UgcStandardVideoEs es) {
        try {
            es.setUniKey(es.getSid() + ":" + es.getVid());
            restTemplate.delete(es.getUniKey(), IndexCoordinates.of(UgcStandardVideoEs.class
                    .getAnnotation(Document.class).indexName()));
        } catch (Exception e) {
            UPDATELOG.error("insertOrUpdate ugcStandardVideo", e);
            throw e;
        }
    }
}