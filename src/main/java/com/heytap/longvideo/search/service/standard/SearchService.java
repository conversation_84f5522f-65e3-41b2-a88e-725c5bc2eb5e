package com.heytap.longvideo.search.service.standard;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.client.media.query.SearchAlbumRequest;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import com.heytap.longvideo.search.constants.SearchConstant;
import com.heytap.longvideo.search.mapper.media.VirtualProgramMapper;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.utils.EntityFieldCacheUtil;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/10/16
 */
@Slf4j
@Service
@AllArgsConstructor
public class SearchService {

    private RestHighLevelClient restHighLevelClient;

    private VirtualProgramMapper virtualProgramMapper;

    public List<StandardAlbum> searchAlbum(SearchAlbumRequest request) {
        try {
            List<StandardAlbum> standardAlbums = new ArrayList<>();

            if (request.getSortColumn() == null) {
                return Lists.newArrayList();
            }

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                    .query(getAlbumQueryBuilder(request))
                    .from(0)
                    .size(100)
                    .trackTotalHits(true)
                    .sort(SortBuilders.fieldSort(SearchConstant.sortTypeMap.get(request.getSortColumn()).get(request.getDataRange()))
                            .order(request.getSortType() != null && "1".equals(request.getSortType()) ? SortOrder.DESC : SortOrder.ASC))
                    .sort("sidL", SortOrder.DESC);


            SearchRequest searchRequest = new SearchRequest()
                    .indices(IndexNameConstant.STANDARD_ALBUM_INDEX)
                    .source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();

            if (searchHits.getHits() != null && searchHits.getHits().length > 0) {
                List<StandardAlbumEs> standardAlbumEss = Lists.newArrayList(searchHits.getHits()).stream()
                        .map(hit -> JsonUtil.fromStr(hit.getSourceAsString(), StandardAlbumEs.class))
                        .collect(Collectors.toList());

                Set<String> sidSet = new HashSet<>();

                for (StandardAlbumEs albumEs : standardAlbumEss) {
                    String uniqueId = fetchUniqueId(albumEs.getSid());
                    if (!sidSet.contains(uniqueId)) {
                        StandardAlbum standardAlbum = new StandardAlbum();
                        BeanUtils.copyProperties(albumEs, standardAlbum);
                        standardAlbums.add(standardAlbum);
                        sidSet.add(uniqueId);
                    }
                }
            }

            return standardAlbums;
        } catch (Exception e) {
            log.error("searchAlbum fail", e);
        }
        return Lists.newArrayList();
    }

    /**
     * @param sid 剧头sid
     * @return String 唯一id
     * @Description: 获取唯一id
     */
    private String fetchUniqueId(String sid) {
        MisVirtualProgramRelation misVirtualProgramRelation = virtualProgramMapper.selectVirtualProgramBySid(sid);
        if (misVirtualProgramRelation != null) {
            return misVirtualProgramRelation.getVirtualSid();
        }
        return sid;
    }

    /**
     * 构造剧头查询条件
     *
     * @param nRequest
     * @return
     */
    private QueryBuilder getAlbumQueryBuilder(SearchAlbumRequest nRequest) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.mustNot(QueryBuilders.matchPhraseQuery("source", "ztv"));

        boolQueryBuilder.mustNot(QueryBuilders.matchPhraseQuery("featureType", 0));

        boolQueryBuilder.must(QueryBuilders.termQuery("status", 1));

        boolQueryBuilder.must(QueryBuilders.termQuery("sourceStatus", 1));

        if (StringUtils.isNotBlank(nRequest.getYearStart())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("year").gte(nRequest.getYearStart()));
        }
        if (StringUtils.isNotBlank(nRequest.getYearEnd())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("year").lte(nRequest.getYearEnd()));
        }
        if (nRequest.getScoreStart() != null) {
            if (nRequest.getScoreStart().compareTo(10F) == 0) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("sourceScore", "10.0"));
            } else {
                BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
                scoreBuilderOr.should().add(QueryBuilders.termsQuery("sourceScore", "10.0"));
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("sourceScore").gte(nRequest.getScoreStart()));
                boolQueryBuilder.must(scoreBuilderOr);
            }
        }
        if (nRequest.getScoreEnd() != null) {
            if (nRequest.getScoreEnd().compareTo(10F) == 0) {
                BoolQueryBuilder scoreBuilderOr = QueryBuilders.boolQuery();
                scoreBuilderOr.should().add(QueryBuilders.termsQuery("sourceScore", "10.0"));
                scoreBuilderOr.should().add(QueryBuilders.rangeQuery("sourceScore").lte("9.9"));
                boolQueryBuilder.must(scoreBuilderOr);
            } else {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("sourceScore").lte(nRequest.getScoreEnd()));
                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("sourceScore", "10.0"));
            }
        }

        buildEsFieldCondition(nRequest, boolQueryBuilder);

        return boolQueryBuilder;
    }

    private void buildEsFieldCondition(SearchAlbumRequest nRequest, BoolQueryBuilder boolQueryBuilder) {
        HashMap<String, Field> entityFieldsCache = EntityFieldCacheUtil.getEntityFieldsCache(
                SearchAlbumRequest.class, EsField.class);

        for (Map.Entry<String, Field> entry : entityFieldsCache.entrySet()) {
            Field field = entry.getValue();
            field.setAccessible(true);
            String fieldValue = (String) ReflectionUtils.getField(field, nRequest);
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }
            fieldValue = fieldValue.trim();
            String fileName = field.getAnnotation(EsField.class).name();
            fileName = StringUtils.isNotEmpty(fileName) ? fileName : entry.getKey();
            if ("tag".equals(entry.getKey())) {
                boolQueryBuilder.must(QueryBuilders.matchQuery("tags", fieldValue.replace(",", "|")));
            } else if ("source".equals(entry.getKey()) || "copyrightCode".equals(entry.getKey())) {
                // 后台直接添加 风行和微迪欧
                if (fieldValue.contains("funshion") && !fieldValue.contains("funshion_lv")) {
                    fieldValue = fieldValue.replace("funshion", "huashi,senyu,weidiou,funshion_lv");
                }
                boolQueryBuilder.must(QueryBuilders.termsQuery(fileName, fieldValue.split(",")));
            } else if ("programType".equals(entry.getKey())) {
                boolQueryBuilder.must(QueryBuilders.termsQuery(fileName, fieldValue.split(",")));
            } else {
                boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fileName, fieldValue));
            }
        }
    }
}
