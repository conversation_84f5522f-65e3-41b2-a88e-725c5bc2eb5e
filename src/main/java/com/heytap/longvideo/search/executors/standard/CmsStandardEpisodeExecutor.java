package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.CmsEpisodeVo;
import com.heytap.longvideo.search.model.param.standard.CmsSearchEpisodeParams;
import com.heytap.longvideo.search.service.standard.StandardEpisodeService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class CmsStandardEpisodeExecutor extends AbstractNextAsyncExecutor<CmsSearchEpisodeParams, StandardResult<PageResponse<CmsEpisodeVo>>> {

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    protected CmsStandardEpisodeExecutor() {
        super(CmsSearchEpisodeParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<PageResponse<CmsEpisodeVo>>> myExecute(CmsSearchEpisodeParams nRequest) throws BizException {
        if (StringUtil.isNotEmpty(nRequest.getOriginStatus())) {
            nRequest.setSourceStatus(nRequest.getOriginStatus());
        }
        try {
            PageResponse<CmsEpisodeVo> pageResponse = standardEpisodeService.searchCmsEpisode(nRequest);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(CmsSearchEpisodeParams nRequest, StandardResult<PageResponse<CmsEpisodeVo>> nResponse) throws BizException {
        return null;
    }
}
