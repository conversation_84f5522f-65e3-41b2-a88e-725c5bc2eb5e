package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.ProgramTypeListParams;
import com.heytap.longvideo.search.model.param.standard.ProgramTypeListVo;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class ProgramTypeListExecutor extends AbstractNextAsyncExecutor<ProgramTypeListParams, StandardResult<List<ProgramTypeListVo>>> {

    @Autowired
    private StandardAlbumService standardAlbumService;

    protected ProgramTypeListExecutor() {
        super(ProgramTypeListParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<List<ProgramTypeListVo>>> myExecute(ProgramTypeListParams nRequest) throws BizException {
        try {
            List<ProgramTypeListVo> vos = standardAlbumService.getProgramType(nRequest.getSource(),nRequest.getVersion());
            return CompletableFuture.completedFuture(StandardResult.success(vos));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(ProgramTypeListParams nRequest, StandardResult<List<ProgramTypeListVo>> nResponse) throws BizException {
        return null;
    }
}
