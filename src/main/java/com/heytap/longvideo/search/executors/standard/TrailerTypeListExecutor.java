package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.TrailerTypeListParams;
import com.heytap.longvideo.search.model.param.standard.TrailerTypeListVo;
import com.heytap.longvideo.search.service.standard.StandardTrailerService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class TrailerTypeListExecutor extends AbstractNextAsyncExecutor<TrailerTypeListParams, StandardResult<List<TrailerTypeListVo>>> {

    @Autowired
    private StandardTrailerService standardTrailerService;

    protected TrailerTypeListExecutor() {
        super(TrailerTypeListParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<List<TrailerTypeListVo>>> myExecute(TrailerTypeListParams nRequest) throws BizException {
        try {
            List<TrailerTypeListVo> vos = standardTrailerService.getTrailerType(nRequest.getSid());
            return CompletableFuture.completedFuture(StandardResult.success(vos));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(TrailerTypeListParams nRequest, StandardResult<List<TrailerTypeListVo>> nResponse) throws BizException {
        return null;
    }
}
