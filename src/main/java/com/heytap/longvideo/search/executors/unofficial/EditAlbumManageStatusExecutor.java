package com.heytap.longvideo.search.executors.unofficial;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.constants.ProgramConstant;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.unofficial.request.ProgramRequest;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 编辑节目的管理状态 - executor
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/15 11:13
 */
@Slf4j
@Service
public class EditAlbumManageStatusExecutor extends AbstractNextAsyncExecutorV2<ProgramRequest, Result<Void>> {

    private final UnofficialAlbumService unofficialAlbumService;

    protected EditAlbumManageStatusExecutor(UnofficialAlbumService unofficialAlbumService) {
        super(ProgramRequest.class);
        this.unofficialAlbumService = unofficialAlbumService;
    }

    /**
     * @Description: 编辑节目管理状态
     * 锁定节目 - editType=lockMetadata
     * 锁定信息 - editType=lockProgram
     * @param nRequest 请求体
     * @return CompletableFuture<Result<Boolean>>
     */
    @Override
    protected CompletableFuture<Result<Void>> myExecute(ProgramRequest nRequest) throws BizException {
        if (Objects.isNull(nRequest)) {
            throw new BizException("request is null", 502);
        }

        if (CollectionUtils.isEmpty(nRequest.getSids())) {
            throw new BizException("sids is empty", 502);
        }

        try {
            // 根据sids去ES中查询剧头
            List<UnofficialAlbumEs> unofficialAlbumEsList = unofficialAlbumService.getAlbumList(nRequest.getSids());

            for (UnofficialAlbumEs unofficialAlbumEs : unofficialAlbumEsList) {
                //根据editType得到锁定信息或锁定节目或解锁，然后设置manageStatus的值
                setManagerStatus(nRequest, unofficialAlbumEs);
            }

            // 将修改的结果同步到ES
            unofficialAlbumService.batchInsertOrUpdate(unofficialAlbumEsList);
            return CompletableFuture.completedFuture(Result.success(null));
        } catch (Exception e) {
            log.error("During EditAlbumManageStatusExecutor error:", e);
            throw new BizException(e.getMessage(), 502);
        }
    }

    /**
     * @Description: 构建Protobuf格式的返回结果，子类必须实现
     * @param nRequest 请求体
     * @param nResponse 响应体
     * @return ByteString
     */
    @Override
    protected ByteString buildProtobufByteString(ProgramRequest nRequest, Result<Void> nResponse) throws BizException {
        return null;
    }

    private void setManagerStatus(ProgramRequest programRequest, UnofficialAlbumEs unofficialAlbumEs) {
        if ("lockMetadata".equals(programRequest.getEditType())) {
            unofficialAlbumEs.setManagerStatus(ProgramConstant.MANAGE_STATUS_LOCK_METADATA);
        } else if ("lockProgram".equals(programRequest.getEditType())) {
            unofficialAlbumEs.setManagerStatus(ProgramConstant.MANAGE_STATUS_LOCK_CONTENT);
        } else {
            unofficialAlbumEs.setManagerStatus(ProgramConstant.MANAGE_STATUS_AUTO_REFRASH);
        }
    }
}