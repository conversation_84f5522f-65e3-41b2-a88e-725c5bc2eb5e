package com.heytap.longvideo.search.executors.unofficial;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.enums.LockedFieldsEnum;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.unofficial.request.GetOriginInfoRequest;
import com.heytap.longvideo.search.model.unofficial.response.GetOriginInfoResponse;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 获取非合作内容方节目锁定信息 - executor
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/14 16:53
 */
@Slf4j
@Service
public class GetLockedFieldsInfoExecutor extends AbstractNextAsyncExecutorV2<GetOriginInfoRequest, Result<GetOriginInfoResponse>> {

    private final UnofficialAlbumService unofficialAlbumService;

    protected GetLockedFieldsInfoExecutor(UnofficialAlbumService unofficialAlbumService) {
        super(GetOriginInfoRequest.class);
        this.unofficialAlbumService = unofficialAlbumService;
    }

    /**
     * @Description: 子类实现业务方法
     * @param nRequest 请求体
     * @return CompletableFuture<Result<GetOriginInfoResponse>>
     */
    @Override
    protected CompletableFuture<Result<GetOriginInfoResponse>> myExecute(GetOriginInfoRequest nRequest) throws BizException {
        if (Objects.isNull(nRequest)) {
            throw new BizException("request is null", 502);
        }

        if (StringUtils.isEmpty(nRequest.getSid()) && StringUtils.isEmpty(nRequest.getSourceAlbumId())) {
            throw new BizException("sourceAlbumId & sid are both empty", 502);
        }

        try {
            UnofficialAlbumEs unofficialAlbumEs = unofficialAlbumService.searchBySid(nRequest.getSid(), UnofficialAlbumEs.class);
            if (Objects.isNull(unofficialAlbumEs)) {
                unofficialAlbumEs = unofficialAlbumService.searchBySourceAlbumId(nRequest.getSourceAlbumId());
                if (Objects.isNull(unofficialAlbumEs)) {
                    log.info("the unofficialAlbumEs is null");
                    return CompletableFuture.completedFuture(Result.fail("the unofficialAlbumEs is null", null));
                }
            }

            GetOriginInfoResponse getOriginInfoResponse = new GetOriginInfoResponse();
            BeanUtils.copyProperties(unofficialAlbumEs, getOriginInfoResponse);

            //获取被锁定的字段
            if (unofficialAlbumEs.getManagerStatus() > 2) {
                getOriginInfoResponse.setLockedFields(getLockedFields(unofficialAlbumEs.getManagerStatus()));
            }
            return CompletableFuture.completedFuture(Result.success(getOriginInfoResponse));
        } catch (Exception e) {
            log.error("During getLockedFieldsInfoExecutor error:", e);
            throw new BizException(e.getMessage(), 502);
        }
    }

    /**
     * @Description: 构建Protobuf格式的返回结果，子类必须实现
     * @param nRequest 请求体
     * @param nResponse 响应体
     * @return ByteString
     */
    @Override
    protected ByteString buildProtobufByteString(GetOriginInfoRequest nRequest, Result<GetOriginInfoResponse> nResponse) throws BizException {
        return null;
    }

    private String getLockedFields(Integer managerStatus) {
        StringBuilder sb = new StringBuilder();
        for (LockedFieldsEnum value : LockedFieldsEnum.values()) {
            if ((managerStatus & value.getManagerStatus()) != value.getManagerStatus()) {
                continue;
            }

            if (StringUtils.isEmpty(sb.toString())) {
                sb.append(value.getLockedField());
                continue;
            }

            sb.append(",");
            sb.append(value.getLockedField());
        }

        return StringUtils.isEmpty(sb.toString()) ? "" : sb.toString();
    }
}
