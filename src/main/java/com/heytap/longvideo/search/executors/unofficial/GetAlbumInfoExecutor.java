package com.heytap.longvideo.search.executors.unofficial;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.entity.UnofficialAlbum;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.unofficial.request.GetOriginInfoRequest;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 获取非合作内容方剧头信息 - executor
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/19 11:30
 */
@Slf4j
@Service
public class GetAlbumInfoExecutor extends AbstractNextAsyncExecutorV2<GetOriginInfoRequest, UnofficialAlbum> {

    private final UnofficialAlbumService unofficialAlbumService;

    protected GetAlbumInfoExecutor(UnofficialAlbumService unofficialAlbumService) {
        super(GetOriginInfoRequest.class);
        this.unofficialAlbumService = unofficialAlbumService;
    }

    /**
     * @Description: 子类实现业务方法
     *
     * @param nRequest 请求体
     * @return CompletableFuture<Result<UnofficialAlbumEs>>
     */
    @Override
    protected CompletableFuture<UnofficialAlbum> myExecute(GetOriginInfoRequest nRequest) throws BizException {
        if (Objects.isNull(nRequest)) {
            throw new BizException("request is null", 502);
        }

        if (StringUtils.isEmpty(nRequest.getSid()) && StringUtils.isEmpty(nRequest.getSourceAlbumId())) {
            throw new BizException("sourceAlbumId & sid are both empty", 502);
        }

        try {
            UnofficialAlbumEs unofficialAlbumEs = unofficialAlbumService.searchBySid(nRequest.getSid(), UnofficialAlbumEs.class);
            if (Objects.isNull(unofficialAlbumEs)) {
                unofficialAlbumEs = unofficialAlbumService.searchBySourceAlbumId(nRequest.getSourceAlbumId());
                if (Objects.isNull(unofficialAlbumEs)) {
                    log.info("the unofficialAlbumEs is null");
                    return CompletableFuture.completedFuture(null);
                }
            }

            UnofficialAlbum unofficialAlbum = new UnofficialAlbum();
            BeanUtils.copyProperties(unofficialAlbumEs, unofficialAlbum);
            return CompletableFuture.completedFuture(unofficialAlbum);
        } catch (Exception e) {
            log.error("During GetAlbumInfoExecutor error:", e);
            throw new BizException(e.getMessage(), 502);
        }
    }

    /**
     * @Description: 构建Protobuf格式的返回结果，子类必须实现
     *
     * @param nRequest 请求体
     * @param nResponse 响应体
     * @return ByteString
     */
    @Override
    protected ByteString buildProtobufByteString(GetOriginInfoRequest nRequest, UnofficialAlbum nResponse) throws BizException {
        return null;
    }
}