package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumRequest;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/3/31 11:23
 */
@Component
@Slf4j
public class UpdateStandardAlbumEsExecutor extends AbstractNextAsyncExecutor<StandardAlbumRequest, Result<String>> {

    @Autowired
    private StandardAlbumService albumService;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;


    protected UpdateStandardAlbumEsExecutor() {
        super(StandardAlbumRequest.class);
    }

    @Override
    protected CompletableFuture<Result<String>> myExecute(StandardAlbumRequest nRequest) throws BizException {
        if (StringUtil.isBlank(nRequest.getSid())) {
            return CompletableFuture.completedFuture(Result.fail("sid is null"));
        }

        StandardAlbum standardAlbum = getStandardAlbumFromDB(nRequest.getSid());
        if (standardAlbum == null) {
            log.error("standardAlbum is null, sid:{}", nRequest.getSid());
            return CompletableFuture.completedFuture(Result.fail("standardAlbum is null"));
        }
        albumService.updateStandardAlbum(standardAlbum);
        return CompletableFuture.completedFuture(Result.success(nRequest.getSid()));
    }

    private StandardAlbum getStandardAlbumFromDB(String sid){
        try {
            RpcResult<StandardAlbum> rpcResult = standardAlbumRpcApi.getBySid(sid).get();
            if (rpcResult == null || rpcResult.getData() == null) {
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("getStandardAlbumFromDB error, sid:{}", sid, e);
        }
        return null;
    }


    @Override
    protected ByteString buildProtobufByteString(StandardAlbumRequest standardAlbum, Result result) throws BizException {
        return null;
    }
}