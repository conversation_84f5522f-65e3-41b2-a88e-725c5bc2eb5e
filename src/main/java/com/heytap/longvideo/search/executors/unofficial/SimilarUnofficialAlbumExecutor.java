package com.heytap.longvideo.search.executors.unofficial;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.entity.UnofficialAlbum;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.unofficial.request.SearchUnofficialAlbumRequest;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/2/12 上午10:50
 */
@Slf4j
@Service
public class SimilarUnofficialAlbumExecutor extends AbstractNextAsyncExecutorV2<SearchUnofficialAlbumRequest, List<UnofficialAlbum>> {
    private final UnofficialAlbumService unofficialAlbumService;

    protected SimilarUnofficialAlbumExecutor(UnofficialAlbumService unofficialAlbumService) {
        super(SearchUnofficialAlbumRequest.class);
        this.unofficialAlbumService = unofficialAlbumService;
    }

    @Override
    protected CompletableFuture<List<UnofficialAlbum>> myExecute(SearchUnofficialAlbumRequest nRequest) throws BizException {
        if (StringUtils.isEmpty(nRequest.getTitle()) || StringUtils.isEmpty(nRequest.getSource())) {
            throw new BizException("title or source is empty", 502);
        }
        try {
            return CompletableFuture.completedFuture(unofficialAlbumService.getSimilarAlbum(nRequest));
        } catch (Exception e) {
            log.error("During unofficialAlbumService getSimilarAlbum error:", e);
            throw new BizException(e.getMessage(), 502);
        }
    }

    @Override
    protected ByteString buildProtobufByteString(SearchUnofficialAlbumRequest nRequest, List<UnofficialAlbum> nResponse) throws BizException {
        return null;
    }
}
