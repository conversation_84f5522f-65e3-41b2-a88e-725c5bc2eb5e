package com.heytap.longvideo.search.executors.app;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.MinorsHotTopParam;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.model.param.app.Urlpack;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class MinorsHotTopExecutor extends AbstractNextAsyncExecutorV2<MinorsHotTopParam, List<String>> {

    @Autowired
    private ListFilterService listFilterService;

    protected MinorsHotTopExecutor() {
        super(MinorsHotTopParam.class);
    }

    @Override
    public CompletableFuture<List<String>> myExecute(MinorsHotTopParam req) throws BizException {
        ListFilterParam params = new ListFilterParam();
        params.setMinors("1");
        params.setCallType(1);
        params.setVipType(req.getVipType());
        params.setNumber(req.getPageSize());

        Urlpack urlpack = new Urlpack();
        urlpack.setContentType("all");
        urlpack.setVersion_tag(req.getVersionTag());
        urlpack.setAgeCode(req.getAgeCode());
        urlpack.setSort("hot");

        Map<String, Object> map = new HashMap(2);
        map.put("cmd_vod", urlpack);

        params.setUrlpack(JSON.toJSONString(map));
        List<ProgramAlbumEs> list = this.listFilterService.listFilter(params);
        List result = list.stream().map(ProgramAlbumEs::getSid).collect(Collectors.toList());
        return CompletableFuture.completedFuture(result);
    }

    @Override
    protected ByteString buildProtobufByteString(MinorsHotTopParam request, List response) throws BizException {
        return null;
    }
}