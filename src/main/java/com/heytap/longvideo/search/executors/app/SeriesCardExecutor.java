package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.google.protobuf.Message;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.cmp.SearchSeriesCmp;
import com.heytap.longvideo.search.model.LongVideoSearchCard;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.app.lib.protobuf.ProtoBufUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pb.pbClass.PbLongVideoSearchCard;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/*
 * Description 系列卡二级页
 * Date 15:05 2023/10/23
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class SeriesCardExecutor extends AbstractNextAsyncExecutorV2<InterveneCardParam, LongVideoSearchCard> {

    protected SeriesCardExecutor() {
        super(InterveneCardParam.class);
    }

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1, timeout = 500)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Autowired
    private SearchSeriesCmp searchSeriesCmp;

    @Autowired
    private SearchIntentService searchIntentService;

    @Override
    protected CompletableFuture<LongVideoSearchCard> myExecute(InterveneCardParam param) throws BizException {
        if (StringUtil.isBlank(param.getCode())) {
            log.error("code is null");
            throw new RuntimeException("code is null");
        }

        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getById(Integer.parseInt(param.getCode())).handleAsync((rpcResult, e) -> {
            if (e != null || rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getById error, the request:{}, the response:{}", param.getCode(), rpcResult, e);
                return null;
            }
            SearchInterveneCardResponse searchInterveneCardResponse = FutureUtil.getFutureIgnoreException(
                    searchSeriesCmp.getSeries(rpcResult.getData(), param.getPageIndex(), 12, param.getVipType(),
                            new KeyWordSearchParamV2(), param.getContentType(), param.getSortType()));

            return searchIntentService.searchToLongVideo(searchInterveneCardResponse);
        });
    }


    @Override
    protected ByteString buildProtobufByteString(InterveneCardParam request, LongVideoSearchCard response) throws BizException {
        Message message = ProtoBufUtils.buildMessage(response, PbLongVideoSearchCard.LongVideoSearchCard.getDescriptor());
        return message.toByteString();
    }
}
