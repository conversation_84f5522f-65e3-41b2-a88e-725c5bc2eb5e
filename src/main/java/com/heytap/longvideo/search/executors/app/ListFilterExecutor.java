package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.ListFilterParam;
import com.heytap.longvideo.search.service.app.ListFilterService;
import com.heytap.longvideo.search.service.app.RecommendInfoService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/*
 * Description 影视筛选
 * Date 14:11 2021/11/29
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class ListFilterExecutor extends AbstractNextAsyncExecutorV2<ListFilterParam, List> {

    protected ListFilterExecutor() {
        super(ListFilterParam.class);
    }

    @Autowired
    private ListFilterService listFilterService;

    @Autowired
    private RecommendInfoService recommendInfoService;

    @Override
    protected CompletableFuture<List> myExecute(ListFilterParam param) throws BizException {
        List<ProgramAlbumEs> list = listFilterService.listFilter(param);
        List<KeyWordSearchResponse> returnList = new ArrayList<>();

        for (ProgramAlbumEs programAlbumEs : list) {
            KeyWordSearchResponse keyWordSearchResponse = new KeyWordSearchResponse();
            BeanUtils.copyProperties(programAlbumEs, keyWordSearchResponse);
            keyWordSearchResponse.setLanguages(programAlbumEs.getLanguage());
            keyWordSearchResponse.setDirectors(programAlbumEs.getDirector());
            keyWordSearchResponse.setLinkValue(programAlbumEs.getSid());
            keyWordSearchResponse.setRecommendInfo(programAlbumEs.getBrief());
            keyWordSearchResponse.setShowScore(CommonUtils.remainByScoreAndProgramType(programAlbumEs.getSourceScore(), programAlbumEs.getContentType()) ? 1: 0);
            returnList.add(keyWordSearchResponse);
        }

        if ("1".equals(param.getNeedRecInfo())) {
            recommendInfoService.handleAlbumRankRecommendInfo(list, returnList, param);
        }

        return CompletableFuture.completedFuture(returnResponse(returnList));
    }

    /**
     * 模仿原有搜索系统返回
     *
     * @param esList
     * @return
     */
    public List<Object> returnResponse(List<KeyWordSearchResponse> esList) {
        List<Object> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(esList)) {
            return resultList;
        }
        Map<String, List<KeyWordSearchResponse>> dataMap = new HashMap<>();
        dataMap.put("data", esList);
        List<Map> dataList = new ArrayList<>();
        dataList.add(dataMap);
        Map<String, Object> albumMap = new HashMap<>();
        albumMap.put("album", dataList);
        resultList.add(albumMap);
        return resultList;
    }

    @Override
    protected ByteString buildProtobufByteString(ListFilterParam request, List response) throws BizException {
        return null;
    }
}
