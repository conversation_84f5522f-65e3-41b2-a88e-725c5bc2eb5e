package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.search.constants.SearchConstant;
import com.heytap.longvideo.search.constants.ThirdPartyMediaTypeEnum;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.VersionUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;


@Component
@Slf4j
public class OutSideSearchExecutor extends AbstractNextAsyncExecutorV2<KeyWordSearchParamV2, SearchResponse> {

    public OutSideSearchExecutor(ConvertResponseService beanConvertService,
                                 StandardAlbumService standardAlbumService) {
        super(KeyWordSearchParamV2.class);
        this.beanConvertService = beanConvertService;
        this.standardAlbumService = standardAlbumService;
    }

    private static final Map<String, String> contentTypeName = new HashMap<>();

    @HeraclesDynamicConfig(key = "outside.search.all.switch", fileName = "search_config.properties")
    private Integer allSwitch;

    @HeraclesDynamicConfig(key = "outside.search.minVersion.str", fileName = "search_config.properties")
    private String minVersion = "4.19.0";

    @HeraclesDynamicConfig(key = "outside.search.exact.match.switch", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, Boolean> exactMatchSwitchMap = new HashMap<String, Boolean>() {{
        put(SearchConstant.SEARCH_FROM_MAGAZINE, true);
        put(SearchConstant.SEARCH_FROM_BREENO, false);
    }};

    @Resource
    private FlowExecutor flowExecutor;

    private final ConvertResponseService beanConvertService;

    private final StandardAlbumService standardAlbumService;

    static {
        Map<String, String> map = StandardConstant.ALBUM_TYPE_MAP;
        for (String key : map.keySet()) {
            contentTypeName.put(map.get(key), key);
        }
    }

    @Override
    protected CompletableFuture<SearchResponse> myExecute(KeyWordSearchParamV2 param) throws BizException {
        log.info("outSideSearchExecutor param:{}", param.toString());

        String keyWord = param.getKeyword();
        if (StringUtils.isEmpty(param.getKeyword()) || allSwitch == 0) {
            return CompletableFuture.completedFuture(SearchResponse.emptyResult());
        }

        param.setIsOut(1);

        // 优先取versionName，没有再取version
        String versionName = param.getVersionName();
        if (StringUtils.isEmpty(param.getVersionName())) {
            if (param.getVersion().equals(50000)) {
                versionName = "6.0.2";
                param.setAppVersion(versionName);
                param.setVersion(VersionUtil.getVersion(versionName));
            } else {
                param.setAppVersion(VersionUtil.getVersionName(param.getVersion()));
            }
        } else {
            try {
                versionName = versionName.substring(versionName.indexOf(".") + 1);
                param.setAppVersion(versionName);
                param.setVersion(VersionUtil.getVersion(param.getVersionName()));
            } catch (Exception e) {
                log.error("parse versionName fail:{}", versionName);
            }
        }

        if (CommonUtils.compareVersion(versionName, minVersion) < 0
                // 浏览器播放场景，不校验版本
                && !ThirdPartyMediaTypeEnum.BROSWER_PLAY.getAppId().equals(param.getAppId())) {
            return CompletableFuture.completedFuture(SearchResponse.emptyResult());
        }

        //如果是搜索sid,直接匹配标准媒资
        if (StringUtils.isNotEmpty(keyWord)
                && (keyWord.length() == 18 || keyWord.length() == 19)
                && Pattern.matches(KeyWordSearchExecutor.SID_PATTERN, keyWord)) {
            StandardAlbumEs standardAlbumEs = standardAlbumService.searchBySid(keyWord);
            return CompletableFuture.completedFuture(standardConvertResponse(standardAlbumEs, param));
        }

        param.setVersionTag(getVersionTag(versionName));

        //搜索流程
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        context.setRequestParam(param);

        LiteflowResponse liftResponse;
        if (SearchConstant.EXACT_MATCH_SEARCH_SOURCE.contains(param.getAppId())) {
            context.setExactMatch(exactMatchSwitchMap.get(param.getAppId()));
            liftResponse = flowExecutor.execute2Resp("outSideSearchByKeywordChainV2", null, context);
        } else {
            liftResponse = flowExecutor.execute2Resp("outSideSearchByKeywordChain", null, context);
        }

        if (Objects.isNull(liftResponse)) {
            log.error("flowExecutor error: liftResponse is null");
            return CompletableFuture.completedFuture(new SearchResponse());
        }

        if (!liftResponse.isSuccess()) {
            log.error("flowExecutor error", liftResponse.getCause());
            return CompletableFuture.completedFuture(new SearchResponse());
        }

        return CompletableFuture.completedFuture(context.getSearchResponse());
    }

    private SearchResponse standardConvertResponse(StandardAlbumEs standardAlbumEs, KeyWordSearchParamV2 param) {
        if (standardAlbumEs == null) {
            return SearchResponse.emptyResult();
        }
        return SearchResponse.singleResult(beanConvertService.standardAlbumToSearchResponse(standardAlbumEs, param));
    }

    private static int getVersionTag(String versionName) {
        int versionTag = 10;
        if (CommonUtils.compareVersion(versionName, "4.19.0") < 0) {
            versionTag = 1;
        } else if (CommonUtils.compareVersion(versionName, "4.23.0") < 0) {
            versionTag = 2;
        } else if (CommonUtils.compareVersion(versionName, "5.0.0") < 0) {
            versionTag = 3;
        } else if (CommonUtils.compareVersion(versionName, "5.23.0") < 0) {
            versionTag = 4;
        } else if (CommonUtils.compareVersion(versionName, "6.3.0") < 0) {
            versionTag = 5;
        } else if (CommonUtils.compareVersion(versionName, "6.8.0") < 0) {
            versionTag = 6;
        } else if (CommonUtils.compareVersion(versionName, "7.12.0") < 0) {
            versionTag = 7;
        } else if (CommonUtils.compareVersion(versionName, "7.16.0") < 0) {
            versionTag = 8;
        } else if (CommonUtils.compareVersion(versionName, "8.1.0") < 0) {
            versionTag = 9;
        }
        return versionTag;
    }


    @Override
    protected ByteString buildProtobufByteString(KeyWordSearchParamV2 request, SearchResponse response) throws BizException {
        return null;
    }
}