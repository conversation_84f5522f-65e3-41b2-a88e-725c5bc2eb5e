package com.heytap.longvideo.search.executors.standard;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.MisAlbumOrgRpcApi;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.longvideo.client.media.entity.MisVideoOrg;
import com.heytap.longvideo.search.mapper.media.MisAlbumOrgMapper;
import com.heytap.longvideo.search.mapper.media.MisVideoOrgMapper;
import com.heytap.longvideo.search.model.param.SyncSohuMediaByAlbumIdParam;
import com.heytap.longvideo.search.model.param.SyncSohuMediaParam;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class SyncSohuMediaInfoByAlbumIdExecutor extends AbstractNextAsyncExecutorV2<SyncSohuMediaByAlbumIdParam, String> {

    protected SyncSohuMediaInfoByAlbumIdExecutor() {
        super(SyncSohuMediaByAlbumIdParam.class);
    }

    @Autowired
    private MisAlbumOrgMapper misAlbumOrgMapper;

    @Autowired
    private MisVideoOrgMapper misVideoOrgMapper;

    @Autowired
    private HttpDataChannel httpDataChannel;

    private String orgAlbumInfoUrl = "http://open.mb.hd.sohu.com/v4/album/info/{sourceAlbumId}.json?api_key=706d814c3f48326c52700c4ce9594540";

    private String orgVideoInfoUrl = "http://open.mb.hd.sohu.com/v4/video/info/{sourceVideoId}.json?api_key=706d814c3f48326c52700c4ce9594540&aid={sourceAlbumId}";

    @Override
    protected CompletableFuture<String> myExecute(SyncSohuMediaByAlbumIdParam request) {

        /**
         * statusText = 'OK'
         */
        syncAlbum(request.getSourceAlbumId(), request.getDatabaseIndex(), request.getTableIndex());
        syncVideo(request.getSourceAlbumId(), request.getDatabaseIndex(), request.getTableIndex());

        return CompletableFuture.completedFuture("ok");
    }

    private void syncAlbum(String sourceAlbumId, int databaseIndex, int tableIndex) {
        MisAlbumOrg orgAlbum = misAlbumOrgMapper.getOrgAlbumByAlbumId(databaseIndex, tableIndex, sourceAlbumId);
        String url = orgAlbumInfoUrl.replace("{sourceAlbumId}", sourceAlbumId);
        try {
            String response = httpDataChannel.getForObject(url, String.class);
            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(response);
            String statusText = jsonObject.getString("statusText");
            if ("OK".equals(statusText)) {
                orgAlbum.setSourceStatus("update");
            } else {
                orgAlbum.setSourceStatus("del");
            }
            misAlbumOrgMapper.updateStatus(databaseIndex, tableIndex, orgAlbum.getSourceStatus(), orgAlbum.getSourceAlbumId());
        } catch (Exception e) {
            log.warn("get sohu albumInfo error! sourceAlbumId:[{}]", sourceAlbumId);
        }
    }

    private void syncVideo(String sourceAlbumId, int databaseIndex, int tableIndex) {
        Long id = 0L;
        int size = 200;
        List<MisVideoOrg> orgVideos = Lists.newArrayList();
        do {
            orgVideos = misVideoOrgMapper.getOrgVideoBatchByAlbumId(databaseIndex, tableIndex, sourceAlbumId, id, size);
            for (MisVideoOrg orgVideo : orgVideos) {
                String url = orgVideoInfoUrl.replace("{sourceAlbumId}", orgVideo.getSourceAlbumId()).replace("{sourceVideoId}", orgVideo.getSourceVideoId());
                try {
                    String response = httpDataChannel.getForObject(url, String.class);
                    JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(response);
                    String statusText = jsonObject.getString("statusText");
                    if ("OK".equals(statusText)) {
                        orgVideo.setSourceStatus("update");
                    } else {
                        orgVideo.setSourceStatus("del");
                    }
                    misVideoOrgMapper.updateStatus(databaseIndex, tableIndex, orgVideo.getSourceStatus(), orgVideo.getSourceAlbumId(), orgVideo.getSourceVideoId());
                } catch (Exception e) {
                    log.warn("get sohu videoInfo error! sourceAlbumId:[{}], sourceVideoId:[{}]", orgVideo.getSourceAlbumId(), orgVideo.getSourceVideoId());
                }
            }
            id = orgVideos.get(orgVideos.size() - 1).getId();
        } while (orgVideos.size() == size);
    }

    @Override
    protected ByteString buildProtobufByteString(SyncSohuMediaByAlbumIdParam request, String response) throws BizException {
        return null;
    }

}
