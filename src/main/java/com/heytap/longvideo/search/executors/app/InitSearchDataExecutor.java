package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.service.app.InitService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.next.executor.NextRequest;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/*
 * Description 搜索数据初始化导入
 * Date 14:45 2021/11/9
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class InitSearchDataExecutor extends AbstractNextAsyncExecutorV2<NextRequest, Boolean> {

    protected InitSearchDataExecutor() {
        super(NextRequest.class);
    }

    @Autowired
    private InitService initService;

    @Override
    protected CompletableFuture<Boolean> myExecute(NextRequest request) throws BizException {
        if (!"oppo123".equals(request.getSession())) {
            return CompletableFuture.completedFuture(true);
        }
        initService.initData();
        return CompletableFuture.completedFuture(true);
    }


    @Override
    protected ByteString buildProtobufByteString(NextRequest request, Boolean response) throws BizException {
        return null;
    }
}
