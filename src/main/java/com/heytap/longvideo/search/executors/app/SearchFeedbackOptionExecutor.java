package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.app.SearchFeedbackOption;
import com.heytap.longvideo.search.service.app.SearchFeedbackService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.next.executor.NextRequest;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class SearchFeedbackOptionExecutor extends AbstractNextAsyncExecutorV2<NextRequest, List<SearchFeedbackOption>> {

    @Autowired
    private SearchFeedbackService searchFeedbackService;

    protected SearchFeedbackOptionExecutor() {
        super(NextRequest.class);
    }

    @Override
    protected CompletableFuture<List<SearchFeedbackOption>> myExecute(NextRequest nextRequest) throws BizException {
        return CompletableFuture.completedFuture(searchFeedbackService.getOptions());
    }

    @Override
    protected ByteString buildProtobufByteString(NextRequest nextRequest, List list) throws BizException {
        return null;
    }
}
