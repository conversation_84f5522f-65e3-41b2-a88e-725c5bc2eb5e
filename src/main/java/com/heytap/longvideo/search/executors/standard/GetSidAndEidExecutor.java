package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.service.standard.StandardEpisodeService;
import com.heytap.longvideo.search.service.standard.StandardTrailerService;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class GetSidAndEidExecutor extends AbstractNextAsyncExecutor<GetSidAndEidParam, StandardResult<GetSidAndEidVo>> {

    @Autowired
    private StandardVideoService standardVideoService;

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    @Autowired
    private StandardTrailerService standardTrailerService;

    protected GetSidAndEidExecutor() {
        super(GetSidAndEidParam.class);
    }

    @Override
    protected CompletableFuture<StandardResult<GetSidAndEidVo>> myExecute(GetSidAndEidParam nRequest) throws BizException {
        try {
            GetSidAndEidVo vo = new GetSidAndEidVo();
            SearchStandardVideoParams videoParams = new SearchStandardVideoParams();
            videoParams.setVid(nRequest.getVid());
            List<StandardVideoVo> videoList = standardVideoService.searchVideo(videoParams).getItemList();
            if (CollectionUtils.isEmpty(videoList)) {
                return CompletableFuture.completedFuture(StandardResult.success(vo));
            }
            StringBuffer sidBuffer = new StringBuffer("");
            StringBuffer eidBuffer = new StringBuffer("");
            if (videoList.get(0).getVideoType() == StandardConstant.VideoType.FEATURE) {
                SearchStandardEpisodeParams episodeParams = new SearchStandardEpisodeParams();
                episodeParams.setVid(nRequest.getVid());
                List<StandardEpisodeVo> itemList = standardEpisodeService.searchEpisode(episodeParams).getItemList();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    itemList.forEach(item -> {
                        sidBuffer.append(item.getSid() + "|");
                        eidBuffer.append(item.getEid() + "|");
                    });
                    sidBuffer.deleteCharAt(sidBuffer.length() - 1);
                    eidBuffer.deleteCharAt(eidBuffer.length() - 1);
                }
            } else {
                SearchStandardTrailerParams trailerParams = new SearchStandardTrailerParams();
                trailerParams.setVid(nRequest.getVid());
                List<StandardTrailerVo> itemList = standardTrailerService.searchTrailer(trailerParams).getItemList();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    itemList.forEach(item -> {
                        sidBuffer.append(item.getSid() + "|");
                    });
                    sidBuffer.deleteCharAt(sidBuffer.length() - 1);
                }
            }
            vo.setSid(sidBuffer.toString());
            vo.setEid(eidBuffer.toString());
            return CompletableFuture.completedFuture(StandardResult.success(vo));
        } catch (Exception e) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(GetSidAndEidParam nRequest, StandardResult<GetSidAndEidVo> nResponse) throws BizException {
        return null;
    }
}
