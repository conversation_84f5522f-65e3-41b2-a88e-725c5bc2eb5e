package com.heytap.longvideo.search.executors.standard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardEpisodeParams;
import com.heytap.longvideo.search.model.param.standard.StandardEpisodeVo;
import com.heytap.longvideo.search.service.standard.StandardEpisodeService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class MediaStandardEpisodeExecutor extends AbstractNextAsyncExecutor<SearchStandardEpisodeParams, StandardResult<PageResponse<StandardEpisodeVo>>> {

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    @Autowired
    private HttpDataChannel httpClient;

    @HeraclesDynamicConfig(key = "tvEpisode.search.url", fileName = "search_config.properties")
    private String tvEpisodeSearchUrl;

    protected MediaStandardEpisodeExecutor() {
        super(SearchStandardEpisodeParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<PageResponse<StandardEpisodeVo>>> myExecute(SearchStandardEpisodeParams nRequest) throws BizException {
        if (StringUtil.isEmpty(nRequest.getSourceStatus()) && StringUtil.isNotEmpty(nRequest.getOriginStatus())) {
            nRequest.setSourceStatus(nRequest.getOriginStatus());
        }
        if("youku".equals(nRequest.getSource())||"mgtv".equals(nRequest.getSource())||"bilibili".equals(nRequest.getSource())) {
            Map<String, Object> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            try {
                String response = httpClient.postForObjectWithGenerics(tvEpisodeSearchUrl, nRequest, String.class, null, 3000);
                StandardResult<PageResponse<StandardEpisodeVo>> standardResult = JSON.parseObject(response, new TypeReference<StandardResult<PageResponse<StandardEpisodeVo>>>(){});
                return CompletableFuture.completedFuture(standardResult);
            }catch (Exception e){
                log.error("tvEpisodeSearchUrl error",e);
                return CompletableFuture.completedFuture(StandardResult.fail(400,"查询电视媒资失败"));
            }
        }
        try {
            PageResponse<StandardEpisodeVo> pageResponse = standardEpisodeService.searchEpisode(nRequest);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(SearchStandardEpisodeParams nRequest, StandardResult<PageResponse<StandardEpisodeVo>> nResponse) throws BizException {
        return null;
    }
}
