package com.heytap.longvideo.search.executors.app;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.cmp.SearchTagCmp;
import com.heytap.longvideo.search.model.LongVideoSearchCard;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.app.lib.protobuf.ProtoBufUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pb.pbClass.PbLongVideoSearchCard;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2023/10/25
 */
@Component
@Slf4j
public class TagCardExecutor extends AbstractNextAsyncExecutorV2<InterveneCardParam, LongVideoSearchCard> {
    protected TagCardExecutor() {
        super(InterveneCardParam.class);
    }

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1, timeout = 500)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Autowired
    private SearchTagCmp searchTagCmp;

    @Autowired
    private SearchIntentService searchIntentService;

    @Override
    protected CompletableFuture<LongVideoSearchCard> myExecute(InterveneCardParam request) throws BizException {
        if (StringUtil.isBlank(request.getCode())) {
            log.error("code is null, request:{}", JSON.toJSONString(request));
            throw new RuntimeException("code is null");
        }

        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getById(Integer.parseInt(request.getCode())).handleAsync((rpcResult, e) -> {
            if (e != null || rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode() || rpcResult.getData() == null) {
                log.error("getById error, the request:{}, the response:{}", request.getCode(), rpcResult, e);
                return null;
            }
            KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
            requestParam.setVersion(request.getVersion());
            requestParam.setVipType(request.getVipType());
            SearchInterveneCardResponse searchCardResponse = FutureUtil.getFutureIgnoreException(
                    searchTagCmp.getTagContent(requestParam, rpcResult.getData(), request.getPageIndex(), request.getSortType(), request.getContentType()));
            return searchIntentService.searchToLongVideo(searchCardResponse);
        });

    }

    @Override
    protected ByteString buildProtobufByteString(InterveneCardParam nRequest, LongVideoSearchCard nResponse) throws BizException {
        Message message = ProtoBufUtils.buildMessage(nResponse, PbLongVideoSearchCard.LongVideoSearchCard.getDescriptor());
        return message.toByteString();
    }
}
