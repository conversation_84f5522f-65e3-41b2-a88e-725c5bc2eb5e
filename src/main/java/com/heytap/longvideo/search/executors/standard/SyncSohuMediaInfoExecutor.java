package com.heytap.longvideo.search.executors.standard;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import com.heytap.longvideo.client.media.entity.MisVideoOrg;
import com.heytap.longvideo.search.mapper.media.MisAlbumOrgMapper;
import com.heytap.longvideo.search.mapper.media.MisVideoOrgMapper;
import com.heytap.longvideo.search.model.param.SyncSohuMediaParam;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class SyncSohuMediaInfoExecutor extends AbstractNextAsyncExecutorV2<SyncSohuMediaParam, String> {

    protected SyncSohuMediaInfoExecutor() {
        super(SyncSohuMediaParam.class);
    }

    @Autowired
    private MisAlbumOrgMapper misAlbumOrgMapper;

    @Autowired
    private MisVideoOrgMapper misVideoOrgMapper;

    @Autowired
    private HttpDataChannel httpDataChannel;

    private String orgAlbumInfoUrl = "http://open.mb.hd.sohu.com/v4/album/info/{sourceAlbumId}.json?api_key=706d814c3f48326c52700c4ce9594540";

    private String orgVideoInfoUrl = "http://open.mb.hd.sohu.com/v4/video/info/{sourceVideoId}.json?api_key=706d814c3f48326c52700c4ce9594540&aid={sourceAlbumId}";

    @Override
    protected CompletableFuture<String> myExecute(SyncSohuMediaParam request) {

        /**
         * statusText = 'OK'
         */
        for (int databaseIndex = 0; databaseIndex < request.getDatabaseCount() - 1; databaseIndex++) {
            for (int tableIndex = 0; tableIndex < request.getTableCount() - 1; tableIndex++) {
                syncAlbum(databaseIndex, tableIndex);
                syncVideo(databaseIndex, tableIndex);
            }
        }

        return CompletableFuture.completedFuture("ok");
    }

    private void syncAlbum(int databaseIndex, int tableIndex) {
        Long id = 0L;
        int size = 200;
        List<MisAlbumOrg> orgAlbums = Lists.newArrayList();
        do {
            orgAlbums = misAlbumOrgMapper.getOrgAlbumBatch(databaseIndex, tableIndex, "sohu", id, size);
            for (MisAlbumOrg orgAlbum : orgAlbums) {
                String url = orgAlbumInfoUrl.replace("{sourceAlbumId}", orgAlbum.getSourceAlbumId());
                try {
                    String response = httpDataChannel.getForObject(url, String.class);
                    JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(response);
                    String statusText = jsonObject.getString("statusText");
                    if ("OK".equals(statusText)) {
                        orgAlbum.setSourceStatus("update");
                    } else {
                        orgAlbum.setSourceStatus("del");
                    }
                    misAlbumOrgMapper.updateStatus(databaseIndex, tableIndex, orgAlbum.getSourceStatus(), orgAlbum.getSourceAlbumId());
                } catch (Exception e) {
                    log.warn("get sohu albumInfo error! sourceAlbumId:[{}]", orgAlbum.getSourceAlbumId());
                }
            }
            id = orgAlbums.get(orgAlbums.size() - 1).getId();
        } while (orgAlbums.size() == size);
    }

    private void syncVideo(int databaseIndex, int tableIndex) {
        Long id = 0L;
        int size = 200;
        List<MisVideoOrg> orgVideos = Lists.newArrayList();
        do {
            orgVideos = misVideoOrgMapper.getOrgVideoBatch(databaseIndex, tableIndex, "sohu", id, size);
            for (MisVideoOrg orgVideo : orgVideos) {
                String url = orgVideoInfoUrl.replace("{sourceAlbumId}", orgVideo.getSourceAlbumId()).replace("{sourceVideoId}", orgVideo.getSourceVideoId());
                try {
                    String response = httpDataChannel.getForObject(url, String.class);
                    JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(response);
                    String statusText = jsonObject.getString("statusText");
                    if ("OK".equals(statusText)) {
                        orgVideo.setSourceStatus("update");
                    } else {
                        orgVideo.setSourceStatus("del");
                    }
                    misVideoOrgMapper.updateStatus(databaseIndex, tableIndex, orgVideo.getSourceStatus(), orgVideo.getSourceAlbumId(), orgVideo.getSourceVideoId());
                } catch (Exception e) {
                    log.warn("get sohu videoInfo error! sourceAlbumId:[{}], sourceVideoId:[{}]", orgVideo.getSourceAlbumId(), orgVideo.getSourceVideoId());
                }
            }
            id = orgVideos.get(orgVideos.size() - 1).getId();
        } while (orgVideos.size() == size);
    }

    @Override
    protected ByteString buildProtobufByteString(SyncSohuMediaParam request, String response) throws BizException {
        return null;
    }

}
