package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.UgcSearchVideoParams;
import com.heytap.longvideo.search.model.param.standard.UgcStandardVideoVo;
import com.heytap.longvideo.search.service.standard.UgcStandardVideoService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/10/25 19:17
 */
@Slf4j
@Service
public class UgcGetVideoPageListExecutor extends AbstractNextAsyncExecutor<UgcSearchVideoParams,
        StandardResult<PageResponse<UgcStandardVideoVo>>> {

    protected UgcGetVideoPageListExecutor() {
        super(UgcSearchVideoParams.class);
    }

    @Autowired
    private UgcStandardVideoService ucgStandardVideoService;

    @Override
    protected CompletableFuture<StandardResult<PageResponse<UgcStandardVideoVo>>> myExecute(
            UgcSearchVideoParams request) throws BizException {
        try{
            PageResponse<UgcStandardVideoVo> pageResponse = ucgStandardVideoService.searchVideo(request);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400, e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(UgcSearchVideoParams searchStandardVideoParams,
        StandardResult<PageResponse<UgcStandardVideoVo>> pageResponseStandardResult) throws BizException {
        return null;
    }

}