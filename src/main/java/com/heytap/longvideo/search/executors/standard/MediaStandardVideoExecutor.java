package com.heytap.longvideo.search.executors.standard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardVideoParams;
import com.heytap.longvideo.search.model.param.standard.StandardVideoVo;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/*
 * Description 搜索视频
 * Date 14:35 2022/3/21
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class MediaStandardVideoExecutor extends AbstractNextAsyncExecutor<SearchStandardVideoParams, StandardResult<PageResponse<StandardVideoVo>>> {

    protected MediaStandardVideoExecutor() {
        super(SearchStandardVideoParams.class);
    }

    @Autowired
    private StandardVideoService standardVideoService;

    @Autowired
    private HttpDataChannel httpClient;

    @HeraclesDynamicConfig(key = "tvVideo.search.url", fileName = "search_config.properties")
    private String tvVideoSearchUrl;

    @Override
    protected CompletableFuture<StandardResult<PageResponse<StandardVideoVo>>> myExecute(SearchStandardVideoParams request) {
        try {
            if("youku".equals(request.getSource())||"mgtv".equals(request.getSource())||"bilibili".equals(request.getSource())) {
                Map<String, Object> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                try {
                    String response = httpClient.postForObjectWithGenerics(tvVideoSearchUrl, request, String.class, null, 3000);
                    StandardResult<PageResponse<StandardVideoVo>> standardResult = JSON.parseObject(response, new TypeReference<StandardResult<PageResponse<StandardVideoVo>>>(){});
                    return CompletableFuture.completedFuture(standardResult);
                }catch (Exception e){
                    log.error("tvVideoSearchUrl error",e);
                    return CompletableFuture.completedFuture(StandardResult.fail(400,"查询电视媒资失败"));
                }
            }
            PageResponse<StandardVideoVo> pageResponse = standardVideoService.searchVideo(request);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }


    @Override
    protected ByteString buildProtobufByteString(SearchStandardVideoParams request, StandardResult<PageResponse<StandardVideoVo>> response) throws BizException {
        return null;
    }

}
