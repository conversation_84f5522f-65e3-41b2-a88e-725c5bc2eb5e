package com.heytap.longvideo.search.executors.app;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;
import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.enums.TemplateDateTypeEnum;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.cmp.SearchSeriesCmp;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.properties.SearchProperties;
import com.heytap.longvideo.search.rpc.consumer.AlbumRankRpcApiProxy;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.common.RelationListService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.video.client.entity.drawitem.VideoDrawerItem;
import com.heytap.video.client.entity.list.VideoPageListV3;
import com.heytap.video.pb.list.PbVideoPageList;
import com.oppo.browser.common.app.lib.protobuf.ProtoBufUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/*
 * 系列卡二级页-长短统一
 */
@Component
@Slf4j
public class SeriesCardV2Executor extends AbstractNextAsyncExecutorV2<InterveneCardParam, VideoPageListV3> {

    protected SeriesCardV2Executor() {
        super(InterveneCardParam.class);
    }

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1, timeout = 500)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Autowired
    private SearchSeriesCmp searchSeriesCmp;

    @Autowired
    private SearchIntentService searchIntentService;

    @Autowired
    private TaskUnlockEpisodeService taskUnlockEpisodeService;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private ConvertResponseService convertResponseService;

    @Autowired
    private AlbumRankRpcApiProxy albumRankRpcApiProxy;

    @Autowired
    private RelationListService relationListService;

    @Override
    protected CompletableFuture<VideoPageListV3> myExecute(InterveneCardParam param) throws BizException {
        if (StringUtil.isBlank(param.getCode())) {
            log.error("code is null");
            throw new RuntimeException("code is null");
        }

        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getById(Integer.parseInt(param.getCode())).handleAsync((rpcResult, e) -> {
            if (e != null || rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getById error, the request:{}, the response:{}", param.getCode(), rpcResult, e);
                return null;
            }
            KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
            requestParam.setVersion(param.getVersion());
            requestParam.setQuickEngineVersion(param.getQuickEngineVersion());
            SearchInterveneCardResponse searchInterveneCardResponse = FutureUtil.getFutureIgnoreException(
                    searchSeriesCmp.getSeries(rpcResult.getData(), param.getPageIndex(), 12, param.getVipType(),
                            requestParam, param.getContentType(), param.getSortType()));
            taskUnlockEpisodeService.handleMarkCode4InterveneCard(param, searchInterveneCardResponse);

            boolean needHeadContent = needHeadContent(param, searchInterveneCardResponse);
            if(CollectionUtils.isNotEmpty(searchInterveneCardResponse.getContents())){
                convertResponseService.handleSubTitle(searchInterveneCardResponse.getContents(),param.getVersion());
            }
            if (needHeadContent) {
                List<String> canSubscribeSidList = convertResponseService.getCanSubscribeSid(searchInterveneCardResponse.getContents());
                //这里需要过滤掉既不能预约又没有预告片的预告
                searchInterveneCardResponse.setContents(searchInterveneCardResponse.getContents().stream().filter(keyWordSearchResponse ->
                        convertResponseService.isNeedFilter(keyWordSearchResponse, canSubscribeSidList)).collect(Collectors.toList()));
                if(CollectionUtils.isNotEmpty(searchInterveneCardResponse.getContents())){
                    prepareHeadContent(searchInterveneCardResponse.getContents().get(0), CommonUtils.getUid(param.getSession()), param.getVersion(),canSubscribeSidList);
                }
            }

            VideoPageListV3 videoPageListV3 = searchIntentService.searchToPageListV3(
                    searchInterveneCardResponse, TemplateDateTypeEnum.SERIES_CARD, param.getVersion());
            if (needHeadContent) {
                convertHeadContent(videoPageListV3);
            }
            return videoPageListV3;
        });
    }


    @Override
    protected ByteString buildProtobufByteString(InterveneCardParam request, VideoPageListV3 response) throws BizException {
        Message message = ProtoBufUtils.buildMessage(response, PbVideoPageList.VideoPageListV3.getDescriptor());
        return message.toByteString();
    }

    /**
     * 8.9 系列卡二级页接口在综合tab下发首位资源
     * @param param
     * @param searchInterveneCardResponse
     * @return
     */
    private boolean needHeadContent(InterveneCardParam param, SearchInterveneCardResponse searchInterveneCardResponse) {
        if (CollectionUtils.isEmpty(searchInterveneCardResponse.getContents())) {
            return false;
        }
        if (param.getVersion() == null || param.getVersion() < searchProperties.getSearchCardOptVersion()) {
            return false;
        }
        if (!"SEARCH_PAGE".equals(param.getScene())) {
            return false;
        }
        // 只有首页+无筛选+默认排序时，才下发首位推荐
        if (param.getPageIndex() != 1 || param.getSortType() != 1 ||
                StringUtil.isNotBlank(param.getContentType()) || "all".equals(param.getContentType())) {
            return false;
        }
        return true;
    }

    private void prepareHeadContent(KeyWordSearchResponse keyWordSearchResponse, String uid, Integer version,List<String> canSubscribeSidList) {
        Map<String, AlbumRecommendInfo> recommendInfoMap = FutureUtil.getFutureIgnoreException(
                albumRankRpcApiProxy.getAlbumRecommendInfo(Sets.newHashSet(keyWordSearchResponse.getSid()), true));
        //收藏和预约都在favoriteMap中
        Map<String, Integer> favoriteMap = relationListService.requestRelationList(uid, Lists.newArrayList(keyWordSearchResponse.getSid()));
        //构建showMsg字段文案
        convertResponseService.handleShowMsg(keyWordSearchResponse, true, version);
        //构建简介
        convertResponseService.buildNewResultCardBaseInfo(keyWordSearchResponse);
        //构建推荐理由
        convertResponseService.buildNewResultHighLights(keyWordSearchResponse, recommendInfoMap);
        //构建下发的按钮列表
        convertResponseService.buildNewResultButtons(keyWordSearchResponse, favoriteMap, canSubscribeSidList,null);
    }

    /**
     * 二级接口返回首位推荐
     */
    private void convertHeadContent(VideoPageListV3 videoPageListV3) {
        VideoDrawerItem headContent = videoPageListV3.getElements().get(0).getContents().remove(0);
        videoPageListV3.getElements().get(0).setHeadContent(headContent);
    }
}
