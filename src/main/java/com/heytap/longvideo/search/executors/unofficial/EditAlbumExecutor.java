package com.heytap.longvideo.search.executors.unofficial;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.unofficial.request.EditAlbumRequest;
import com.heytap.longvideo.search.service.spider.UnofficialAlbumImageUrlTransformService;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * @Description: 编辑节目信息 - executor
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/15 17:46
 */
@Slf4j
@Service
public class EditAlbumExecutor extends AbstractNextAsyncExecutorV2<EditAlbumRequest, Result<Void>> {

    private final Executor upLoadToOcsThreadPool;

    private final UnofficialAlbumService unofficialAlbumService;

    private final UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService;

    protected EditAlbumExecutor(@Qualifier("upLoadToOcsThreadPool") Executor upLoadToOcsThreadPool,
                                UnofficialAlbumService unofficialAlbumService,
                                UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService) {
        super(EditAlbumRequest.class);
        this.upLoadToOcsThreadPool = upLoadToOcsThreadPool;
        this.unofficialAlbumService = unofficialAlbumService;
        this.unofficialAlbumImageUrlTransformService = unofficialAlbumImageUrlTransformService;
    }

    /**
     * @Description: 编辑节目信息
     * @param nRequest 请求体
     * @return CompletableFuture<Result<UnofficialAlbumEs>>
     */
    @Override
    protected CompletableFuture<Result<Void>> myExecute(EditAlbumRequest nRequest) throws BizException {
        if (Objects.isNull(nRequest)) {
            throw new BizException("request is null", 502);
        }

        if (StringUtils.isEmpty(nRequest.getSid())) {
            throw new BizException("sid is empty", 502);
        }

        // 修改节目信息
        UnofficialAlbumEs unofficialAlbumEs = editAlbum(nRequest);
        if (Objects.isNull(unofficialAlbumEs.getManagerStatus())) {
            unofficialAlbumEs.setManagerStatus(0);
        }

        try {
            unofficialAlbumService.saveOrUpdate(unofficialAlbumEs);
        } catch (IOException e) {
            throw new BizException(e.getMessage(), 502);
        }
        try {
            // 人工编辑内容同步
            upLoadToOcsThreadPool.execute(() ->
                    unofficialAlbumImageUrlTransformService.generateImageAndSyncToEs(unofficialAlbumEs, false, true)
            );
            unofficialAlbumService.mqSendMsg(unofficialAlbumEs, 6);
        }catch (Exception e){
            log.error("unofficial album sends msg to mq error",e);
        }
        return CompletableFuture.completedFuture(Result.success(null));
    }

    /**
     * @Description: 构建Protobuf格式的返回结果，子类必须实现
     * @param nRequest 请求体
     * @param nResponse 响应体
     * @return ByteString
     */
    @Override
    protected ByteString buildProtobufByteString(EditAlbumRequest nRequest, Result<Void> nResponse) throws BizException {
        return null;
    }

    private UnofficialAlbumEs editAlbum(EditAlbumRequest request) {
        UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
        BeanUtils.copyProperties(request, unofficialAlbumEs);

        unofficialAlbumEs.setUpdateTime(new Date());
        return unofficialAlbumEs;
    }
}