package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.CmsEpisodeVo;
import com.heytap.longvideo.search.model.param.standard.CmsSearchEpisodeParams;
import com.heytap.longvideo.search.service.standard.StandardEpisodeService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class CmsEpisodeDetailExecutor extends AbstractNextAsyncExecutor<CmsSearchEpisodeParams, StandardResult<CmsEpisodeVo>> {

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    protected CmsEpisodeDetailExecutor() {
        super(CmsSearchEpisodeParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<CmsEpisodeVo>> myExecute(CmsSearchEpisodeParams nRequest) throws BizException {
        if (StringUtil.isNotEmpty(nRequest.getOriginStatus())) {
            nRequest.setSourceStatus(nRequest.getOriginStatus());
        }
        if (StringUtil.isNotEmpty(nRequest.getId())) {
            nRequest.setEid(nRequest.getId());
        }
        try {
            List<CmsEpisodeVo> itemList = standardEpisodeService.searchCmsEpisode(nRequest).getItemList();
            CmsEpisodeVo cmsEpisodeVo = new CmsEpisodeVo();
            if (CollectionUtils.isNotEmpty(itemList)) {
                cmsEpisodeVo = itemList.get(0);
            }
            return CompletableFuture.completedFuture(StandardResult.success(cmsEpisodeVo));
        } catch (Exception e) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(CmsSearchEpisodeParams nRequest, StandardResult<CmsEpisodeVo> nResponse) throws BizException {
        return null;
    }
}
