package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.es.*;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/*
 * Description 数据初始化
 * Date 14:35 2022/3/21
 * Author songjiajia 80350688
 */
@Slf4j
@Component
public class InitStandardAlbumExecutor extends AbstractNextAsyncExecutorV2<InitDataParam, String> {

    private final InitStandardDataService initStandardDataService;

    private final ElasticSearchService elasticsearchService;

    protected InitStandardAlbumExecutor(InitStandardDataService initStandardDataService,
                                        ElasticSearchService elasticsearchService) {
        super(InitDataParam.class);
        this.initStandardDataService = initStandardDataService;
        this.elasticsearchService = elasticsearchService;
    }

    /**
     * @Description: es索引类型map
     */
    private static final Map<String, Class<?>> elasticSearchIndexMap = new HashMap<String, Class<?>>() {{
        put("album", StandardAlbumEs.class);
        put("video", StandardVideoEs.class);
        put("episode", StandardEpisodeEs.class);
        put("trailer", StandardTrailerEs.class);
        put("ugcVideo", UgcStandardVideoEs.class);
        put("unofficialAlbum", UnofficialAlbumEs.class);
    }};

    @Override
    protected CompletableFuture<String> myExecute(InitDataParam request) {
        try {
            if (!"oppo123".equals(request.getSession())) {
                return CompletableFuture.completedFuture("111");
            }
            // 非合作内容 初始化索引
            if ("unofficialAlbum".equals(request.getType())) {
                return initStandardDataService.initUnofficialAlbum(request);
            }

            Class<?> clazz = elasticSearchIndexMap.get(request.getType());
            elasticsearchService.deleteIndex(clazz);
            elasticsearchService.createIndexAndMapping(clazz);


            return CompletableFuture.completedFuture(initStandardDataService.initData(request));
        } catch (Exception e) {
            log.error("during init index data error occur:", e);
            throw new RuntimeException(e);
        }
    }


    @Override
    protected ByteString buildProtobufByteString(InitDataParam request, String response) throws BizException {
        return null;
    }

}
