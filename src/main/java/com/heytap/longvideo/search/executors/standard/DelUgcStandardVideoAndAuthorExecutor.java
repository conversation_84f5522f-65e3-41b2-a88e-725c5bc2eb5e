package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.es.UgcStandardAuthorEs;
import com.heytap.longvideo.search.model.entity.es.UgcStandardVideoEs;
import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/10/26 14:34
 */
@Component
public class DelUgcStandardVideoAndAuthorExecutor extends AbstractNextAsyncExecutorV2<InitDataParam, String> {
    protected DelUgcStandardVideoAndAuthorExecutor() {
        super(InitDataParam.class);
    }

    @Autowired
    private ElasticSearchService elasticsearchService;

    @Override
    protected CompletableFuture<String> myExecute(InitDataParam initDataParam) throws BizException {
        elasticsearchService.deleteIndex(UgcStandardAuthorEs.class);
        elasticsearchService.deleteIndex(UgcStandardVideoEs.class);
        String res = "del index success";
        return CompletableFuture.completedFuture(res);
    }

    @Override
    protected ByteString buildProtobufByteString(InitDataParam initDataParam, String s) throws BizException {
        return null;
    }
}