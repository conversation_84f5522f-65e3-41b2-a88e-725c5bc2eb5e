package com.heytap.longvideo.search.executors.standard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.entity.StandardPerson;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.ListStandardPersonRequest;
import com.heytap.longvideo.search.service.standard.StandardPersonService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * @Description: 影人列表分页查询 实现逻辑
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/5/13
 */
@Slf4j
@Service
public class ListStandardPersonExecutor extends AbstractNextAsyncExecutor<ListStandardPersonRequest, Result<PageResponse<StandardPerson>>> {

    private final StandardPersonService standardPersonService;

    protected ListStandardPersonExecutor(StandardPersonService standardPersonService) {
        super(ListStandardPersonRequest.class);
        this.standardPersonService = standardPersonService;
    }

    /**
     * @Description: 子类实现业务方法
     * @Param nRequest 请求
     * @Return CompletableFuture<Pagination<StandardPerson>>
     */
    @Override
    public CompletableFuture<Result<PageResponse<StandardPerson>>> myExecute(ListStandardPersonRequest nRequest) throws BizException {
        // 优先走ES查询

        return standardPersonService.listMediaPerson(nRequest).thenApply(ret -> {
            if (ret == null || ret.getCode() != 0) {
                log.error("standardPersonService.listMediaPerson({}) error.", nRequest);
                return Result.error(-999, "standardPersonService.listMediaPerson error.");
            }

            PageResponse<StandardPerson> pagination = new PageResponse<>();
            IPage<StandardPerson> page = ret.getData();

            if (CollectionUtils.isEmpty(page.getRecords())) {
                log.warn("standardPersons is null.");
                return Result.success(null);
            }

            pagination.setItemList(page.getRecords());
            pagination.setCurrentPage((int) page.getCurrent());
            pagination.setItemListSize(page.getRecords().size());
            pagination.setMaxPage((int) page.getPages());
            pagination.setTotalCount(page.getTotal());
            pagination.setPageSize((int) page.getSize());

            return Result.success(pagination);
        });
    }

    /**
     * @Description: 构建Protobuf格式的返回结果，子类必须实现
     * @param nRequest 请求
     * @param nResponse 影人分页查询结果
     * @return pb
     */
    @Override
    protected ByteString buildProtobufByteString(ListStandardPersonRequest nRequest, Result<PageResponse<StandardPerson>> nResponse) throws BizException {
        return null;
    }
}
