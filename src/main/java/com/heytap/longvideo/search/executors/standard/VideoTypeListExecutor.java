package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.VideoTypeListParams;
import com.heytap.longvideo.search.model.param.standard.VideoTypeListVo;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class VideoTypeListExecutor extends AbstractNextAsyncExecutor<VideoTypeListParams, StandardResult<List<VideoTypeListVo>>> {

    @Autowired
    private StandardVideoService standardVideoService;

    protected VideoTypeListExecutor() {
        super(VideoTypeListParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<List<VideoTypeListVo>>> myExecute(VideoTypeListParams nRequest) throws BizException {
        try {
            List<VideoTypeListVo> vos = standardVideoService.getVideoType(nRequest.getSource());
            return CompletableFuture.completedFuture(StandardResult.success(vos));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(VideoTypeListParams nRequest, StandardResult<List<VideoTypeListVo>> nResponse) throws BizException {
        return null;
    }
}
