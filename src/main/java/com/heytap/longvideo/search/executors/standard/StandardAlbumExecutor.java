package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2023/2/15 16:59
 */
@Component
public class StandardAlbumExecutor extends AbstractNextAsyncExecutor<StandardAlbumParams, Result<StandardAlbumDetailVo>> {

    protected StandardAlbumExecutor() {
        super(StandardAlbumParams.class);
    }


    @Autowired
    private StandardAlbumService standardAlbumService;

    @Override
    protected CompletableFuture<Result<StandardAlbumDetailVo>> myExecute(StandardAlbumParams nRequest) throws BizException {
        if (StringUtil.isBlank(nRequest.getSid())) {
            return CompletableFuture.completedFuture(Result.fail("sid is null"));
        }

        StandardAlbumEs standardAlbumEs = standardAlbumService.searchBySid(nRequest.getSid());
        if (standardAlbumEs == null) {
            return CompletableFuture.completedFuture(Result.fail("the album is not exist"));
        }
        StandardAlbumDetailVo detailVo = new StandardAlbumDetailVo();
        BeanUtils.copyProperties(standardAlbumEs, detailVo);
        // 将sourceStatus赋值给originalStatus
        detailVo.setOriginStatus(standardAlbumEs.getSourceStatus());
        return CompletableFuture.completedFuture(Result.success(detailVo));
    }

    @Override
    protected ByteString buildProtobufByteString(StandardAlbumParams standardAlbumParams, Result<StandardAlbumDetailVo> pageResponseStandardResult) throws BizException {
        return null;
    }
}