package com.heytap.longvideo.search.executors.standard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardTrailerParams;
import com.heytap.longvideo.search.model.param.standard.StandardTrailerVo;
import com.heytap.longvideo.search.service.standard.StandardTrailerService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class MediaStandardTrailerExecutor extends AbstractNextAsyncExecutor<SearchStandardTrailerParams, StandardResult<PageResponse<StandardTrailerVo>>> {

    @Autowired
    private StandardTrailerService standardTrailerService;

    @Autowired
    private HttpDataChannel httpClient;

    @HeraclesDynamicConfig(key = "tvTrailer.search.url", fileName = "search_config.properties")
    private String tvTrailerSearchUrl;

    protected MediaStandardTrailerExecutor() {
        super(SearchStandardTrailerParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<PageResponse<StandardTrailerVo>>> myExecute(SearchStandardTrailerParams nRequest) throws BizException {
        if (StringUtil.isBlank(nRequest.getTitle()) && StringUtil.isNotBlank(nRequest.getProgramTitle())) {
            nRequest.setTitle(nRequest.getProgramTitle());
        }
        if (StringUtil.isEmpty(nRequest.getSourceStatus()) && StringUtil.isNotEmpty(nRequest.getOriginStatus())) {
            nRequest.setSourceStatus(nRequest.getOriginStatus());
        }
        if ("youku".equals(nRequest.getSource()) || "mgtv".equals(nRequest.getSource()) || "bilibili".equals(nRequest.getSource())) {
            Map<String, Object> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            try {
                String response = httpClient.postForObjectWithGenerics(tvTrailerSearchUrl, nRequest, String.class, null, 3000);
                StandardResult<PageResponse<StandardTrailerVo>> standardResult = JSON.parseObject(response, new TypeReference<StandardResult<PageResponse<StandardTrailerVo>>>() {
                });
                return CompletableFuture.completedFuture(standardResult);
            } catch (Exception e) {
                log.error("tvTrailerSearchUrl error", e);
                return CompletableFuture.completedFuture(StandardResult.fail(400, "查询电视媒资失败"));
            }
        }
        try {
            PageResponse<StandardTrailerVo> pageResponse = standardTrailerService.searchTrailer(nRequest);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        } catch (Exception e) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(SearchStandardTrailerParams nRequest, StandardResult<PageResponse<StandardTrailerVo>> nResponse) throws BizException {
        return null;
    }
}
