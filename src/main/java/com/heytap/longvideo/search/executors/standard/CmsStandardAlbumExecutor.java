package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.JackSonDataToStringUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/*
 * Description 编排搜索剧头
 * Date 14:35 2022/3/21
 * Author songjiajia 80350688
 */
@Component
public class CmsStandardAlbumExecutor extends AbstractNextAsyncExecutor<SearchStandardAlbumParams, StandardResult<PageResponse<StandardAlbumVo>>> {

    protected CmsStandardAlbumExecutor() {
        super(SearchStandardAlbumParams.class);
    }

    @Autowired
    private StandardAlbumService standardAlbumService;

    @Override
    protected CompletableFuture<StandardResult<PageResponse<StandardAlbumVo>>> myExecute(SearchStandardAlbumParams request) {
        if (StringUtil.isNotBlank(request.getOriginStatus())&& StringUtil.isBlank(request.getSourceStatus())) {
            request.setSourceStatus(request.getOriginStatus());
        }
        request.setChannel(2);
        try {
            if (request.getSourceScoreStart() != null && (request.getSourceScoreStart() > 10F || request.getSourceScoreStart() < 0)) {
                return CompletableFuture.completedFuture(StandardResult.fail(400, "无效的评分区间"));
            }
            if (request.getSourceScoreEnd() != null && (request.getSourceScoreEnd() > 10F || request.getSourceScoreEnd() < 0)) {
                return CompletableFuture.completedFuture(StandardResult.fail(400, "无效的评分区间"));
            }
            PageResponse<StandardAlbumVo> pageResponse = standardAlbumService.searchAlbum(request);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }


    @Override
    protected ByteString buildProtobufByteString(SearchStandardAlbumParams request, StandardResult<PageResponse<StandardAlbumVo>> response) throws BizException {
        return null;
    }

    @Override
    protected ByteString buildJosnByteString(SearchStandardAlbumParams nRequest, StandardResult<PageResponse<StandardAlbumVo>> nResponse) throws BizException {
        return (nResponse == null) ? null : ByteString.copyFrom(JackSonDataToStringUtil.toJsonBytes(nResponse));
    }

}
