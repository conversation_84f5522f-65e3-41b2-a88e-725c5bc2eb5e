package com.heytap.longvideo.search.executors.inside;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.cmp.SearchTagCmp;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.inside.SearchByTagRequest;
import com.heytap.longvideo.search.model.param.inside.SearchByTagResponse;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.trace.async.TraceBiFunction;
import com.oppo.trace.async.TraceFunction;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2025/4/21
 */
@Service
@Slf4j
public class SearchByTagExecutor {

    @Autowired
    private SearchTagCmp searchTagCmp;

    public CompletableFuture<SearchByTagResponse> myExecute(SearchByTagRequest request) {
        log.info("start search by tag success, keyword:{}, requestId:{}", request.getKeyword(), request.getRequestId());
        SearchByTagResponse response = new SearchByTagResponse();
        LvSearchIntervene lvSearchIntervene = new LvSearchIntervene();
        lvSearchIntervene.setId(-1);
        lvSearchIntervene.setTitle("固定标签卡");
        lvSearchIntervene.setLinkType(TemplateLinkTypeEnum.TAG.getCode());
        lvSearchIntervene.setLinkValue(request.getKeyword());

        //构建筛选条件，调用筛选服务获取筛选数据
        InterveneCardParam param = new InterveneCardParam();
        param.setVersion(request.getVersion());
        param.setPageIndex(request.getPageIndex());
        param.setPageSize(request.getPageSize());
        //区分排序方式，关联内容池时不区分排序方式
        param.setSortType(request.getSortType());
        param.setVipType(request.getVipType());
        return searchTagCmp.getItemByTag(lvSearchIntervene, param).handle(new TraceBiFunction<>((searchTagResponse, e) -> {
            if (e != null) {
                log.error("SearchByTagExecutor.getSearchIntervene response error,keyWord:{}, vipType:{}, requestId{}, e:{}",
                        request.getKeyword(), request.getVipType(), request.getRequestId(), e);
            }
            if (searchTagResponse == null || CollectionUtils.isEmpty(searchTagResponse.getContents())) {
                log.warn("SearchByTagExecutor.getSearchIntervene handle response is null,keyWord:{}, vipType:{}, requestId{}", request.getKeyword(), request.getVipType(), request.getKeyword());
                return response;
            }
            log.info("getItemByTag success, keyword:{}, requestId:{}", request.getKeyword(), request.getRequestId());
            response.setContents(searchTagResponse.getContents());
            return response;
        }));
    }
}
