package com.heytap.longvideo.search.executors.unofficial;


import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class MediaUnofficialAlbumExecutor extends AbstractNextAsyncExecutor<SearchStandardAlbumParams, StandardResult<PageResponse<StandardAlbumVo>>> {

    @Autowired
    private UnofficialAlbumService unofficialAlbumService;


    protected MediaUnofficialAlbumExecutor() {
        super(SearchStandardAlbumParams.class);
    }

    @Override
    public CompletableFuture<StandardResult<PageResponse<StandardAlbumVo>>> myExecute(SearchStandardAlbumParams request) {
        if (StringUtil.isBlank(request.getTitle()) && StringUtil.isNotBlank(request.getProgramTitle())) {
            request.setTitle(request.getProgramTitle());
        }
        if (request.getSourceScoreStart() != null && (request.getSourceScoreStart() > 10F || request.getSourceScoreStart() < 0)) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, "无效的评分区间"));
        }
        if (request.getSourceScoreEnd() != null && (request.getSourceScoreEnd() > 10F || request.getSourceScoreEnd() < 0)) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, "无效的评分区间"));
        }
        if (StringUtil.isNotBlank(request.getOriginStatus()) && StringUtil.isBlank(request.getSourceStatus())) {
            request.setSourceStatus(request.getOriginStatus());
        }
        try {
            PageResponse<StandardAlbumVo> pageResponse = unofficialAlbumService.searchUnofficialAlbum(request);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(SearchStandardAlbumParams searchStandardAlbumParams, StandardResult<PageResponse<StandardAlbumVo>> pageResponseStandardResult) throws BizException {
        return null;
    }
}
