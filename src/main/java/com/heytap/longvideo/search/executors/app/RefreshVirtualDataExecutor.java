package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.service.common.VirtualProgramService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.next.executor.NextRequest;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/*
 * Description 虚拟节目刷新
 * Date 16:51 2021/12/27
 * Author songjiajia 80350688
*/
@Component
@Slf4j
public class RefreshVirtualDataExecutor extends AbstractNextAsyncExecutorV2<NextRequest, Boolean> {

    protected RefreshVirtualDataExecutor() {
        super(NextRequest.class);
    }

    @Autowired
    private VirtualProgramService virtualProgramService;

    @Override
    protected CompletableFuture<Boolean> myExecute(NextRequest request) throws BizException {
        if (!"oppo123".equals(request.getSession())) {
            return CompletableFuture.completedFuture(true);
        }
        virtualProgramService.createCacheAndReturnAllAlbum();
        return CompletableFuture.completedFuture(true);
    }


    @Override
    protected ByteString buildProtobufByteString(NextRequest request, Boolean response) throws BizException {
        return null;
    }
}
