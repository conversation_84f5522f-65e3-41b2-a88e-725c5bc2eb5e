package com.heytap.longvideo.search.executors.app;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.LvSearchIntervene;
import com.heytap.longvideo.client.arrange.search.enums.InterveneTypeEnum;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.cmp.SearchRecommendCmp;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.LongVideoSearchCard;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.TransparentInfo;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.app.lib.protobuf.ProtoBufUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pb.pbClass.PbLongVideoSearchCard;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 推荐卡二级页接口
 *
 * <AUTHOR> Yanping
 * @date 2023/10/27
 */
@Component
@Slf4j
public class RecommendCardExecutor extends AbstractNextAsyncExecutorV2<InterveneCardParam, LongVideoSearchCard> {

    protected RecommendCardExecutor() {
        super(InterveneCardParam.class);
    }

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1, timeout = 500)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Autowired
    private SearchRecommendCmp searchRecommendCmp;

    @Autowired
    private SearchIntentService searchIntentService;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor commonThreadPool;

    static String sidPattern = "^\\d{18}$";

    @Override
    protected CompletableFuture<LongVideoSearchCard> myExecute(InterveneCardParam request) throws BizException {
        if (StringUtil.isBlank(request.getCode())) {
            log.error("code is null, request:{}", com.alibaba.fastjson.JSON.toJSONString(request));
            throw new RuntimeException("code is null");
        }
        if (request.getCode().trim().startsWith("{") && request.getCode().trim().endsWith("}")) {
            //走的算法
            TransparentInfo transparentInfo = JSON.parseObject(request.getCode(), TransparentInfo.class);
            if (transparentInfo == null) {
                log.error("transparentInfo is empty, request:{}", request);
                return CompletableFuture.completedFuture(null);
            }
            KeyWordSearchParamV2 keyWordSearchParamV2 = new KeyWordSearchParamV2();
            if (request.getAttributeValues() != null ) {
                keyWordSearchParamV2.setDv(String.valueOf(request.getAttributeValues().getDeviceType()));
            }
            keyWordSearchParamV2.setPageIndex(request.getPageIndex());
            keyWordSearchParamV2.setRequestId(request.getRequestId());
            keyWordSearchParamV2.setKeyword((transparentInfo.getKeyword()));
            keyWordSearchParamV2.setVersion(request.getVersion());
            keyWordSearchParamV2.setQuickEngineVersion(request.getQuickEngineVersion());

            List<KeyWordSearchResponse> searchResult = new ArrayList<>();
            KeyWordSearchResponse contentItem = new KeyWordSearchResponse();
            contentItem.setSid(transparentInfo.getSid());
            searchResult.add(contentItem);

            SearchInterveneCardResponse searchCardResponse = FutureUtil.getFutureIgnoreException(
                    searchRecommendCmp.getItemByAlgorithm(keyWordSearchParamV2, searchResult, request.getSortType()));
            return CompletableFuture.completedFuture(searchIntentService.searchToLongVideo(searchCardResponse));
        }

        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getById(Integer.parseInt(request.getCode())).thenComposeAsync((rpcResult) -> {
            if (rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode() || rpcResult.getData() == null) {
                log.error("getById error, the request:{}, the response:{}", request, rpcResult);
                return CompletableFuture.completedFuture(null);
            }
            SearchByKeyWordContext context = new SearchByKeyWordContext();

            LvSearchIntervene lvSearchIntervene = rpcResult.getData();
            Map<InterveneTypeEnum, LvSearchIntervene> interveneConfigMap = new HashMap<>();
            interveneConfigMap.put(InterveneTypeEnum.RECOMMEND, lvSearchIntervene);
            context.setInterveneConfigMap(interveneConfigMap);

            KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
            requestParam.setPageIndex(request.getPageIndex());
            context.setRequestParam(requestParam);

            SearchInterveneCardResponse searchCardResponse = FutureUtil.getFutureIgnoreException(
                    searchRecommendCmp.getItemByOperation(context, request.getSortType()));
            return CompletableFuture.completedFuture(searchIntentService.searchToLongVideo(searchCardResponse));
        }, commonThreadPool);
    }

    @Override
    protected ByteString buildProtobufByteString(InterveneCardParam nRequest, LongVideoSearchCard nResponse) throws BizException {
        Message message = ProtoBufUtils.buildMessage(nResponse, PbLongVideoSearchCard.LongVideoSearchCard.getDescriptor());
        return message.toByteString();
    }
}
