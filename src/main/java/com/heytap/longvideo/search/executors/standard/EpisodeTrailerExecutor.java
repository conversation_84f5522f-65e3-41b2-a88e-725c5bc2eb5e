package com.heytap.longvideo.search.executors.standard;

import com.google.common.collect.Lists;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.StandardEpisodeRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.StandardEpisode;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.*;
import com.heytap.longvideo.search.service.standard.StandardEpisodeService;
import com.heytap.longvideo.search.service.standard.StandardTrailerService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/11/7
 */
@Slf4j
@Service
public class EpisodeTrailerExecutor extends AbstractNextAsyncExecutor<EpisodeTrailerParam, StandardResult<PageResponse<EpisodeTrailerVo>>> {

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    @Autowired
    private StandardTrailerService standardTrailerService;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", parameter = {"stamp", "1"})
    private StandardEpisodeRpcApi standardEpisodeRpcApi;

    protected EpisodeTrailerExecutor() {
        super(EpisodeTrailerParam.class);
    }

    @Override
    protected CompletableFuture<StandardResult<PageResponse<EpisodeTrailerVo>>> myExecute(EpisodeTrailerParam nRequest) throws BizException {
        PageResponse<EpisodeTrailerVo> response = new PageResponse<>();
        List<EpisodeTrailerVo> vos = Lists.newArrayList();
        if (StringUtils.isBlank(nRequest.getSid())) {
            response.setItemList(vos);
            return CompletableFuture.completedFuture(StandardResult.success(response));
        }
        if (nRequest.getDataType() != null && nRequest.getDataType() == 1) {
            SearchStandardEpisodeParams episodeParams = new SearchStandardEpisodeParams();
            episodeParams.setPreciseFlag(true);
            episodeParams.setSid(nRequest.getSid());
            episodeParams.setVid(nRequest.getVid());
            episodeParams.setEid(nRequest.getEid());
            episodeParams.setTitle(nRequest.getTitle());
            episodeParams.setPageIndex(nRequest.getPageIndex());
            episodeParams.setPageSize(nRequest.getPageSize());
            PageResponse<StandardEpisodeVo> episodes = null;
            try {
                episodes = standardEpisodeService.searchEpisode(episodeParams);
            } catch (Exception e) {
                log.warn("query es episode error", e);
                return CompletableFuture.completedFuture(null);
            }
            response.setCurrentPage(episodes.getCurrentPage());
            response.setItemListSize(episodes.getItemListSize());
            response.setMaxPage(episodes.getMaxPage());
            response.setPageSize(episodes.getPageSize());
            response.setTotalCount(episodes.getTotalCount());
            List<StandardEpisodeVo> episodeVos = episodes.getItemList();
            episodeVos.forEach(episodeVo -> {
                EpisodeTrailerVo vo = new EpisodeTrailerVo();
                BeanUtils.copyProperties(episodeVo, vo);
                vo.setVideoType(episodeVo.getFeatureType());
                vo.setAlbumTitle(episodeVo.getAlbumTitle());
                vo.setAlbumStatus(episodeVo.getAlbumStatus());
                vo.setAlbumPayStatus(episodeVo.getAlbumPayStatus());
                vo.setScore(episodeVo.getScore());
                vo.setFeatureType(episodeVo.getAlbumFeatureType());
                vo.setProgramType(episodeVo.getAlbumProgramType());
                vo.setCompleted(episodeVo.getAlbumCompleted());
                vo.setTags(episodeVo.getAlbumTags());
                vo.setYear(episodeVo.getAlbumYear());
                vo.setLanguage(episodeVo.getAlbumLanguage());
                vo.setShowTime(episodeVo.getAlbumShowTime());
                vo.setArea(episodeVo.getAlbumArea());
                vo.setBrief(episodeVo.getAlbumBrief());
                vo.setActor(episodeVo.getAlbumActor());
                vo.setDirector(episodeVo.getAlbumDirector());
                vo.setInformation(episodeVo.getAlbumInformation());
                vo.setProgramInfo(episodeVo.getAlbumProgramInfo());
                vo.setHorizontalIcon(episodeVo.getAlbumHorizontalIcon());
                vo.setVerticalIcon(episodeVo.getAlbumVerticalIcon());
                vo.setSourceAlbumId(episodeVo.getSourceAlbumId());
                vo.setSourceVideoId(episodeVo.getSourceVideoId());
                vos.add(vo);
            });

        } else if (nRequest.getDataType() != null && nRequest.getDataType() == 2) {
            SearchStandardTrailerParams trailerParams = new SearchStandardTrailerParams();
            trailerParams.setPreciseFlag(true);
            trailerParams.setSid(nRequest.getSid());
            trailerParams.setVid(nRequest.getVid());
            trailerParams.setTid(nRequest.getEid());
            trailerParams.setTitle(nRequest.getTitle());
            trailerParams.setPageIndex(nRequest.getPageIndex());
            trailerParams.setPageSize(nRequest.getPageSize());
            PageResponse<StandardTrailerVo> trailers = null;
            try {
                trailers = standardTrailerService.searchTrailer(trailerParams);
            } catch (Exception e) {
                log.warn("query es trailer error", e);
                return CompletableFuture.completedFuture(null);
            }
            response.setCurrentPage(trailers.getCurrentPage());
            response.setItemListSize(trailers.getItemListSize());
            response.setMaxPage(trailers.getMaxPage());
            response.setPageSize(trailers.getPageSize());
            response.setTotalCount(trailers.getTotalCount());
            List<StandardTrailerVo> trailerVos = trailers.getItemList();
            trailerVos.forEach(trailerVo -> {
                EpisodeTrailerVo vo = new EpisodeTrailerVo();
                BeanUtils.copyProperties(trailerVo, vo);
                vo.setVideoType(trailerVo.getTrailerType());
                vo.setAlbumTitle(trailerVo.getAlbumTitle());
                vo.setAlbumStatus(trailerVo.getAlbumStatus());
                vo.setAlbumPayStatus(trailerVo.getAlbumPayStatus());
                vo.setScore(trailerVo.getScore());
                vo.setFeatureType(trailerVo.getAlbumFeatureType());
                vo.setProgramType(trailerVo.getAlbumProgramType());
                vo.setCompleted(trailerVo.getAlbumCompleted());
                vo.setTags(trailerVo.getAlbumTags());
                vo.setYear(trailerVo.getAlbumYear());
                vo.setLanguage(trailerVo.getAlbumLanguage());
                vo.setShowTime(trailerVo.getAlbumShowTime());
                vo.setArea(trailerVo.getAlbumArea());
                vo.setBrief(trailerVo.getAlbumBrief());
                vo.setActor(trailerVo.getAlbumActor());
                vo.setDirector(trailerVo.getAlbumDirector());
                vo.setInformation(trailerVo.getAlbumInformation());
                vo.setProgramInfo(trailerVo.getAlbumProgramInfo());
                vo.setHorizontalIcon(trailerVo.getAlbumHorizontalIcon());
                vo.setVerticalIcon(trailerVo.getAlbumVerticalIcon());
                vo.setSourceAlbumId(trailerVo.getSourceAlbumId());
                vo.setSourceVideoId(trailerVo.getSourceVideoId());
                vos.add(vo);
            });

        } else if (nRequest.getDataType() != null && nRequest.getDataType() == 3) {
            SearchStandardAlbumParams albumParams = new SearchStandardAlbumParams();
            albumParams.setSid(nRequest.getSid());
            albumParams.setPageIndex(nRequest.getPageIndex());
            albumParams.setPageSize(nRequest.getPageSize());
            PageResponse<StandardAlbumVo> albums = null;
            StandardEpisode standardEpisode = null;
            StandardAlbum albumVo = null;
            try {
                albumVo = standardAlbumRpcApi.getBySid(nRequest.getSid()).get().getData();
                standardEpisode = standardEpisodeRpcApi.getBySids(Lists.newArrayList(nRequest.getSid())).get().getData().get(nRequest.getSid());
            } catch (Exception e) {
                log.warn("query es album error", e);
            }
            if (albumVo == null) {
                response.setItemList(vos);
                return CompletableFuture.completedFuture(StandardResult.success(response));
            }
            EpisodeTrailerVo vo = new EpisodeTrailerVo();
            vo.setSid(albumVo.getSid());
            vo.setAlbumTitle(albumVo.getTitle());
            vo.setAlbumStatus(albumVo.getStatus());
            vo.setAlbumPayStatus(albumVo.getPayStatus());
            vo.setPayStatus(standardEpisode.getPayStatus());
            vo.setScore(albumVo.getSourceScore());
            vo.setFeatureType(albumVo.getFeatureType());
            vo.setProgramType(albumVo.getProgramType());
            vo.setCompleted(albumVo.getCompleted());
            vo.setTags(albumVo.getTags());
            vo.setYear(albumVo.getYear());
            vo.setLanguage(albumVo.getLanguage());
            vo.setShowTime(albumVo.getShowTime());
            vo.setArea(albumVo.getArea());
            vo.setBrief(albumVo.getBrief());
            vo.setActor(albumVo.getActor());
            vo.setDirector(albumVo.getDirector());
            vo.setInformation(albumVo.getInformation());
            vo.setProgramInfo(albumVo.getProgramInfo());
            vo.setHorizontalIcon(albumVo.getHorizontalIcon());
            vo.setVerticalIcon(albumVo.getVerticalIcon());
            vo.setSource(albumVo.getSource());
            vo.setSourceAlbumId(albumVo.getSourceAlbumId());
            vo.setSourceVideoId(standardEpisode.getSourceVideoId());
            vo.setVid(standardEpisode.getVid());
            vo.setEid(standardEpisode.getEid());
            vo.setTitle(standardEpisode.getTitle());
            vo.setVideoType(standardEpisode.getFeatureType());
            vo.setStatus(standardEpisode.getStatus());
            vos.add(vo);
        }
        response.setItemList(vos);
        return CompletableFuture.completedFuture(StandardResult.success(response));
    }

    @Override
    protected ByteString buildProtobufByteString(EpisodeTrailerParam nRequest, StandardResult<PageResponse<EpisodeTrailerVo>> nResponse) throws BizException {
        return null;
    }
}
