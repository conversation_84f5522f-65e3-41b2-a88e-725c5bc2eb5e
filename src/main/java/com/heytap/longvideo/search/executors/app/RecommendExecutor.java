package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.RecommendParam;
import com.heytap.longvideo.search.service.app.RelationRecommendService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.CompletableFuture;

/*
 * Description 推荐
 * Date 16:07 2023/2/1
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class RecommendExecutor extends AbstractNextAsyncExecutorV2<RecommendParam, Set> {

    protected RecommendExecutor() {
        super(RecommendParam.class);
    }

    @Autowired
    private RelationRecommendService relationRecommendService;

    @Override
    protected CompletableFuture<Set> myExecute(RecommendParam param) throws BizException {
        return CompletableFuture.completedFuture(relationRecommendService.getRecommend(param));
    }


    @Override
    protected ByteString buildProtobufByteString(RecommendParam request, Set response) throws BizException {
        return null;
    }
}
