package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.constants.SearchTabEnum;
import com.heytap.longvideo.search.liteflow.cmp.SearchDuanjuCmp;
import com.heytap.longvideo.search.liteflow.context.SearchByKeyWordContext;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.longvideo.search.model.param.app.SearchResponse;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/*
 * Description 根据关键字搜索
 * Date 14:11 2021/11/29
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class KeyWordSearchExecutor extends AbstractNextAsyncExecutorV2<KeyWordSearchParamV2, SearchResponse> {

    protected KeyWordSearchExecutor() {
        super(KeyWordSearchParamV2.class);
    }

    @Resource
    private FlowExecutor flowExecutor;

    @Autowired
    private StandardAlbumService standardAlbumService;

    @Autowired
    private ConvertResponseService beanConvertService;

    @Autowired
    private SearchDuanjuCmp searchDuanjuCmp;

    static String SID_PATTERN = "^\\d{18}$";

    @Override
    protected CompletableFuture<SearchResponse> myExecute(KeyWordSearchParamV2 param) throws BizException {
        String keyWord = param.getKeyword();

        //如果是搜索sid,直接匹配标准媒资
        if (StringUtil.isNotBlank(keyWord) && (keyWord.length() == 18 || keyWord.length() == 19) && Pattern.matches(SID_PATTERN, keyWord)) {
            StandardAlbumEs standardAlbumEs = standardAlbumService.searchBySid(keyWord);
            return CompletableFuture.completedFuture(standardConvertResponse(standardAlbumEs, param));
        }

        //如果是短剧tab搜索，只调短剧接口
        if (SearchTabEnum.DUANJU.getCode().equals(param.getSearchType())) {
            List<KeyWordSearchResponse> duanjuSearchResult = searchDuanjuCmp.searchDuanju(param);
            return CompletableFuture.completedFuture(
                    SearchResponse.duanjuResult(duanjuSearchResult, param.getPageIndex(), param.getDuanjuHasMore()));
        }

        param.setVersionTag(CommonUtils.getVersionTag(param.getVersion()));
        //搜索流程
        SearchByKeyWordContext context = new SearchByKeyWordContext();
        context.setRequestParam(param);
        LiteflowResponse response = flowExecutor.execute2Resp("searchByKeywordChain", null, context);
        if (!response.isSuccess()) {
            log.error("flowExecutor error", response.getCause());
            throw new RuntimeException(response.getCause());
        }
        return CompletableFuture.completedFuture(context.getSearchResponse());
    }


    private SearchResponse standardConvertResponse(StandardAlbumEs standardAlbumEs, KeyWordSearchParamV2 param) {
        if (standardAlbumEs == null) {
            return SearchResponse.emptyResult();
        }
        return SearchResponse.singleResult(beanConvertService.standardAlbumToSearchResponse(standardAlbumEs, param));
    }


    @Override
    protected ByteString buildProtobufByteString(KeyWordSearchParamV2 request, SearchResponse response) throws BizException {

        return null;
    }
}
