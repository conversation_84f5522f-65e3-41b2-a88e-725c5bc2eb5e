package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardVideoParams;
import com.heytap.longvideo.search.model.param.standard.StandardVideoVo;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import com.heytap.longvideo.search.utils.JackSonDataToStringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/*
 * Description 搜索视频
 * Date 14:35 2022/3/21
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class CmsStandardVideoExecutor extends AbstractNextAsyncExecutor<SearchStandardVideoParams, StandardResult<PageResponse<StandardVideoVo>>> {

    protected CmsStandardVideoExecutor() {
        super(SearchStandardVideoParams.class);
    }

    @Autowired
    private StandardVideoService standardVideoService;

    @Override
    protected CompletableFuture<StandardResult<PageResponse<StandardVideoVo>>> myExecute(SearchStandardVideoParams request) {
        try {
            request.setChannel(2);
            PageResponse<StandardVideoVo> pageResponse = standardVideoService.searchVideo(request);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }


    @Override
    protected ByteString buildProtobufByteString(SearchStandardVideoParams request, StandardResult<PageResponse<StandardVideoVo>> response) throws BizException {
        return null;
    }

    @Override
    protected ByteString buildJosnByteString(SearchStandardVideoParams nRequest, StandardResult<PageResponse<StandardVideoVo>> nResponse) throws BizException {
        return (nResponse == null) ? null : ByteString.copyFrom(JackSonDataToStringUtil.toJsonBytes(nResponse));
    }

}
