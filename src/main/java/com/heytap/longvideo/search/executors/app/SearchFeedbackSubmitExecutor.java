package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.app.SearchFeedbackSubmitParam;
import com.heytap.longvideo.search.service.app.SearchFeedbackService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class SearchFeedbackSubmitExecutor extends AbstractNextAsyncExecutorV2<SearchFeedbackSubmitParam, Boolean> {

    @Autowired
    private SearchFeedbackService searchFeedbackService;

    protected SearchFeedbackSubmitExecutor() {
        super(SearchFeedbackSubmitParam.class);
    }

    @Override
    protected CompletableFuture<Boolean> myExecute(SearchFeedbackSubmitParam param) throws BizException {
        return CompletableFuture.completedFuture(searchFeedbackService.submit(param));
    }

    @Override
    protected ByteString buildProtobufByteString(SearchFeedbackSubmitParam searchFeedbackSubmitParam, Boolean aBoolean) throws BizException {
        return null;
    }
}
