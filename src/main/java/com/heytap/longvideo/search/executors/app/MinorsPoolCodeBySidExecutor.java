package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.param.MinorsPoolCodeParam;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

import static com.heytap.longvideo.search.constants.SearchConstant.MINORS_POOL_CODE;
import static com.heytap.longvideo.search.constants.SearchConstant.SID;

@Component
public class MinorsPoolCodeBySidExecutor extends AbstractNextAsyncExecutorV2<MinorsPoolCodeParam, String> {

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    protected MinorsPoolCodeBySidExecutor() {
        super(MinorsPoolCodeParam.class);
    }

    @Override
    public CompletableFuture<String> myExecute(MinorsPoolCodeParam param) throws BizException {
        if (StringUtils.isEmpty(param.getSid())) {
            return CompletableFuture.completedFuture(null);
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery(SID, param.getSid()));
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery searchQuery = queryBuilder
                .withQuery(boolQuery)
                .withFields(SID, MINORS_POOL_CODE)
                .build();
        SearchHits<ProgramAlbumEs> searchHits = this.restTemplate.search(searchQuery, ProgramAlbumEs.class);

        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            ProgramAlbumEs albumEs = searchHit.getContent();
            if (albumEs != null) {
                return CompletableFuture.completedFuture(albumEs.getMinorsPoolCode());
            }
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    protected ByteString buildProtobufByteString(MinorsPoolCodeParam nRequest, String nResponse) throws BizException {
        return null;
    }
}