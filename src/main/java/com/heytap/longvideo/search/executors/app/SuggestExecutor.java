package com.heytap.longvideo.search.executors.app;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParam;
import com.heytap.longvideo.search.service.app.SuggestService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/*
 * Description 智能提示
 * Date 11:09 2022/6/10
 * Author songjiajia 80350688
*/
@Component
@Slf4j
public class SuggestExecutor extends AbstractNextAsyncExecutorV2<KeyWordSearchParam, List> {

    protected SuggestExecutor() {
        super(KeyWordSearchParam.class);
    }

    @Autowired
    private SuggestService suggestService;



    @Override
    protected CompletableFuture<List> myExecute(KeyWordSearchParam param) throws BizException {
        return CompletableFuture.completedFuture(suggestService.suggest(param));
    }


    @Override
    protected ByteString buildProtobufByteString(KeyWordSearchParam request, List response) throws BizException {
        return null;
    }
}
