package com.heytap.longvideo.search.executors.standard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchStandardAlbumParams;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/*
 * Description 搜索剧头
 * Date 14:35 2022/3/21
 * Author songjiajia 80350688
 */
@Component
@Slf4j
public class MediaStandardAlbumExecutor extends AbstractNextAsyncExecutor<SearchStandardAlbumParams, StandardResult<PageResponse<StandardAlbumVo>>> {

    protected MediaStandardAlbumExecutor() {
        super(SearchStandardAlbumParams.class);
    }

    @Autowired
    private StandardAlbumService standardAlbumService;

    @Autowired
    private HttpDataChannel httpClient;

    @HeraclesDynamicConfig(key = "tvAlbum.search.url", fileName = "search_config.properties")
    private String tvAlbumSearchUrl;

    @Override
    protected CompletableFuture<StandardResult<PageResponse<StandardAlbumVo>>> myExecute(SearchStandardAlbumParams request) {
        if (StringUtil.isBlank(request.getTitle()) && StringUtil.isNotBlank(request.getProgramTitle())) {
            request.setTitle(request.getProgramTitle());
        }
        if("youku".equals(request.getSource())||"mgtv".equals(request.getSource())||"bilibili".equals(request.getSource())) {
            Map<String, Object> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            try {
                String response = httpClient.postForObjectWithGenerics(tvAlbumSearchUrl, request, String.class, null, 3000);
                StandardResult<PageResponse<StandardAlbumVo>> standardResult = JSON.parseObject(response, new TypeReference<StandardResult<PageResponse<StandardAlbumVo>>>(){});
                return CompletableFuture.completedFuture(standardResult);
            }catch (Exception e){
                log.error("tvAlbumSearchUrl error",e);
                return CompletableFuture.completedFuture(StandardResult.fail(400,"查询电视媒资失败"));
            }
        }
        if (request.getSourceScoreStart() != null && (request.getSourceScoreStart() > 10F || request.getSourceScoreStart() < 0)) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, "无效的评分区间"));
        }
        if (request.getSourceScoreEnd() != null && (request.getSourceScoreEnd() > 10F || request.getSourceScoreEnd() < 0)) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, "无效的评分区间"));
        }
        if (StringUtil.isNotBlank(request.getOriginStatus()) && StringUtil.isBlank(request.getSourceStatus())) {
            request.setSourceStatus(request.getOriginStatus());
        }
        try {
            PageResponse<StandardAlbumVo> pageResponse = standardAlbumService.searchAlbum(request);
            return CompletableFuture.completedFuture(StandardResult.success(pageResponse));
        }catch (Exception e){
            return CompletableFuture.completedFuture(StandardResult.fail(400,e.getMessage()));
        }
    }


    @Override
    protected ByteString buildProtobufByteString(SearchStandardAlbumParams request, StandardResult<PageResponse<StandardAlbumVo>> response) throws BizException {
        return null;
    }

}
