package com.heytap.longvideo.search.executors.app;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.ByteString;
import com.google.protobuf.Message;
import com.heytap.longvideo.client.arrange.enums.TemplateDateTypeEnum;
import com.heytap.longvideo.client.arrange.search.api.LvSearchInterveneRpcApi;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.liteflow.cmp.SearchTagCmp;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.video.client.entity.list.VideoPageListV3;
import com.heytap.video.pb.list.PbVideoPageList;
import com.oppo.browser.common.app.lib.protobuf.ProtoBufUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 标签卡详情页-长短统一
 */
@Component
@Slf4j
public class TagCardV2Executor extends AbstractNextAsyncExecutorV2<InterveneCardParam, VideoPageListV3> {
    @Autowired
    private ConvertResponseService convertResponseService;

    protected TagCardV2Executor() {
        super(InterveneCardParam.class);
    }

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1, timeout = 500)
    private LvSearchInterveneRpcApi lvSearchInterveneRpcApi;

    @Autowired
    private SearchTagCmp searchTagCmp;

    @Autowired
    private SearchIntentService searchIntentService;

    @Autowired
    private TaskUnlockEpisodeService taskUnlockEpisodeService;

    @Override
    protected CompletableFuture<VideoPageListV3> myExecute(InterveneCardParam request) throws BizException {
        if (StringUtil.isBlank(request.getCode())) {
            log.error("code is null, request:{}", JSON.toJSONString(request));
            throw new RuntimeException("code is null");
        }

        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return lvSearchInterveneRpcApi.getById(Integer.parseInt(request.getCode())).handleAsync((rpcResult, e) -> {
            if (e != null || rpcResult == null || rpcResult.getCode() != ResultCode.SUCCESS.getCode() || rpcResult.getData() == null) {
                log.error("getById error, the request:{}, the response:{}", request.getCode(), rpcResult, e);
                return null;
            }
            KeyWordSearchParamV2 requestParam = new KeyWordSearchParamV2();
            requestParam.setVersion(request.getVersion());
            requestParam.setVipType(request.getVipType());
            requestParam.setQuickEngineVersion(request.getQuickEngineVersion());
            SearchInterveneCardResponse searchCardResponse = FutureUtil.getFutureIgnoreException(
                    searchTagCmp.getTagContent(requestParam, rpcResult.getData(), request.getPageIndex(),
                            request.getSortType(), request.getContentType()));
            taskUnlockEpisodeService.handleMarkCode4InterveneCard(request, searchCardResponse);
            if(searchCardResponse != null && CollectionUtils.isNotEmpty(searchCardResponse.getContents())){
                convertResponseService.handleSubTitle(searchCardResponse.getContents(), requestParam.getVersion());
            }
            return searchIntentService.searchToPageListV3(searchCardResponse, TemplateDateTypeEnum.TAG_CARD, request.getVersion());
        });

    }

    @Override
    protected ByteString buildProtobufByteString(InterveneCardParam nRequest, VideoPageListV3 nResponse) throws BizException {
        Message message = ProtoBufUtils.buildMessage(nResponse, PbVideoPageList.VideoPageListV3.getDescriptor());
        return message.toByteString();
    }
}
