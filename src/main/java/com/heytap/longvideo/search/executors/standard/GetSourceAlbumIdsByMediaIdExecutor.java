package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.param.standard.GetSourceAlbumIdsByMediaIdRequest;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/4/23
 */
@Service
public class GetSourceAlbumIdsByMediaIdExecutor extends AbstractNextAsyncExecutor<GetSourceAlbumIdsByMediaIdRequest, Result<List<String>>> {

    @Autowired
    private StandardAlbumService standardAlbumService;

    protected GetSourceAlbumIdsByMediaIdExecutor() {
        super(GetSourceAlbumIdsByMediaIdRequest.class);
    }

    @Override
    protected CompletableFuture<Result<List<String>>> myExecute(GetSourceAlbumIdsByMediaIdRequest getSourceAlbumIdsByMediaIdRequest) throws BizException {
        List<String> sourceAlbumIds = standardAlbumService.getSourceAlbumIds(getSourceAlbumIdsByMediaIdRequest.getMediaId(), getSourceAlbumIdsByMediaIdRequest.getVersion());
        return CompletableFuture.completedFuture(Result.success(sourceAlbumIds));
    }

    @Override
    protected ByteString buildProtobufByteString(GetSourceAlbumIdsByMediaIdRequest getSourceAlbumIdsByMediaIdRequest, Result<List<String>> listResult) throws BizException {
        return null;
    }
}
