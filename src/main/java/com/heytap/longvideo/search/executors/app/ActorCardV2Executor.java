package com.heytap.longvideo.search.executors.app;


import com.google.protobuf.ByteString;
import com.google.protobuf.Message;
import com.heytap.longvideo.client.arrange.enums.TemplateDateTypeEnum;
import com.heytap.longvideo.search.liteflow.cmp.SearchActorCmp;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.longvideo.search.model.param.InterveneCardParam;
import com.heytap.longvideo.search.model.param.app.KeyWordSearchParamV2;
import com.heytap.longvideo.search.service.app.SearchIntentService;
import com.heytap.longvideo.search.service.app.TaskUnlockEpisodeService;
import com.heytap.longvideo.search.service.common.ConvertResponseService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.video.client.entity.list.VideoPageListV3;
import com.heytap.video.pb.list.PbVideoPageList;
import com.oppo.browser.common.app.lib.protobuf.ProtoBufUtils;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 影人卡详情页-长短统一
 */
@Component
@Slf4j
public class ActorCardV2Executor extends AbstractNextAsyncExecutorV2<InterveneCardParam, VideoPageListV3> {
    protected ActorCardV2Executor() {
        super(InterveneCardParam.class);
    }

    @Autowired
    private SearchActorCmp searchActorCmp;

    @Autowired
    private SearchIntentService searchIntentService;

    @Autowired
    private TaskUnlockEpisodeService taskUnlockEpisodeService;
    @Autowired
    private ConvertResponseService convertResponseService;

    @Override
    protected CompletableFuture<VideoPageListV3> myExecute(InterveneCardParam param) throws BizException {
        if (StringUtil.isBlank(param.getCode())) {
            log.error("code is null");
            throw new RuntimeException("code is null");
        }
        KeyWordSearchParamV2 keyWordSearchParam = new KeyWordSearchParamV2();
        keyWordSearchParam.setKeyword(param.getCode());
        keyWordSearchParam.setVersion(param.getVersion());
        keyWordSearchParam.setVipType(param.getVipType());
        keyWordSearchParam.setPageIndex(param.getPageIndex());
        keyWordSearchParam.setQuickEngineVersion(param.getQuickEngineVersion());
        keyWordSearchParam.setSidBlackList(param.getSidBlackList());
        SearchInterveneCardResponse cardResponse = searchActorCmp.queryActorCard(keyWordSearchParam, param.getSortType(), param.getContentType());
        if(cardResponse != null && CollectionUtils.isNotEmpty(cardResponse.getContents())){
            convertResponseService.handleSubTitle(cardResponse.getContents(), param.getVersion());
        }
        taskUnlockEpisodeService.handleMarkCode4InterveneCard(param, cardResponse);

        return CompletableFuture.completedFuture(searchIntentService.searchToPageListV3(
                cardResponse, TemplateDateTypeEnum.ACTOR_CARD, param.getVersion()));
    }

    @Override
    protected ByteString buildProtobufByteString(InterveneCardParam nRequest, VideoPageListV3 nResponse) throws BizException {
        Message message = ProtoBufUtils.buildMessage(nResponse, PbVideoPageList.VideoPageListV3.getDescriptor());
        return message.toByteString();
    }
}
