package com.heytap.longvideo.search.executors.standard;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.CmsSearchTrailerParams;
import com.heytap.longvideo.search.model.param.standard.CmsTrailerVo;
import com.heytap.longvideo.search.service.standard.StandardTrailerService;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Slf4j
@Service
public class CmsTrailerDetailExecutor extends AbstractNextAsyncExecutor<CmsSearchTrailerParams, StandardResult<CmsTrailerVo>> {

    @Autowired
    private StandardTrailerService standardTrailerService;

    protected CmsTrailerDetailExecutor() {
        super(CmsSearchTrailerParams.class);
    }

    @Override
    protected CompletableFuture<StandardResult<CmsTrailerVo>> myExecute(CmsSearchTrailerParams nRequest) throws BizException {
        if (StringUtil.isNotEmpty(nRequest.getOriginStatus())) {
            nRequest.setSourceStatus(nRequest.getOriginStatus());
        }
        if (StringUtil.isNotEmpty(nRequest.getId())) {
            nRequest.setTid(nRequest.getId());
        }
        try {
            List<CmsTrailerVo> itemList = standardTrailerService.searchCmsTrailer(nRequest).getItemList();
            CmsTrailerVo cmsTrailerVo = new CmsTrailerVo();
            if (CollectionUtils.isNotEmpty(itemList)) {
                cmsTrailerVo = itemList.get(0);
            }
            return CompletableFuture.completedFuture(StandardResult.success(cmsTrailerVo));
        } catch (Exception e) {
            return CompletableFuture.completedFuture(StandardResult.fail(400, e.getMessage()));
        }
    }

    @Override
    protected ByteString buildProtobufByteString(CmsSearchTrailerParams nRequest, StandardResult<CmsTrailerVo> nResponse) throws BizException {
        return null;
    }
}