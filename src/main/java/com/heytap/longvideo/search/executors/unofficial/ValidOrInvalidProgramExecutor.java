package com.heytap.longvideo.search.executors.unofficial;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.client.media.enums.MediaStatusEnum;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.unofficial.request.ProgramRequest;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 生/失效节目 - executor
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/15 16:02
 */
@Slf4j
@Service
public class ValidOrInvalidProgramExecutor extends AbstractNextAsyncExecutorV2<ProgramRequest, Result<Void>> {

    private final UnofficialAlbumService unofficialAlbumService;

    protected ValidOrInvalidProgramExecutor(UnofficialAlbumService unofficialAlbumService) {
        super(ProgramRequest.class);
        this.unofficialAlbumService = unofficialAlbumService;
    }

    /**
     * @Description: 节目剧头生效/失效并同步
     * 1、通过sid和source获取节目信息
     * 2、根据validType得到生效或失效状态，然后设置status的值
     * 3、保存更新后的节目信息到ES
     *
     * @param nRequest 请求体
     * @return CompletableFuture<Result<Boolean>>
     */
    @Override
    protected CompletableFuture<Result<Void>> myExecute(ProgramRequest nRequest) throws BizException {
        if (Objects.isNull(nRequest)) {
            throw new BizException("request is null", 502);
        }

        if (CollectionUtils.isEmpty(nRequest.getSids())) {
            throw new BizException("sids is empty", 502);
        }

        try {
            List<UnofficialAlbumEs> unofficialAlbumEsList = unofficialAlbumService.getAlbumList(nRequest.getSids());

            // 根据editType得到生效或失效状态，然后设置status的值
            for (UnofficialAlbumEs unofficialAlbumEs : unofficialAlbumEsList) {
                setStatus(nRequest, unofficialAlbumEs);
                try {
                    // 人工编辑内容同步
                    unofficialAlbumService.mqSendMsg(unofficialAlbumEs, 3);
                }catch (Exception e){
                    log.error("unofficial album sends msg to mq error",e);
                }
            }

            // 将修改的结果保存到ES
            unofficialAlbumService.batchInsertOrUpdate(unofficialAlbumEsList);
            return CompletableFuture.completedFuture(Result.success(null));
        } catch (Exception e) {
            log.error("During ValidOrInvalidProgramExecutor error:", e);
            throw new BizException(e.getMessage(), 502);
        }
    }

    /**
     * @Description: 构建Protobuf格式的返回结果，子类必须实现
     * @param nRequest 请求体
     * @param nResponse 响应体
     * @return ByteString
     */
    @Override
    protected ByteString buildProtobufByteString(ProgramRequest nRequest, Result<Void> nResponse) throws BizException {
        return null;
    }

    private void setStatus(ProgramRequest programRequest, UnofficialAlbumEs unofficialAlbumEs) {
        if (MediaStatusEnum.VALID.getKey().equals(programRequest.getEditType())) {
            unofficialAlbumEs.setStatus(MediaStatusEnum.VALID.getValue());

        } else if (MediaStatusEnum.INVALID.getKey().equals(programRequest.getEditType())) {
            unofficialAlbumEs.setStatus(MediaStatusEnum.INVALID.getValue());
        }
    }
}