package com.heytap.longvideo.search.executors.inside;

import com.google.protobuf.ByteString;
import com.heytap.longvideo.common.lib.rpc.ResultCode;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.model.param.inside.SearchUnOfficialAlbumRequest;
import com.heytap.longvideo.search.model.param.inside.SearchUnOfficialAlbumResponse;
import com.oppo.browser.common.next.executor.AbstractNextAsyncExecutorV2;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2025/4/21
 */
@Slf4j
@Service
public class SearchUnOfficialAlbumExecutor {

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    public CompletableFuture<SearchUnOfficialAlbumResponse> myExecute(SearchUnOfficialAlbumRequest request) {
        SearchUnOfficialAlbumResponse response = new SearchUnOfficialAlbumResponse();
        // 构造参数
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("sid", request.getSid()));
        // 源状态=可用
        boolQuery.must(QueryBuilders.termQuery("sourceStatus", 1));
        // 状态=生效
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        PageRequest pageRequest = PageRequest.of(0, 2);
        NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQuery)
                .withPageable(pageRequest)
                .build();
        // 查询ES
        SearchHits<UnofficialAlbumEs> searchHits = restTemplate.search(searchQuery, UnofficialAlbumEs.class);
        if (CollectionUtils.isEmpty(searchHits.getSearchHits())) {
            log.error("search result is null, sid:{}, requestId:{}", request.getSid(), request.getRequestId());
            return CompletableFuture.completedFuture(null);
        }
        response.setUnofficialAlbum(searchHits.getSearchHits().get(0).getContent());
        log.info("search result sid:{}, requestId:{}, result", request.getSid(), request.getRequestId());
        return CompletableFuture.completedFuture(response);
    }
}
