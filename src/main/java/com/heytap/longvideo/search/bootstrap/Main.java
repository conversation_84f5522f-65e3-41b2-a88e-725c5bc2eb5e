package com.heytap.longvideo.search.bootstrap;

import com.oppo.basic.heracles.client.HeraclesBootstrap;
import com.oppo.trace.restlight1x.TraceFilter;
import esa.rpc.spring.boot.annation.EnableESARPC;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableESARPC(scanBasePackages = "com.heytap.longvideo.search.rpc")
@SpringBootApplication
@ImportResource("classpath:application-context.xml")
@MapperScan("com.heytap.longvideo.search.mapper")
@EnableScheduling
public class Main {
    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {
        System.setProperty("zookeeper.sasl.client", "false");
        HeraclesBootstrap.getInstance().init("*");
        SpringApplication.run(Main.class, args);
        logger.info("longvideo-search-rest start success...");
    }



    @Bean
    public TraceFilter traceFilter() {
        return new TraceFilter();
    }
}