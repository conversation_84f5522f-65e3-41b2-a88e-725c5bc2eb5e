package com.heytap.longvideo.search.bootstrap;

import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 应用启动完成后初始化器
 * 用于在Spring Boot应用完全启动后执行AlbumVideoCollectAllJob的初始化操作
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Component
public class AlbumVideoCollectInitializer implements ApplicationRunner {

    private final UnofficialAlbumService unofficialAlbumService;

    public AlbumVideoCollectInitializer(UnofficialAlbumService unofficialAlbumService) {
        this.unofficialAlbumService = unofficialAlbumService;
    }

    /**
     * 应用启动完成后执行的初始化方法
     * 该方法会在Spring Boot应用完全启动后自动调用
     * 
     * @param args 应用启动参数
     */
    @Override
    public void run(ApplicationArguments args) {
        try {
            log.info("开始执行AlbumVideoCollectAllJob初始化...");
            
            // 调用AlbumVideoCollectAllJob的init方法
//            String sid = "";
//            unofficialAlbumService.deleteDocumentById(sid);

            String field = "";
            String value = "";
            unofficialAlbumService.deleteByQuery(field, value);
            
            log.info("AlbumVideoCollectAllJob初始化完成");
        } catch (Exception e) {
            log.error("AlbumVideoCollectAllJob初始化失败", e);
            // 根据业务需求决定是否抛出异常阻止应用启动
            // throw new RuntimeException("AlbumVideoCollectAllJob初始化失败", e);
        }
    }
}
