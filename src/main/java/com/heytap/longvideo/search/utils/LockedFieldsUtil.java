package com.heytap.longvideo.search.utils;

import com.heytap.longvideo.client.media.enums.LockedFieldsEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;
import java.util.function.BiPredicate;

/**
 * @Description: 锁定字段处理工具类
 * @Author: 80398885
 * @Date: 2025/7/8
 */
@Slf4j
public class LockedFieldsUtil {

    /**
     * 复制符合条件的字段值
     * @param source 源对象
     * @param target 目标对象
     * @param condition 字段过滤条件
     * @param <T> 对象类型
     */
    public static <T> void handleLockedFields(T source, T target, Integer managerStatus, BiPredicate<Field, Integer> condition) {
        if (source == null || target == null) {
            log.error("Source and target objects must not be null");
            return;
        }

        try {
            Class<?> clazz = source.getClass();
            ReflectionUtils.doWithFields(clazz, field -> {
                // 1. 设置字段可访问
                ReflectionUtils.makeAccessible(field);

                // 2. 获取源值
                Object value = ReflectionUtils.getField(field, source);

                // 3. 写入目标对象
                ReflectionUtils.setField(field, target, value);
            }, field -> condition.test(field, managerStatus)); // 4. 应用条件过滤
        } catch (Throwable throwable) {
            log.error("copy {}'s fields to {} failed, error:", source, target, throwable);
        }
    }

    public static Set<String> getLockedFields(Integer managerStatus) {
        Set<String> lockedFieldSet = new HashSet<>();
        for (LockedFieldsEnum value : LockedFieldsEnum.values()) {
            if ((managerStatus & value.getManagerStatus()) != value.getManagerStatus()) {
                continue;
            }

            lockedFieldSet.add(value.getLockedField());
        }

        return lockedFieldSet;
    }
}