package com.heytap.longvideo.search.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

@Slf4j
public class AESUtil {
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding"; // 使用CBC模式和PKCS5填充

    /**
     * 加密数据
     * @param data  原文（UTF-8编码）
     * @return      初始向量（Base64编码）,密文（Base64编码）,
     */
    public static Pair<String, String> encrypt(String data, String key) {
        try {
            // 生成随机的16字节IV
            SecureRandom random = new SecureRandom();
            byte[] ivBytes = new byte[16];
            random.nextBytes(ivBytes);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION);

            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // 将IV和加密后的数据都转换为Base64字符串
            String ivBase64 = Base64.getEncoder().encodeToString(ivBytes);
            String encryptedDataBase64 = Base64.getEncoder().encodeToString(encryptedBytes);

            return Pair.of(ivBase64, encryptedDataBase64);
        }  catch (Exception e) {
            log.error("AESUtil encrypt error", e);
            throw new RuntimeException("AES加密错误");
        }
    }

    /**
     * 解密数据
     *
     * @param encryptedBase64   密文（Base64编码）
     * @param key               密钥
     * @param ivBase64          CBC加密模式的初始向量（Base64编码）
     * @return 解密后的原始数据
     */
    public static String decrypt(String encryptedBase64, String key, String ivBase64) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);

            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(Base64.getDecoder().decode(ivBase64));

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decodedBytes = Base64.getDecoder().decode(encryptedBase64);
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }  catch (Exception e) {
            log.error("AESUtil decrypt error", e);
            throw new RuntimeException("AES解密错误");
        }
    }
}
