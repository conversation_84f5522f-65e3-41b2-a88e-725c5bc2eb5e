package com.heytap.longvideo.search.utils;

import com.heytap.longvideo.client.arrange.enums.TemplateLinkTypeEnum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: 80339123 liu ying
 * @Date: 2022/3/13 12:40
 */
@Slf4j
@Service
public class DeepLinkUtils {

    @HeraclesDynamicConfig(key = "deeplink.template", fileName = "search_config.properties", textType = TextType.JSON)
    private Map<String, String> deepLinkMap;

    /**
     * 获取不同合作方的剧头deepLink，
     * 部分特殊合作形式的合作方deepLink和正常剧头不同
     * @param source
     * @param sid
     * @param sourceWebUrl
     * @param values
     * @return
     */
    public String getAlbumDeepLink(String source, String sid, String sourceWebUrl, String... values) {
        int type = TemplateLinkTypeEnum.ALBUM.getCode();
        String linkValue = sid;
        if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            type = TemplateLinkTypeEnum.WEB_FAST_APP.getCode();
            linkValue = sourceWebUrl;
        } else if (SourceEnum.ZTV.getDataSource().equals(source) || SourceEnum.MIGU_OLYMPIC.getDataSource().equals(source)) {
            type = TemplateLinkTypeEnum.H5.getCode();
            linkValue = sourceWebUrl;
        }
        return getDeeplinkByType(type, linkValue);
    }

    /**
     * 根据跳转类型获取dp链接
     */
    public String getDeeplinkByType(Integer type, String... values) {
        try {
            if (type == null) {
                return null;
            }
            if (deepLinkMap == null || deepLinkMap.size() == 0) {
                return null;
            }
            if (Objects.equals(type, TemplateLinkTypeEnum.FAST_APP.getCode())) {
                return values[0];
            }

            String deepLinkFormat = deepLinkMap.get(type.toString());

            if (StringUtils.isBlank(deepLinkFormat)) {
                return null;
            }

            // 如果是H5 并且没有经过url编码
            if ((type == 12 || type == TemplateLinkTypeEnum.WEB_FAST_APP.getCode()) && values.length > 0 && !UrlCoderUtil.hasEnCode(values[0])) {
                values[0] = UrlCoderUtil.encode(values[0], StandardCharsets.UTF_8);
            }

            return String.format(deepLinkFormat, values);

        } catch (Exception e) {
            log.error("getDeeplinkByType type={},values={}", type, values, e);
            return null;
        }
    }

    /**
     * 获取无感跳端dp---一般给三方使用
     * @param source
     * @param sid
     * @param sourceWebUrl
     * @return
     */
    public String getDirectPageDeepLink(String source, String sid, String sourceWebUrl) {
        String deepLink = getDeeplinkByType(TemplateLinkTypeEnum.ALBUM_DIRECT_PAGE.getCode(), sid);
        if (SourceEnum.YOUKU_MOBILE.getDataSource().equals(source)) {
            // 无感跳转快应用，使用快应用无感跳端dp
            deepLink = getDeeplinkByType(TemplateLinkTypeEnum.DIRECT_WEB_FAST_APP.getCode(), sourceWebUrl);
        }
        return deepLink;
    }
}
