package com.heytap.longvideo.search.utils;


import com.heytap.longvideo.client.media.constant.StandardConstant;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.oppo.browser.common.app.lib.cookie.CookieFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Pattern;

/**
 * 功能说明：com.oppo.cms.utils.CommonUtils
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/10/15 14:29
 */
@Slf4j
public class CommonUtils {

    public static final String RELEASE = "release";
    private static final Pattern MAIN_VERSION_PATTERN = Pattern.compile("^\\d.\\d");

    public static final String EPISODE_TITLE_PREFIX = "第";


    /**
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     *
     * @param version1 String
     * @param version2 String
     * @return int
     */
    public static int compareVersion(String version1, String version2) {
        // 如果有一个为空，则返回0直接通过
        if (StringUtils.isBlank(version1) || StringUtils.isBlank(version2)) {
            return 0;
        }
        String[] versionArray1 = version1.split("\\.");// 注意此处为正则匹配，不能用"."；
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        int minLength = Math.min(versionArray1.length, versionArray2.length);// 取最小长度值
        int diff = 0;
        while (idx < minLength && (diff = versionArray1[idx].length() - versionArray2[idx].length()) == 0// 先比较长度
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {// 再比较字符
            ++idx;
        }
        // 如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }

    public static boolean matchVersion(String beginVersion, String endVersion, String appVersion) {
        if (compareVersion(appVersion, beginVersion) < 0
                || compareVersion(appVersion, endVersion) > 0) {
            return false;
        } else {
            return true;
        }
    }

    public static String getUid(String session) {
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(session)) {
                return CookieFactory.getCookie(session).getInfo().getUid();
            }
        } catch (Exception e) {
            log.info("getUid error", e);
        }

        return null;
    }

    public static int getVersionTag(int version) {
        int versionTag = 10;
        if (version == 0) {
            versionTag = 5;
        } else if (version < 41900) {
            versionTag = 1;
        } else if (version < 42300) {
            versionTag = 2;
        } else if (version < 50000) {
            versionTag = 3;
        } else if (version < 52300) {
            versionTag = 4;
        } else if (version < 60300) {
            versionTag = 5;
        } else if (version < 60800) {
            versionTag = 6;
        } else if (version < 71200) {
            versionTag = 7;
        } else if (version < 71600) {
            versionTag = 8;
        } else if (version < 80100) {
            versionTag = 9;
        }
        return versionTag;
    }

    public static boolean remainByScoreAndProgramType(float score, String programType) {
        if (StringUtils.isBlank(programType)) {
            return false;
        }
        return score >= 6 && StandardConstant.ProgramType.MOVIE.equals(programType);
    }

    public static String getEpisodeTitle(int episode, int episodeUnit, String contentType) {
        String episodeTitle = "";
        if (episode >= 0 && !ContentTypeEnum.MOVIE.getCode().equals(contentType)) {
            if (episodeUnit == 1) {
                episodeTitle = EPISODE_TITLE_PREFIX + episode + "集";
            } else if (episodeUnit == 2) {
                episodeTitle = EPISODE_TITLE_PREFIX + episode + "期";
            } else if (episodeUnit == 3) {
                episodeTitle = EPISODE_TITLE_PREFIX + episode + "话";
            } else if (episodeUnit == 4) {
                episodeTitle = EPISODE_TITLE_PREFIX + episode + "番";
            } else {
                episodeTitle = EPISODE_TITLE_PREFIX + episode + "集";
            }
        }
        return episodeTitle;
    }



}
