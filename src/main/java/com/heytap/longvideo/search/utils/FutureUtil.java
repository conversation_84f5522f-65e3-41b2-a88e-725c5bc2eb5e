package com.heytap.longvideo.search.utils;

import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

/**
 * @Description future工具
 * @<NAME_EMAIL>
 * @Date 2022/1/25
 * @Version 1.0
 **/

@Slf4j
public class FutureUtil {

    private static final Logger NORMAL_EXCEPTION_LOG = LoggerFactory.getLogger("normal_exception");


    public static  <T> T getFutureIgnoreException(Future<T> future) {
        try {
            return (future != null) ? future.get(4,TimeUnit.SECONDS) : null;
        } catch (Exception e) {
            if(e instanceof HttpDataChannelException || e instanceof TimeoutException){
                NORMAL_EXCEPTION_LOG.error("error get result from future", e);
            }else{
                log.error("error get result from future", e);
            }
            return null;
        }
    }

    public static  <T> T getIgnoreExceptionWhenHttp(Future<T> future) {
        try {
            return (future != null) ? future.get(4,TimeUnit.SECONDS) : null;
        } catch (Exception e) {
            NORMAL_EXCEPTION_LOG.error("error get result from future", e);
            return null;
        }
    }

    public static  <T> T getFutureIgnoreException(Future<T> future,long timeout, TimeUnit unit) {
        try {
            return (future != null) ? future.get(timeout,unit) : null;
        } catch (Exception e) {
            if(e instanceof HttpDataChannelException || e instanceof TimeoutException){
                NORMAL_EXCEPTION_LOG.error("error get result from future", e);
            }else {
                log.error("error get result from future", e);
            }
            return null;
        }
    }

    /**
     * 解压真实的异常
     * @param throwable
     * @return
     */
    public static Throwable extractRealException(Throwable throwable) {
        if (throwable instanceof CompletionException || throwable instanceof ExecutionException) {
            if (throwable.getCause() != null) {
                return throwable.getCause();
            }
        }
        return throwable;
    }
}
