package com.heytap.longvideo.search.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR> ye mengsheng
 * @date 2024/9/2 19:57
 */
@Slf4j
public class CSVExportUtil {

    /**
     * 文件不存在，则创建，并保证不乱码
     *
     * @param file
     */
    private static void newCsvFile(File file) throws IOException {
        try (FileOutputStream out = new FileOutputStream(file, true);
             OutputStreamWriter osw = new OutputStreamWriter(out, StandardCharsets.UTF_8);
             BufferedWriter bw = new BufferedWriter(osw)) {
            bw.write('\ufeff');
        } catch (Exception e) {
            throw new IOException(e);
        }
    }

    /**
     * 导出CSV
     *
     * @param file     csv文件(路径+文件名)
     * @param dataList 数据
     * @return
     */
    public static void exportCsv(File file, List<String> dataList) throws IOException {
        if (!file.exists()) {
            newCsvFile(file);
        }
        try (FileOutputStream out = new FileOutputStream(file, true);
             OutputStreamWriter osw = new OutputStreamWriter(out, StandardCharsets.UTF_8);
             BufferedWriter bw = new BufferedWriter(osw)) {
            if (dataList != null && !dataList.isEmpty()) {
                for (String data : dataList) {
                    bw.append(data).append("\r");
                }
            }
        } catch (Exception e) {
            throw new IOException(e);
        }
    }
}