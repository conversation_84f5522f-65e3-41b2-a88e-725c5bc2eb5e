package com.heytap.longvideo.search.utils;

import com.oppo.browser.common.app.lib.strategy.AttributeValuesCreator;
import com.oppo.browser.strategy.model.AttributeValues;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
public class StrategyUtil {
    /**
     * 针对三方kkua场景，补充其余属性信息：视频版本号
     * @param attributeValues
     * @param appVersion
     */
    public static void perfectAttributeValues(AttributeValues attributeValues, String appVersion) {
        if (attributeValues != null) {
            attributeValues.setClientFullBrowserVersion(appVersion);
            AttributeValuesCreator.buildBrowserVersion(attributeValues);
        }
    }

    public static boolean isTransparentKkua(AttributeValues attributeValues) {
        if (attributeValues == null) {
            return false;
        }
        // 不传kkua，attributeValues也会有值，只能通过其中的字段是否有值来判断
        return StringUtils.isNotBlank(attributeValues.getPhone()) && StringUtils.isNotBlank(attributeValues.getRom());
    }
}
