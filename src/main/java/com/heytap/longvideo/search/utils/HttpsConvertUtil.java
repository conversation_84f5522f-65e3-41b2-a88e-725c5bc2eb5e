package com.heytap.longvideo.search.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;

/**
 * @Description: http ——> https 转换
 * @Author: 80398885WT
 * @Date: 2025/6/5
 */
@Slf4j
public class HttpsConvertUtil {

    /**
     * 将 HTTP URL 转换为 HTTPS，处理边界情况
     * @param originalUrl 原始 URL 字符串
     * @param forceHttps 是否强制转换无协议的 URL 为 HTTPS
     * @return 转换后的 HTTPS URL，若无法转换则返回 null
     */
    public static String convertToHttps(String originalUrl, boolean forceHttps) {
        if (originalUrl == null || originalUrl.trim().isEmpty()) {
            return null;
        }

        try {
            URI uri = new URI(originalUrl);

            // 处理 scheme 部分
            String scheme = uri.getScheme();
            if (Objects.nonNull(scheme)) {
                if ("https".equalsIgnoreCase(scheme)) {
                    return originalUrl; // 已经是 HTTPS
                }
                if (!"http".equalsIgnoreCase(scheme)) {
                    return originalUrl; // 非 HTTP 协议不处理（如 ftp, mailto）
                }
            } else if (!forceHttps) {
                return originalUrl; // 无协议且不强制转换
            }

            // 构建新 URI
            int port = uri.getPort();
            if (port == 80 || port == -1) { // 移除显式 HTTP 默认端口
                port = -1;
            }

            return new URI(
                    "https",
                    uri.getUserInfo(),
                    uri.getHost(),
                    port,
                    uri.getPath(),
                    uri.getQuery(),
                    uri.getFragment()
            ).toString();

        } catch (URISyntaxException e) {
            // 处理非法 URL 格式
            log.error("Invalid URL format:{}, error:", originalUrl, e);
            return null;
        }
    }
}