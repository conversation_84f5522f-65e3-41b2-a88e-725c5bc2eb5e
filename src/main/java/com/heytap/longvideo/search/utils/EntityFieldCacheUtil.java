package com.heytap.longvideo.search.utils;


import org.springframework.util.ReflectionUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: 80339123 liu ying
 * @Date: 2021/8/3 15:43
 */
public class EntityFieldCacheUtil<T> {

    /**
     * 实体字段缓存
     */
    private static final Map<Class<?>, HashMap<String, Field>> entityFieldsCache
            = new ConcurrentHashMap<>();

    /**
     * 获取实体字段缓存
     *
     * @param requestClass
     * @return
     */
    public static <T extends Annotation> HashMap<String, Field> getEntityFieldsCache(Class<?> requestClass, Class<T> annotationClass) {
        return entityFieldsCache.computeIfAbsent(requestClass, key -> {

            //没有命中缓存,通过反射获取参与签名的字段
            HashMap<String, Field> fieldsMap = new HashMap<>();

            ReflectionUtils.doWithFields(requestClass, field -> {
                T annotation = field.getAnnotation(annotationClass);
                if (annotation != null) {
                    fieldsMap.put(field.getName(), field);
                }
            });

            return fieldsMap;
        });
    }


}
