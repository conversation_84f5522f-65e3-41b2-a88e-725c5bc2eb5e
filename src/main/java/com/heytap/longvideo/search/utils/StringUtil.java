package com.heytap.longvideo.search.utils;

import com.github.stuxuhai.jpinyin.PinyinHelper;

import java.util.*;
import java.util.regex.Pattern;

/*
 * Description string工具类
 * Date 10:45 2021/12/6
 * Author songjiajia 80350688
*/
public class StringUtil extends org.apache.commons.lang3.StringUtils {

    static Pattern numberPattern;

    static{
        numberPattern = Pattern.compile("[0-9]*");
    }


    public static boolean isChar(String str) {
        char[] chars = str.toCharArray();
        boolean isPhontic = false;
        for (int i = 0; i < chars.length; i++) {
            isPhontic = (chars[i] >= 'a' && chars[i] <= 'z') || (chars[i] >= 'A' && chars[i] <= 'Z');
            if (!isPhontic) {
                return false;
            }
        }
        return true;
    }


    public static boolean isNumericOrChar(String str) {
        char[] chars = str.toCharArray();
        boolean isPhontic = false;
        for (char aChar : chars) {
            isPhontic = (aChar >= 'a' && aChar <= 'z') || (aChar >= 'A' && aChar <= 'Z');
            if (!isPhontic && !numberPattern.matcher(str).matches()) {
                return false;
            }
        }
        return true;
    }

    public static String toPinyin(String hanyu){
        StringBuilder pinyinBuild = new StringBuilder();
        char[] hanYuArr = hanyu.toCharArray();
        for (int i = 0, len = hanYuArr.length; i < len; i++) {
            if (Character.toString(hanYuArr[i]).matches("[\\u4E00-\\u9FA5]+")) {
                String[] pys = PinyinHelper.convertToPinyinArray(hanYuArr[i]);
                pinyinBuild.append(pys[0]);
            } else {
                pinyinBuild.append(hanYuArr[i]);
            }
        }
        return pinyinBuild.toString();
    }
    
    /**
     * 计算两个字符串的文本匹配率（支持中英文和空格）
     * @param searchTerm 搜索词A
     * @param title 标题B
     * @return 匹配率是否达标
     */
    public static boolean isMatchRateQualified(String searchTerm, String title) {
        // 提取有效字符（中文、英文、数字和空格）
        List<String> termChars = extractValidChars(searchTerm);
        List<String> titleChars = extractValidChars(title);

        int termLength = termChars.size();
        int titleLength = titleChars.size();

        // 处理空字符串情况
        if (termLength == 0 || titleLength == 0) {
            return false;
        }

        // 计算匹配字数
        int matchedCount = countMatchedChars(termChars, titleChars);

        // 计算匹配率
        double matchRate = (double) matchedCount / Math.max(termLength, titleLength);

        // 根据搜索词长度判断是否达标
        if (termLength <= 3) {
            return matchRate >= 1.0;    // 3字及以下要求100%匹配
        } else if (termLength == 4) {
            return matchRate >= 0.75;    // 4字要求75%匹配
        } else {
            return matchRate >= 0.8;     // 5字及以上要求80%匹配
        }
    }

    /**
     * 提取字符串中的有效字符（中文、英文、数字和空格）
     */
    private static List<String> extractValidChars(String str) {
        List<String> chars = new ArrayList<>();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (isValidCharacter(c)) {
                chars.add(String.valueOf(c));
            }
        }
        return chars;
    }

    /**
     * 判断是否为有效字符（中文、英文、数字或空格）
     */
    private static boolean isValidCharacter(char c) {
        // 空格
        if (c == ' ') {
            return true;
        }
        // 数字
        if (c >= '0' && c <= '9') {
            return true;
        }
        // 英文字母（大小写）
        if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
            return true;
        }
        // 中文字符
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
    }

    /**
     * 计算两个字符列表的匹配字数（考虑顺序）
     */
    private static int countMatchedChars(List<String> termChars, List<String> titleChars) {
        int matched = 0;
        int titleIndex = 0;

        for (String termChar : termChars) {
            // 在标题中查找当前字符
            boolean found = false;
            while (titleIndex < titleChars.size()) {
                if (termChar.equalsIgnoreCase(titleChars.get(titleIndex))) {
                    matched++;
                    titleIndex++;
                    found = true;
                    break;
                }
                titleIndex++;
            }
            if (!found) {
                break; // 如果没找到，终止匹配
            }
        }

        return matched;
    }
}
