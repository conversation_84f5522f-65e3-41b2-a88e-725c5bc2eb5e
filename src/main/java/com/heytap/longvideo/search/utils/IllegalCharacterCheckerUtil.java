package com.heytap.longvideo.search.utils;

import java.util.regex.Pattern;

/**
 * <AUTHOR> ye mengsheng
 * @Description：非法字符检测工具类
 * @Version: 1.0
 * @date 2025/6/19 下午2:11
 */
public class IllegalCharacterCheckerUtil {

    /**
     * 返回true表示含有非法字符
     */
    public static boolean containsInvalidCharacters(String input) {
        // 匹配中文字符、字母、数字和常见符号
        String validPattern = "[\\u4e00-\\u9fa5a-zA-Z0-9\\p{P}\\s]";
        return !Pattern.compile(validPattern + "+").matcher(input).matches();
    }
}
