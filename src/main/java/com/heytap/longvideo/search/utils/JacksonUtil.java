package com.heytap.longvideo.search.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;

@Slf4j
public class JacksonUtil {
    private static ObjectMapper mapper = new ObjectMapper();

    // 为了防止替换fastjson后出现问题，特性保持一致
    static {
        // 不输出transient字段
        mapper.configure(MapperFeature.PROPAGATE_TRANSIENT_MARKER, true);
        // 按字母顺序输出
        mapper.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true);
        // key或value为null时不输出
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // Date类型格式
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        // 允许key没有引号
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        // 允许key为单引号
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        // 忽略不存在的字段【重要】
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public static String toJSONString(Object o) {
        try {
            return mapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.error("serialize error", e);
            throw new RuntimeException("serialize error");
        }
    }


    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return mapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("parse error", e);
            throw new RuntimeException("parse error");
        }
    }

    public static <T> T parseObject(String json, TypeReference<T> valueTypeRef) {
        try {
            return mapper.readValue(json, valueTypeRef);
        } catch (JsonProcessingException e) {
            log.error("parse error", e);
            throw new RuntimeException("parse error");
        }
    }
}