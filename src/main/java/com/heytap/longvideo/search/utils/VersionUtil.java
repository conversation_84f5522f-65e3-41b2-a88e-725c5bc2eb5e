package com.heytap.longvideo.search.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/19 17:07
 */
@Slf4j
public class VersionUtil {

    private static final Pattern pattern = Pattern.compile("\\d+");

    /**客户端侧转换版本的逻辑：
     * 注意：该方法仅仅用来api请求server时上报客户端版本以供服务端区分客户端版本，该字段规则变更需要和服务端协商，该字段不要用于其他判断！！！
     */
    public static Integer getVersion(String versionName) {
        //jekins打包的（灰度包）versionName 是 20.4.10.1.0beta 或者 40.4.10.1.0beta 所以只取中间三位做版本号
        //jekins打包的（日常测试包）versionName 是 20.4.10.0der 或者 40.4.10.0der 所以只取后三位做版本号
        //jekins打包的（全量包）versionName 是 20.4.10.1 或者 40.4.10.1
        try {
            String[] codes = versionName.split("\\.");
            if (codes.length == 5 || codes.length == 4) {
                int majorVersion = Integer.parseInt(codes[1]);
                int minorVersion = Integer.parseInt(codes[2]);
                Matcher matcher = pattern.matcher(codes[3]);
                matcher.find();
                int patchVersion = Integer.parseInt(matcher.group());
                int version2Server = majorVersion * 10000 + minorVersion * 100 + patchVersion;
                return version2Server;
            } else if (codes.length == 3) {
                //如果客户端开发run出来的包的话 versionName 是 4.10.1这种，所以可以直接返回
                int majorVersion = Integer.parseInt(codes[0]);
                int minorVersion = Integer.parseInt(codes[1]);
                int patchVersion = Integer.parseInt(codes[2]);
                int version2Server = majorVersion * 10000 + minorVersion * 100 + patchVersion;
                return version2Server;
            }
            return 0;
        } catch (Exception e) {
            log.error("getVersion error from versionName, versionName:{}, error:{}", versionName, e);
            return 0;
        }

    }

    public static String getVersionName(Integer version) {
        try {
            int oneVersion = version / 10000;
            int twoVersion = (version - oneVersion * 10000) / 100;
            int threeVersion = (version - oneVersion * 10000 - twoVersion * 100);
            return String.join(".", String.valueOf(oneVersion), String.valueOf(twoVersion), String.valueOf(threeVersion));
        } catch (Exception e) {
            log.error("getVersionName error, version:{}, error msg:", version, e);
            return "";
        }
    }
}
