package com.heytap.longvideo.search.constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_FUNSHION_LONGVIDEO;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_HUASHI;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_LETV;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_MGMOBILE;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_MIGUOLMPIC;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_SENYU;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_SOHU;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_WEIDIOU;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_YOUKU_MOBILE;
import static com.heytap.longvideo.search.constants.CopyrightConstant.COPYRIGHT_YST;


public class CommonConstant {

    /**
     * 需要过滤掉的演员和导演名字
     */
    public static Set<String> errorActorName = new HashSet<>();
    /**
     * 搜索支持的版权方
     */
    public static List<String> allowSource = new ArrayList<>();

    /**
     * 每个版本支持的版权方
     */
    public static Map<Integer, List<String>> versionTagAllowSource = new HashMap<>();

    public final static String LONGVIDEO = "longvideo";

    /**
     *  预约
     */
    public final static String ACTION_SUBSCRIBE = "subscribe";
    /**
     * 追剧
     */
    public final static String FAVORITE = "favorite";

    public static final String DEFAULT_HANDLER_SOURCE = "default";


    static {
        allowSource.add("sohu");
        allowSource.add("huashi");
        allowSource.add("senyu");
        allowSource.add("funshion");
        allowSource.add("mgmobile");
        allowSource.add("letv");
        allowSource.add("yst");
        allowSource.add("youkumobile");
        allowSource.add("funshion_lv");
        allowSource.add("weidiou");

        errorActorName.add("未知");
        errorActorName.add("无");
        errorActorName.add("明星");

        versionTagAllowSource.put(0, Arrays.asList(COPYRIGHT_SOHU));
        versionTagAllowSource.put(1, Arrays.asList(COPYRIGHT_SOHU));
        versionTagAllowSource.put(2, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU));
        versionTagAllowSource.put(3, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU));
        versionTagAllowSource.put(4, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE));
        versionTagAllowSource.put(5, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE));
        versionTagAllowSource.put(6, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE, COPYRIGHT_LETV, COPYRIGHT_YOUKU_MOBILE));
        versionTagAllowSource.put(7, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE, COPYRIGHT_LETV, COPYRIGHT_YST, COPYRIGHT_YOUKU_MOBILE));
        versionTagAllowSource.put(8, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE, COPYRIGHT_LETV, COPYRIGHT_YST, COPYRIGHT_MIGUOLMPIC, COPYRIGHT_YOUKU_MOBILE));
        versionTagAllowSource.put(9, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE, COPYRIGHT_LETV, COPYRIGHT_YST, COPYRIGHT_MIGUOLMPIC, COPYRIGHT_YOUKU_MOBILE));
        versionTagAllowSource.put(10, Arrays.asList(COPYRIGHT_SOHU, COPYRIGHT_HUASHI, COPYRIGHT_SENYU, COPYRIGHT_MGMOBILE, COPYRIGHT_LETV, COPYRIGHT_YST, COPYRIGHT_MIGUOLMPIC, COPYRIGHT_YOUKU_MOBILE, COPYRIGHT_FUNSHION_LONGVIDEO, COPYRIGHT_WEIDIOU));

    }

    public static final Map<String, String> targetAppNameMap = new HashMap<String, String>() {{
        put("oppo", "OPPO视频");
        put("realme", "视频");
        put("oneplus", "视频");
    }};
}