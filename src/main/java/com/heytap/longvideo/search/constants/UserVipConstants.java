package com.heytap.longvideo.search.constants;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
public class UserVipConstants {

    /**
     * heytap视频会员
     */
    public static final String VIDEO_VIP = "video_vip";

    /**
     * 芒果视频会员
     */
    public static final String MONGO_VIDEO_VIP = "mongo_video_vip";

    public static final String DEFAULT = "default";

    public static final String MONGO_AND_VIDEO_VIP = "mongo_video_vip,video_vip";

    /**
     * 影视会员对应的源列表
     */
    public static final List<String> VIDEO_VIP_SOURCE_LIST = Lists.newArrayList("senyu", "huashi", "funshion_lv", "weidiou");

    /**
     * 双会员对应的源列表
     */
    public static final List<String> VIP_ALL_SOURCE_LIST = Lists.newArrayList("mgmobile", "senyu", "huashi", "funshion_lv", "weidiou");
}