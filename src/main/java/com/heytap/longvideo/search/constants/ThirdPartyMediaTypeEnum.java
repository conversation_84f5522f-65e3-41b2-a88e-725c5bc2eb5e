package com.heytap.longvideo.search.constants;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
public enum ThirdPartyMediaTypeEnum {

    QUAN_SOU("quansou_sousuozhida", "quansou", "全搜搜索直达"),
    BROSWER("browser_sousuozhida", "browser", "浏览器搜索直达"),
    MAGAZINE("magazine", "magazine", "锁屏"),
    BREENO("breeno", "breeno", "小布"),
    BROSWER_PLAY("browser_play_page", "browser", "浏览器播放"),
    ;

    private String appId;

    private String mediaType;

    private String desc;

    ThirdPartyMediaTypeEnum(String appId, String mediaType, String desc) {
        this.appId = appId;
        this.mediaType = mediaType;
        this.desc = desc;
    }

    public String getAppId() {
        return appId;
    }

    public String getMediaType() {
        return mediaType;
    }

    public String getDesc() {
        return desc;
    }

    public static String getMediaTypeByAppId(String appId) {
        for (ThirdPartyMediaTypeEnum mediaTypeEnum : ThirdPartyMediaTypeEnum.values()) {
            if (mediaTypeEnum.getAppId().equals(appId)) {
                return mediaTypeEnum.getMediaType();
            }
        }
        return "";
    }
}
