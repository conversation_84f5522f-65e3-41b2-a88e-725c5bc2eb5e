package com.heytap.longvideo.search.constants;

/*
 * Description 视频类型
 * Date 10:55 2022/4/21
 * Author songjiajia 80350688
*/
public enum ContentTypeEnum {
    MOVIE("movie", "电影"),
    TV("tv", "电视剧"),
    SHOW("show", "综艺"),
    DOC("doc", "纪录片"),
    COMIC("comic", "动漫"),
    KIDS("kids", "少儿"),
    LIVE("live", "直播"),
    NEWS("news","新闻"),
    MUSIC("music", "音乐"),
    ;

    public String code;
    public String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    ContentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ContentTypeEnum  getByCode(String code){
        for (ContentTypeEnum contentTypeEnum : ContentTypeEnum.values()) {
            if(code.equals(contentTypeEnum.getCode())){
                return contentTypeEnum;
            }
        }
        return null;
    }
}
