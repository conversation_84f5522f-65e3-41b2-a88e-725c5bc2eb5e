package com.heytap.longvideo.search.constants;

import com.google.common.collect.Sets;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/10/16
 */
public class SearchConstant {
    /**
     * markCode-预告片
     */
    public static final String MARK_CODE_YG = "YG";
    /**
     * featureType-预告片
     */
    public static final int FEATURE_TYPE_YG = 2;

    public static final String SID = "sid";

    public static final String MINORS_POOL_CODE = "minorsPoolCode";

    public static Map<String, Map<String, String>> sortTypeMap = new HashMap<String, Map<String, String>>() {{
        put("0", new HashMap<String, String>() {{
            put("0", "last7DaysClickPv");
            put("1", "last15DaysClickPv");
            put("2", "last30DaysClickPv");
        }});
        put("1", new HashMap<String, String>() {{
            put("0", "last7DaysPlayPv");
            put("1", "last15DaysPlayPv");
            put("2", "last30DaysPlayPv");
        }});
        put("2", new HashMap<String, String>() {{
            put("0", "year");
        }});
        put("3", new HashMap<String, String>() {{
            put("0", "sourceScore");
        }});
    }};

    /**
     * 搜索请求来源：锁屏
     */
    public static String SEARCH_FROM_MAGAZINE = "magazine";

    /**
     * 搜索请求来源：小布
     */
    public static String SEARCH_FROM_BREENO = "breeno";

    /**
     * 搜索请求来源：全搜
     */
    public static String SEARCH_FROM_QUANSOU = "quansou_sousuozhida";

    /**
     * 搜索请求来源：浏览器
     */
    public static String SEARCH_FROM_BROWSER = "browser_sousuozhida";

    /**
     * 8.9 可由策略控制是否出全网搜结果的搜索来源集合
     */
    public static Set<String> STRATEGY_CONTROL_SEARCH_SOURCE = Sets.newHashSet("magazine", "quansou_sousuozhida", "breeno");

    /**
     * 8.9 支持严格精准匹配的搜索来源集合
     */
    public static Set<String> EXACT_MATCH_SEARCH_SOURCE = Sets.newHashSet("magazine", "breeno");

    /**
     * 按钮文案
     */
    public final static String VIP_BUTTON_TEXT = "免费看";

    public final static String NOT_VIP_BUTTON_TEXT = "立即看";

    public final static String THIRD_SEARCH_TEXT = "去观看";

    /**
     * 需要过滤的标签
     */
    public final static Set<String> FILTER_TAGS = Sets.newHashSet("其他", "其它");

    // 浏览器-搜索结果卡
    public final static String BROWSER_RESULT_CARD = "browserResultCard";

    // 全搜-搜索结果卡
    public final static String QS_RESULT_CARD = "qsResultCard";

    // 锁屏-搜索结果卡
    public final static String MAGAZINE_RESULT_CARD = "magazineResultCard";

    // 小布-搜索结果卡
    public final static String BREENO_RESULT_CARD = "breenoResultCard";
}
