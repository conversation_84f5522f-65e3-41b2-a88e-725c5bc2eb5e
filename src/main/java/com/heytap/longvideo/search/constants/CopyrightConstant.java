package com.heytap.longvideo.search.constants;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/*
 * Description 版权方
 * Date 10:42 2021/12/2
 * Author songjiajia 80350688
*/
public class CopyrightConstant {

    public static final String COPYRIGHT_TENCENT = "tencent";
    public static final String COPYRIGHT_YOUKU = "youku";
    public static final String COPYRIGHT_SOHU = "sohu";
    public static final String COPYRIGHT_HUASHI = "huashi";
    public static final String COPYRIGHT_SENYU = "senyu";
    public static final String COPYRIGHT_FUNSHION_LONGVIDEO = "funshion_lv";
    public static final String COPYRIGHT_WEIDIOU = "weidiou";
    public static final String COPYRIGHT_MGMOBILE = "mgmobile";
    public static final String COPYRIGHT_LETV = "letv";
    public static final String COPYRIGHT_IQIYI = "iqiyi";
    public static final String COPYRIGHT_YST = "yst";
    public static final String COPYRIGHT_MIGUOLMPIC = "miguolympic";
    public static final String COPYRIGHT_YOUKU_MOBILE = "youkumobile";


}
