package com.heytap.longvideo.search.constants;

import lombok.Getter;

/**
 * author: igorea
 * 注: RelationType枚举值在longvideo-user-rest中还有很多其他的类型,本次只用到了这两种类型,后续需要用到其他类型自行补充
 */
@Getter
public enum RelationTypeEnum {
    /**
     * 预约
     */
    SUBSCRIBE(4, "subscribe"),
    /**
     * 追剧
     */
    CHASE_ALBUM(6, "chaseAlbum"),
    /**
     * 观看历史
     */
    WATCH_HISTORY(8,"watchHistory");

    private final int code ;
    private final String msg;

    RelationTypeEnum(int code , String msg ){
        this.code = code ;
        this.msg = msg;
    }
}
