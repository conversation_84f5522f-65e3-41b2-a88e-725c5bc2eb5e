package com.heytap.longvideo.search.constants;

import java.util.Objects;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/2/13 上午11:00
 */

public enum ScoreLevelEnum {
    SCORE_LEVEL_1(1, "6分以下", new ScoreRange("0.0", "5.9")),
    SCORE_LEVEL_2(2, "6-7分", new ScoreRange("6.0", "7.0")),
    SCORE_LEVEL_3(3, "7-8分", new ScoreRange("7.0", "8.0")),
    SCORE_LEVEL_4(4, "8-9分", new ScoreRange("8.0", "9.0")),
    SCORE_LEVEL_5(5, "9-10分", new ScoreRange("9.0", "9.9")),
    SCORE_LEVEL_6(6, "空", new ScoreRange("-1.0", "-1.0"));
    Integer level;
    String desc;

    public Integer getLevel() {
        return level;
    }

    public ScoreRange getScoreRange() {
        return scoreRange;
    }

    public String getDesc() {
        return desc;
    }

    ScoreRange scoreRange;

    ScoreLevelEnum(int level, String desc, ScoreRange scoreRange) {
        this.level = level;
        this.desc = desc;
        this.scoreRange = scoreRange;
    }

    public static ScoreRange getByLevel(Integer level) {
        for (ScoreLevelEnum scoreLevel : ScoreLevelEnum.values()) {
            if (scoreLevel.getLevel().equals(level)) {
                return scoreLevel.getScoreRange();
            }
        }
        return null;
    }
}


