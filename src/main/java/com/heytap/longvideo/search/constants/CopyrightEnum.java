package com.heytap.longvideo.search.constants;

import lombok.Getter;

public enum CopyrightEnum {
    MG_MOBILE("tencent", "腾讯视频"),
    IQIYI("iqiyi",  "爱奇艺"),
    SOHU("sohu",  "搜狐视频"),
    YOUKU("youku",  "优酷视频"),
    BILIBILI("bilibili",  "哔哩哔哩"),
    ;

    @Getter
    private String source;
    @Getter
    private String desc;

    CopyrightEnum(String source, String desc) {
        this.source = source;
        this.desc = desc;
    }

    public static CopyrightEnum getBySource(String source){
        for (CopyrightEnum copyrightEnum : CopyrightEnum.values()) {
            if(copyrightEnum.getSource().equals(source)){
                return copyrightEnum;
            }
        }
        return null;
    }
}
