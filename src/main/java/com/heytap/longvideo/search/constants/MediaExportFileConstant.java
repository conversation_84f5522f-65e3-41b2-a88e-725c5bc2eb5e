package com.heytap.longvideo.search.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ye mengsheng
 * @date 2024/9/9 下午6:21
 */
public class MediaExportFileConstant {


    public static final String SEARCH_MEDIA_EXPORT_REDIS_STATUS = "search_media_export_status:";
    public static final String SEARCH_MEDIA_EXPORT_REDIS_URL = "search_media_export_url:";

    /**
     * 每个视频页对应的编码不一样
     */
    public static final Map<String, String> MEDIA_TYPE_MAP = new HashMap<String, String>() {
        {
            put("sohu_0", "正片");
            put("sohu_1", "1");
            put("sohu_2", "预告片");
            put("sohu_8", "特辑");
            put("sohu_9", "片花");
            put("sohu_10", "花絮");
            put("sohu_99", "其他");

            put("huashi_0", "正片");
            put("huashi_2", "预告片");
            put("huashi_10", "花絮");
            put("huashi_14", "高能看点");
            put("huashi_99", "其他");

            put("senyu_0", "正片");
            put("senyu_2", "预告片");
            put("senyu_10", "花絮");
            put("senyu_14", "高能看点");
            put("senyu_99", "其他");

            put("weidiou_0", "正片");
            put("weidiou_2", "预告片");
            put("weidiou_10", "花絮");
            put("weidiou_14", "高能看点");
            put("weidiou_99", "其他");

            put("funshion_lv_0", "正片");
            put("funshion_lv_2", "预告片");
            put("funshion_lv_10", "花絮");
            put("funshion_lv_14", "高能看点");
            put("funshion_lv_99", "其他");

            put("funshion_0", "正片");
            put("funshion_2", "预告片");
            put("funshion_10", "花絮");
            put("funshion_14", "高能看点");
            put("funshion_99", "其他");

            put("mgmobile_0", "正片");
            put("mgmobile_1", "1");
            put("mgmobile_2", "预告片");
            put("mgmobile_3", "短片");
            put("mgmobile_4", "番外");
            put("mgmobile_5", "前序");
            put("mgmobile_6", "分支剧情");
            put("mgmobile_7", "摘要");
            put("mgmobile_10", "花絮");
            put("mgmobile_12", "分段");
            put("mgmobile_13", "彩蛋");
            put("mgmobile_99", "其他");

            put("letv_0", "正片");
            put("letv_1", "1");
            put("letv_2", "预告片");
            put("letv_8", "特辑");
            put("letv_9", "片花");
            put("letv_10", "花絮");
            put("letv_99", "其他");

            put("yst_0", "正片");
            put("yst_2", "预告片");
            put("yst_10", "花絮");
        }
    };
    //0：免费，1：收费，2：单点，3：用券
    public static Map<Integer, String> payStatusMap = new HashMap<Integer, String>() {
        {
            put(0, "免费");
            put(1, "收费");
            put(2, "单点");
            put(3, "用券");
        }
    };

}
