package com.heytap.longvideo.search.constants;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/6/5 下午8:11
 */
public class OutSideSearchConstant {

    /**
     * 对外搜索按钮文案策略名称
     */
    public final static String SERVICE_ID_OUTSIDE_BUTTON = "set_quanSouButtonConfig";

    /**
     * 对外搜索下发全网搜策略名称
     */
    public final static String SERVICE_ID_SEARCH_NET_RESULTS = "set_SearchNetResults";

    /**
     * 做任务解锁剧集策略名称--影视
     */
    public final static String TASK_FOR_EPISODE = "set_taskForEpisode";

    /**
     * 做任务解锁剧集策略名称--芒果
     */
    public final static String TASK_FOR_EPISODE_MONGO = "set_taskForEpisodeMongo";

    /**
     * 影视会员--能免费解锁的源（weidiou,funshion_lv不支持免费解锁）
     * 芒果支持免费解锁版本：80500
     */
    public final static List<String> VIDEO_VIP_SOURCE_LIST = Arrays.asList("senyu", "huashi");

    /**
     * 影视会员免费解锁开始版本
     */
    public final static Integer TASK_UNLOCK_EPISODE_VERSION_FOR_VIDEO_VIP = 71200;

    /**
     * 芒果会员免费解锁开始版本
     */
    public final static Integer TASK_UNLOCK_EPISODE_VERSION_FOR_MONGO_VIP = 80500;

    /**
     * 非入库详情页最低支持版本
     */
    public final static Integer NON_INSTOCK_DETAIL_CERSION = 80700;

}