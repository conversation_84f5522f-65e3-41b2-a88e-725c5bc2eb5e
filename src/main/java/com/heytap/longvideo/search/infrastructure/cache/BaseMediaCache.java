package com.heytap.longvideo.search.infrastructure.cache;

import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.cpc.video.framework.lib.utils.CollectionUtilsExt;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import redis.clients.jedis.JedisCluster;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
public abstract class BaseMediaCache<T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseMediaCache.class);

    @Autowired
    protected JedisCluster jedisCluster;

    /**
     * 缓存key
     */
    abstract String buildCacheKey(String id);

    /**
     * 缓存时间，秒
     */
    abstract int ttl();

    /**
     * 返回id关联的媒资缓存数据<br/> 未命中缓存时，尝试从cacheloader获取数据并写入缓存
     *
     * @param id          媒资数据id（如sid/eid/vid）
     * @param mediaType   媒资类型 比如:
     *                    {@link StandardAlbum}
     * @param cacheLoader 缓存数据加载器
     */
    public T get(String id, Class<T> mediaType, Function<String, T> cacheLoader) {
        String cacheKey = buildCacheKey(id);
        String cacheData = null;
        try {
            cacheData = jedisCluster.get(cacheKey);
        } catch (Exception e) {
            LOGGER.error("error get {} from cache:{}", mediaType, cacheKey, e);
            //查缓存失败不影响主流程
        }

        if (StringUtils.isNotEmpty(cacheData)) {
            //命中缓存
            return JsonUtil.fromStr(cacheData, mediaType);
        }

        //没有命中缓存，尝试从cacheLoader加载数据，并写入缓存
        T data = null;
        if (cacheLoader != null) {
            data = cacheLoader.apply(cacheKey);

            if (data != null) {
                try {
                    cacheData = JsonUtil.toJson(data);
                    //放入缓存
                    jedisCluster.setex(cacheKey, ttl(), cacheData);
                } catch (Exception e) {
                    LOGGER.error("error set {} to cache:{},{}", mediaType, cacheKey, cacheData, e);
                    //写入缓存失败不影响主流程
                }
            }
        }
        return data;
    }


    public List<T> mget(Collection<String> ids, Class<T> mediaType) {
        List<String> keys = ids.stream().map(id -> buildCacheKey(id))
                .collect(Collectors.toList());

        List<String> resultList = jedisCluster.mgetExt(keys.toArray(new String[0]));

        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        return resultList.stream()
                .filter(Objects::nonNull)
                .map(result -> JsonUtil.fromStr(result, mediaType))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    public Map<String /* id */ , T> mget(Collection<String> ids, Class<T> mediaType, Function<T, String> idIdentity,
                                         Function<Collection<String>, Map<String /* id */ , T>> cacheLoader) {
        //先从缓存批量获取
        List<T> mediaList = mget(ids, mediaType);

        //按转为id为key的索引
        //之所以空列表要用new HashMap()，而不是emptyMap()，是因为后面需要putAll
        Map<String /* id */, T> mediaMap = mediaList.isEmpty() ? new HashMap<>() : CollectionUtilsExt.index(mediaList,
                idIdentity);

        //提取未命中缓存的ids
        Collection<String> missCacheIds = extractMissCacheIds(ids, mediaMap);

        LOGGER.debug("missCacheIds:{}", missCacheIds);

        if (!missCacheIds.isEmpty()) {
            //未命中缓存的id，从数据库加载
            Map<String/* id */, T> mediaMapFromDb = cacheLoader.apply(missCacheIds);
            mediaMap.putAll(mediaMapFromDb);

            //放入缓存
            mset(mediaMapFromDb);
        }

        return mediaMap;
    }

    private Collection<String> extractMissCacheIds(Collection<String> ids, Map<String, T> mediaMap) {
        if (mediaMap.isEmpty()) {
            //全部未命中
            return ids;
        }
        List<String> missCacheIds = new ArrayList<>();

        for (String id : ids) {
            if (!mediaMap.containsKey(id)) {
                missCacheIds.add(id);
            }
        }
        return missCacheIds;
    }

    public String set(String id, T media) {
        String cacheKey = buildCacheKey(id);
        return jedisCluster.setex(cacheKey, ttl(), JsonUtil.toJson(media));
    }

    public void mset(Map<String/* id */, T> mediaMap) {
        for (Map.Entry<String, T> entry : mediaMap.entrySet()) {
            set(entry.getKey(), entry.getValue());
        }
    }


    public Long delete(String id) {
        String cacheKey = buildCacheKey(id);
        return jedisCluster.del(cacheKey);
    }
}
