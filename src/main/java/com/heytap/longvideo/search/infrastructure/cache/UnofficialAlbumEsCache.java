package com.heytap.longvideo.search.infrastructure.cache;

import com.heytap.longvideo.search.config.EsMediaCacheConfig;
import com.heytap.longvideo.search.infrastructure.constants.CacheKeyConstants;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Service
public class UnofficialAlbumEsCache extends BaseMediaCache<UnofficialAlbumEs> {

    @Autowired
    private EsMediaCacheConfig esMediaCacheConfig;

    @Override
    String buildCacheKey(String sid) {
        return CacheKeyConstants.UNOFFICIAL_ALBUM_ES_KEY_PREFIX + sid;
    }

    @Override
    int ttl() {
        //加上散列时间，避免缓存集中失效
        return esMediaCacheConfig.getAlbumEsCacheTtl() + RandomUtils.nextInt(0, esMediaCacheConfig.getMaxCacheHashTimes());
    }
}
