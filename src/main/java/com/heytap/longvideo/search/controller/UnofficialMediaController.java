package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.unofficial.*;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.standard.StandardAlbumVo;
import com.heytap.longvideo.search.model.request.OcsImageUploadAndEsSyncRequest;
import com.heytap.longvideo.search.model.request.MagazinePoolSuggestRequest;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.core.annotation.QueryBean;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestBody;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * @Description: 非合作方媒资 - controller
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/14 16:50
 */
@Slf4j
@RestController
@RequestMapping("/gateway/meizi/media-rest/unofficialAlbum")
public class UnofficialMediaController {

    private final MediaUnofficialAlbumExecutor mediaUnofficialAlbumExecutor;

    private final GetLockedFieldsInfoExecutor getLockedFieldsInfoExecutor;

    private final EditAlbumManageStatusExecutor editAlbumManageStatusExecutor;

    private final EditAlbumExecutor editAlbumExecutor;

    private final ValidOrInvalidProgramExecutor validOrInvalidProgramExecutor;

    private final GetAlbumInfoExecutor getAlbumInfoExecutor;

    private final SimilarUnofficialAlbumExecutor similarUnofficialAlbumExecutor;

    private final UnofficialAlbumService unofficialAlbumService;

    public UnofficialMediaController(MediaUnofficialAlbumExecutor mediaUnofficialAlbumExecutor,
                                     GetLockedFieldsInfoExecutor getLockedFieldsInfoExecutor,
                                     EditAlbumManageStatusExecutor editAlbumManageStatusExecutor,
                                     EditAlbumExecutor editAlbumExecutor,
                                     ValidOrInvalidProgramExecutor validOrInvalidProgramExecutor,
                                     GetAlbumInfoExecutor getAlbumInfoExecutor,
                                     SimilarUnofficialAlbumExecutor similarUnofficialAlbumExecutor,
                                     UnofficialAlbumService unofficialAlbumService) {
        this.mediaUnofficialAlbumExecutor = mediaUnofficialAlbumExecutor;
        this.getLockedFieldsInfoExecutor = getLockedFieldsInfoExecutor;
        this.editAlbumManageStatusExecutor = editAlbumManageStatusExecutor;
        this.editAlbumExecutor = editAlbumExecutor;
        this.validOrInvalidProgramExecutor = validOrInvalidProgramExecutor;
        this.getAlbumInfoExecutor = getAlbumInfoExecutor;
        this.similarUnofficialAlbumExecutor = similarUnofficialAlbumExecutor;
        this.unofficialAlbumService = unofficialAlbumService;
    }

    /**
     * 媒资后台查询非合作剧头
     */
    @RequestMapping(value = {"/search"})
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> searchUnofficialAlbum(AsyncRequest r){
        return mediaUnofficialAlbumExecutor.execute(r);
    }

    @RequestMapping(value = {"/getLockedFieldsInfo", "/getProgramInfo"})
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> getLockedFieldsInfo(AsyncRequest r) {
        return getLockedFieldsInfoExecutor.execute(r);
    }

    /**
     * @Description: 锁定信息
     */
    @RequestMapping(value = "/lockMetadata")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> lockMetadata(AsyncRequest r) {
        return editAlbumManageStatusExecutor.execute(r);
    }

    /**
     * @Description: 锁定节目
     */
    @RequestMapping(value = "/lockProgram")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> lockProgram(AsyncRequest r) {
        return editAlbumManageStatusExecutor.execute(r);
    }

    /**
     * @Description: 解锁
     */
    @RequestMapping(value = "/autoRefresh")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> autoRefresh(AsyncRequest r) {
        return editAlbumManageStatusExecutor.execute(r);
    }

    /**
     * @Description: 修改剧集
     * 根据editType参数区分业务类型
     * editType = edit, 修改
     * editType = editASyncProgram, 修改并同步
     * editType = editSyncLockMetaData, 修改锁定并同步
     *
     * @param r 请求体
     * @return CompletableFuture<ResponseEntity.Response>
     */
    @RequestMapping(value = {"/edit", "/editASyncProgram", "/editSyncLockMetaData"})
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> edit(AsyncRequest r) {
        return editAlbumExecutor.execute(r);
    }

    /**
     * @Description: 节目生效
     *
     * @param r 请求体
     * @return CompletableFuture<ResponseEntity.Response>
     */
    @RequestMapping(value = "/valid")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> valid(AsyncRequest r) {
        return validOrInvalidProgramExecutor.execute(r);
    }

    /**
     * @Description: 节目失效
     *
     * @param r 请求体
     * @return CompletableFuture<ResponseEntity.Response>
     */
    @RequestMapping(value = "/invalid")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> invalid(AsyncRequest r) {
        return validOrInvalidProgramExecutor.execute(r);
    }

    /**
     * @Description: 获取剧头信息
     * @param r 请求体
     * @return CompletableFuture<ResponseEntity.Response>
     */
    @RequestMapping(value = "/getAlbumInfo")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> getAlbumInfo(AsyncRequest r) {
        return getAlbumInfoExecutor.execute(r);
    }

    @RequestMapping(value = "/getSimilarUnofficialAlbumInfo")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> getSimilarAlbumInfo(AsyncRequest r) {
        return similarUnofficialAlbumExecutor.execute(r);
    }

    /**
     * 非在库内容建议--同步锁屏
     */
    @RequestMapping(value = "/magazinePool/suggest")
    public Result<PageResponse<StandardAlbumVo>> magazinePoolSuggest(@QueryBean MagazinePoolSuggestRequest request) {
        return Result.success(unofficialAlbumService.magazinePoolSuggest(request));
    }

    /**
     * 处理全网节目，图片ocs地址不存在的
     * cupfox 目前无法生成ocs图片
     */
    @RequestMapping(value = "/ocsImageUploadAndEsSync")
    public Result<String> ocsImageUploadAndEsSync(@RequestBody OcsImageUploadAndEsSyncRequest request) {
        try {
            unofficialAlbumService.ocsImageUploadAndEsSync(request);
        } catch (Exception e) {
            log.error("unofficialAlbumService generateUnofficialAlbumOcsImage error", e);
            return Result.error(500, "error");
        }
        return Result.success();
    }
}