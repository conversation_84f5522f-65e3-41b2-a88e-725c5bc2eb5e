package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.standard.GetSourceAlbumIdsByMediaIdExecutor;
import com.heytap.longvideo.search.executors.standard.UpdateStandardAlbumEsExecutor;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2023/3/31 11:22
 */
@Controller
@Slf4j
public class MediaStandardAlbumController {
    @Autowired
    private UpdateStandardAlbumEsExecutor updateStandardAlbumEsExecutor;

    @Autowired
    private GetSourceAlbumIdsByMediaIdExecutor getSourceAlbumIdsByMediaIdExecutor;

    @RequestMapping("/search/updateAlbumEs")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> updateStandardAlbumEs(AsyncRequest r) {
        return updateStandardAlbumEsExecutor.execute(r);
    }


    @RequestMapping("/search/getSourceAlbumIdsByMediaId")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> getSourceAlbumIdsByMediaId(AsyncRequest r) {
        return getSourceAlbumIdsByMediaIdExecutor.execute(r);
    }
}