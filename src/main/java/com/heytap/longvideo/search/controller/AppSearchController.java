package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.app.*;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.service.common.VideoOperationLogService;
import com.oppo.browser.gateway.pb.ResponseEntity.Response;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;

/*
 * Description app端搜索
 * Date 14:21 2021/12/23
 * Author songjiajia 80350688
*/
@Controller
public class AppSearchController {

    @Autowired
    private InitSearchDataExecutor initSearchDataExecutor;


    @Autowired
    private KeyWordSearchExecutor keyWordSearchV2Executor;

    @Autowired
    private SuggestExecutor suggestExecutor;

    @Autowired
    private ListFilterExecutor filterExecutor;

    @Autowired
    private MinorsHotTopExecutor minorsHotTopExecutor;

    @Autowired
    private MinorsPoolCodeBySidExecutor minorsPoolCodeBySidExecutor;

    @Autowired
    private RefreshVirtualDataExecutor refreshVirtualDataExecutor;

    @Autowired
    private RecommendExecutor recommendExecutor;

    @Autowired
    private SeriesCardExecutor seriesCardExecutor;

    @Autowired
    private TagCardExecutor tagCardExecutor;

    @Autowired
    private RecommendCardExecutor recommendCardExecutor;

    @Autowired
    private SeriesCardV2Executor seriesCardV2Executor;

    @Autowired
    private TagCardV2Executor tagCardV2Executor;

    @Autowired
    private ActorCardV2Executor actorCardV2Executor;

    @Autowired
    private RecommendCardV2Executor recommendCardV2Executor;

    @Autowired
    private VideoOperationLogService videoOperationService;

    @Autowired
    private SearchFeedbackSubmitExecutor searchFeedbackSubmitExecutor;

    @Autowired
    private SearchFeedbackOptionExecutor searchFeedbackOptionExecutor;

    @Autowired
    private InitService initService;

    /**
     * 数据初始化
     * @param r
     * @return
     */
    @GetMapping("/init")
    @ResponseEntityResolver
    public CompletableFuture<Response> init(AsyncRequest r){
        return initSearchDataExecutor.execute(r);
    }

    /**
     * 全网搜es--创建索引
     */
    @RequestMapping("/init/unofficialAlbum/createIndex")
    public String unofficialAlbumCreateIndex(@RequestParam("session") String session) {
        if (!"oppo123".equals(session)) {
            return "error";
        }
        initService.unofficialAlbumCreateIndex();
        return "success";
    }

    /**
     * 影视筛选
     * @param r
     * @return
     */
    @GetMapping("/search/listFilter")
    @ResponseEntityResolver
    public CompletableFuture<Response> filter(AsyncRequest r){
        return filterExecutor.execute(r);
    }

    @GetMapping("/search/minorsHotTop")
    @ResponseEntityResolver
    public CompletableFuture<Response> minorsHotTop(AsyncRequest r){
        return minorsHotTopExecutor.execute(r);
    }

    @GetMapping("/search/minorsPoolCode/listBySid")
    @ResponseEntityResolver
    public CompletableFuture<Response> listMinorsPoolCodeBySid(AsyncRequest r){
        return minorsPoolCodeBySidExecutor.execute(r);
    }

    /**
     * 按关键字查询V2
     * @param r
     * @return
     */
    @RequestMapping("/search/v2/searchByKeyword")
    @ResponseEntityResolver
    public CompletableFuture<Response> searchByKeyword(AsyncRequest r){
        return keyWordSearchV2Executor.execute(r);
    }

    /**
     * 智能提示
     * @param r
     * @return
     */
    @GetMapping("/search/suggest")
    @ResponseEntityResolver
    public CompletableFuture<Response> suggestion(AsyncRequest r){
        return suggestExecutor.execute(r);
    }

    /**
     * 影视筛选
     * @param r
     * @return
     */
    @GetMapping("/refreshVirtualProgram")
    @ResponseEntityResolver
    public CompletableFuture<Response> refreshVirtualProgram(AsyncRequest r){
        return refreshVirtualDataExecutor.execute(r);
    }

    /**
     * 相关推荐
     * @param r
     * @return
     */
    @PostMapping("/search/relationRecommend")
    @ResponseEntityResolver
    public CompletableFuture<Response> detailRecommend(AsyncRequest r){
        return recommendExecutor.execute(r);
    }

    @GetMapping("/search/seriesCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> seriesCardDetail(AsyncRequest r){
        return seriesCardExecutor.execute(r);
    }

    @GetMapping("/search/tagCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> tagCardDetail(AsyncRequest r){
        return tagCardExecutor.execute(r);
    }

    /**
     * 推荐卡二级页
     * @param r
     * @return
     */
    @GetMapping("/search/recommendCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> recommendCardDetail(AsyncRequest r){
        return recommendCardExecutor.execute(r);
    }

    /**
     * 系列卡详情-长短统一
     * @param r
     * @return
     */
    @GetMapping("/search/v2/seriesCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> seriesCardDetailV2(AsyncRequest r){
        return seriesCardV2Executor.execute(r);
    }

    /**
     * 标签卡详情-长短统一
     * @param r
     * @return
     */
    @GetMapping("/search/v2/tagCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> tagCardDetailV2(AsyncRequest r){
        return tagCardV2Executor.execute(r);
    }

    /**
     * 推荐卡详情-长短统一
     * @param r
     * @return
     */
    @GetMapping("/search/v2/recommendCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> recommendCardDetailV2(AsyncRequest r){
        return recommendCardV2Executor.execute(r);
    }

    /**
     * 影人卡详情-长短统一
     * @param r
     * @return
     */
    @GetMapping("/search/v2/actorCardDetail")
    @ResponseEntityResolver
    public CompletableFuture<Response> actorCardDetailV2(AsyncRequest r){
        return actorCardV2Executor.execute(r);
    }

    @GetMapping("/search/createVideoOperationLogIndex")
    @ResponseBody
    public Result createVideoOperationLogIndex(){
        videoOperationService.createIndex();
        return Result.success();
    }

    /**
     * 提交用户搜索反馈
     * @param r
     * @return
     */
    @PostMapping("/search/feedback/submit")
    @ResponseEntityResolver
    public CompletableFuture<Response> searchFeedbackSubmit(AsyncRequest r){
        return searchFeedbackSubmitExecutor.execute(r);
    }

    /**
     * 查询用户搜索反馈问题项
     * @param r
     * @return
     */
    @GetMapping("/search/feedback/option")
    @ResponseEntityResolver
    public CompletableFuture<Response> searchFeedbackOption(AsyncRequest r){
        return searchFeedbackOptionExecutor.execute(r);
    }

}
