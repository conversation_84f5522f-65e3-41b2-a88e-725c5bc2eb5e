package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.standard.SyncSohuMediaInfoByAlbumIdExecutor;
import com.heytap.longvideo.search.executors.standard.SyncSohuMediaInfoExecutor;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/4/17
 */
@Controller
public class SohuOrgMediaInfoSyncController {

    @Autowired
    private SyncSohuMediaInfoExecutor syncSohuMediaInfoExecutor;

    @Autowired
    private SyncSohuMediaInfoByAlbumIdExecutor syncSohuMediaInfoByAlbumIdExecutor;

    @RequestMapping("/syncSohuMediaInfo")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> syncSohuMediaInfo(AsyncRequest r) {
        return syncSohuMediaInfoExecutor.execute(r);
    }

    @RequestMapping("/syncSohuMediaInfoByAlbumId")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> syncSohuMediaInfoByAlbumId(AsyncRequest r) {
        return syncSohuMediaInfoByAlbumIdExecutor.execute(r);
    }
}
