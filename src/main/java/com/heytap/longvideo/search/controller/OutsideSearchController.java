package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.app.OutSideSearchExecutor;
import com.oppo.browser.gateway.pb.ResponseEntity.Response;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.GetMapping;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;


@Controller
public class OutsideSearchController {

    private final OutSideSearchExecutor outSideSearchExecutor;

    public OutsideSearchController(OutSideSearchExecutor outSideSearchExecutor) {
        this.outSideSearchExecutor = outSideSearchExecutor;
    }

    /**
     * 按关键字查询V2
     */
    @GetMapping("/search/api/searchByKeyword")
    @ResponseEntityResolver
    public CompletableFuture<Response> searchByKeyword(AsyncRequest r) {
        return outSideSearchExecutor.execute(r);
    }
}