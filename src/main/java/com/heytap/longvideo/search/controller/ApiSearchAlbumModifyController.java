package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.client.media.entity.AlbumSearchResponse;
import com.heytap.longvideo.client.media.query.AlbumSearchParameterModifier;
import com.heytap.longvideo.search.service.standard.AdjustApiSearchAlbumService;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

/*
 * 更新 es index: api_search_album 的操作
 * 2024/9月23日
 *
 */
@Controller
@RequestMapping("/apiSearchAlbum")
public class ApiSearchAlbumModifyController {

    @Autowired
    private AdjustApiSearchAlbumService adjustApiSearchAlbumService;

    @RequestMapping("/adjustSingleApiSearchAlbum")
    public Boolean adjustSingleApiSearchAlbum(AlbumSearchParameterModifier param) {
        return this.adjustApiSearchAlbumService.adjustSingleApiSearchAlbum(param);
    }

    @RequestMapping("/adjustApiSearchAlbum")
    @ResponseBody
    public List<AlbumSearchResponse> adjustApiSearchAlbum(AlbumSearchParameterModifier param) {
        return this.adjustApiSearchAlbumService.adjustApiSearchAlbum(param);
    }
}
