package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.client.media.model.dto.TopWechatDoubanMediaListDto;
import com.heytap.longvideo.client.media.query.TopWechatDoubanMediaRequest;
import com.heytap.longvideo.search.model.entity.Result;
import com.heytap.longvideo.search.service.spider.DouBanTopMediaSpiderService;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestBody;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.ResponseBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * 豆瓣小程序top数据
 * <AUTHOR>
 * @date 2025/7/7 9:28
 */
@Slf4j
@Controller
public class DouBanTopMediaSpiderDataController {

    @Autowired
    private DouBanTopMediaSpiderService douBanTopMediaSpiderService;


    @ResponseBody
    @RequestMapping("/search/spider/top/mediaList")
    public Result<TopWechatDoubanMediaListDto> douBanTopMediaList(@RequestBody TopWechatDoubanMediaRequest request) {
        return douBanTopMediaSpiderService.getDouBanWechatTop(request);
    }

}
