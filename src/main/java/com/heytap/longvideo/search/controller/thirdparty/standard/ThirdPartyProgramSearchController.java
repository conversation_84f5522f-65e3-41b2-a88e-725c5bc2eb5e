package com.heytap.longvideo.search.controller.thirdparty.standard;

import com.heytap.longvideo.search.model.param.PageResponse;
import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.thirdparty.standard.GetAlbumInfoRequest;
import com.heytap.longvideo.search.model.response.thirdparty.GetAlbumInfoResponse;
import com.heytap.longvideo.search.service.standard.thirdparty.GetAlbumInfoService;
import esa.restlight.core.annotation.QueryBean;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestBody;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@RestController
public class ThirdPartyProgramSearchController {

    @Autowired
    private GetAlbumInfoService getAlbumInfoService;

    /**
     * 根据sid查询非在库节目--to三方
     * @param request
     * @return
     */
    @RequestMapping("/search/thirdParty/getAlbumInfoBySids")
    public StandardResult<PageResponse<GetAlbumInfoResponse>> getAlbumInfoBy(@RequestBody GetAlbumInfoRequest request){
        return getAlbumInfoService.getAlbumInfo(request);
    }
}
