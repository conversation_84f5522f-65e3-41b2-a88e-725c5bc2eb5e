package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.model.param.StandardResult;
import com.heytap.longvideo.search.model.param.standard.SearchExportVo;
import com.heytap.longvideo.search.model.param.standard.SearchStandardVideoParams;
import com.heytap.longvideo.search.model.param.standard.UgcSearchExportStatusVo;
import com.heytap.longvideo.search.model.param.standard.UgcSearchVideoParams;
import com.heytap.longvideo.search.service.standard.StandardVideoService;
import com.heytap.longvideo.search.service.standard.UgcStandardVideoService;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestBody;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestParam;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.ResponseBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * 媒资后台导出接口
 *
 * <AUTHOR> ye mengsheng
 * @date 2024/9/9 下午12:47
 */
@Slf4j
@Controller
public class ExportMediaController {


    @Autowired
    private UgcStandardVideoService ugcStandardVideoService;

    @Autowired
    private StandardVideoService standardVideoService;


    @RequestMapping("/gateway/meizi/ugc/searchAndExport")
    @ResponseBody
    public StandardResult<SearchExportVo> searchAndExport(@RequestParam(value = "vid", required = false) String vid,
                                                          @RequestParam(value = "title", required = false) String title,
                                                          @RequestParam(value = "programType", required = false) String programType,
                                                          @RequestParam(value = "sourceAuthorId", required = false) String sourceAuthorId,
                                                          @RequestParam(value = "authorName", required = false) String authorName,
                                                          @RequestParam(value = "source") String source,
                                                          @RequestParam(value = "videoManageName", required = false) String videoManageName,
                                                          @RequestParam(value = "tag", required = false) String tag,
                                                          @RequestParam(value = "durationMin", required = false) Integer durationMin,
                                                          @RequestParam(value = "durationMax", required = false) Integer durationMax,
                                                          @RequestParam(value = "subTag", required = false) String subTag
    ) {
        try {
            UgcSearchVideoParams request = new UgcSearchVideoParams();
            request.setVid(vid);
            request.setTitle(title);
            request.setProgramType(programType);
            request.setSourceAuthorId(sourceAuthorId);
            request.setAuthorName(authorName);
            request.setSource(source);
            request.setVideoManageName(videoManageName);
            if (StringUtils.isNotEmpty(tag)) {
                request.setTag(tag.toLowerCase());
            }
            request.setDurationMin(durationMin);
            request.setDurationMax(durationMax);
            request.setSubTag(subTag);
            SearchExportVo searchExportVo = ugcStandardVideoService.searchAndExportVideo(request);
            return StandardResult.success(searchExportVo);
        } catch (Exception e) {
            return StandardResult.fail(400, e.getMessage());
        }
    }

    @RequestMapping("/gateway/meizi/csvFile/exportStatus")
    @ResponseBody
    public StandardResult<UgcSearchExportStatusVo> exportStatus(@RequestParam("searchExportId") String searchExportId) {
        return ugcStandardVideoService.searchExportStatus(searchExportId);
    }


    @RequestMapping("/gateway/meizi/media-rest/mediaVideo/searchAndExport")
    @ResponseBody
    public StandardResult<SearchExportVo> mediaSearchAndExport(@RequestBody SearchStandardVideoParams request) {
        try {
            SearchExportVo searchExportVo = standardVideoService.searchAndExport(request);
            return StandardResult.success(searchExportVo);
        } catch (Exception e) {
            return StandardResult.fail(400, e.getMessage());
        }
    }
}
