package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.standard.DelUgcStandardVideoAndAuthorExecutor;
import com.heytap.longvideo.search.executors.standard.UgcGetVideoPageListExecutor;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;

/**
 * ugc媒资管理后台视频相关接口
 *
 * <AUTHOR>
 * @date 2022/10/25 14:36
 */
@Controller
@RequestMapping("/ugc")
public class UgcMediaController {

    @Autowired
    private UgcGetVideoPageListExecutor ugcGetVideoPageListExecutor;

    @Autowired
    DelUgcStandardVideoAndAuthorExecutor delUgcStandardVideoAndAuthorExecutor;


    /**
     * ugc视频分页查询接口
     *
     * @param r
     * @return
     */
    @RequestMapping("/searchByPage")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> searchByPage(AsyncRequest r) {
        return ugcGetVideoPageListExecutor.execute(r);
    }


    @RequestMapping("/delIndex")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> delIndex(AsyncRequest r) {
        return delUgcStandardVideoAndAuthorExecutor.execute(r);
    }
}