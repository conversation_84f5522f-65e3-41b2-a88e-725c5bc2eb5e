package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.standard.InitStandardAlbumExecutor;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;


/*
 * Description standard表数据初始化
 * Date 20:38 2022/3/24
 * Author songjiajia 80350688
 */
@Controller
@Slf4j
public class StandardInitController {

    private final InitStandardAlbumExecutor execute;

    public StandardInitController(InitStandardAlbumExecutor execute) {
        this.execute = execute;
    }

    @RequestMapping("/search/initStandardData")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> init(AsyncRequest r){
        return execute.execute(r);
    }
}