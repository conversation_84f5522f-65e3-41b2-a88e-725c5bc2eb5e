package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.standard.*;
import com.oppo.browser.gateway.pb.ResponseEntity.Response;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;

/*
 * Description 媒资、编排后台搜索
 * Date 14:21 2021/12/23
 * Author songjiajia 80350688
*/
@Controller
public class StandardSearchController {

    @Autowired
    private CmsStandardAlbumExecutor cmsStandardAlbumExecutor;

    @Autowired
    private MediaStandardAlbumExecutor mediaStandardAlbumExecutor;

    @Autowired
    private CmsStandardVideoExecutor cmsStandardVideoExecutor;

    @Autowired
    private MediaStandardVideoExecutor mediaStandardVideoExecutor;

    @Autowired
    private CmsStandardEpisodeExecutor cmsStandardEpisodeExecutor;

    @Autowired
    private MediaStandardEpisodeExecutor mediaStandardEpisodeExecutor;

    @Autowired
    private CmsStandardTrailerExecutor cmsStandardTrailerExecutor;

    @Autowired
    private MediaStandardTrailerExecutor mediaStandardTrailerExecutor;

    @Autowired
    private CmsEpisodeDetailExecutor cmsEpisodeDetailExecutor;

    @Autowired
    private CmsTrailerDetailExecutor cmsTrailerDetailExecutor;

    @Autowired
    private ProgramTypeListExecutor programTypeListExecutor;

    @Autowired
    private GetSidAndEidExecutor getSidAndEidExecutor;

    @Autowired
    private VideoTypeListExecutor videoTypeListExecutor;

    @Autowired
    private EpisodeTrailerExecutor episodeTrailerExecutor;

    @Autowired
    private TrailerTypeListExecutor trailerTypeListExecutor;


    @Autowired
    private StandardAlbumExecutor standardAlbumExecutor;

    /**
     * 媒资后台查询剧头
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/searchStandardAlbum","/gateway/meizi/media-rest/mediaAlbum/pageList"})
    @ResponseEntityResolver
    public CompletableFuture<Response> mediaSearchAlbum(AsyncRequest r){
        return mediaStandardAlbumExecutor.execute(r);
    }

    /**
     * 编排后台查询剧头
     * @param r
     * @return
     */
    @RequestMapping(value = "/gateway/oppomobile/programalbum/pagelist")
    @ResponseEntityResolver
    public CompletableFuture<Response> cmsSearchAlbum(AsyncRequest r){
        return cmsStandardAlbumExecutor.execute(r);
    }

    /**
     * 媒资查询视频
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/searchStandardVideo","/gateway/meizi/media-rest/mediaVideo/searchByPage"})
    @ResponseEntityResolver
    public CompletableFuture<Response> mediaSearchVideo(AsyncRequest r){
        return mediaStandardVideoExecutor.execute(r);
    }

    /**
     * 编排查询剧头
     * @param r
     * @return
     */
    @RequestMapping(value = "/gateway/oppomobile/programvideo/pagelist")
    @ResponseEntityResolver
    public CompletableFuture<Response> cmssearchVideo(AsyncRequest r){
        return cmsStandardVideoExecutor.execute(r);
    }


    /**
     * 媒资查询剧集
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/searchStandardEpisode","/gateway/meizi/media-rest/mediaAlbum/getEpisodeByPage"})
    @ResponseEntityResolver
    public CompletableFuture<Response> mediaSearchEpisode(AsyncRequest r){
        return mediaStandardEpisodeExecutor.execute(r);
    }

    /**
     * 媒资查询片花
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/searchStandardTrailer","/gateway/meizi/media-rest/mediaAlbum/getAlbumPreview"})
    @ResponseEntityResolver
    public CompletableFuture<Response> mediaSearchTrailer(AsyncRequest r){
        return mediaStandardTrailerExecutor.execute(r);
    }

    /**
     * 编排查询剧集
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/searchCmsEpisode","/gateway/oppomobile/programepisode/pagelist"})
    @ResponseEntityResolver
    public CompletableFuture<Response> cmsSearchEpisode(AsyncRequest r){
        return cmsStandardEpisodeExecutor.execute(r);
    }

    /**
     * 编排查询片花
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/searchCmsTrailer","/gateway/oppomobile/programtrailer/pagelist"})
    @ResponseEntityResolver
    public CompletableFuture<Response> cmsSearchTrailer(AsyncRequest r){
        return cmsStandardTrailerExecutor.execute(r);
    }

    /**
     * 编排查询剧集详情
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/cmsEpisodeDetail","/gateway/oppomobile/programepisode/detail"})
    @ResponseEntityResolver
    public CompletableFuture<Response> cmsEpisodeDetail(AsyncRequest r){
        return cmsEpisodeDetailExecutor.execute(r);
    }

    /**
     * 编排查询片花详情
     * @param r
     * @return
     */
    @RequestMapping(value = {"/search/cmsTrailerDetail","/gateway/oppomobile/programtrailer/detail"})
    @ResponseEntityResolver
    public CompletableFuture<Response> cmsTrailerDetail(AsyncRequest r){
        return cmsTrailerDetailExecutor.execute(r);
    }

    /**
     * 查询节目类型
     * @param r
     * @return
     */
    @RequestMapping(value = {"/gateway/meizi/search/programTypeList","/gateway/meizi/misProgram/getProgramTypeDict"})
    @ResponseEntityResolver
    public CompletableFuture<Response> programTypeList(AsyncRequest r){
        return programTypeListExecutor.execute(r);
    }

    /**
     * 查询视频类型
     * @param r
     * @return
     */
    @RequestMapping(value = {"/gateway/meizi/search/videoTypeList"})
    @ResponseEntityResolver
    public CompletableFuture<Response> videoTypeList(AsyncRequest r){
        return videoTypeListExecutor.execute(r);
    }


    /**
     * 根据vid查询sid+eid
     * @param r
     * @return
     */
    @RequestMapping(value = {"/gateway/meizi/search/getSidAndEidByVid","/gateway/meizi/media-rest/mediaVideo/getSidAndEid"})
    @ResponseEntityResolver
    public CompletableFuture<Response> getSidAndEidByVid(AsyncRequest r){
        return getSidAndEidExecutor.execute(r);
    }

    /**
     * 查询视频详情接口
     * @param r
     * @return
     */
    @RequestMapping("/search/episodeTrailer")
    @ResponseEntityResolver
    public CompletableFuture<Response> episodeTrailer(AsyncRequest r){
        return episodeTrailerExecutor.execute(r);
    }

    /**
     * 查询周边视频类型
     * @param r
     * @return
     */
    @RequestMapping(value = {"/gateway/meizi/search/trailerTypeList"})
    @ResponseEntityResolver
    public CompletableFuture<Response> trailerTypeList(AsyncRequest r){
        return trailerTypeListExecutor.execute(r);
    }


    /**
     * 查询节目详情
     * @param r
     * @return
     */
    @RequestMapping(value = {"/gateway/meizi/search/detailBySid"})
    @ResponseEntityResolver
    public CompletableFuture<Response> detailBySid(AsyncRequest r){
        return standardAlbumExecutor.execute(r);
    }
}
