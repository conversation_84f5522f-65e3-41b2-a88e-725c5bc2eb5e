package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.standard.ListStandardPersonExecutor;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RequestMapping;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * @Description: 影人媒资 接口入口
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/5/13
 */
@Slf4j
@RestController
@AllArgsConstructor
public class StandardPersonController {

    private ListStandardPersonExecutor listStandardPersonExecutor;

    @RequestMapping("/gateway/meizi/mediaPerson/pageList")
    @ResponseEntityResolver
    public CompletableFuture<ResponseEntity.Response> listMediaPerson(AsyncRequest request) {
        return listStandardPersonExecutor.execute(request);
    }
}

