package com.heytap.longvideo.search.controller;

import com.heytap.longvideo.search.executors.inside.SearchByTagExecutor;
import com.heytap.longvideo.search.executors.inside.SearchUnOfficialAlbumExecutor;
import com.heytap.longvideo.search.model.param.inside.SearchByTagRequest;
import com.heytap.longvideo.search.model.param.inside.SearchByTagResponse;
import com.heytap.longvideo.search.model.param.inside.SearchUnOfficialAlbumRequest;
import com.heytap.longvideo.search.model.param.inside.SearchUnOfficialAlbumResponse;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.gateway.pb.ResponseEntity;
import esa.httpserver.core.AsyncRequest;
import esa.restlight.core.annotation.QueryBean;
import esa.restlight.plugin.browser.ResponseEntityResolver;
import esa.restlight.spring.shaded.org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * 给内部其他应用提供的http接口
 * <AUTHOR> Yanping
 * @date 2025/4/21
 */
@Slf4j
@RestController
public class InsideSearchController {

    @Autowired
    private SearchByTagExecutor searchByTagExecutor;

    @Autowired
    private SearchUnOfficialAlbumExecutor searchUnOfficialAlbumExecutor;

    /**
     * 根据标签查询节目列表
     * @return
     */
    @RequestMapping("/search/inside/searchByTag")
    public CompletableFuture<SearchByTagResponse> searchByKeyword(@QueryBean SearchByTagRequest request) {
        return searchByTagExecutor.myExecute(request);
    }

    /**
     * 根据sid查询非在库节目
     * @param request
     * @return
     */
    @RequestMapping("/search/inside/searchUnOfficialAlbum")
    public CompletableFuture<SearchUnOfficialAlbumResponse> searchUnOfficialAlbum(@QueryBean SearchUnOfficialAlbumRequest request){
        return searchUnOfficialAlbumExecutor.myExecute(request);
    }
}
