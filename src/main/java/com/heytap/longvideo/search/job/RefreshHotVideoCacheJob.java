package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.service.app.HotVideoService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/*
 * Description 定时同步媒资数据到搜索（补充mq没有接收到媒资数据的情况）
 * Date 17:15 2022/4/13
 * Author songjiajia 80350688
 */
@Component
public class RefreshHotVideoCacheJob {

    @Autowired
    private HotVideoService hotVideoService;

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

    @Scheduled(cron = "0 15 6 */1 * ?")
    public void scheduled() {
        try {
            updateLog.info("job RefreshHotVideoCacheJob start");
            for (ContentTypeEnum contentTypeEnum : ContentTypeEnum.values()) {
                hotVideoService.initHotVideoSensitiveWordMap(contentTypeEnum);
            }
            updateLog.info("job RefreshHotVideoCacheJob end");
        } catch (Exception e) {
            updateLog.error("job RefreshHotVideoCacheJob error,e:{}", e);
        }

    }
}


