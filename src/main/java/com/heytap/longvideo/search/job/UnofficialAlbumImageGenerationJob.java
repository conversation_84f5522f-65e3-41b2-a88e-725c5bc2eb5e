package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.model.request.OcsImageUploadAndEsSyncRequest;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.elasticsearch.search.SearchHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/7/8 下午9:05
 */

@Component
@Slf4j
public class UnofficialAlbumImageGenerationJob implements SimpleJob {

    @Autowired
    private UnofficialAlbumService unofficialAlbumService;

    @HeraclesDynamicConfig(key = "unofficial.album.ocs.image.whitelist", fileName = "configure.properties")
    private List<String> supportedSources;

    /**
     * 全网节目--查询es(未生成ocs图片),一次返回的数据量
     */
    @HeraclesDynamicConfig(key = "unofficial.album.query.es.batch.size", fileName = "configure.properties")
    private Integer batchSize = 20;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            log.warn("unofficialAlbumService generateUnofficialAlbumOcsImage begin");
            SearchHits searchHits = unofficialAlbumService.getUnprocessedUnofficialAlbum(supportedSources, "1");
            if (searchHits == null) {
                log.error("unofficialAlbumService.getUnprocessedUnofficialAlbum() result is null");
                return;
            }
            if (searchHits.getTotalHits() != null && searchHits.getTotalHits().value == 0) {
                log.warn("unofficialAlbumService generateUnofficialAlbumOcsImage end, all the programs have been processed");
                return;
            }
            OcsImageUploadAndEsSyncRequest ocsImageUploadAndEsSyncRequest = new OcsImageUploadAndEsSyncRequest();
            ocsImageUploadAndEsSyncRequest.setSourceList(supportedSources);
            ocsImageUploadAndEsSyncRequest.setCount((int) (searchHits.getTotalHits().value / batchSize + 1));
            unofficialAlbumService.ocsImageUploadAndEsSync(ocsImageUploadAndEsSyncRequest);
            log.warn("unofficialAlbumService generateUnofficialAlbumOcsImage end");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}