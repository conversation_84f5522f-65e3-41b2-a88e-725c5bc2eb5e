package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.model.param.InitDataParam;
import com.heytap.longvideo.search.service.standard.InitStandardDataService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/*
 * Description 定时同步媒资数据到搜索（补充mq没有接收到媒资数据的情况）
 * Date 17:15 2022/4/13
 * Author songjiajia 80350688
 */
@Component
public class UpdateStandardDataJob implements SimpleJob {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");
    @HeraclesDynamicConfig(key = "media.datebaseEnd", fileName = "search_config.properties")
    private static int datebaseEnd;
    @HeraclesDynamicConfig(key = "media.tableEnd", fileName = "search_config.properties")
    private static int tableEnd;
    @Autowired
    private InitStandardDataService initStandardDataService;

    //    @Scheduled(cron = "0 5 */1 * * ?")
    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            updateLog.info("job UpdateStandardData start");
            InitDataParam initDataParam = new InitDataParam();
            initDataParam.setType("album");
            initDataParam.setDataBaseEnd(datebaseEnd);
            initDataParam.setTableEnd(tableEnd);
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.now();
            initDataParam.setUpdateTime(localDateTime.minusHours(1).format(fmt));
            initStandardDataService.initData(initDataParam);
            initDataParam.setType("video");
            initStandardDataService.initData(initDataParam);
            initDataParam.setType("episode");
            initStandardDataService.initData(initDataParam);
            initDataParam.setType("trailer");
            initStandardDataService.initData(initDataParam);
            updateLog.info("job UpdateStandardData end");
        } catch (Exception e) {
            updateLog.error("job UpdateStandardData error,e:{}", e);
        }

    }
}


