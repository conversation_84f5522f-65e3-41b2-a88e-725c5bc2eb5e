package com.heytap.longvideo.search.job;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.heytap.longvideo.search.config.CsvExportConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * OCS 定时删除媒资后台导出的文件
 *
 * <AUTHOR> ye mengsheng
 * @date 2024/9/9 下午7:34
 */

@Component
@Slf4j
public class DeleteOcsFileJob {

    @Autowired
    CsvExportConfig csvExportConfig;

    @Scheduled(cron = "0 20 5 */2 * ?")
    public void scheduled() {
        log.warn("The DeleteOcsFile Job is beginning.");
        deleteOcsFile();
        log.warn("The DeleteOcsFile Job is end.");
    }

    public void deleteOcsFile() {
        String AccessKeyId = csvExportConfig.getAccessKeyId();
        String AccessKeySecret = csvExportConfig.getAccessKeySecret();
        String EndPoint = csvExportConfig.getEndPoint();
        String Region = csvExportConfig.getRegion();
        String BucketName = csvExportConfig.getBucketName();
        String OcsPath = csvExportConfig.getOcsPath();

        AWSCredentials awsCredentials = new BasicAWSCredentials(AccessKeyId, AccessKeySecret);
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        ClientConfiguration clientConfiguration = new ClientConfiguration()
                .withProtocol(Protocol.HTTP);

        AmazonS3 s3 = AmazonS3ClientBuilder.standard().withCredentials(awsCredentialsProvider)
                .withPathStyleAccessEnabled(true)
                .withClientConfiguration(clientConfiguration)
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(EndPoint, Region))
                .build();
        // maxKeys is set to 1000 to demonstrate the use of
        // ListObjectsV2Result.getNextContinuationToken()
        //删除指定目录下的文件
        ListObjectsV2Request req = new ListObjectsV2Request().withBucketName(BucketName).withPrefix(OcsPath).withMaxKeys(1000);
        try {
            ListObjectsV2Result result = s3.listObjectsV2(req);
            if (result != null) {
                for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
                    s3.deleteObject(BucketName, objectSummary.getKey());
                    log.warn("delete file in {} : {}", BucketName, objectSummary.getKey());
                }
            }
        } catch (AmazonS3Exception exception) {//AmazonS3Exception
            int statusCode = exception.getStatusCode();
            if (statusCode / 100 == 4) {
                log.error("an error occurred in client,{}", exception.getErrorResponseXml());
            } else if (statusCode / 100 == 5) {
                log.error("an error occurred in ocs,{}", exception.getErrorResponseXml());
            }
        } catch (SdkClientException exception) {
            log.error("Caught an AmazonClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with S3, "
                    + "such as not being able to access the network.Error Message: {}", exception.getMessage());
        }
    }
}
