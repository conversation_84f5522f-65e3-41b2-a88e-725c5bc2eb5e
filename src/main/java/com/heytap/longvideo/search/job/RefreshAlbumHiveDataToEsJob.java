package com.heytap.longvideo.search.job;

import com.heytap.longvideo.model.entity.LvAlbumConsumeHiveData;
import com.heytap.longvideo.search.mq.AlbumHiveDataService;
import com.heytap.longvideo.search.rpc.consumer.LvAlbumConsumeHiveDataRpcApiProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/9/1
 */
@Slf4j
@Service
public class RefreshAlbumHiveDataToEsJob implements SimpleJob {

    @Autowired
    private AlbumHiveDataService hiveDataService;

    @Autowired
    private LvAlbumConsumeHiveDataRpcApiProxy lvAlbumConsumeHiveDataRpcApiProxy;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("==================== RefreshAlbumHiveDataToEs start ====================");
        List<LvAlbumConsumeHiveData> consumeHiveDataList = null;
        try {
            consumeHiveDataList = lvAlbumConsumeHiveDataRpcApiProxy.getByDayno(LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"))).get().getData();
        } catch (Exception e) {
            log.error("call arrange-service lvAlbumConsumeHiveData api error!", e);
        }
        if (CollectionUtils.isNotEmpty(consumeHiveDataList)) {
            hiveDataService.processData(consumeHiveDataList);
        }
        log.info("==================== RefreshAlbumHiveDataToEs end ====================");
    }
}
