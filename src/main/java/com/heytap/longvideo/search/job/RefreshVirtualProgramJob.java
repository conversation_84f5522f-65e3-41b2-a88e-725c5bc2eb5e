package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.service.common.VirtualProgramService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RefreshVirtualProgramJob implements SimpleJob {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");
    @Autowired
    private VirtualProgramService virtualProgramService;

    //    @Scheduled(cron = "0 20 6 */1 * ?")
    @Override
    public void execute(ShardingContext shardingContext) {
        createCache();
    }

    public void createCache() {
        try {
            log.info("refreshVirtualProgram start");
            virtualProgramService.createCacheAndReturnAllAlbum();
            log.info("refreshVirtualProgram end");
        } catch (Exception e) {
            updateLog.error("job RefreshVirtualProgramJob error,e:{}", e);
        }
    }
}


