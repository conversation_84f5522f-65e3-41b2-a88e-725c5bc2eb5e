package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.service.app.InitService;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class InitApiSearchEsJob implements SimpleJob {

    @Autowired
    private InitService initService;


    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");

//    @Scheduled(cron = "0 9 2 */2 * ?")
    @Override
    public void execute(ShardingContext shardingContext) {
        updateLog.info("job initApiSearchEsJob start");
        try {
            initService.initData();
            updateLog.info("job initApiSearchEsJob end");
        } catch (Exception e) {
            updateLog.error("job initApiSearchEsJob error,retry,e:{}", e);
            try {
                initService.initData();
            } catch (Exception e1) {
                updateLog.error("job initApiSearchEsJobError,e:{}", e1);
            }
        }
    }
}


