package com.heytap.longvideo.search.job;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.model.param.app.MgData;
import com.heytap.longvideo.search.model.param.app.MgHitDocs;
import com.heytap.longvideo.search.model.param.app.MgHotResponse;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GetOutsideHotVideoToEsJob implements SimpleJob {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");
    @HeraclesDynamicConfig(key = "mg.hot.url", fileName = "hot_video_config.properties")
    private static String mgHotUrl;
    private static Map<String, Integer> contentTypeMap = new HashMap<>();

    static {
        contentTypeMap.put(ContentTypeEnum.SHOW.getCode(), 1);
        contentTypeMap.put(ContentTypeEnum.TV.getCode(), 2);
        contentTypeMap.put(ContentTypeEnum.MOVIE.getCode(), 3);
        contentTypeMap.put(ContentTypeEnum.KIDS.getCode(), 10);
        contentTypeMap.put(ContentTypeEnum.COMIC.getCode(), 50);
    }

    private final OkHttpClient client = new OkHttpClient();
    @Autowired
    private HotVideoService hotVideoService;

//    @Scheduled(cron = "0 10 12 */1 * ?")
    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            updateLog.info("job GetOutsideHotVideoToEsJob start");
            for (String s : contentTypeMap.keySet()) {
                Request request = new Request.Builder()
                        .url(mgHotUrl + "&channelId=" + contentTypeMap.get(s))
                        .build();
                Response response = client.newCall(request).execute();
                if (!response.isSuccessful()) {
                    updateLog.error("response not success:{}", JSON.toJSONString(response));
                    return;
                }
                String responseBody = response.body().string();
                MgHotResponse mgHotResponse = JSON.parseObject(responseBody, MgHotResponse.class);
                if (mgHotResponse.getCode() != 200) {
                    updateLog.error("response code not 200,responseBody:{}", responseBody);
                    return;
                }
                MgData mgData = mgHotResponse.getData();
                if (mgData == null || CollectionUtils.isEmpty(mgData.getHitDocs())) {
                    return;
                }
                List<String> titleList = mgData.getHitDocs().stream().map(MgHitDocs::getTitle).collect(Collectors.toList());
                if (ContentTypeEnum.SHOW.getCode().equals(s) && titleList.size() > 20) {
                    titleList = titleList.subList(0, 20);
                } else if (titleList.size() > 30) {
                    titleList = titleList.subList(0, 30);
                }
                updateLog.info("GetOutsideHotVideoToEsJob response:{},{}", s, JSON.toJSONString(titleList));
                hotVideoService.insertOutHotVideoToEs(titleList, s);
            }
            updateLog.info("job GetOutsideHotVideoToEsJob end");
        } catch (Exception e) {
            updateLog.error("job GetOutsideHotVideoToEsJob error,e:{}", e);
        }
    }
}


