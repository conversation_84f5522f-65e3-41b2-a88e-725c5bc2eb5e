package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.service.app.YunheRankService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class YunheRankJob implements SimpleJob {
    @Autowired
    private YunheRankService yunheRankService;

    @Override
    public void execute(ShardingContext shardingContext) {
        yunheRankService.collect();
    }
}
