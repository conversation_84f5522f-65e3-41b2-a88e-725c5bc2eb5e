package com.heytap.longvideo.search.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.heytap.longvideo.search.service.sync.AlbumSyncToSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 将段内数据和全网搜数据同步给浏览器的定时任务(只执行一次,本次操作是幂等的可以重复调用)
 */
@Component
@Slf4j
public class ExternalSearchSyncJob implements SimpleJob {

    private final static String STANDARD_TYPE = "standard";
    private final static String UNOFFICIAL_TYPE = "unofficial";

    @Autowired
    private AlbumSyncToSearchService albumSyncToSearchService;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            log.warn("[ExternalSearchSyncJob] job start...");
            if(StringUtils.isBlank(shardingContext.getJobParameter())){
                log.warn("[ExternalSearchSyncJob.getJobParameter] is blank");
                return ;
            }
            JSONObject jsonObject = JSON.parseObject(shardingContext.getJobParameter());
            if(!jsonObject.containsKey("type")){
                log.warn("[ExternalSearchSyncJob.getJobParameter] not contain type");
                return ;
            }
            String type = jsonObject.getString("type");
            if(STANDARD_TYPE.equals(type)){
                log.warn("[ExternalSearchSyncJob] stockSyncWithStandardsAlbum action");
                albumSyncToSearchService.stockSyncWithStandardsAlbum();
                return ;
            }
            if(UNOFFICIAL_TYPE.equals(type)){
                log.warn("[ExternalSearchSyncJob] stockSyncWithUnofficialAlbum action");
                albumSyncToSearchService.stockSyncWithUnofficialAlbum();
                return ;
            }
            log.warn("[ExternalSearchSyncJob] type not match");
        } catch (Exception e) {
            log.error("[ExternalSearchSyncJob.execute] error", e);
        }finally {
            log.warn("[ExternalSearchSyncJob] end ...");
        }
    }
}
