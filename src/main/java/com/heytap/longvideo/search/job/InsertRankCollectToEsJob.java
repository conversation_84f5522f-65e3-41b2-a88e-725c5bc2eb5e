package com.heytap.longvideo.search.job;

import com.heytap.longvideo.search.service.app.HotVideoService;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/*
 * Description 定时同步媒资数据到搜索（补充mq没有接收到媒资数据的情况）
 * Date 17:15 2022/4/13
 * Author songjiajia 80350688
 */
@Component
public class InsertRankCollectToEsJob implements SimpleJob {

    private static final Logger updateLog = LoggerFactory.getLogger("updatelog");
    @Autowired
    private HotVideoService hotVideoService;

    //    @Scheduled(cron = "0 10 */6 */1 * ?")
    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            updateLog.info("job InsertRankCollectToEsJob start");
            hotVideoService.insertRankCollectVideoToEs();
            updateLog.info("job InsertRankCollectToEsJob end");
        } catch (Exception e) {
            updateLog.error("job InsertRankCollectToEsJob error,e:{}", e);
        }
    }
}


