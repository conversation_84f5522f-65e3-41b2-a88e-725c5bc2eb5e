package com.heytap.longvideo.search.job;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.service.app.SearchFeedbackService;
import com.heytap.longvideo.search.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 周期性统计用户搜索反馈数据，生成excel上传ocs
 */
@Component
@Slf4j
public class CollectSearchFeedbackJob implements SimpleJob {
    @Autowired
    private SearchFeedbackService searchFeedbackService;

    @Override
    public void execute(ShardingContext shardingContext) {
        // 任务的自定义参数  {"cycleDay":7,"startTime":"2024-11-11", "endTime":"2024-11-12" }
        Map<String, Object> params = JacksonUtil.parseObject(shardingContext.getJobParameter(), Map.class);
        searchFeedbackService.collect(params);
    }
}
