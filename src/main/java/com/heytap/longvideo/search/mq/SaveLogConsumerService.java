package com.heytap.longvideo.search.mq;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.model.entity.es.VideoOperationLogEs;
import com.heytap.longvideo.search.service.common.VideoOperationLogService;
import com.heytap.video.client.search.model.VideoOperationLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class SaveLogConsumerService implements MessageListenerConcurrently {

    @Autowired
    private VideoOperationLogService videoOperationService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(msgs)) {
            log.info("msg null,return");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        MessageExt messageExt = msgs.get(0);
        String data = null;
        try {
            data = new String(messageExt.getBody(), RemotingHelper.DEFAULT_CHARSET);
            log.info("insert lvLog:{}", data);
            VideoOperationLog lvLog = JSON.parseObject(data, VideoOperationLog.class);
            VideoOperationLogEs es = new VideoOperationLogEs();
            BeanUtils.copyProperties(lvLog, es);
            videoOperationService.insert(es);
        } catch (Throwable e) {
            log.error("msg consume fail,data:{}", data, e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}

