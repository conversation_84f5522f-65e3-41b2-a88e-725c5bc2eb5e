package com.heytap.longvideo.search.mq;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.constants.HandleTypeEnum;
import com.heytap.longvideo.search.mapper.media.StandardAlbumMapper;
import com.heytap.longvideo.search.service.app.InitService;
import com.heytap.longvideo.search.model.entity.es.*;
import com.heytap.longvideo.search.service.common.ElasticSearchService;
import com.heytap.longvideo.search.service.standard.*;
import com.heytap.longvideo.search.service.sync.AlbumSyncToSearchService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.cpc.video.framework.lib.jins.RowData;
import esa.rpc.config.annotation.Reference;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.*;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class MediaJinsConsumerService implements MessageListenerConcurrently {

    private static final Logger LOG = LoggerFactory.getLogger("updatelog");

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private StandardAlbumRpcApi standardAlbumRpcApi;

    @Autowired
    private StandardAlbumService albumService;

    @Autowired
    private StandardVideoService standardVideoService;

    @Autowired
    private StandardEpisodeService standardEpisodeService;

    @Autowired
    private StandardTrailerService standardTrailerService;

    @Autowired
    private InitService initService;

    @Autowired
    private StandardAlbumMapper standardAlbumMapper;


    @Autowired
    private ElasticSearchService elasticSearchService;

    @Autowired
    private UgcStandardVideoService ugcStandardVideoService;

    @Autowired
    private AlbumSyncToSearchService albumSyncToSearchService;

    @HeraclesDynamicConfig(key = "media.datebaseEnd", fileName = "search_config.properties")
    private static int datebaseEnd;

    @HeraclesDynamicConfig(key = "media.tableEnd", fileName = "search_config.properties")
    private static int tableEnd;



    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(msgs)) {
            LOG.info("msg null,return");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        MessageExt messageExt = msgs.get(0);
        String data = null;
        try {
            data = new String(messageExt.getBody(), RemotingHelper.DEFAULT_CHARSET);
            RowData rowData = JSON.parseObject(data, RowData.class);
            if (rowData.getTableName().startsWith("standard_album")) {
                consumeAlbumEvent(rowData);
            } else if (rowData.getTableName().startsWith("standard_video")) {
                consumeVideoEvent(rowData);
            } else if (rowData.getTableName().startsWith("standard_episode")) {
                consumeEpisodeEvent(rowData);
            } else if (rowData.getTableName().startsWith("standard_trailer")) {
                consumeTrailerEvent(rowData);
            } else if (rowData.getTableName().startsWith("ugc_standard_video")){
                consumeUgcVideoEvent(rowData);
            }
        } catch (Throwable e) {
            LOG.error("msg consume fail,data:{},e:{}", data, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


    private void consumeAlbumEvent(RowData rowData) throws Throwable {
        String eventType = rowData.getEventType();
        StandardAlbumEs standardAlbumEs = JSON.parseObject(JSON.toJSONString(rowData.getAfter()),
                StandardAlbumEs.class);
        setStandardAlbumEs(standardAlbumEs, null);
        // 更新api_search_album
        initService.update(standardAlbumEs, rowData);
        switch (eventType) {
            case "INSERT":
                LOG.info("insert album msg start,standardAlbumEs:{}", JSON.toJSONString(standardAlbumEs));
                albumService.insertOrUpdate(standardAlbumEs);
                albumSyncToSearchService.incrementSyncWithStandardsAlbum(standardAlbumEs, HandleTypeEnum.INSERT.getValue());
                break;
            case "UPDATE":
                LOG.info("update album msg start,standardAlbumEs:{}", JSON.toJSONString(standardAlbumEs));
                albumService.insertOrUpdate(standardAlbumEs);
                albumSyncToSearchService.incrementSyncWithStandardsAlbum(standardAlbumEs,HandleTypeEnum.UPDATE.getValue());
                break;
            case "DELETE":
                standardAlbumEs = JSON.parseObject(JSON.toJSONString(rowData.getBefore()), StandardAlbumEs.class);
                LOG.info("delete album msg start,sid:{}", standardAlbumEs.getSid());
                albumService.delete(standardAlbumEs);
                albumSyncToSearchService.incrementSyncWithStandardsAlbum(standardAlbumEs,HandleTypeEnum.DELETE.getValue());
                break;
            default:
                break;
        }
    }

    public void setStandardAlbumEs(StandardAlbumEs standardAlbumEs, StandardAlbum standardAlbum) {
        standardAlbumEs.setSidL(Long.valueOf(standardAlbumEs.getSid()));
        standardAlbumEs.setHasPreview((standardAlbumEs.getPreviewInfo() != null && standardAlbumEs.getPreviewInfo().length() > 10) ? 1 : 0);
        StandardAlbumEs oldStandardAlbumEs = null;
        StandardAlbum dbAlbum = standardAlbum;
        try {
            oldStandardAlbumEs = albumService.searchBySid(standardAlbumEs.getSid());
            if (dbAlbum == null) {
                int database = Math.abs(standardAlbumEs.getSid().hashCode() / (tableEnd + 1)) % (datebaseEnd + 1);
                int table = Math.abs(standardAlbumEs.getSid().hashCode() % (tableEnd + 1));
                dbAlbum = standardAlbumMapper.getStandardAlbumBySid(database, table, standardAlbumEs.getSid());
            }
            standardAlbumEs.setStatus(dbAlbum.getStatus());
            standardAlbumEs.setSourceStatus(dbAlbum.getSourceStatus());
            standardAlbumEs.setSourceScore(dbAlbum.getSourceScore());
            standardAlbumEs.setCpScore(dbAlbum.getCpScore());
            standardAlbumEs.setDoubanScore(dbAlbum.getDoubanScore());
            standardAlbumEs.setOppoScore(dbAlbum.getOppoScore());
        } catch (Exception e) {
            LOG.error("query standardAlbum es error! sid = [{}]", standardAlbumEs.getSid(), e);
        }

        if (oldStandardAlbumEs != null) {
            standardAlbumEs.setOppoHot(oldStandardAlbumEs.getOppoHot());
            standardAlbumEs.setDayNo(oldStandardAlbumEs.getDayNo());
            standardAlbumEs.setLast7DaysPlayPv(oldStandardAlbumEs.getLast7DaysPlayPv());
            standardAlbumEs.setLast15DaysPlayPv(oldStandardAlbumEs.getLast15DaysPlayPv());
            standardAlbumEs.setLast30DaysPlayPv(oldStandardAlbumEs.getLast30DaysPlayPv());
            standardAlbumEs.setLast7DaysClickPv(oldStandardAlbumEs.getLast7DaysClickPv());
            standardAlbumEs.setLast15DaysClickPv(oldStandardAlbumEs.getLast15DaysClickPv());
            standardAlbumEs.setLast30DaysClickPv(oldStandardAlbumEs.getLast30DaysClickPv());
        }
    }

    private void consumeVideoEvent(RowData rowData) throws Throwable {
        String eventType = rowData.getEventType();
        StandardVideoEs standardVideoEs = JSON.parseObject(JSON.toJSONString(rowData.getAfter()),
                StandardVideoEs.class);
        switch (eventType) {
            case "INSERT":
                LOG.info("insert video msg start,vid:{}", standardVideoEs.getVid());
                standardVideoService.insertOrUpdate(standardVideoEs);
                break;
            case "UPDATE":
                LOG.info("update video msg start,vid:{}", standardVideoEs.getVid());
                standardVideoService.insertOrUpdate(standardVideoEs);
                break;
            case "DELETE":
                standardVideoEs = JSON.parseObject(JSON.toJSONString(rowData.getBefore()), StandardVideoEs.class);
                LOG.info("delete video msg start,vid:{}", standardVideoEs.getVid());
                standardVideoService.delete(standardVideoEs);
                break;
            default:
                break;
        }
    }

    private void consumeEpisodeEvent(RowData rowData) throws Throwable {
        String eventType = rowData.getEventType();
        StandardEpisodeEs standardEpisodeEs = JSON.parseObject(JSON.toJSONString(rowData.getAfter()),
                StandardEpisodeEs.class);
        switch (eventType) {
            case "INSERT":
                LOG.info("insert episode msg start,eid:{}", standardEpisodeEs.getEid());
                standardEpisodeService.insertOrUpdate(standardEpisodeEs);
                break;
            case "UPDATE":
                LOG.info("update episode msg start,eid:{}", standardEpisodeEs.getEid());
                standardEpisodeService.insertOrUpdate(standardEpisodeEs);
                break;
            case "DELETE":
                standardEpisodeEs = JSON.parseObject(JSON.toJSONString(rowData.getBefore()),
                        StandardEpisodeEs.class);
                LOG.info("delete episode msg start,eid:{}", standardEpisodeEs.getEid());
                standardEpisodeService.delete(standardEpisodeEs);
                break;
            default:
                break;
        }
    }

    private void consumeTrailerEvent(RowData rowData) throws Throwable {
        String eventType = rowData.getEventType();
        StandardTrailerEs standardTrailerEs = JSON.parseObject(JSON.toJSONString(rowData.getAfter()),
                StandardTrailerEs.class);
        standardTrailerEs.setTidL(Long.valueOf(standardTrailerEs.getTid()));
        switch (eventType) {
            case "INSERT":
                LOG.info("insert trailer msg start,tid:{}", standardTrailerEs.getTid());
                standardTrailerService.insertOrUpdate(standardTrailerEs);
                break;
            case "UPDATE":
                LOG.info("update trailer msg start,tid:{}", standardTrailerEs.getTid());
                standardTrailerService.insertOrUpdate(standardTrailerEs);
                break;
            case "DELETE":
                standardTrailerEs = JSON.parseObject(JSON.toJSONString(rowData.getBefore()),
                        StandardTrailerEs.class);
                LOG.info("delete trailer msg start,tid:{}", standardTrailerEs.getTid());
                standardTrailerService.delete(standardTrailerEs);
                break;
            default:
                break;
        }
    }

    private void consumeUgcVideoEvent(RowData rowData) throws Throwable{
        String eventType = rowData.getEventType();
        UgcStandardVideoEs ugcStandardVideoEs = JSON.parseObject(JSON.toJSONString(rowData.getAfter()),
                UgcStandardVideoEs.class);
        switch (eventType) {
            case "INSERT":
                LOG.info("insert ugc video msg start,vid:{}", ugcStandardVideoEs.getVid());
                ugcStandardVideoService.insertOrUpdate(ugcStandardVideoEs);
                break;
            case "UPDATE":
                LOG.info("update ugc video msg start,vid:{}", ugcStandardVideoEs.getVid());
                ugcStandardVideoService.insertOrUpdate(ugcStandardVideoEs);
                break;
            case "DELETE":
                ugcStandardVideoEs = JSON.parseObject(JSON.toJSONString(rowData.getBefore()),
                        UgcStandardVideoEs.class);
                LOG.info("delete ugc video msg start,vid:{}", ugcStandardVideoEs.getVid());
                ugcStandardVideoService.delete(ugcStandardVideoEs);
                break;
            default:
                break;
        }
    }
}

