package com.heytap.longvideo.search.mq;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.model.entity.LvAlbumConsumeHiveData;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import com.heytap.longvideo.search.rpc.consumer.LvAlbumConsumeHiveDataRpcApiProxy;
import com.heytap.longvideo.search.rpc.consumer.VirtualProgramRpcApiProxy;
import com.heytap.longvideo.search.service.standard.StandardAlbumService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AlbumHiveDataService {

    @Autowired
    private LvAlbumConsumeHiveDataRpcApiProxy lvAlbumConsumeHiveDataRpcApiProxy;

    @Autowired
    private StandardAlbumService albumService;

    @Autowired
    private VirtualProgramRpcApiProxy virtualProgramRpcApiProxy;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    public void processData(List<LvAlbumConsumeHiveData> consumeHiveDataList) {
        consumeHiveDataList.forEach(data -> {
            refreshStandardAlbumEs(data);
            refreshApiSearchAlbumEs(data);
        });
    }

    private void refreshApiSearchAlbumEs(LvAlbumConsumeHiveData data) {
        String virtualSid = null;
        MisVirtualProgramRelation virtualProgramRelation = null;
        try {
            virtualProgramRelation = virtualProgramRpcApiProxy.queryBySid(data.getSid()).get().getData();
        } catch (Exception e) {
            log.error("query virtualProgram error! sid = [{}]", data.getSid(), e);
        }
        if (virtualProgramRelation != null) {
            virtualSid = virtualProgramRelation.getVirtualSid();
        }
        Long playPv = 0L;
        Long last7DaysPlayPv = 0L;
        Long last15DaysPlayPv = 0L;
        Long last30DaysPlayPv = 0L;
        Long last7DaysClickPv = 0L;
        Long last15DaysClickPv = 0L;
        Long last30DaysClickPv = 0L;
        String dayno = data.getDayno();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (virtualSid == null) {
            playPv = data.getPlayPv();
            last7DaysPlayPv = data.getLast7DaysPlayPv();
            last15DaysPlayPv = data.getLast15DaysPlayPv();
            last30DaysPlayPv = data.getLast30DaysPlayPv();
            last7DaysClickPv = data.getLast7DaysClickPv();
            last15DaysClickPv = data.getLast15DaysClickPv();
            last30DaysClickPv = data.getLast30DaysClickPv();
            boolQuery.filter(QueryBuilders.termQuery("sid", data.getSid()));
        } else {
            Map<String, Long> consumeMap = calcProgramPlayPv(virtualSid, dayno);
            playPv = consumeMap.get("playPv");
            last7DaysPlayPv = consumeMap.get("last7DaysPlayPv");
            last15DaysPlayPv = consumeMap.get("last15DaysPlayPv");
            last30DaysPlayPv = consumeMap.get("last30DaysPlayPv");
            last7DaysClickPv = consumeMap.get("last7DaysClickPv");
            last15DaysClickPv = consumeMap.get("last15DaysClickPv");
            last30DaysClickPv = consumeMap.get("last30DaysClickPv");
            boolQuery.filter(QueryBuilders.termQuery("virtualSid", virtualSid));
        }
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQuery).build();
        SearchHits<ProgramAlbumEs> searchHits = elasticsearchRestTemplate.search(searchQuery, ProgramAlbumEs.class);
        for (SearchHit<ProgramAlbumEs> searchHit : searchHits) {
            ProgramAlbumEs programAlbumEs = searchHit.getContent();
            programAlbumEs.setOppoHot(playPv);
            programAlbumEs.setDayNo(Integer.valueOf(dayno));
            programAlbumEs.setLast7DaysPlayPv(last7DaysPlayPv);
            programAlbumEs.setLast15DaysPlayPv(last15DaysPlayPv);
            programAlbumEs.setLast30DaysPlayPv(last30DaysPlayPv);
            programAlbumEs.setLast7DaysClickPv(last7DaysClickPv);
            programAlbumEs.setLast15DaysClickPv(last15DaysClickPv);
            programAlbumEs.setLast30DaysClickPv(last30DaysClickPv);
            elasticsearchRestTemplate.save(programAlbumEs);
        }
    }

    private Map<String, Long> calcProgramPlayPv(String virtualSid, String dayno) {
        Long playPv = 0L;
        Long last7DaysPlayPv = 0L;
        Long last15DaysPlayPv = 0L;
        Long last30DaysPlayPv = 0L;
        Long last7DaysClickPv = 0L;
        Long last15DaysClickPv = 0L;
        Long last30DaysClickPv = 0L;
        List<MisVirtualProgramRelation> programRelations = null;
        try {
            programRelations = virtualProgramRpcApiProxy.queryByVirtualSid(virtualSid).get().getData();
        } catch (Exception e) {
            log.error("query virtualProgram error! virtualSid = [{}]", virtualSid, e);
        }
        for (MisVirtualProgramRelation programRelation : programRelations) {
            LvAlbumConsumeHiveData data = null;
            try {
                data = lvAlbumConsumeHiveDataRpcApiProxy.getBySidAndDayno(programRelation.getSid(), dayno).get().getData();
            } catch (Exception e) {
                log.error("query local albumHiveData error! params = [sid = {}, dayno = {}]", programRelation.getSid(), dayno);
            }
            if (data != null) {
                playPv += data.getPlayPv();
                last7DaysPlayPv += data.getLast7DaysPlayPv();
                last15DaysPlayPv += data.getLast15DaysPlayPv();
                last30DaysPlayPv += data.getLast30DaysPlayPv();
                last7DaysClickPv += data.getLast7DaysClickPv();
                last15DaysClickPv += data.getLast15DaysClickPv();
                last30DaysClickPv += data.getLast30DaysClickPv();
            }
        }
        Map<String, Long> consumeMap = Maps.newHashMap();
        consumeMap.put("playPv", playPv);
        consumeMap.put("last7DaysPlayPv", last7DaysPlayPv);
        consumeMap.put("last15DaysPlayPv", last15DaysPlayPv);
        consumeMap.put("last30DaysPlayPv", last30DaysPlayPv);
        consumeMap.put("last7DaysClickPv", last7DaysClickPv);
        consumeMap.put("last15DaysClickPv", last15DaysClickPv);
        consumeMap.put("last30DaysClickPv", last30DaysClickPv);
        return consumeMap;
    }

    private void refreshStandardAlbumEs(LvAlbumConsumeHiveData data) {

        Set<String> sids = Sets.newHashSet();
        String virtualSid = null;
        MisVirtualProgramRelation virtualProgramRelation = null;
        try {
            virtualProgramRelation = virtualProgramRpcApiProxy.queryBySid(data.getSid()).get().getData();
        } catch (Exception e) {
            log.error("query virtualProgram error! sid = [{}]", data.getSid(), e);
        }
        if (virtualProgramRelation != null) {
            virtualSid = virtualProgramRelation.getVirtualSid();
            List<MisVirtualProgramRelation> programRelations = null;
            try {
                programRelations = virtualProgramRpcApiProxy.queryByVirtualSid(virtualSid).get().getData();
            } catch (Exception e) {
                log.error("query virtualProgram error! virtualSid = [{}]", virtualSid, e);
            }
            if (CollectionUtils.isNotEmpty(programRelations)) {
                sids = programRelations.stream().map(programRelation -> programRelation.getSid()).collect(Collectors.toSet());
            }
        }
        sids.add(data.getSid());

        sids.forEach(sid -> {
            StandardAlbumEs standardAlbumEs = null;
            try {
                standardAlbumEs = albumService.searchBySid(sid);
            } catch (Exception e) {
                log.error("query standardAlbum es error! sid = [{}]", sid, e);
            }
            if (standardAlbumEs != null) {
                buildStandardAlbumEs(standardAlbumEs, data);
                try {
                    albumService.insertOrUpdate(standardAlbumEs);
                } catch (Exception e) {
                    log.error("update standardAlbum es error! sid = [{}]", sid, e);
                }
            }
        });
    }

    private void buildStandardAlbumEs(StandardAlbumEs standardAlbumEs, LvAlbumConsumeHiveData data) {
        if (data.getDayno().equals(String.valueOf(standardAlbumEs.getDayNo()))) {
            standardAlbumEs.setOppoHot(Math.max(data.getPlayPv(), standardAlbumEs.getOppoHot()));
            standardAlbumEs.setLast7DaysPlayPv(Math.max(data.getLast7DaysPlayPv(), standardAlbumEs.getLast7DaysPlayPv()));
            standardAlbumEs.setLast15DaysPlayPv(Math.max(data.getLast15DaysPlayPv(), standardAlbumEs.getLast15DaysPlayPv()));
            standardAlbumEs.setLast30DaysPlayPv(Math.max(data.getLast30DaysPlayPv(), standardAlbumEs.getLast30DaysPlayPv()));
            standardAlbumEs.setLast7DaysClickPv(Math.max(data.getLast7DaysClickPv(), standardAlbumEs.getLast7DaysClickPv()));
            standardAlbumEs.setLast15DaysClickPv(Math.max(data.getLast15DaysClickPv(), standardAlbumEs.getLast15DaysClickPv()));
            standardAlbumEs.setLast30DaysClickPv(Math.max(data.getLast30DaysClickPv(), standardAlbumEs.getLast30DaysClickPv()));
        } else {
            standardAlbumEs.setOppoHot(data.getPlayPv());
            standardAlbumEs.setLast7DaysPlayPv(data.getLast7DaysPlayPv());
            standardAlbumEs.setLast15DaysPlayPv(data.getLast15DaysPlayPv());
            standardAlbumEs.setLast30DaysPlayPv(data.getLast30DaysPlayPv());
            standardAlbumEs.setLast7DaysClickPv(data.getLast7DaysClickPv());
            standardAlbumEs.setLast15DaysClickPv(data.getLast15DaysClickPv());
            standardAlbumEs.setLast30DaysClickPv(data.getLast30DaysClickPv());
        }
        standardAlbumEs.setDayNo(Integer.valueOf(data.getDayno()));
    }
}

