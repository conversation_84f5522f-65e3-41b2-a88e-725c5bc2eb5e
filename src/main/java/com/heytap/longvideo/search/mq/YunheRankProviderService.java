package com.heytap.longvideo.search.mq;

import com.heytap.longvideo.client.media.enums.YunheRankKindEnum;
import com.heytap.longvideo.client.media.enums.YunheRankTypeEnum;
import com.heytap.longvideo.client.media.yunhe.YunheRank;
import com.heytap.longvideo.search.properties.MqProperties;
import com.heytap.longvideo.search.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * 发送MQ给中台，锁屏自行消费
 */
@Slf4j
@Service
public class YunheRankProviderService {
    @Autowired
    private MqProperties mqProperties;

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public void sendMQ(List<YunheRank> yunheRankList) {
        if (CollectionUtils.isEmpty(yunheRankList)) {
            return;
        }

        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, mqProperties.getYunheRankServers());
        props.put(ProducerConfig.CLIENT_ID_CONFIG, "longvideo-search-rest");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.IntegerSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.RETRIES_CONFIG, 2);

        try (Producer<Integer, String> producer = new KafkaProducer<>(props)) {
            for (YunheRank yunheRank : yunheRankList) {
                try {
                    String chartType = convertChartType(yunheRank);
                    if (StringUtils.isBlank(chartType)) {
                        continue;
                    }

                    String body = buildBody(yunheRank, chartType);
                    ProducerRecord<Integer, String> record = new ProducerRecord<>(
                            mqProperties.getYunheRankTopic(), mqProperties.getYunheRankKey().hashCode(), body);
                    producer.send(record).get(3, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("yunheRank sendMQ fail", e);
                }
            }
        }
    }

    public String convertChartType(YunheRank yunheRank) {
        if (YunheRankTypeEnum.TV.getCode().equals(yunheRank.getType())
                && YunheRankKindEnum.TOP.getCode().equals(yunheRank.getKind())) {
            return "连续剧霸屏榜";
        }
        if (YunheRankTypeEnum.ART.getCode().equals(yunheRank.getType())
                && YunheRankKindEnum.HOT.getCode().equals(yunheRank.getKind())) {
            return "综艺热度榜";
        }
        if (YunheRankTypeEnum.ANIMATION.getCode().equals(yunheRank.getType())
                && YunheRankKindEnum.TOP.getCode().equals(yunheRank.getKind())) {
            return "动漫霸屏榜";
        }
        return "";
    }

    public String buildBody(YunheRank yunheRank, String chartType) {
        Map<String, Object> params = new HashMap<>();
        params.put("source", "云合数据");
        params.put("channel", "视频影视综");
        params.put("theme_label", "无");
        params.put("ranking", yunheRank.getRank());
        params.put("title", yunheRank.getTitle());
        params.put("crawlerTime", LocalDateTime.now().format(dtf));
        params.put("versionCode", System.currentTimeMillis());
        if (YunheRankTypeEnum.ART.getCode().equals(yunheRank.getType())) {
            params.put("hotspot", yunheRank.getValue().intValue());
        }

        Map<String, Object> extMap = new HashMap<>();
        extMap.put("chartType", chartType);
        if (YunheRankTypeEnum.TV.getCode().equals(yunheRank.getType())) {
            extMap.put("occupationRatio", yunheRank.getValue());
        }
        params.put("ext", extMap);

        return JacksonUtil.toJSONString(params);
    }

}
