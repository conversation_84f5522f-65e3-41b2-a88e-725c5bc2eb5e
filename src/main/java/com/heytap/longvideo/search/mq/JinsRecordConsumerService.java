package com.heytap.longvideo.search.mq;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.model.entity.es.*;
import com.oppo.cpc.video.framework.lib.jins.RowData;

import java.util.*;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.RestStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;

@Slf4j
@Data
public class JinsRecordConsumerService implements MessageListenerConcurrently {
    /**
     * 需要记录日志的表（不设置则为所有表都需记录）
     */
    private Set<String> tableSet;

    private Set<String> excludeTableSet;

    /**
     * 表主键（如果某个表未设置，则先尝试取code,无则尝试取id）
     */
    private Map<String, String> tablePrimaryKeyMap = new HashMap();

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    public JinsRecordConsumerService() {
    }

    private Set<String> exExistsIndexSet =new HashSet<>();

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(msgs)) {
            log.info("msg null,return");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        MessageExt messageExt = msgs.get(0);
        String data = null;
        try {
            data = new String(messageExt.getBody(), RemotingHelper.DEFAULT_CHARSET);
            RowData rowData = JSON.parseObject(data, RowData.class);
            String tableName = rowData.getTableName();
            if (CollectionUtils.isNotEmpty(excludeTableSet) && needExcludeTable(tableName)) {
                log.info("filter data,tableName:{}",tableName);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            if (CollectionUtils.isNotEmpty(tableSet) && !needSaveTable(tableName)) {
                log.info("filter data,tableName:{}",tableName);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            consumeAlbumEvent(rowData);
        } catch (Throwable e) {
            log.error("msg consume fail,data:{}", data, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private boolean needSaveTable(String tableName) {
        for (String str : tableSet) {
            if (tableName.startsWith(str)) {
                return true;
            }
        }
        return false;
    }

    private boolean needExcludeTable(String tableName) {
        for (String str : excludeTableSet) {
            if (tableName.startsWith(str)) {
                return true;
            }
        }
        return false;
    }


    private void consumeAlbumEvent(RowData rowData) throws Throwable {
        String eventType = rowData.getEventType();
        JinsRecordEs jinsRecordEs = new JinsRecordEs();
        jinsRecordEs.setCreateTime(System.currentTimeMillis());
        jinsRecordEs.setTableName(rowData.getTableName());
        jinsRecordEs.setDbName(rowData.getSchemaName());
        switch (eventType) {
            case "INSERT":
                log.info("insert jinsRecordEs start,jinsRecordEs:{}", rowData.getBefore());
                jinsRecordEs.setEventType("insert");
                jinsRecordEs.setPrimaryKey(getPrimaryKey(rowData.getTableName(), rowData.getAfter()));
                jinsRecordEs.setUpdateAfter(rowData.getAfter().toString());
                break;
            case "UPDATE":
                log.info("update jinsRecordEs start,jinsRecordEs:{}", rowData.getAfter());
                jinsRecordEs.setEventType("update");
                jinsRecordEs.setPrimaryKey(getPrimaryKey(rowData.getTableName(), rowData.getAfter()));
                jinsRecordEs.setUpdateBefore(rowData.getBefore().toString());
                jinsRecordEs.setUpdateAfter(rowData.getAfter().toString());
                break;
            case "DELETE":
                log.info("insert jinsRecordEs start,jinsRecordEs:{}", rowData.getBefore());
                jinsRecordEs.setEventType("insert");
                jinsRecordEs.setPrimaryKey(getPrimaryKey(rowData.getTableName(), rowData.getBefore()));
                jinsRecordEs.setUpdateBefore(rowData.getBefore().toString());
                break;
            default:
                break;
        }
        String indexName =rowData.getSchemaName() + "_update_snapshot";
        IndexRequest indexRequest = new IndexRequest(indexName);
        indexRequest.source(jinsRecordEs, XContentType.JSON);
        createIndex(indexName);
        restTemplate.save(jinsRecordEs, IndexCoordinates.of(indexName));
    }

    private String getPrimaryKey(String tableName, Map<String, Object> map) {
        for (String s : tablePrimaryKeyMap.keySet()) {
            if (tableName.startsWith(s)) {
                return map.get(tablePrimaryKeyMap.get(s)).toString();
            }
        }
        if (map.containsKey("code")) {
            return map.get("code").toString();
        }
        if (map.containsKey("id")) {
            return map.get("id").toString();
        }
        return "";
    }

    private String mapping="{\n" +
            "\t\"properties\": {\n" +
            "\t\t\"primaryKey\": {\n" +
            "\t\t\t\"type\": \"keyword\"\n" +
            "\t\t},\n" +
            "\t\t\"eventType\": {\n" +
            "\t\t\t\"type\": \"keyword\"\n" +
            "\t\t},\n" +
            "\t\t\"tableName\": {\n" +
            "\t\t\t\"type\": \"keyword\"\n" +
            "\t\t},\n" +
            "\t\t\"dbName\": {\n" +
            "\t\t\t\"type\": \"keyword\"\n" +
            "\t\t},\n" +
            "\t\t\"updateBefore\": {\n" +
            "\t\t\t\"type\": \"text\",\n" +
            "\t\t\t\"index\": false\n" +
            "\t\t},\n" +
            "\t\t\"updateAfter\": {\n" +
            "\t\t\t\"type\": \"text\",\n" +
            "\t\t\t\"index\": false\n" +
            "\t\t},\n" +
            "\t\t\"createTime\": {\n" +
            "\t\t\t\"type\": \"long\"\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}";

    private void createIndex(String indexName) throws Exception{
        if(exExistsIndexSet.contains(indexName)){
            return;
        }
        GetIndexRequest request = new GetIndexRequest(indexName);
        boolean indexExists = restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        if (indexExists == false) {

            CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);
            createIndexRequest.mapping(mapping,XContentType.JSON);
            CreateIndexResponse createResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
            if (!createResponse.isAcknowledged()) {
                log.error("index {} create error", indexName);
            }else{
                exExistsIndexSet.add(indexName);
            }
        }
    }


}

