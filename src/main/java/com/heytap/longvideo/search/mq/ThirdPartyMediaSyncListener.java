package com.heytap.longvideo.search.mq;

import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.service.spider.UnofficialAlbumImageUrlTransformService;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.service.TranslateAreaService;
import com.heytap.longvideo.search.service.standard.unofficialalbum.service.TranslateTagsService;
import com.heytap.longvideo.search.service.sync.AlbumSyncToSearchService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * @Description: 非合作内容方 - 媒资同步es消费者
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/12 20:43
 */
@Component
@Slf4j
public class ThirdPartyMediaSyncListener implements MessageListenerConcurrently {

    private static final Logger mqLogger = LoggerFactory.getLogger("RocketmqClient");

    private final UnofficialAlbumService unofficialAlbumService;
    private final TranslateAreaService translateAreaService;
    private final TranslateTagsService translateTagsService;
    private final UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService;
    private final AlbumSyncToSearchService albumSyncToSearchService;

    /**
     * 全网节目--标准化时，是否生存ocs图片
     */
    @HeraclesDynamicConfig(key = "unofficial.album.mq.standard.generate.ocs.image", fileName = "configure.properties")
    private Boolean generateOcsImageSwitch;

    public ThirdPartyMediaSyncListener(UnofficialAlbumService unofficialAlbumService,
                                       TranslateAreaService translateAreaService,
                                       TranslateTagsService translateTagsService,
                                       UnofficialAlbumImageUrlTransformService unofficialAlbumImageUrlTransformService,
                                       AlbumSyncToSearchService albumSyncToSearchService) {
        this.unofficialAlbumService = unofficialAlbumService;
        this.translateAreaService = translateAreaService;
        this.translateTagsService = translateTagsService;
        this.unofficialAlbumImageUrlTransformService = unofficialAlbumImageUrlTransformService;
        this.albumSyncToSearchService = albumSyncToSearchService ;
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(msgList)) {
            mqLogger.info("[ThirdPartyMediaSyncListener] messageList is null");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        UnofficialAlbumEs unofficialAlbumEs;
        try {
            MessageExt messageExt = msgList.get(0);
            String data = new String(messageExt.getBody(), RemotingHelper.DEFAULT_CHARSET);
            mqLogger.info("[ThirdPartyMediaSyncListener] consumed message {}", data);
            unofficialAlbumEs = JsonUtil.fromStr(data, UnofficialAlbumEs.class);
        } catch (Exception e) {
            mqLogger.error("[ThirdPartyMediaSyncListener] build unofficialAlbumEs error:", e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        try {
            // 更新地域转换
            handleArea(unofficialAlbumEs);

            // 标签转换
            String translateTags = translateTagsService.translateTags(unofficialAlbumEs.getTags(), unofficialAlbumEs.getProgramType());
            if (translateTags != null && !translateTags.equals(unofficialAlbumEs.getMappingTags())) {
                log.info("unofficialAlbumEs translateTags, sourceAlbumId:{}, before:{}, after:{}", unofficialAlbumEs.getSourceAlbumId(), unofficialAlbumEs.getTags(), translateTags);
                unofficialAlbumEs.setMappingTags(translateTags);
            }
            // s8.10 全网内容同步锁屏，大小横竖图不能为空
            if (StringUtils.isEmpty(unofficialAlbumEs.getHorizontalIcon()) || StringUtils.isEmpty(unofficialAlbumEs.getHorizontalImage())) {
                String image = StringUtils.isEmpty(unofficialAlbumEs.getHorizontalIcon()) ? unofficialAlbumEs.getHorizontalImage() : unofficialAlbumEs.getHorizontalIcon();
                unofficialAlbumEs.setHorizontalIcon(image);
                unofficialAlbumEs.setHorizontalImage(image);
            }
            if (StringUtils.isEmpty(unofficialAlbumEs.getVerticalIcon()) || StringUtils.isEmpty(unofficialAlbumEs.getVerticalImage())) {
                String image = StringUtils.isEmpty(unofficialAlbumEs.getVerticalIcon()) ? unofficialAlbumEs.getVerticalImage() : unofficialAlbumEs.getVerticalIcon();
                unofficialAlbumEs.setVerticalIcon(image);
                unofficialAlbumEs.setVerticalImage(image);
            }
            if (generateOcsImageSwitch) {
                // 处理全网ocs图片
                Boolean res = unofficialAlbumImageUrlTransformService.generateImageAndSyncToEs(unofficialAlbumEs, false, false);
                if (!Boolean.TRUE.equals(res)) {
                    log.warn("unofficial album handle Ocs Image error, sid:{}", unofficialAlbumEs.getSid());
                }
            }

            // 非合作方影人落库 & 构建非合作方节目与影人关系
            unofficialAlbumService.buildMisPersonAndUnofficialAlbumRoleRel(unofficialAlbumEs);

            // 处理锁定的字段
            unofficialAlbumService.handleLockedFields(unofficialAlbumEs);

            // 保存到ES
            unofficialAlbumService.saveOrUpdate(unofficialAlbumEs);

            //发送kafka,将消息同步给浏览器
            albumSyncToSearchService.incrementSyncWithUnofficialAlbum(unofficialAlbumEs);

            // 豆瓣爬取数据存在一个或者多个版权方，当某一个版权方不存在的时候，应该删除；
            // 如果本身就没有爬取到可以播放的版权方（不会下发，下发有判断）；不应该失效，例如，评分字段还在使用
            handleDoubanContent(unofficialAlbumEs);

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            mqLogger.error("[ThirdPartyMediaSyncListener] save third media to es error:", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }


    private void handleArea(UnofficialAlbumEs unofficialAlbumEs) {
        String area = unofficialAlbumEs.getArea();
        if (StringUtils.isNotEmpty(area)) {
            String[] areaSplit = area.split(" / ");
            String translateArea;
            if (areaSplit.length > 1) {
                ArrayList<String> list = new ArrayList<>();
                for (String item : areaSplit) {
                    String translated = translateAreaService.translateArea(unofficialAlbumEs.getProgramType(), item);
                    if (StringUtils.isNotBlank(translated)) {
                        list.add(translated);
                    }
                }
                // 列表个数为1也可以正确处理
                translateArea = String.join(" / ", list);
            } else {
                translateArea = translateAreaService.translateArea(unofficialAlbumEs.getProgramType(), unofficialAlbumEs.getArea());
            }
            if (translateArea != null && !translateArea.equals(unofficialAlbumEs.getArea())) {
                log.info("unofficialAlbumEs translateArea, sourceAlbumId:{}, before:{}, after:{}", unofficialAlbumEs.getSourceAlbumId(), unofficialAlbumEs.getArea(), translateArea);
            }
            unofficialAlbumEs.setArea(translateArea);
        }
    }

    private void handleDoubanContent(UnofficialAlbumEs unofficialAlbumEs) throws IOException {
        if ("douban".equals(unofficialAlbumEs.getSource())) {
            List<String> copyrightCodeList = unofficialAlbumEs.getCopyrightCodeList();
            List<UnofficialAlbumEs> esList = unofficialAlbumService.searchListBySourceAlbumId(unofficialAlbumEs.getSourceAlbumId());
            if (CollectionUtils.isNotEmpty(copyrightCodeList) && copyrightCodeList.size() >= 1) {
                for (UnofficialAlbumEs albumEs : esList) {
                    if (StringUtils.isNotEmpty(albumEs.getCopyrightCode()) &&
                            copyrightCodeList.contains(albumEs.getCopyrightCode())) {
                        continue;
                    }
                    albumEs.setStatus(0);
                    unofficialAlbumService.saveOrUpdate(albumEs);
                    // 失效内容同步锁屏
                    unofficialAlbumService.mqSendMsg(albumEs, 3);
                }
            }
        }
    }
}