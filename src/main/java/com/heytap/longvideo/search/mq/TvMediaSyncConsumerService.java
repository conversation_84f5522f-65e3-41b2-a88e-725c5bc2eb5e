package com.heytap.longvideo.search.mq;

import com.heytap.longvideo.search.model.entity.es.TvAlbumEs;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description: 電視媒資增量同步至非合作内容庫 - 消費者
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/10/31 11:10
 */
@Service("tvMediaSyncConsumerService")
public class TvMediaSyncConsumerService implements MessageListenerConcurrently {

    private static final Logger mqLogger = LoggerFactory.getLogger("RocketmqClient");

    private final UnofficialAlbumService unofficialAlbumService;

    @HeraclesDynamicConfig(key = "unofficial.album.sync.sources", fileName = "search_config.properties")
    private Set<String> sources = new HashSet<String>() {{
        add("tencent");
        add("iqiyi");
    }};

    public TvMediaSyncConsumerService(UnofficialAlbumService unofficialAlbumService) {
        this.unofficialAlbumService = unofficialAlbumService;
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(msgList)) {
            mqLogger.info("[TvMediaSyncConsumerService ] messageList is null");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        UnofficialAlbumEs unofficialAlbumEs;
        try {
            MessageExt messageExt = msgList.get(0);
            String data = new String(messageExt.getBody(), RemotingHelper.DEFAULT_CHARSET);
            TvAlbumEs tvAlbumEs = JsonUtil.fromStr(data, TvAlbumEs.class);
            if (!sources.contains(tvAlbumEs.getSource())) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            unofficialAlbumEs = unofficialAlbumService.buildUnofficialALbumEs(tvAlbumEs);
        } catch (Exception e) {
            mqLogger.error("[TvMediaSyncConsumerService ] building unofficialAlbumEs message:{} error:", msgList, e);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        try {
            // 非合作方影人落库 & 构建非合作方节目与影人关系
            unofficialAlbumService.buildMisPersonAndUnofficialAlbumRoleRel(unofficialAlbumEs);

            // 处理锁定的字段
            unofficialAlbumService.handleLockedFields(unofficialAlbumEs);

            // 保存到ES
            unofficialAlbumService.saveOrUpdate(unofficialAlbumEs);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            mqLogger.error("[TvMediaSyncConsumerService ] save tv media to es error:", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
}