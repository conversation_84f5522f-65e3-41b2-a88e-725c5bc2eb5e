package com.heytap.longvideo.search.mq.producer;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import static org.apache.rocketmq.remoting.common.RemotingHelper.DEFAULT_CHARSET;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/6/30 下午8:39
 */
@Service
public class RocketMqProducer {

    private static final Logger log = LoggerFactory.getLogger("rocketmq_produce_log");

    private final DefaultMQProducer producer;

    public RocketMqProducer(DefaultMQProducer producer) {
        this.producer = producer;
    }


    public void mqSendMsg(String topic, String tags, String key, String message,Integer delayLevel) {
        try {
            Message msg = new Message(topic, tags, key, message.getBytes(DEFAULT_CHARSET));
            if (delayLevel != null) {
                msg.setDelayTimeLevel(delayLevel);
            }

            SendResult sendResult = null;
            for (int i = 0; i < 3; i++) {
                sendResult = producer.send(msg);
                if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                    break;
                }
                Thread.sleep(200);
            }
            if (!sendResult.getSendStatus().equals(SendStatus.SEND_OK)) {
                log.error("MQ send failed after retries. Topic: {}, Tags: {}, Key: {}, Message: {}, Status: {}",
                        topic, tags, key, message, sendResult.getSendStatus());
            }
        } catch (Exception e) {
            log.error("send error, topic:{}, key={}", topic, key, e);
        }
    }
}