package com.heytap.longvideo.search.rpc.provider;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.ApiSearchAlbumAdjustRpcApi;
import com.heytap.longvideo.client.media.SearchRpcApi;
import com.heytap.longvideo.client.media.entity.AlbumSearchResponse;
import com.heytap.longvideo.client.media.query.AlbumSearchParameterModifier;
import com.heytap.longvideo.search.service.standard.AdjustApiSearchAlbumService;
import esa.rpc.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024/9/23 15:08
 */
@Service
public class ApiSearchAlbumAdjustRpcApiService implements ApiSearchAlbumAdjustRpcApi {
    @Autowired
    private AdjustApiSearchAlbumService adjustApiSearchAlbumService;


    @Override
    public CompletableFuture<RpcResult<Boolean>> adjustSingleApiSearchAlbum(AlbumSearchParameterModifier param) {
        return CompletableFuture.completedFuture(new RpcResult<>(this.adjustApiSearchAlbumService.adjustSingleApiSearchAlbum(param)));
    }

    @Override
    public CompletableFuture<RpcResult<List<AlbumSearchResponse>>> adjustApiSearchAlbum(AlbumSearchParameterModifier param) {
        return CompletableFuture.completedFuture(new RpcResult<>(this.adjustApiSearchAlbumService.adjustApiSearchAlbum(param)));
    }

    @Override
    public CompletableFuture<RpcResult<List<AlbumSearchResponse>>> delApiSearchAlbumPoolCode(AlbumSearchParameterModifier param) {
        return CompletableFuture.completedFuture(new RpcResult<>(this.adjustApiSearchAlbumService.delApiSearchAlbumPoolCode(param)));
    }

    @Override
    public CompletableFuture<RpcResult<Boolean>> collateSinglePoolCodeApiSearchAlbum(AlbumSearchParameterModifier param) {
        return CompletableFuture.completedFuture(new RpcResult<>(this.adjustApiSearchAlbumService.collateSinglePoolCodeApiSearchAlbum(param)));
    }
}
