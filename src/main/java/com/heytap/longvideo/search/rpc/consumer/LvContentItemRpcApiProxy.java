package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.LvContentItemRpcApi;
import com.heytap.longvideo.client.common.RpcResult;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024/9/25 15:00
 */
@Slf4j
@Service
public class LvContentItemRpcApiProxy {


    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 1, timeout = 3000L)
    private LvContentItemRpcApi lvContentItemRpcApi;

    public CompletableFuture<RpcResult<List<String>>> getItemsByPoolCode(String contentPoolCode, Integer pageIndex, Integer pageSize) {
        return this.lvContentItemRpcApi.getSidListByPoolCode(contentPoolCode, pageIndex, pageSize);
    }

    public CompletableFuture<RpcResult<Integer>> getItemCount(String contentPoolCode) {
        return lvContentItemRpcApi.getItemCountByContentPoolCode(contentPoolCode);
    }

    public CompletableFuture<RpcResult<List<String>>> searchExistSid(List<String> sids, String contentPoolCode) {
        return lvContentItemRpcApi.searchExistSid(sids, contentPoolCode);
    }

}
