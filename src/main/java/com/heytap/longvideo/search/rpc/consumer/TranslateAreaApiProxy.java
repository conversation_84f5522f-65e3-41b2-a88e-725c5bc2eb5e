package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.MisAreaRpcApi;
import com.heytap.longvideo.client.media.MisOrgAreaRpcApi;
import com.heytap.longvideo.client.media.entity.MisArea;
import com.heytap.longvideo.client.media.entity.MisOrgArea;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/4/23 上午10:18
 */

@Service
public class TranslateAreaApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private MisOrgAreaRpcApi misOrgAreaRpcApi;

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private MisAreaRpcApi misAreaRpcApi;

    public CompletableFuture<RpcResult<List<MisOrgArea>>> getMisOrgAreasByNameAndProgramType(String programType, String orgArea) {
        return misOrgAreaRpcApi.getMisOrgAreasByNameAndProgramType(programType, orgArea);
    }

    public CompletableFuture<RpcResult<List<MisArea>>> getAreaListByAreaIdAndProgramType(Integer areaId, String programType) {
        return misAreaRpcApi.getAreaListByAreaIdAndProgramType(areaId, programType);
    }
}
