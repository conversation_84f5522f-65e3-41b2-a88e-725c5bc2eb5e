package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.LvContentMaterialRpcApi;
import com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO;
import com.heytap.longvideo.client.common.RpcResult;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 素材海报rpc
 * @Author: 80398885WT
 * @Date: 2025/5/30
 */
@Slf4j
@Service
public class LvContentMaterialRpcApiProxy {

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 0)
    private LvContentMaterialRpcApi lvContentMaterialRpcApi;

    public Map<String, List<LvContentMaterialVO>> getMaterials(List<String> sidList) {
        try {
            RpcResult<Map<String, List<LvContentMaterialVO>>> rpcResult = lvContentMaterialRpcApi.getMaterials(sidList).get(5, TimeUnit.SECONDS);
            if (Objects.isNull(rpcResult)) {
                log.error("lvContentMaterialRpcApi.getMaterials({}) return null", sidList);
                return null;
            }

            return rpcResult.getData();
        } catch (Exception e) {
            log.error("lvContentMaterialRpcApi.getMaterials({}) error:", sidList, e);
            return null;
        }
    }
}