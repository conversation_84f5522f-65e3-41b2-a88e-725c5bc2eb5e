package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.MisPersonRpcApi;
import com.heytap.longvideo.client.media.entity.MisPerson;
import com.heytap.longvideo.client.media.entity.SaveOrUpdateResult;
import com.heytap.longvideo.search.utils.FutureUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description: 腾讯库影人rpc
 * @Author: 80398885WT
 * @Date: 2025/6/25
 */
@Slf4j
@Service
public class MisPersonRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private MisPersonRpcApi misPersonRpcApi;

    public Integer saveOrUpdate(MisPerson misPerson) {
        RpcResult<Integer> rpcResult = FutureUtil.getFutureIgnoreException(misPersonRpcApi.saveOrUpdate(misPerson));
        if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.getData())) {
            return 0;
        }

        return rpcResult.getData();
    }

    public MisPerson queryByCnName(String cnName) {
        RpcResult<MisPerson> rpcResult = FutureUtil.getFutureIgnoreException(misPersonRpcApi.queryByCnName(cnName));
        if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.getData())) {
            return null;
        }

        return rpcResult.getData();
    }

    public SaveOrUpdateResult saveOrUpdateSafely(MisPerson misPerson) {
        com.heytap.longvideo.common.lib.rpc.RpcResult<SaveOrUpdateResult> rpcResult = FutureUtil.getFutureIgnoreException(misPersonRpcApi.saveOrUpdateSafely(misPerson));
        if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.getData())) {
            return null;
        }

        return rpcResult.getData();
    }
}