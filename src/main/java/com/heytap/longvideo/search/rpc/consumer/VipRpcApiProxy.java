package com.heytap.longvideo.search.rpc.consumer;

import com.oppo.cpc.video.framework.lib.vip.RpcResult;
import com.oppo.cpc.video.framework.lib.vip.UserVipInfoRequest;
import com.oppo.cpc.video.framework.lib.vip.UserVipInfoRpcApi;
import com.oppo.cpc.video.framework.lib.vip.VideoVipInfo;
import esa.rpc.config.annotation.Reference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2023/7/26 16:05
 */
@Getter
@Slf4j
@Service
public class VipRpcApiProxy {

    @Reference(providerAppId = "oppomobile-vip-rest", protocol = "dubbo", timeout = 500)
    private UserVipInfoRpcApi vipInfoRpcApi;

    public CompletableFuture<RpcResult<VideoVipInfo>> queryUserVipInfo(UserVipInfoRequest request) {
        return vipInfoRpcApi.queryUserVipInfo(request);
    }
}