package com.heytap.longvideo.search.rpc.consumer;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.ImageTagRpcApi;
import com.heytap.longvideo.client.arrange.entity.MtvImageTag;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.search.utils.CommonUtils;
import com.heytap.longvideo.search.utils.StringUtil;
import com.oppo.trace.async.TraceBiFunction;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Cache;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Caffeine;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @Author: 80339123 liu ying
 * @Date: 2022/3/24 21:49
 */
@Service
@Slf4j
public class ImageTagRpcApiProxy {

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 0)
    private ImageTagRpcApi imageTagRpcApi;

    //region 新缓存 一个code 对应一个list
    Cache<String, Map<String, List<MtvImageTag>>> imageTagCacheV2 = Caffeine.newBuilder()
            .maximumSize(2)
            .expireAfterWrite(32, TimeUnit.MINUTES)
            .build();

    private static String imageTagCacheKeyV2 = "imageTagV2";
    //endregion

    public CompletableFuture<RpcResult<List<MtvImageTag>>> findByStatus(int status) {
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return imageTagRpcApi.findByStatus(status);
    }

    public MtvImageTag getByCodeV2(String code) {
        try {
            Map<String, List<MtvImageTag>> imageTagMap = imageTagCacheV2.getIfPresent(imageTagCacheKeyV2);

            if (MapUtils.isNotEmpty(imageTagMap) && imageTagMap.containsKey(code)) {
                List<MtvImageTag> imageTagList = imageTagMap.get(code);

                for (MtvImageTag tag : imageTagList) {
                    if (CommonUtils.matchVersion(tag.getBeginVersion(), tag.getEndVersion(), "6.2.0")) {
                        return tag;
                    }
                }
            }

            List<MtvImageTag> imageTagList = getImageTagList(code);

            if (CollectionUtils.isEmpty(imageTagList)) {
                return null;
            }

            for (MtvImageTag tag : imageTagList) {
                if (CommonUtils.matchVersion(tag.getBeginVersion(), tag.getEndVersion(), "6.2.0")) {
                    return tag;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("ImageTagRpcApiProxy findByCode error,", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<MtvImageTag> getImageTagList(String code) throws ExecutionException, InterruptedException, TimeoutException {
        //无需加锁
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        CompletableFuture<List<MtvImageTag>> completableFuture = imageTagRpcApi.findByStatus(1)
                .handle(new TraceBiFunction<>((rpcResult, e) -> {
                    if (e != null) {
                        log.error("ImageTagRpcApiProxy findByCode error,", e);
                        throw new RuntimeException(e);
                    }
                    if (rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                        log.error("imageTagRpcApi findByCode not success,rpcResult:{}", JSON.toJSONString(rpcResult));
                        throw new RuntimeException(rpcResult.getMsg());
                    }
                    List<MtvImageTag> imageTagList = rpcResult.getData();
                    if (CollectionUtils.isNotEmpty(imageTagList)) {
                        Map<String, List<MtvImageTag>> map = imageTagList.stream().collect(Collectors.groupingBy(MtvImageTag::getCode));
                        imageTagCacheV2.put(imageTagCacheKeyV2, map);
                        return map.get(code);
                    } else {
                        imageTagCacheV2.put(imageTagCacheKeyV2, new HashMap<>());
                        return null;
                    }
                }));
        return completableFuture.get(2, TimeUnit.SECONDS);
    }


    /**
     * 获取图片url
     *
     * @param code
     * @return
     */
    public String getImageUrl(String code) {
        if(StringUtil.isBlank(code)){
            return null;
        }
        MtvImageTag imageTag = this.getByCodeV2(code);

        if (imageTag == null) {
            return null;
        }

        return imageTag.getImageUrl();
    }
    public MtvImageTag getByCodeV2(String code, String appVersion) {
        try {

            if (StringUtils.isBlank(code) || StringUtils.isBlank(appVersion)) {
                return null;
            }

            Map<String, List<MtvImageTag>> imageTagMap = imageTagCacheV2.getIfPresent(imageTagCacheKeyV2);

            if (MapUtils.isNotEmpty(imageTagMap) && imageTagMap.containsKey(code)) {
                List<MtvImageTag> imageTagList = imageTagMap.get(code);

                for (MtvImageTag tag : imageTagList) {
                    if (CommonUtils.matchVersion(tag.getBeginVersion(), tag.getEndVersion(), appVersion)) {
                        return tag;
                    }
                }
            }
            //无需加锁
            CompletableFuture<List<MtvImageTag>> completableFuture = imageTagRpcApi.findByStatus(1)
                    .handle(new TraceBiFunction<>((rpcResult, e) -> {
                        if (e != null) {
                            log.error("ImageTagRpcApiProxy findByCode error,", e);
                            throw new RuntimeException(e);
                        }
                        if (rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                            log.error("imageTagRpcApi findByCode not success,rpcResult:{}", JSON.toJSONString(rpcResult));
                            throw new RuntimeException(rpcResult.getMsg());
                        }
                        List<MtvImageTag> imageTagList = rpcResult.getData();
                        if (CollectionUtils.isNotEmpty(imageTagList)) {
                            Map<String, List<MtvImageTag>> map = imageTagList.stream().collect(Collectors.groupingBy(MtvImageTag::getCode));
                            imageTagCacheV2.put(imageTagCacheKeyV2, map);
                            return map.get(code);
                        } else {
                            imageTagCacheV2.put(imageTagCacheKeyV2, new HashMap<>());
                            return null;
                        }
                    }));

            List<MtvImageTag> imageTagList = completableFuture.get(2, TimeUnit.SECONDS);

            if (CollectionUtils.isEmpty(imageTagList)) {
                return null;
            }

            for (MtvImageTag tag : imageTagList) {
                if (CommonUtils.matchVersion(tag.getBeginVersion(), tag.getEndVersion(), appVersion)) {
                    return tag;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("ImageTagRpcApiProxy findByCode error,", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 获取图片url
     *
     * @param code
     * @return
     */
    public String getImageUrl(String code, String appVersion) {
        MtvImageTag imageTag = this.getByCodeV2(code, appVersion);

        if (imageTag == null) {
            return null;
        }

        return imageTag.getImageUrl();
    }


}
