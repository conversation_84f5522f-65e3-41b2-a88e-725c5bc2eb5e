package com.heytap.longvideo.search.rpc.provider;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.SearchRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.query.SearchAlbumRequest;
import com.heytap.longvideo.search.service.standard.SearchService;
import esa.rpc.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/10/16
 */
@Service
public class SearchRpcApiService implements SearchRpcApi {

    @Autowired
    private SearchService searchService;

    @Override
    public CompletableFuture<RpcResult<List<StandardAlbum>>> searchAlbumByRule(SearchAlbumRequest request) {
        return CompletableFuture.completedFuture(new RpcResult<>(searchService.searchAlbum(request)));
    }
}
