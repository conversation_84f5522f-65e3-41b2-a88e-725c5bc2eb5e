package com.heytap.longvideo.search.rpc.consumer;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.heytap.longvideo.client.arrange.*;
import com.heytap.longvideo.client.arrange.entity.LvAlbumListItem;
import com.heytap.longvideo.client.arrange.entity.LvContentItem;
import com.heytap.longvideo.client.arrange.entity.LvPage;
import com.heytap.longvideo.client.arrange.entity.MtvSubjectitem;
import com.heytap.longvideo.client.arrange.entity.vo.LvContentMaterialVO;
import com.heytap.longvideo.client.arrange.model.response.ItemForListCardWithMoreVO;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
/*
 * Description 调用编排api库接口
 * Date 11:05 2023/7/21
 * Author songjiajia 80350688
*/
@Slf4j
@Service
public class ArrangeRpcApiProxy {

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 1)
    private LvContentItemRpcApi lvContentItemRpcApi;

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 1)
    SubjectRpcApi subjectRpcApi;

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 1)
    PageRpcApi pageRpcApi;

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 1)
    private LvAlbumListItemRpcApi lvAlbumListItemRpcApi;

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", retries = 1)
    private LvContentMaterialRpcApi lvContentMaterialRpcApi;


    public CompletableFuture<Page<LvContentItem>> getLvContentItems(String contentPoolCode, Integer status, Integer pageIndex, Integer pageSize) {
        return lvContentItemRpcApi.getItemsWithCache(contentPoolCode, status, pageIndex, pageSize).handle((result, e) -> {
            if (e != null || result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getItems error, the request:{}, the response:{}", contentPoolCode, result, e);
                throw new RuntimeException("getLvContentItems error");
            }
            return result.getData();
        });
    }

    public CompletableFuture<List<MtvSubjectitem>> getSubjectItemForListCard(String code, Integer linkType, Integer size) {
        return subjectRpcApi.getItemForListCard(code, linkType, size).handle((result, e) -> {
            if (e != null || result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getItemForListCard error, the request:{}, the response:{}", code, result, e);
                throw new RuntimeException("getMtvSubjectitemForListCard error");
            }
            return result.getData();
        });
    }

    public CompletableFuture<ItemForListCardWithMoreVO> getSubjectItemForListCardWithMore(String code, Integer linkType, Integer pageIndex, Integer pageSize) {
        return subjectRpcApi.getItemForListCardWithMore(code, linkType, pageIndex, pageSize).handle((result, e) -> {
            if (e != null || result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getSubjectItemForListCardWithMore error, the request:{}, the response:{}", code, result, e);
                throw new RuntimeException("getSubjectItemForListCardWithMore error");
            }
            return result.getData();
        });
    }

    public CompletableFuture<LvPage> findPageByCode(String code) {
        return pageRpcApi.findByCodeWithCache(code).handle((result, e) -> {
            if (e != null || result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("pageRpcApi findByCodeWithCache error, the request:{}, the response:{}", code, result, e);
                throw new RuntimeException("pageRpcApi findByCodeWithCache error");
            }
            return result.getData();
        });
    }

    public CompletableFuture<Page<LvAlbumListItem>> getLvAlbumListByAlbumListCode(String albumListCode, Integer status, Integer pageIndex, Integer pageSize) {
        return lvAlbumListItemRpcApi.getPageByAlbumListCode(albumListCode, status, pageIndex, pageSize).handle((result, e) -> {
            if (e != null || result == null || result.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("getPageByAlbumListCode error, the request:{}, the response:{}", albumListCode, result, e);
                throw new RuntimeException("getLvAlbumListByAlbumListCode error");
            }
            return result.getData();
        });
    }



    public CompletableFuture<RpcResult<Map<String, List<LvContentMaterialVO>>>> getMaterials(List<String> ids, String stamp) {
            RpcContext.getConsumerContext().setAttachment("stamp", stamp);
            //todo 需要清除这里之前为什么指明读cms库
            RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "cms");
        return lvContentMaterialRpcApi.getMaterials(ids);
    }
}
