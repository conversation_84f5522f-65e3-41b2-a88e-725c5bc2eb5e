package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.media.YunheRankRpcApi;
import com.heytap.longvideo.client.media.yunhe.YunheRank;
import com.heytap.longvideo.client.media.yunhe.YunheRankRequest;
import esa.rpc.common.context.RpcContext;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class YunheRankRpcApiProxy {
    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private YunheRankRpcApi yunheRankRpcApi;

    public CompletableFuture<Boolean> batchUpsert(List<YunheRank> list) {
        return yunheRankRpcApi.batchUpsert(list).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("batchInsert error, ret:{}", ret, e);
                return false;
            }

            return ret.getData();
        });
    }

    /**
     * 查询云合榜单的数据
     * @param request
     * @return
     */
    public CompletableFuture<List<YunheRank>> queryListByDayAndLimitNum(YunheRankRequest request) {
        return yunheRankRpcApi.queryListByDayAndLimitNum(request).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("queryListByDayAndLimitNum error, ret:{}", ret, e);
                return Collections.emptyList();
            }
            return ret.getData();
        });
    }

    public CompletableFuture<List<String>> queryDistinctTitlesByDay(String beginDay, String endDay) {
        RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
        return yunheRankRpcApi.queryDistinctTitlesByDay(beginDay, endDay).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("queryDistinctTitlesByDay error, ret:{}", ret, e);
                return Collections.emptyList();
            }
            return ret.getData();
        });
    }
}
