package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.search.api.TopSearchWordRpcApi;
import com.heytap.longvideo.client.arrange.search.request.TopSearchWordRequest;
import com.heytap.longvideo.client.common.ResultCode;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/5/14 17:40
 */
@Slf4j
@Service
public class TopSearchWordRpcApiProxy {

    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1)
    private TopSearchWordRpcApi topSearchWordRpcApi;


    public CompletableFuture<List<String>> querySearchWords(TopSearchWordRequest request) {
        return topSearchWordRpcApi.querySearchWords(request).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("querySearchWords error, ret:{}", ret, e);
                return Collections.emptyList();
            }
            return ret.getData();
        });
    }
}
