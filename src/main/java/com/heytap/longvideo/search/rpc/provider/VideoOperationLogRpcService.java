package com.heytap.longvideo.search.rpc.provider;

import com.heytap.longvideo.search.model.entity.es.VideoOperationLogEs;
import com.heytap.longvideo.search.service.common.VideoOperationLogService;
import com.heytap.video.client.search.api.VideoOperationLogRpcApi;
import com.heytap.video.client.search.model.VideoOperationLog;
import com.heytap.video.client.search.model.request.VideoOperationLogQueryRequest;
import com.heytap.video.client.search.model.response.PageResponse;
import esa.rpc.config.annotation.Service;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class VideoOperationLogRpcService implements VideoOperationLogRpcApi {

    @Autowired
    private VideoOperationLogService videoOperationLogService;


    @Override
    public PageResponse<VideoOperationLog> queryPage(VideoOperationLogQueryRequest request) {
        return videoOperationLogService.queryPage(request);
    }



    @Override
    public Integer insert(VideoOperationLog videoOperationLog) {
        VideoOperationLogEs es =new VideoOperationLogEs();
        BeanUtils.copyProperties(videoOperationLog,es);
        videoOperationLogService.insert(es);
        return 1;
    }
}
