package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.search.api.QuerySidSearchRpcApi;
import com.heytap.longvideo.client.arrange.search.entity.QuerySidSearch;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.search.utils.FutureUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class QuerySidSearchRpcApiProxy {
    @Reference(providerAppId = "arrange-search-service", protocol = "dubbo", retries = 1)
    private QuerySidSearchRpcApi querySidSearchRpcApi;

    /**
     * 通过搜索的关键词和sid或者到一个querySidSearch对象
     */
    public QuerySidSearch getQuerySidSearch(String keyword,String sid){
        CompletableFuture<RpcResult<QuerySidSearch>> future = querySidSearchRpcApi.getQuerySidSearch(keyword, sid);
        RpcResult<QuerySidSearch> rpcResult = FutureUtil.getFutureIgnoreException(future, 1, TimeUnit.SECONDS);
        if(rpcResult == null || ResultCode.SUCCESS.getCode() != rpcResult.getCode() || rpcResult.getData() == null){
            log.error("[QuerySidSearchRpcApi.getQuerySidSearch] error,keyword:{},sid:{},rpcResult:{}", keyword, sid,rpcResult);
            return null;
        }
        return rpcResult.getData();
    }
}
