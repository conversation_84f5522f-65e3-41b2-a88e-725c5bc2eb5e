package com.heytap.longvideo.search.rpc.consumer;

import com.google.common.collect.Lists;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.VirtualProgramRelationRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.search.utils.FutureUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 虚拟节目rpc
 * @Author: 80398885WT
 * @Date: 2025/6/9
 */
@Slf4j
@Service
public class VirtualProgramRelationRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 0)
    private VirtualProgramRelationRpcApi virtualProgramRelationRpcApi;

    private final StandardAlbumRpcApiProxy standardAlbumRpcApiProxy;

    public VirtualProgramRelationRpcApiProxy(StandardAlbumRpcApiProxy standardAlbumRpcApiProxy) {
        this.standardAlbumRpcApiProxy = standardAlbumRpcApiProxy;
    }

    public Map<String/* source */, StandardAlbum> getAllRelAlbumsBySid(String sid) {
        try {
            CompletableFuture<RpcResult<Map<String/* sid */, List<MisVirtualProgramRelation>>>> rpcResultCF =
                    virtualProgramRelationRpcApi.getAllRelBySids(Lists.newArrayList(sid));
            RpcResult<Map<String/* sid */, List<MisVirtualProgramRelation>>> rpcResult = FutureUtil.getFutureIgnoreException(rpcResultCF);
            if (Objects.isNull(rpcResult)) {
                return null;
            }

            Map<String/* sid */, List<MisVirtualProgramRelation>> listMap = rpcResult.getData();
            if (MapUtils.isEmpty(listMap)) {
                return null;
            }

            List<MisVirtualProgramRelation> virtualProgramRelationList = listMap.get(sid);
            if (CollectionUtils.isEmpty(virtualProgramRelationList)) {
                return null;
            }

            Map<String, StandardAlbum> albumMap = standardAlbumRpcApiProxy.getBySidsFilterInvalid(virtualProgramRelationList.stream()
                    .map(MisVirtualProgramRelation::getSid).collect(Collectors.toList()));
            if (MapUtils.isEmpty(albumMap)) {
                return null;
            }

            Map<String/* source */, StandardAlbum> resultMap = new HashMap<>();
            for (MisVirtualProgramRelation virtualProgramRelation : virtualProgramRelationList) {
                StandardAlbum standardAlbum = albumMap.get(virtualProgramRelation.getSid());
                if (Objects.isNull(standardAlbum)) {
                    continue;
                }

                resultMap.put(standardAlbum.getSource(), standardAlbum);
            }

            return resultMap;
        } catch (Exception e) {
            log.error("getAllRelAlbumsBySid({}) error:", sid, e);
            return null;
        }
    }
}