package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.media.UnofficialAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.UnofficialAlbum;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @Description: 非合作内容方剧头 - RPC接口代理
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/14 17:21
 */
@Slf4j
@Service
public class UnofficialAlbumRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 0)
    private UnofficialAlbumRpcApi unofficialAlbumRpcApi;

    public CompletableFuture<Integer> saveOrUpdate(UnofficialAlbum unofficialAlbum) {
        return unofficialAlbumRpcApi.saveOrUpdate(unofficialAlbum).thenApply(ret -> {
            if (Objects.isNull(ret) || !Objects.equals(ret.getCode(), 0)) {
                log.error("[unofficialAlbumRpcApi.saveOrUpdate({})] failed.", unofficialAlbum.getSid());
                return null;
            }

            return ret.getData();
        });
    }

    public UnofficialAlbum getBySid(String sid) throws ExecutionException, InterruptedException, TimeoutException {
        return unofficialAlbumRpcApi.getBySid(sid).thenApply(ret -> {
            if (Objects.isNull(ret) || !Objects.equals(ret.getCode(), 0)) {
                log.error("[unofficialAlbumRpcApi.getBySid({})] failed.", sid);
                return null;
            }

            return ret.getData();
        }).get(5L, TimeUnit.SECONDS);
    }
}
