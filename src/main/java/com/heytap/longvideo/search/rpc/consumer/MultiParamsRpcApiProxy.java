package com.heytap.longvideo.search.rpc.consumer;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.MultiParamsRpcApi;
import com.heytap.longvideo.client.arrange.entity.MtvMultiparams;
import com.heytap.longvideo.client.common.ResultCode;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Yanping
 * @date 2023/10/24
 */
@Slf4j
@Service
public class MultiParamsRpcApiProxy {

    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo", timeout = 500)
    private MultiParamsRpcApi multiParamsRpcApi;

    public CompletableFuture<List<MtvMultiparams>> findFilterAll() {
        try {
            return multiParamsRpcApi.findFilterAll().handle((rpcResult, e) -> {
                if (e != null) {
                    log.error("multiParamsRpcApi findFilterAll error,", e);
                    return Collections.emptyList();
                }
                if (rpcResult.getCode() != ResultCode.SUCCESS.getCode()) {
                    log.error("multiParamsRpcApi findFilterAll not success,rpcResult:{}", JSON.toJSONString(rpcResult));
                    return Collections.emptyList();
                }
                return rpcResult.getData();
            });
        } catch (Exception e) {
            log.error("multiParamsRpcApi findFilterAll error,", e);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
    }
}
