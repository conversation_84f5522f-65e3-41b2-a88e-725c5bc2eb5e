package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardEpisodeRpcApi;
import com.heytap.longvideo.client.media.entity.StandardEpisodeBO;
import com.heytap.longvideo.client.media.query.EpisodeExample;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.StringUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class StandardEpisodeRpcApiProxy {
    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private StandardEpisodeRpcApi standardEpisodeRpcApi;

    /**
     * 通过标准剧头的sid列出其下标准剧集的信息
     */
    public List<StandardEpisodeBO> listStandardEpisodeBoBySid(String sid,Boolean isOrder) {
        if(StringUtil.isBlank(sid)){
            return new ArrayList<>();
        }
        CompletableFuture<RpcResult<List<StandardEpisodeBO>>> completableFuture = standardEpisodeRpcApi.listStandardEpisodeBoBySid(sid,isOrder);
        RpcResult<List<StandardEpisodeBO>> rpcResult = FutureUtil.getFutureIgnoreException(completableFuture, 1, TimeUnit.SECONDS);
        if(rpcResult == null || ResultCode.SUCCESS.getCode() != rpcResult.getCode() || rpcResult.getData() == null){
            log.error("[StandardEpisodeRpcApiProxy.listStandardEpisodeBoBySid] rpc error,sid:{},rpcResult:{}", sid, rpcResult);
            return new ArrayList<>();
        }
        return rpcResult.getData();
    }


    /**
     * 获取sid的前X集后Y集（综艺只获取后Y集）
     */
    public List<StandardEpisodeBO> getStartEndEpisodeBySid(String sid, Integer startNum, Integer endNum, Integer descNum, Boolean orderByDesc) {
        if(StringUtil.isBlank(sid)){
            return new ArrayList<>();
        }
        EpisodeExample example = new EpisodeExample();
        example.setSid(sid);
        example.setStartNum(startNum);
        example.setEndNum(endNum);
        example.setDescNum(descNum);
        example.setDesc(orderByDesc);
        CompletableFuture<RpcResult<List<StandardEpisodeBO>>> completableFuture = standardEpisodeRpcApi.getStartEndEpisodeBySid(example);
        RpcResult<List<StandardEpisodeBO>> rpcResult = FutureUtil.getFutureIgnoreException(completableFuture, 1, TimeUnit.SECONDS);
        if(rpcResult == null || ResultCode.SUCCESS.getCode() != rpcResult.getCode() || rpcResult.getData() == null){
            log.error("[StandardEpisodeRpcApiProxy.getStartEndEpisodeBySid] rpc error,sid:{},rpcResult:{}", sid, rpcResult);
            return new ArrayList<>();
        }
        return rpcResult.getData();
    }

    /**
     * 通过标准剧头的sid获取其下剧集的数量
     */
    public Integer countStandardEpisodeBySid(String sid) {
        if(StringUtil.isBlank(sid)){
            return null ;
        }
        CompletableFuture<RpcResult<Integer>> completableFuture = standardEpisodeRpcApi.countStandardEpisodeBySid(sid);
        RpcResult<Integer> rpcResult = FutureUtil.getFutureIgnoreException(completableFuture, 1, TimeUnit.SECONDS);
        if(rpcResult == null || ResultCode.SUCCESS.getCode() != rpcResult.getCode() || rpcResult.getData() == null){
            log.error("[StandardEpisodeRpcApiProxy.countStandardEpisodeBySid] rpc error,sid:{},rpcResult:{}", sid, rpcResult);
            return null;
        }
        return rpcResult.getData();
    }

}
