package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.LvContentPoolRpcApi;
import com.heytap.longvideo.client.arrange.entity.MinorPoolCodeRecord;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.search.utils.FutureUtil;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Cache;
import esa.httpclient.cache.shaded.com.github.benmanes.caffeine.cache.Caffeine;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class LvContentPoolRpcApiProxy {

    Cache<String, String> minorsPoolCodeCache = Caffeine.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(32, TimeUnit.MINUTES)
            .build();
    @Reference(providerAppId = "longvideo-arrange-service", protocol = "dubbo", retries = 0)
    private LvContentPoolRpcApi lvContentPoolRpcApi;

    public CompletableFuture<List<MinorPoolCodeRecord>> getMinorsPoolCode(Integer ageCode) {
        return lvContentPoolRpcApi.getMinorsPoolCode(ageCode).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("rpc:getMinorsPoolCode err, ret:{}", ret, e);
                return Collections.emptyList();
            }
            return ret.getData();
        });
    }

    public Collection<String> getMinorsPoolCodes(List<String> ageCodeList) {
        Map<String, String> resultMap;
        Map<String, String> ageCodeCache = minorsPoolCodeCache.getAllPresent(ageCodeList);
        if (ageCodeCache.size() == ageCodeList.size()) {
            return ageCodeCache.values();
        }

        CompletableFuture<Map<String, String>> codeListCf = lvContentPoolRpcApi.getMinorsPoolCodeMapByAgeCodeList(ageCodeList).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode()) {
                log.error("rpc:getMinorsPoolCode err, ret:{}", ret, e);
                return Collections.emptyMap();
            }
            return ret.getData();
        });
        resultMap = FutureUtil.getFutureIgnoreException(codeListCf, 2, TimeUnit.SECONDS);

        if (resultMap != null) {
            minorsPoolCodeCache.putAll(resultMap);
        }
        return minorsPoolCodeCache.getAllPresent(ageCodeList).values();
    }
}
