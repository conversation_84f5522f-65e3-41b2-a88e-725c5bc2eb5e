package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.interfaces.api.LvAlbumConsumeHiveDataRpcApi;
import com.heytap.longvideo.model.entity.LvAlbumConsumeHiveData;
import esa.rpc.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/8/29
 */
@Service
public class LvAlbumConsumeHiveDataRpcApiProxy {

    @Reference(providerAppId = "arrange-default-service", protocol = "dubbo", retries = 0, timeout = 10000)
    private LvAlbumConsumeHiveDataRpcApi lvAlbumConsumeHiveDataRpcApi;

    public CompletableFuture<RpcResult<List<LvAlbumConsumeHiveData>>> getByDayno(String dayno) {
        return lvAlbumConsumeHiveDataRpcApi.getByDayno(dayno);
    }

    public CompletableFuture<RpcResult<LvAlbumConsumeHiveData>> getBySidAndDayno(String sid, String dayno) {
        return lvAlbumConsumeHiveDataRpcApi.getBySidAndDayno(sid, dayno);
    }
}
