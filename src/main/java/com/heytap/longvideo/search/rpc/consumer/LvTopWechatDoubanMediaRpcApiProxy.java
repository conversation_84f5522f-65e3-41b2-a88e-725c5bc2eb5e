package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.search.entity.QuerySidSearch;
import com.heytap.longvideo.client.common.ResultCode;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.LvTopWechatDoubanMediaRpcApi;
import com.heytap.longvideo.client.media.model.dto.TopWechatDoubanMediaListDto;
import com.heytap.longvideo.client.media.query.TopWechatDoubanMediaRequest;
import com.heytap.longvideo.search.utils.FutureUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/17 11:17
 */
@Slf4j
@Service
public class LvTopWechatDoubanMediaRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private LvTopWechatDoubanMediaRpcApi lvTopWechatDoubanMediaRpcApi;

    public TopWechatDoubanMediaListDto listByCache(TopWechatDoubanMediaRequest request){
        CompletableFuture<RpcResult<TopWechatDoubanMediaListDto>> future = lvTopWechatDoubanMediaRpcApi.listByCache(request);
        RpcResult<TopWechatDoubanMediaListDto> rpcResult = FutureUtil.getFutureIgnoreException(future, 1, TimeUnit.SECONDS);
        if(rpcResult == null || ResultCode.SUCCESS.getCode() != rpcResult.getCode() || rpcResult.getData() == null){
            log.error("[lvTopWechatDoubanMediaRpcApi.listByCache] error,request:{},rpcResult:{}", request,rpcResult);
            return null;
        }
        return rpcResult.getData();
    }
}
