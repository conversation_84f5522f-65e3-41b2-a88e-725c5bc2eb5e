package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.arrange.AlbumRankRpcApi;
import com.heytap.longvideo.client.arrange.entity.AlbumRecommendInfo;
import com.heytap.longvideo.client.common.ResultCode;
import esa.rpc.config.annotation.Reference;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: 榜单查询RPC
 * @author: zhangxu
 * @date: 2023年9月15日11:28:22
 */
@Slf4j
@Service
public class AlbumRankRpcApiProxy {
    @Reference(providerAppId = "arrange-list-service", protocol = "dubbo")
    private AlbumRankRpcApi albumRankRpcApi;

    public CompletableFuture<Map<String, AlbumRecommendInfo>> getAlbumRecommendInfo(Set<String> sids, Boolean isHomeScreen) {
        return albumRankRpcApi.getAlbumRecommendInfo(sids, isHomeScreen).handle((ret, e) -> {
            if (ret == null || e != null || ResultCode.SUCCESS.getCode() != ret.getCode() || ret.getData() == null) {
                log.error("rpc:getAlbumRecommendInfo wrong, ret:{}", ret, e);
                return Collections.emptyMap();
            }

            return ret.getData();
        });
    }
}