package com.heytap.longvideo.search.rpc.provider;

import com.heytap.longvideo.common.lib.rpc.RpcResult;
import com.heytap.longvideo.common.lib.utils.BasicValidator;
import com.heytap.longvideo.search.api.UnofficialAlbumEsRpcApi;
import com.heytap.longvideo.search.api.entity.UnofficialAlbumEsDTO;
import com.heytap.longvideo.search.infrastructure.cache.UnofficialAlbumEsCache;
import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import com.heytap.longvideo.search.service.standard.UnofficialAlbumService;
import com.oppo.cpc.video.framework.lib.utils.CollectionUtilsExt;
import esa.rpc.config.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yanping
 * @date 2025/6/24
 */
@Slf4j
@Service
public class UnofficialAlbumEsRpcApiService implements UnofficialAlbumEsRpcApi {

    @Autowired
    private UnofficialAlbumService unofficialAlbumService;

    @Autowired
    private UnofficialAlbumEsCache unofficialAlbumEsCache;

    @Override
    public CompletableFuture<RpcResult<Map<String, UnofficialAlbumEsDTO>>> searchBySidsFilterInvalid(List<String> sids) {
        //参数校验
        BasicValidator.notEmpty("sids", sids);
        log.info("sids:{}", sids);
        Map<String, UnofficialAlbumEsDTO> unofficialAlbumEsDTOMap = new HashMap<>();
        Map<String, UnofficialAlbumEs> unofficialAlbumEsMap = unofficialAlbumEsCache.mget(sids, UnofficialAlbumEs.class,
                UnofficialAlbumEs::getSid, this::getBySidsFromEs);
        if (MapUtils.isNotEmpty(unofficialAlbumEsMap)) {
            log.info("unofficialAlbumEsList is not empty, sids:{}", sids);
            // 过滤失效节目
            unofficialAlbumEsDTOMap = unofficialAlbumEsMap.entrySet().stream()
                    .filter(entry -> entry.getValue().getStatus() == 1)
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                                UnofficialAlbumEsDTO unofficialAlbumEsDTO = new UnofficialAlbumEsDTO();
                                BeanUtils.copyProperties(entry.getValue(), unofficialAlbumEsDTO);
                                return unofficialAlbumEsDTO;
                            }
                    ));
        }
        return CompletableFuture.completedFuture(new RpcResult<>(unofficialAlbumEsDTOMap));
    }

    private Map<String, UnofficialAlbumEs> getBySidsFromEs(Collection<String> sids) {
        List<UnofficialAlbumEs> albumList = unofficialAlbumService.getAlbumList((List<String>) sids);
        return CollectionUtilsExt.index(albumList, UnofficialAlbumEs::getSid);
    }

    @Override
    public CompletableFuture<RpcResult<Map<String, UnofficialAlbumEsDTO>>> searchBySids(List<String> sids) {
        //参数校验
        BasicValidator.notEmpty("sids", sids);
        log.info("sids:{}", sids);
        List<UnofficialAlbumEs> unofficialAlbumEsList = unofficialAlbumService.getAlbumList(sids);
        Map<String, UnofficialAlbumEsDTO> unofficialAlbumEsDTOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unofficialAlbumEsList)) {
            log.info("unofficialAlbumEsList is not empty, sids:{}", sids);
            if (CollectionUtils.isNotEmpty(unofficialAlbumEsList)) {
                for (UnofficialAlbumEs unofficialAlbumEs : unofficialAlbumEsList) {
                    UnofficialAlbumEsDTO unofficialAlbumEsDTO = new UnofficialAlbumEsDTO();
                    BeanUtils.copyProperties(unofficialAlbumEs, unofficialAlbumEsDTO);
                    unofficialAlbumEsDTOMap.put(unofficialAlbumEs.getSid(), unofficialAlbumEsDTO);
                }
            }
        }
        return CompletableFuture.completedFuture(new RpcResult<>(unofficialAlbumEsDTOMap));
    }

    @Override
    public CompletableFuture<RpcResult<Boolean>> updateUnofficialAlbumEs(UnofficialAlbumEsDTO unofficialAlbumEsDTO) {
        try {
            UnofficialAlbumEs unofficialAlbumEs = new UnofficialAlbumEs();
            BeanUtils.copyProperties(unofficialAlbumEsDTO, unofficialAlbumEs);
            unofficialAlbumService.saveOrUpdate(unofficialAlbumEs);
            return CompletableFuture.completedFuture(new RpcResult<>(true));
        } catch (IOException e) {
            log.error("updateUnofficialAlbumImageOcs update unofficialAlbumEs error");
            return CompletableFuture.completedFuture(new RpcResult<>(false));
        }
    }

    @Override
    public CompletableFuture<RpcResult<UnofficialAlbumEsDTO>> queryBySid(String sid) {
        return CompletableFuture.completedFuture(new RpcResult<>(unofficialAlbumService.searchBySid(sid, UnofficialAlbumEsDTO.class)));
    }

    @Override
    public CompletableFuture<RpcResult<Integer>> updateBySId(String sid, String directorStr, String actorStr) {
        if (StringUtils.isEmpty(sid)) {
            throw new RuntimeException("sid is empty");
        }

        UnofficialAlbumEs existUnofficialAlbumEs = unofficialAlbumService.searchBySid(sid, UnofficialAlbumEs.class);
        if (Objects.isNull(existUnofficialAlbumEs)) {
            throw new RuntimeException(String.format("cannot find the unofficial_album={sid=%s} in es", sid));
        }

        existUnofficialAlbumEs.setDirector(directorStr);
        existUnofficialAlbumEs.setActor(actorStr);

        try {
            unofficialAlbumService.saveOrUpdate(existUnofficialAlbumEs);
            return CompletableFuture.completedFuture(new RpcResult<>(200));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}