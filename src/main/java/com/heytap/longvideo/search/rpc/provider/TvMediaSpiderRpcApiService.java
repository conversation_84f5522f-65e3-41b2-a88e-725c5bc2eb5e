package com.heytap.longvideo.search.rpc.provider;

import com.heytap.longvideo.common.lib.rpc.RpcResult;
import com.heytap.longvideo.search.api.TvMediaSpiderRpcApi;
import com.heytap.longvideo.search.api.model.request.TvMediaScrollSearchRequest;
import com.heytap.longvideo.search.api.model.response.TvMediaScrollSearchResponse;
import com.heytap.longvideo.search.service.spider.TvMediaSpiderService;
import esa.rpc.config.annotation.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/5/27 下午7:27
 */

@Service
@Slf4j
public class TvMediaSpiderRpcApiService implements TvMediaSpiderRpcApi {

    @Autowired
    private TvMediaSpiderService tvMediaSpiderService;

    @Override
    public CompletableFuture<RpcResult<TvMediaScrollSearchResponse>> getTvMediaAll(TvMediaScrollSearchRequest request) {
        try {
            return CompletableFuture.completedFuture(new RpcResult<>(tvMediaSpiderService.tvMediaSpiderAll(request)));
        } catch (Exception e) {
            log.error("tv media spider all error!", e);
        }
        return CompletableFuture.completedFuture(new RpcResult<>(null));
    }

    @Override
    public CompletableFuture<RpcResult<TvMediaScrollSearchResponse>> getTvMediaIncr(TvMediaScrollSearchRequest request) {
        try {
            return CompletableFuture.completedFuture(new RpcResult<>(tvMediaSpiderService.tvMediaSpiderIncr(request)));
        } catch (Exception e) {
            log.error("tv media spider incr error!", e);
        }
        return CompletableFuture.completedFuture(new RpcResult<>(null));
    }
}
