package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.common.lib.rpc.ResultCode;
import com.heytap.longvideo.search.utils.FutureUtil;
import com.heytap.longvideo.search.utils.JacksonUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class StandardAlbumRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 1)
    private StandardAlbumRpcApi standardAlbumRpcApi;

    public Map<String, StandardAlbum> getBySidsFilterInvalid(List<String> sidList) {
        CompletableFuture<RpcResult<Map<String, StandardAlbum>>> albumFuture = standardAlbumRpcApi.getBySidsFilterInvalid(sidList);
        RpcResult<Map<String, StandardAlbum>> albumRpcResult = FutureUtil.getFutureIgnoreException(albumFuture, 1, TimeUnit.SECONDS);
        if (null == albumRpcResult || albumRpcResult.getCode() != ResultCode.SUCCESS.getCode() || albumRpcResult.getData() == null) {
            log.error("albumRpcApiProxy.getBySid code error,sid:{},result:{}", sidList, JacksonUtil.toJSONString(albumRpcResult));
            return Collections.emptyMap();
        }
        return albumRpcResult.getData();
    }

    public StandardAlbum getBySid(String sid) {
        try {
            RpcResult<StandardAlbum> rpcResult = standardAlbumRpcApi.getBySid(sid).get(5, TimeUnit.SECONDS);
            if (Objects.isNull(rpcResult)) {
                log.error("standardAlbumRpcApi.getBySid({}) return null", sid);
                return null;
            }

            return rpcResult.getData();
        } catch (Exception e) {
            log.error("standardAlbumRpcApi.getBySid({}) error:", sid, e);
            return null;
        }
    }
}