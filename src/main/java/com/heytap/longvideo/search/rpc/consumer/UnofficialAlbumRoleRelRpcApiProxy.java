package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.media.UnofficialAlbumRoleRelRpcApi;
import com.heytap.longvideo.client.media.entity.UnofficialAlbumRoleRel;
import com.heytap.longvideo.client.media.query.RoleExample;
import com.heytap.longvideo.common.lib.rpc.RpcResult;
import com.heytap.longvideo.search.utils.FutureUtil;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Description: 非合作方节目与腾讯库影人关系 rpc
 * @Author: 80398885WT
 * @Date: 2025/6/30
 */
@Slf4j
@Service
public class UnofficialAlbumRoleRelRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo", retries = 0)
    private UnofficialAlbumRoleRelRpcApi unofficialAlbumRoleRelRpcApi;

    public Integer saveOrUpdate(UnofficialAlbumRoleRel unofficialAlbumRoleRel) {
        RpcResult<Integer> rpcResult = FutureUtil.getFutureIgnoreException(
                unofficialAlbumRoleRelRpcApi.saveOrUpdate(unofficialAlbumRoleRel));
        if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.getData())) {
            return 0;
        }

        return rpcResult.getData();
    }

    public UnofficialAlbumRoleRel queryOne(RoleExample example) {
        RpcResult<UnofficialAlbumRoleRel> rpcResult = FutureUtil.getFutureIgnoreException(
                unofficialAlbumRoleRelRpcApi.queryOne(example));
        if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.getData())) {
            return null;
        }

        return rpcResult.getData();
    }

    public Integer deleteLockedActorAndDirector(String sid, boolean actorLock, boolean directorLock) {
        RpcResult<Integer> rpcResult = FutureUtil.getFutureIgnoreException(
                unofficialAlbumRoleRelRpcApi.deleteLockedActorAndDirector(sid, actorLock, directorLock));
        if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.getData())) {
            return 0;
        }

        return rpcResult.getData();
    }
}