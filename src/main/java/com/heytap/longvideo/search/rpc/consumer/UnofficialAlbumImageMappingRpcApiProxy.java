package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.UnofficialAlbumImageMappingRpcApi;
import com.heytap.longvideo.client.media.entity.UnofficialAlbumOriginalImageMappingOcs;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> ye mengsheng
 * @Description：
 * @Version: 1.0
 * @date 2025/7/1 下午8:51
 */

@Slf4j
@Service
public class UnofficialAlbumImageMappingRpcApiProxy {
    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private UnofficialAlbumImageMappingRpcApi unofficialAlbumImageMappingRpcApi;

    public CompletableFuture<RpcResult<List<UnofficialAlbumOriginalImageMappingOcs>>> findByOriginalUrl(String sid) {
        return unofficialAlbumImageMappingRpcApi.findByOriginalUrl(sid);
    }

    public void batchAdd(List<UnofficialAlbumOriginalImageMappingOcs> urlMappingOcsUrls) {
        unofficialAlbumImageMappingRpcApi.batchAdd(urlMappingOcsUrls);
    }
}
