package com.heytap.longvideo.search.rpc.consumer;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.VirtualProgramRelationRpcApi;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import esa.rpc.config.annotation.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/*
 * Description  虚拟节目
 * Date 17:39 2022/3/30
 * Author songjiajia 80350688
 */
@Slf4j
@Service
public class VirtualProgramRpcApiProxy {

    @Reference(providerAppId = "longvideo-media-service", protocol = "dubbo")
    private VirtualProgramRelationRpcApi relationRpcApi;

    public CompletableFuture<RpcResult<MisVirtualProgramRelation>> queryBySid(String sid) {
        return relationRpcApi.queryBySid(sid);
    }

    public CompletableFuture<RpcResult<List<MisVirtualProgramRelation>>> queryByVirtualSid(String virtualSid) {
        return relationRpcApi.queryByVirtualSid(virtualSid);
    }
}
