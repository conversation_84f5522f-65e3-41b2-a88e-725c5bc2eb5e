package com.heytap.longvideo.search.mapper.media;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heytap.longvideo.client.media.entity.MisVideoOrg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MisVideoOrgMapper extends BaseMapper<MisVideoOrg> {

    List<MisVideoOrg> getOrgVideoBatchByAlbumId(@Param("databaseIndex") Integer databaseIndex, @Param("tableIndex") Integer tableIndex,
                                                @Param("sourceAlbumId") String sourceAlbumId, @Param("id") Long id, @Param("size") Integer size);

    List<MisVideoOrg> getOrgVideoBatch(@Param("databaseIndex") Integer databaseIndex, @Param("tableIndex") Integer tableIndex,
                                       @Param("source") String source, @Param("id") Long id, @Param("size") Integer size);

    void updateStatus(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("sourceStatus") String sourceStatus,
                      @Param("sourceAlbumId") String sourceAlbumId, @Param("sourceVideoId") String sourceVideoId);
}