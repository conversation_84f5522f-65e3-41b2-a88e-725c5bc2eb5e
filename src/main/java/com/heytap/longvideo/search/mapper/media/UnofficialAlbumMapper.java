package com.heytap.longvideo.search.mapper.media;

import com.heytap.longvideo.search.model.entity.es.UnofficialAlbumEs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/10/28 19:13
 */
public interface UnofficialAlbumMapper {

    List<UnofficialAlbumEs> selectUnofficialAlbumList(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime, @Param("start") int start, @Param("size") int size);

    int selectStandardAlbumCount(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime);
}
