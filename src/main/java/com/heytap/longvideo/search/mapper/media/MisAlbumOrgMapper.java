package com.heytap.longvideo.search.mapper.media;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.heytap.longvideo.client.media.entity.MisAlbumOrg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MisAlbumOrgMapper extends BaseMapper<MisAlbumOrg> {

    MisAlbumOrg getOrgAlbumByAlbumId(@Param("databaseIndex") Integer databaseIndex, @Param("tableIndex") Integer tableIndex,
                                     @Param("sourceAlbumId") String sourceAlbumId);

    List<MisAlbumOrg> getOrgAlbumBatch(@Param("databaseIndex") Integer databaseIndex, @Param("tableIndex") Integer tableIndex,
                                       @Param("source") String source, @Param("id") Long id, @Param("size") Integer size);

    int updateStatus(@Param("databaseIndex") Integer databaseIndex, @Param("tableIndex") Integer tableIndex,
                     @Param("sourceStatus") String sourceStatus, @Param("sourceAlbumId") String sourceAlbumId);
}