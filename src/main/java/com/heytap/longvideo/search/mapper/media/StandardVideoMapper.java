package com.heytap.longvideo.search.mapper.media;


import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StandardVideoMapper {

        List<StandardVideoEs> selectStandardVideoList(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime, @Param("id") Long id, @Param("size") int size);

        int selectStandardVideoCount(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime);

}
