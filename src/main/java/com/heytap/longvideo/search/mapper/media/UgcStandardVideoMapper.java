package com.heytap.longvideo.search.mapper.media;

import com.heytap.longvideo.search.model.entity.es.StandardVideoEs;
import com.heytap.longvideo.search.model.entity.es.UgcStandardVideoEs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 15:15
 */
public interface UgcStandardVideoMapper {
    List<UgcStandardVideoEs> selectUgcStandardVideoList(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime, @Param("id") Long id, @Param("size") int size);

    int selectUgcStandardVideoCount(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime);
}
