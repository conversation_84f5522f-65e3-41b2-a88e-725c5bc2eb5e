package com.heytap.longvideo.search.mapper.media;


import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.model.entity.es.ProgramAlbumEs;
import com.heytap.longvideo.search.model.entity.es.StandardAlbumEs;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface StandardAlbumMapper {

    List<StandardAlbumEs> selectStandardAlbumList(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime, @Param("start") int start, @Param("size") int size);

    int selectStandardAlbumCount(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("updateTime") String updateTime);

    List<ProgramAlbumEs> selectStandardAlbumListForApp(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex ,@Param("sourceList") List sourceList);

    StandardAlbumEs selectStandardAlbumBySid(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("sid") String sid,@Param("status") int status);

    StandardAlbum getStandardAlbumBySid(@Param("databaseIndex") int databaseIndex, @Param("tableIndex") int tableIndex, @Param("sid") String sid);

}
