package com.heytap.longvideo.search.model.param.outside;

import lombok.Data;

/**
 * <AUTHOR> ye mengsheng
 * @Description：对外搜索按钮文案配置
 * @Version: 1.0
 * @date 2025/6/5 下午5:24
 */

@Data
public class OutSideSearchButtonConfig {

    /**
     * 免费节目使用的按钮文案
     */
    private String freeText;

    /**
     * 需会员身份观看的节目使用的按钮文案，包括芒果会员、影视会员、优酷会员
     */
    private String vipText;

    /**
     * 免费解锁的节目使用的按钮文案，仅当app版本及当前用户支持免费解锁功能时下发，依赖全搜在请求参数中增加ssoid和kkua
     */
    private String taskForEpisodeText;

    /**
     * 限时免费的节目使用的按钮文案
     */
    private String timeLimitedFreeText;

    /**
     * 非在库内容(全网搜)使用的文案
     */
    private String outOfStockText;

    /**
     * 单点付费的节目使用的文案
     */
    private String payText;

    /**
     * 默认文案，当无法判断节目类型/无上述节目类型时，使用当前文案
     */
    private String defaultText = "0";

}