package com.heytap.longvideo.search.model.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR> 80237102
 * @date 2021/8/20 14:20
 */
@Data
public class IdGeneratorResult {

    private int code;

    private String message;

    private RespResult data;

    public String getId(){
        return data.getResourceId();
    }

    @Data
    public class RespResult{
        @JSONField(name = "biz_name")
        private String bizName;
        @JSONField(name = "cp_doc_id")
        private String cpDocId;
        @JSONField(name = "cp_channel")
        private String cpChannel;
        @JSONField(name = "cp_channel_name")
        private String cpChannelName;
        @JSONField(name = "resource_id")
        private String resourceId;

    }

}
