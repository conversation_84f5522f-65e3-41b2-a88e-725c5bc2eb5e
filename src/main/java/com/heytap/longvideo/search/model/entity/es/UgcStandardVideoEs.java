package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.util.Date;

/**
 * <AUTHOR> Yanping
 * @date 2022/10/24 11:49
 */
@Data
@Document(indexName = IndexNameConstant.UGC_STANDARD_VIDEO_INDEX)
@Setting(settingPath = "/esConfig/setting.json")
public class UgcStandardVideoEs {
    @Id
    private String uniKey;

    @Field(type = FieldType.Keyword)
    private String vid;

    @Field(type = FieldType.Keyword)
    private String sid;

    private String sourceAuthorId;

    private String sourceVideoId;

    private String source;

    @Field(type = FieldType.Text)
    private String title;

    @Field(type = FieldType.Keyword)
    private String verticalIcon;

    @Field(type = FieldType.Keyword)
    private String horizontalIcon;

    private Integer duration;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String tag;

    private String programType;

    private Integer screenMode;

    @Field(type = FieldType.Text)
    private String authorName;

    private String authorPhoto;

    private Integer sourceStatus;

    private Integer status;

    private Date publishTime;

    private Date createTime;

    private Date updateTime;

    private Long idOffSet;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String subTag;

    /**
     * @Description: 关联的长视频media_id（风行短视频）
     */
    private String sourceMediaId;

    /**
     * @Description: 题材（风行短视频）
     */
    private String category;

    /**
     * @Description: 关联的长视频sid（风行短视频）
     */
    @Field(type = FieldType.Keyword)
    private String linkSid;

    /**
     * @Description: 关联的长视频标题（风行短视频）
     */
    private String linkTitle;

    /**
     * @Description: 播放地址（风行短视频）
     */
    private String playUrl;

    /**
     * @Description: 视频频道
     */
    private String channel;

    /**
     * @Description: 频道id
     */
    private String channelId;
}