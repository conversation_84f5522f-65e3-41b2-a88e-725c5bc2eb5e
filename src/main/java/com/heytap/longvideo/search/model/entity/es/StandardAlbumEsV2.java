package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;


/*
 * Description 标准化
 * Date 10:43 2022/3/21
 * Author songjiajia 80350688
 */
@Data
@Document(indexName = IndexNameConstant.STANDARD_ALBUM_INDEX)
public class StandardAlbumEsV2 {

    @Id
    private String sid;

    private String title;

    private String subTitle;

    private String markCode;

    private String source;

    private String verticalIcon;

    private String horizontalIcon;

    private Integer featureType;

    private Integer payStatus;

    private String copyrightCode;

    private String programInfo;

    private String language;

    private Integer vipType;

    private Integer year;

    private String brief;

    private String tags;

    private String actor;

    private String director;

    private String area;

    private String programType;

    private String subProgramType;

    private String information;

    private String sourceScore;

    private String oppoScore;

    private String showTime;

    private Integer dayNo;

    private Long oppoHot;

    private Float sourceHot;

    private String sourceWebUrl;
}
