package com.heytap.longvideo.search.model;

import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.heytap.video.client.entity.video.ActorInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class SearchInterveneCardResponse implements Serializable{
    private static final long serialVersionUID = 1;
    private String title;
    private String code;
    private String deepLink;
    private Boolean hasMore = false;
    private List<SortType> sortTypeList;
    private List<String> filterList;
    private List<KeyWordSearchResponse> contents = new ArrayList<>();

    /**
     * 是否来源于算法，
     * 0：否， 1：是
     */
    private Integer aiSource = 0;

    /**
     * 算法透传字段
     */
    private String transparent;

    /**
     * 是否存在结果干预（影响影人卡与结果卡的排序）
     */
    private Boolean hasIntervene = false;

    /**
     * 影人信息
     */
    private ActorInfo actorInfo;

    @Data
    public static class SortType implements Serializable {
        private static final long serialVersionUID = 1;
        /**
         * 排序方式
         * 1：最热排序
         * 2：最新排序
         */
        private Integer sortType;

        /**
         * 排序方式名称
         */
        private String sortName;
    }
}
