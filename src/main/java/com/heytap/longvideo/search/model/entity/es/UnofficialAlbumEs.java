package com.heytap.longvideo.search.model.entity.es;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.InnerField;
import org.springframework.data.elasticsearch.annotations.MultiField;
import org.springframework.data.elasticsearch.annotations.Setting;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 非合作内容库 - 剧头索引定义
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/10/23 17:04
 */
@Data
@Setting(settingPath = "/esConfig/shardingSetting.json")
@Document(indexName = IndexNameConstant.UNOFFICIAL_ALBUM_INDEX)
public class UnofficialAlbumEs {

    /**
     * 节目唯一id
     */
    @Id
    @NotEmpty(message = "sid is required")
    private String sid;

    /**
     * 标签列表
     */
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String tags;

    /**
     * 演员（或者嘉宾）
     */
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String actor;

    /**
     * 导演（或者主持人）
     */
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String director;

    /**
     * 地区
     */
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String area;

    /**
     * 语言
     */
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String language;

    /**
     * 节目类型
     */
    @Field(type = FieldType.Keyword)
    private String programType;

    @Field(type = FieldType.Keyword)
    private String subProgramType;

    /**
     * 简介信息
     */
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DEFAULT_INSERT_ANALYZER)
    private String information;

    /**
     * 源站评分 如 优酷评分，腾讯评分
     */
    @Field(type = FieldType.Keyword)
    private String sourceScore;

    /**
     * 上映时间
     */
    @Field(type = FieldType.Keyword)
    private String showTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Integer dayNo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long oppoHot;

    @Field(type = FieldType.Long)
    private Long sidL;

    @Field(type = FieldType.Integer)
    private Integer hasPreview;

    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String mappingTags;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last7DaysPlayPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last15DaysPlayPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last30DaysPlayPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last7DaysClickPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last15DaysClickPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last30DaysClickPv;

    /**
     * 最新高能视频ID
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String highLightVid;

    /**
     * 节目标题
     */
    @MultiField(mainField = @Field(type = FieldType.Text),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String title;

    /**
     * 节目副标题
     */
    @Field(type = FieldType.Text)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String subTitle;

    /**
     * 媒资后台:0:自动更新,1:锁定视频matadata,2:锁定节目
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer managerStatus;

    /**
     * 下发时以这个字段值为准判断状态:-2:黑名单,-1:删除,0:失效,1:生效,2:系统失效,3:合并失效,4:探测失效,5:源下线,6:注入失效
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    /**
     * 节目源状态，0：不可用 1：可用
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sourceStatus;

    /**
     * 牌照方审核状态，0:审核不通过，1审核通过，与source_status整合
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer verifyStatus;

    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotEmpty(message = "source is required")
    private String source;

    /**
     * 合作方专辑ID
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotEmpty(message = "sourceAlbumId is required")
    private String sourceAlbumId;

    /**
     * 源站的URL地址
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sourceWebUrl;

    /**
     * 入库原因:0：自动注入;1：人工新增;
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sourceType;

    /**
     * 剧头单位 如，期 部 话等 0：默认 ，1：集，2：期，3:话，4：番
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer unit;

    /**
     * 专辑分类信息（用于分类标签）
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String category;

    /**
     * 时长
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer duration;

    /**
     * 年代
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer year;

    /**
     * 获奖信息
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String honor;

    /**
     * 有效剧集数
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer validEpisode;

    /**
     * 总集数
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer totalEpisode;

    /**
     * 是否已完结 0：未完结 1 已完结
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer completed;

    /**
     * 简短推荐语
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String brief;

    /**
     * 更新周期
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String period;

    /**
     * 海报小竖图
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String verticalIcon;

    /**
     * 海报小竖图OCS地址
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String verticalIconOcs;


    /**
     * 海报小横图
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String horizontalIcon;

    /**
     * 海报小横图OCS地址
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String horizontalIconOcs;

    /**
     * 海报大竖图
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String verticalImage;

    /**
     * 海报大竖图OCS地址
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String verticalImageOcs;

    /**
     * 海报大横图
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String horizontalImage;

    /**
     * 海报大横图OCS地址
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String horizontalImageOcs;

    /**
     * 竖图图片背景色值 H S(18%) B(84%)
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String backGroundColor;

    /**
     * 竖图图片背景色值 H S(55%) B(33%)
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String backGroundColorTwo;

    /**
     * 图片背景色值
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String backGroundColorJson;

    /**
     * 1：正片， 2：预告片，3：微电影，4，精彩看点
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer featureType;

    /**
     * 高危标识,0-否，1-是
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer riskFlag;

    /**
     * 版权标识,0-否，1-是
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer copyright;

    /**
     * 版权方标识
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String copyrightCode;

    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String programInfo;

    /**
     * 1:PC,2:mobile
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer medium;

    /**
     * 0:免费，1：会员免费，2：单片付费
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer payStatus;

    /**
     * 0-所有人能看 1-会员能看
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer vipType;

    /**
     * 单片付费类型: 0-非单片付费，1-单片付费
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer chargeType;

    /**
     * 价格,可能有多个价格，需要扩展字段
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer price;

    /**
     * 关键词列表(一般用于搜索),搜索用,其他字段组合而来,看是否删掉
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String keyword;

    /**
     * 扩展信息
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String extraInfo;

    /**
     * 原始播放量
     */
    @Field(type = FieldType.Long)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long sourcePlayCount;

    /**
     * 供应类型:special-特供，sole独播,normal-普通
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String supplyType;

    /**
     * 清晰度
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String definition;

    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String downloadMarkcode;

    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer downloadAble;

    /**
     * 会员角标
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String markCode;

    /**
     * 单买节目原始价格
     */
    @Field(type = FieldType.Scaled_Float, scalingFactor = 100)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal formerPrice;

    /**
     * 单买节目现价格
     */
    @Field(type = FieldType.Scaled_Float, scalingFactor = 100)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal nowPrice;

    /**
     * 单买节目VIP价格
     */
    @Field(type = FieldType.Scaled_Float, scalingFactor = 100)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal vipPrice;

    /**
     * 付费观看有效天数
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer payEffectDays;

    /**
     * 发布时间
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date publishTime;

    /**
     * 标准化处理状态
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer processStatus;

    /**
     * 创建时间/入库时间
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    /**
     * 最近一次更新时间
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;

    /**
     * 剧头热度
     */
    @Field(type = FieldType.Double)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double sourceHot;

    /**
     * 直播开始时间
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date startTime;

    /**
     * 直播结束时间
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date endTime;

    /**
     * 源系列id
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sourceSeriesId;

    /**
     * 是否预推 0：否  1：是
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer prePush;

    /**
     * 预推上线时间
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date preOnlineTime;

    /**
     * 预告片关系Json
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String previewInfo;

    /**
     * 图谱标签
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tuputag;

    /**
     * 赛事id
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String competitionId;

    /**
     * 赛事名称
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String competitionName;

    /**
     * 赛季id
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String seasonId;

    /**
     * 赛季名称
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String seasonName;

    /**
     * 参赛方 0-人物，1-队伍，3-ufc，4-团体 5-双打
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer participantType;

    /**
     * 赛事类型， 0:非对抗赛 1:对抗赛
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer competitionType;

    /**
     * 阶段
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String phase;

    /**
     * 小组
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String matchGroup;


    /**
     * 节目开始时间（不同于赛事开始时间，直播开始了，比赛不一定开始了）
     */
    @Field(type = FieldType.Date)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date matchStartTime;

    /**
     * 轮次
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String round;

    /**
     * 参赛国家列表，逗号隔开
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String participantCountrys;

    /**
     * 大项名称（奥运赛事用）
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sportName;

    /**
     * 小项名称（奥运赛事用）
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String eventName;

    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String presenter;

    /**
     * 最小适龄
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer fitAgeMin;

    /**
     * 最大适龄
     */
    @Field(type = FieldType.Integer)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer fitAgeMax;

    /**
     * cp方适龄原始内容
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String fitAge;

    /**
     * 排序分数
     */
    @Field(type = FieldType.Float, index = false)
    private Float sortDefine;

    /**
     * 播放的版权方列表
     */
    private List<String> copyrightCodeList;

    /**
     * 人工配置的播放链接
     */
    @Field(type = FieldType.Keyword)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String manualWebUrl;

    /**
     * 人工配置的播放链接开关
     */
    @Field(type = FieldType.Boolean)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean manualWebUrlPriority;
}