package com.heytap.longvideo.search.model.unofficial.response;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
public class YunheTopRankResp {
    private Long date;
    private List<YunheTopRank> content;

    @Data
    @ToString
    public static class YunheTopRank {
        // 播放次数
        private Long playTimes;

        // 点赞数
        private Integer up;

        // 评论数
        private Integer commentCount;

        // 弹幕数
        private Integer barrageCount;

        // 绝对排名预测
        private Integer rankPredictedAbs;

        // 相对排名预测
        private Integer rankPredictedRel;

        // 数据类型
        private String dataType;

        // 名称ID
        private Integer nameID;

        // 上线天数
        private Integer occurDays;

        // 排名变化
        private Integer status;

        // 是否为新内容
        private Integer isNew;

        // 上线时间（时间戳）
        private Long releaseTime;

        // 是否为竖屏内容
        private Boolean isVertical;

        // 当前集数
        private Integer episodeNum;

        // 总集数
        private Integer totalEpisodeNum;

        // 名称
        private String name;

        // 渠道
        private String channel;

        // 节目类型
        private String channelType;

        // 日期（格式化字符串）
        private String date;

        // 是否为网络剧
        private Integer isNet;

        // 市场占有率
        private BigDecimal marketShare;

        // 黄金指数
        private BigDecimal goldIndex;

        // 云合评级数据
        private List<YunheLevel> yunheLevel;
    }
}
