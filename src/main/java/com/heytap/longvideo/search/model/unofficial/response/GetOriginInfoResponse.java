package com.heytap.longvideo.search.model.unofficial.response;

import lombok.Data;

/**
 * @Description: 获取非合作内容方节目锁定信息 - 响应体
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/14 17:04
 */
@Data
public class GetOriginInfoResponse {

    /**
     * @Description: 节目类型
     */
    private String programType;

    /**
     * @Description: 简介
     */
    private String information;

    /**
     * @Description: 推荐语
     */
    private String brief;

    /**
     * @Description: 语言
     */
    private String language;

    /**
     * @Description: 海报小横图
     */
    private String horizontalIcon;

    /**
     * @Description: 海报大横图
     */
    private String horizontalImage;

    /**
     * @Description: 海报小竖图
     */
    private String verticalIcon;

    /**
     * @Description: 海报大竖图
     */
    private String verticalImage;

    /**
     * @Description: 评分
     */
    private String sourceScore;

    /**
     * @Description: 类型标签，对应媒资方标签
     */
    private String tags;

    /**
     * @Description: 映射标签
     */
    private String category;

    /**
     * @Description: 更新周期
     */
    private String period;

    /**
     * @Description: 更新信息
     */
    private String programInfo;


    /**
     * @Description: 被锁定字段
     */
    private String lockedFields;
}
