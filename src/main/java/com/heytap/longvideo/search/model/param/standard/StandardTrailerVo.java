package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.entity.StandardTrailer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Data
public class StandardTrailerVo extends StandardTrailer {

    private String id;

    private String tid;

    private String sid;

    private String vid;

    private String linkValue;

    private String source;

    private String sourceSite;

    private String sourceVideoId;

    private String title;

    private String verticalIcon;

    private String horizontalIcon;

    private Integer status;

    private Integer sourceStatus;

    private Integer trailerType;

    private Integer orderNum;

    private Integer payStatus;

    private String markCode;

    private Integer riskFlag;

    private Integer duration;

    private Long sourcePlayCount;

    private Date createTime;

    private Date updateTime;

    private Integer headTime;

    private Integer tailTime;

    private String url;

    private int originStatus;

    private String sourceVideoType;

    private String sourceVipType;

    /**
     * 剧集信息
     */
    private String epsiodeTitle;
    /**
     * 剧头信息
     */
    private String albumTitle;
    private String score;
    private Integer albumFeatureType;
    private String albumProgramType;
    private Integer albumStatus;
    private Integer albumCompleted;
    private Integer albumPayStatus;
    private String albumTags;
    private String albumArea;
    private Integer albumYear;
    private String albumLanguage;
    private String albumShowTime;
    private String albumBrief;
    private String albumDirector;
    private String albumActor;
    private String albumInformation;
    private String albumProgramInfo;
    private String albumHorizontalIcon;
    private String albumVerticalIcon;
    private String sourceAlbumId;
}
