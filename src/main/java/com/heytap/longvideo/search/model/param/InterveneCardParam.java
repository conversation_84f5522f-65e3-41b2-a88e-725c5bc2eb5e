package com.heytap.longvideo.search.model.param;

import com.heytap.longvideo.search.utils.StringUtil;
import com.heytap.video.ad.common.entity.req.BuriedCommonReqProperty;
import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.ToString;

import java.util.List;


/*
 * Description 标签卡/系列卡/推荐卡/影人卡 二级页入参
 * Date 15:07 2023/10/23
 * Author songjiajia 80350688
 */
@Data
@ToString
public class InterveneCardParam extends BuriedCommonReqProperty {
    private String code;

    /**
     * 排序方式，1：最热排序  2：最新排序  3：免费优先
     */
    private Integer sortType = 1;

    /**
     * 节目类型
     */
    private String contentType;

    private int pageIndex = 1;

    private Integer pageSize = 12;

    /**
     * video_vip、mongo_video_vip
     */
    private String vipType;

    private Integer version = 70200;

    /**
     * 快应用版本 样式90200
     */
    private Integer quickEngineVersion;

    /**
     * sid黑名单，逗号分割
     */
    private String sidBlackList;

    /**
     * 调用场景
     * SEARCH_PAGE：搜索综合页   SERIES_PAGE：系列卡二级页
     */
    private String scene;

    private String appVersion;


    public String getVipType() {
        if (StringUtil.isBlank(vipType)) {
            vipType = "default";
        }
        return vipType;
    }
}
