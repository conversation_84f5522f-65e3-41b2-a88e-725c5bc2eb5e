package com.heytap.longvideo.search.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

/*
 * Description 媒资系统返回数据格式
 * Date 15:00 2022/3/21
 * Author songjiajia 80350688
*/

@Data
public class StandardResult<T> {

    private int code;

    private String msg = "";

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date cacheDate;

    private long cost = 0L;

    private T data;


    public static <T> StandardResult<T> success(T data) {
        return new StandardResult(data);
    }

    public static <T> StandardResult<T> fail(int code,String msg) {
        return new StandardResult(code,msg);
    }

    private StandardResult(T data) {
        this.code = 200;
        this.cacheDate = new Date();
        this.data = data;
    }

    private StandardResult(int code,String msg) {
        this.code = code;
        this.cacheDate = new Date();
        this.msg = msg;
    }

    public StandardResult() {
    }
}
