package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.client.media.entity.StandardTrailer;
import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Data
@Document(indexName = IndexNameConstant.STANDARD_TRAILER_INDEX)
@Setting(settingPath = "/esConfig/setting.json")
public class StandardTrailerEs extends StandardTrailer {
    @Id
    private String tid;

    @Field(type = FieldType.Keyword)
    private String sid;

    @Field(type = FieldType.Keyword)
    private String vid;

    @Field(type = FieldType.Keyword)
    private String eid;

    @Field(type = FieldType.Keyword)
    private String source;

    @Field(type = FieldType.Keyword)
    private String url;

    @Field(type = FieldType.Keyword)
    private String sourceVideoId;

    @Field(type = FieldType.Keyword)
    private String sourceSite;

    @Field(type = FieldType.Keyword)
    private String verticalIcon;

    @Field(type = FieldType.Keyword)
    private String horizontalIcon;

    @Field(type = FieldType.Keyword)
    private String markCode;

    @Field(type = FieldType.Long)
    private Long tidL;

    /**
     * 剧集信息
     */
    private String epsiodeTitle;

    /**
     * 剧头信息
     */
    private String albumTitle;
    private String score;
    private Integer albumFeatureType;
    private String albumProgramType;
    private Integer albumStatus;
    private Integer albumCompleted;
    private Integer albumPayStatus;
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String albumTags;
    private String albumArea;
    private Integer albumYear;
    private String albumLanguage;
    private String albumShowTime;
    private String albumBrief;
    private String albumDirector;
    private String albumActor;
    private String albumInformation;
    private String albumProgramInfo;
    private String albumHorizontalIcon;
    private String albumVerticalIcon;
    private String sourceAlbumId;
}
