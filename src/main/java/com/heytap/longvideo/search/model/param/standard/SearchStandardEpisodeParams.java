package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Data
public class SearchStandardEpisodeParams extends PageRequestParam {

    @EsField
    private String sid;

    @EsField
    private String vid;

    @EsField
    private String source;

    @EsField
    private String eid;

    @EsField
    private String title;

    @EsField
    private String featureType;

    @EsField
    private String payStatus;

    @EsField
    private String status;

    @EsField
    private String sourceStatus;

    @EsField
    private String sportsVideoType;

    private String originStatus;

    private String order;

    private boolean preciseFlag;
}
