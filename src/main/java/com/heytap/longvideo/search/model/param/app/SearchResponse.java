package com.heytap.longvideo.search.model.param.app;

import com.heytap.longvideo.search.constants.SearchTabEnum;
import com.heytap.longvideo.search.model.SearchInterveneCardResponse;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/*
 * Description 搜索返回对象
 * Date 11:33 2021/11/29
 * Author songjiajia 80350688
 */
@Data
@ToString
public class SearchResponse {

    private List<KeyWordSearchResponse> longVideoSearchResult;
    private Integer hasMore = 0;
    private int pageIndex;
    private int pageSize;
    private SearchInterveneCardResponse longVideoRecommend;
    private SearchInterveneCardResponse longVideoSeries;
    private SearchInterveneCardResponse longVideoTag;
    private SearchInterveneCardResponse longVideoDefaultRecommend;
    private SearchInterveneCardResponse longVideoActor;
    private List<LvDrawerItemVO> longVideoBannerList;
    private String searchTab;


    public static SearchResponse emptyResult() {
        SearchResponse responseV2 = new SearchResponse();
        responseV2.setHasMore(0);
        responseV2.setPageIndex(1);
        responseV2.setPageSize(0);
        responseV2.setLongVideoSearchResult(new ArrayList<>());
        return responseV2;
    }

    public static SearchResponse singleResult(KeyWordSearchResponse keyWordSearchResponse) {
        SearchResponse responseV2 = new SearchResponse();
        responseV2.setHasMore(0);
        responseV2.setPageIndex(1);
        responseV2.setPageSize(1);
        List<KeyWordSearchResponse> list = new ArrayList<>();
        if (keyWordSearchResponse != null) {
            list.add(keyWordSearchResponse);
        }
        responseV2.setLongVideoSearchResult(list);
        return responseV2;
    }

    public static SearchResponse duanjuResult(List<KeyWordSearchResponse> duanjuSearchResult, Integer pageIndex, boolean hasMore) {
        SearchResponse searchResponse = new SearchResponse();
        searchResponse.setLongVideoSearchResult(duanjuSearchResult);
        searchResponse.setHasMore(hasMore ? 1 : 0);
        searchResponse.setPageIndex(pageIndex);
        searchResponse.setSearchTab(SearchTabEnum.DUANJU.getCode());
        return searchResponse;
    }
}