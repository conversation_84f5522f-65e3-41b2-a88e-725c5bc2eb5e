package com.heytap.longvideo.search.model.unofficial.request;

import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 编辑节目管理状态 - 请求体
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/15 11:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EditAlbumRequest extends NextRequest {

    /**
     * 节目唯一id
     */
    private String sid;

    /**
     * 标签列表
     */
    private String tags;

    /**
     * 演员（或者嘉宾）
     */
    private String actor;

    /**
     * 导演（或者主持人）
     */
    private String director;

    /**
     * 地区
     */
    private String area;

    /**
     * 语言
     */
    private String language;

    /**
     * 节目类型
     */
    private String programType;

    private String subProgramType;

    /**
     * 简介信息
     */
    private String information;

    /**
     * 源站评分 如 优酷评分，腾讯评分
     */
    private String sourceScore;

    /**
     * 上映时间
     */
    private String showTime;

    private Integer dayNo;

    private Long oppoHot;

    private Long sidL;

    private Integer hasPreview;

    private String mappingTags;

    /**
     * 最新高能视频ID
     */
    private String highLightVid;

    /**
     * 节目标题
     */
    private String title;


    /**
     * 节目副标题
     */
    private String subTitle;

    /**
     * 媒资后台:0:自动更新,1:锁定视频matadata,2:锁定节目
     */
    private Integer managerStatus;

    /**
     * 下发时以这个字段值为准判断状态:-2:黑名单,-1:删除,0:失效,1:生效,2:系统失效,3:合并失效,4:探测失效,5:源下线,6:注入失效
     */
    private Integer status;

    /**
     * 节目源状态，0：不可用 1：可用
     */
    private Integer sourceStatus;

    /**
     * 牌照方审核状态，0:审核不通过，1审核通过，与source_status整合
     */
    private Integer verifyStatus;

    private String source;

    /**
     * 合作方专辑ID
     */
    private String sourceAlbumId;

    /**
     * 源站的URL地址
     */
    private String sourceWebUrl;

    /**
     * 入库原因:0：自动注入;1：人工新增;
     */
    private Integer sourceType;

    /**
     * 剧头单位 如，期 部 话等 0：默认 ，1：集，2：期，3:话，4：番
     */
    private Integer unit;

    /**
     * 专辑分类信息（用于分类标签）
     */
    private String category;

    /**
     * 时长
     */
    private Integer duration;

    /**
     * 年代
     */
    private Integer year;

    /**
     * 获奖信息
     */
    private String honor;

    /**
     * 有效剧集数
     */
    private Integer validEpisode;

    /**
     * 总集数
     */
    private Integer totalEpisode;

    /**
     * 是否已完结 0：未完结 1 已完结
     */
    private Integer completed;

    /**
     * 简短推荐语
     */
    private String brief;

    /**
     * 更新周期
     */
    private String period;

    /**
     * 海报小竖图
     */
    private String verticalIcon;

    /**
     * 海报小竖图OCS地址
     */
    private String verticalIconOcs;


    /**
     * 海报小横图
     */
    private String horizontalIcon;

    /**
     * 海报小横图OCS地址
     */
    private String horizontalIconOcs;

    /**
     * 海报大竖图
     */
    private String verticalImage;

    /**
     * 海报大竖图OCS地址
     */
    private String verticalImageOcs;

    /**
     * 海报大横图
     */
    private String horizontalImage;

    /**
     * 海报大横图OCS地址
     */
    private String horizontalImageOcs;

    /**
     * 竖图图片背景色值 H S(18%) B(84%)
     */
    private String backGroundColor;

    /**
     * 竖图图片背景色值 H S(55%) B(33%)
     */
    private String backGroundColorTwo;

    /**
     * 图片背景色值
     */
    private String backGroundColorJson;

    /**
     * 1：正片， 2：预告片，3：微电影，4，精彩看点
     */
    private Integer featureType;

    /**
     * 高危标识,0-否，1-是
     */
    private Integer riskFlag;

    /**
     * 版权标识,0-否，1-是
     */
    private Integer copyright;

    /**
     * 版权方标识
     */
    private String copyrightCode;

    private String programInfo;

    /**
     * 1:PC,2:mobile
     */
    private Integer medium;

    /**
     * 0:免费，1：会员免费，2：单片付费
     */
    private Integer payStatus;

    /**
     * 0-所有人能看 1-会员能看
     */
    private Integer vipType;

    /**
     * 单片付费类型: 0-非单片付费，1-单片付费
     */
    private Integer chargeType;

    /**
     * 价格,可能有多个价格，需要扩展字段
     */
    private Integer price;

    /**
     * 关键词列表(一般用于搜索),搜索用,其他字段组合而来,看是否删掉
     */
    private String keyword;

    /**
     * 扩展信息
     */
    private String extraInfo;

    /**
     * 原始播放量
     */
    private Long sourcePlayCount;

    /**
     * 供应类型:special-特供，sole独播,normal-普通
     */
    private String supplyType;

    /**
     * 清晰度
     */
    private String definition;

    private String downloadMarkcode;

    private Integer downloadAble;

    /**
     * 会员角标
     */
    private String markCode;

    /**
     * 单买节目原始价格
     */
    private BigDecimal formerPrice;

    /**
     * 单买节目现价格
     */
    private BigDecimal nowPrice;

    /**
     * 单买节目VIP价格
     */
    private BigDecimal vipPrice;

    /**
     * 付费观看有效天数
     */
    private Integer payEffectDays;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 标准化处理状态
     */
    private Integer processStatus;

    /**
     * 创建时间/入库时间
     */
    private Date createTime;

    /**
     * 最近一次更新时间
     */
    private Date updateTime;

    /**
     * 剧头热度
     */
    private Double sourceHot;

    /**
     * 直播开始时间
     */
    private Date startTime;

    /**
     * 直播结束时间
     */
    private Date endTime;

    /**
     * 源系列id
     */
    private String sourceSeriesId;

    /**
     * 是否预推 0：否  1：是
     */
    private Integer prePush;

    /**
     * 预推上线时间
     */
    private Date preOnlineTime;

    /**
     * 预告片关系Json
     */
    private String previewInfo;

    /**
     * 图谱标签
     */
    private String tuputag;

    /**
     * 赛事id
     */
    private String competitionId;

    /**
     * 赛事名称
     */
    private String competitionName;

    /**
     * 赛季id
     */
    private String seasonId;

    /**
     * 赛季名称
     */
    private String seasonName;

    /**
     * 参赛方 0-人物，1-队伍，3-ufc，4-团体 5-双打
     */
    private Integer participantType;

    /**
     * 赛事类型， 0:非对抗赛 1:对抗赛
     */
    private Integer competitionType;

    /**
     * 阶段
     */
    private String phase;

    /**
     * 小组
     */
    private String matchGroup;


    /**
     * 节目开始时间（不同于赛事开始时间，直播开始了，比赛不一定开始了）
     */
    private Date matchStartTime;

    /**
     * 轮次
     */
    private String round;

    /**
     * 参赛国家列表，逗号隔开
     */
    private String participantCountrys;

    /**
     * 大项名称（奥运赛事用）
     */
    private String sportName;

    /**
     * 小项名称（奥运赛事用）
     */
    private String eventName;

    private String presenter;

    /**
     * 最小适龄
     */
    private Integer fitAgeMin;

    /**
     * 最大适龄
     */
    private Integer fitAgeMax;

    /**
     * cp方适龄原始内容
     */
    private String fitAge;

    /**
     * 排序分数
     */
    private Float sortDefine;

    /**
     * @Description: 区分业务字段
     */
    private String editType;

    /**
     * 人工配置的播放链接
     */
    private String manualWebUrl;

    /**
     * 人工配置的播放链接开关
     */
    private Boolean manualWebUrlPriority;
}
