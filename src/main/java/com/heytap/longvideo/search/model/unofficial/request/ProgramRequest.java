package com.heytap.longvideo.search.model.unofficial.request;

import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 编辑节目管理状态 - 请求体
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/15 11:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProgramRequest extends NextRequest {

    private String source;

    private String editType;

    private List<String> sids;
}