package com.heytap.longvideo.search.model.unofficial.request;

import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 获取非合作内容方节目锁定信息 - 请求体
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/11/14 17:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetOriginInfoRequest extends NextRequest {

    private String sid;

    private String sourceAlbumId;
}