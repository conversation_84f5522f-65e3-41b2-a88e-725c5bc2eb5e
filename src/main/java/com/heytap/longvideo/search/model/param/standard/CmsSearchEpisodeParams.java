package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description :
 */
@Setter
@Getter
public class CmsSearchEpisodeParams extends PageRequestParam {

    private String id;

    @EsField
    private String sid;
    @EsField
    private String eid;
    @EsField
    private String title;
    @EsField
    private String featureType;
    @EsField
    private String status;
    private String originStatus;
    @EsField
    private String sourceStatus;
    @EsField
    private String verifyStatus;
    private String publishStatus;
    private String source;
    private String chargeType;
    private String riskFlag;
    private String copyright;
    private String vipType;

    private String copyrightCode;
    private String recommendCategory;

    private String order;
    /**
     * 正片标题
     */
    @EsField
    private String albumTitle;
    /**
     * 多源 eg:sohu,letv
     */
    @EsField
    private String moreSource;

    @EsField
    private String payStatus;
    private String prepareOpenable;   //注：是否预生成打开第三方应用相关数据：linkData/restUri等。  1-预生成；0或空-不生成

}
