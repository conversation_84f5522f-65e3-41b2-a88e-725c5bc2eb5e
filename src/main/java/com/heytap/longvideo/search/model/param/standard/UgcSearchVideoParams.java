package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 20:20
 */
@Data
public class UgcSearchVideoParams extends PageRequestParam {
    @EsField(name = "sid")
    private String sid;

    @EsField(name = "sourceStatus")
    private String originStatus;
    @EsField
    private String status;
    @EsField
    private String title;
    @EsField
    private String programType;
    @EsField
    private String vid;

    @EsField
    private String source;

    //    @EsField 标签支持模糊查询
    @EsField
    private String tag;

    //up主id
    @EsField
    private String sourceAuthorId;

    //up主昵称
    @EsField
    private String authorName;

    /**
     * 排序
     */
    private String order;

    /**
     * 开始时间
     */
    private Integer startDuration;

    /**
     * 结束时间
     */
    private Integer endDuration;

    @EsField
    private String subTag;

    //原始视频Id
    private List<String> sourceVideoIdList;

    //视频Id列表
    private List<String> vidList;

    /**
     * @Description: 是否关联正片
     */
    private Integer isRelatedAlbum;

    /**
     * @Description: 正片id
     */
    @EsField
    private String linkSid;

    /**
     * 媒资系统视频管理名称
     */
    private String videoManageName;

    @EsField
    private String category;


    private Integer durationMin;

    private Integer durationMax;

}