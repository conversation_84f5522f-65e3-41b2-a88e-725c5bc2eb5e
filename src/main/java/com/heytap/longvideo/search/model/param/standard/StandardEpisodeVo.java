package com.heytap.longvideo.search.model.param.standard;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Data
public class StandardEpisodeVo {

    private String id;

    private String eid;

    private String sid;

    private String vid;

    private String title;

    private String orderNum;

    private Integer featureType;

    private String episode;

    private String episodeTerm;

    private String verticalIcon;

    private String horizontalIcon;

    private Integer copyright;

    private String copyrightCode;

    private Integer status;

    private Integer sourceStatus;

    private String source;

    private Integer duration;

    private String sourceSite;

    private String sourceVideoId;

    private Integer payStatus;

    private String markCode;

    private Integer vipType;

    private Integer chargeType;

    private String subscriptCode;

    private Integer verifyStatus;

    private Integer riskFlag;

    private Long sourcePlayCount;

    private Date createTime;

    private Date updateTime;

    private Integer headTime;

    private Integer tailTime;

    private String url;

    private int originStatus;

    /**
     * 剧头信息
     */
    private String albumTitle;
    private String score;
    private Integer albumFeatureType;
    private String albumProgramType;
    private Integer albumStatus;
    private Integer albumCompleted;
    private Integer albumPayStatus;
    private String albumTags;
    private String albumArea;
    private Integer albumYear;
    private String albumLanguage;
    private String albumShowTime;
    private String albumBrief;
    private String albumDirector;
    private String albumActor;
    private String albumInformation;
    private String albumProgramInfo;
    private String albumHorizontalIcon;
    private String albumVerticalIcon;
    private String sourceAlbumId;
}
