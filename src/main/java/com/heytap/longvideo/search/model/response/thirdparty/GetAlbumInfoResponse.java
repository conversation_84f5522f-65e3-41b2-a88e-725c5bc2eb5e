package com.heytap.longvideo.search.model.response.thirdparty;

import com.heytap.longvideo.client.media.entity.StandardAlbum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
public class GetAlbumInfoResponse {
    private static final long serialVersionUID = 1;
    //节目SID
    private String sid;

    //节目标题
    private String title;

    // 副标题
    private String subTitle;

    private Integer status;

    private Integer sourceStatus;

    //会员角标CODE
    private String markCode;

    //会员角标CODE
    private String markCodeUrl;

    //导演（或者主持人）
    private String director;

    private String source;

    private String sourceWebUrl;

    private String sourceAlbumId;

    private String sourceScore;

    private String programType;

    private Integer duration;

    private String actor;

    private Integer year;

    private String area;

    private String tags;

    private String language;

    private String information;

    private Integer validEpisode;

    private Integer totalEpisode;

    private Integer completed;

    private String brief;

    private String period;

    /**
     * 竖图
     */
    private String verticalIcon;

    private String verticalIconOcs;
    /**
     * 横图
     */
    private String horizontalIcon;

    private String horizontalIconOcs;

    private String verticalImage;

    /**
     * 海报大竖图OCS地址
     */
    private String verticalImageOcs;

    /**
     * 海报大横图
     */
    private String horizontalImage;

    /**
     * 海报大横图OCS地址
     */
    private String horizontalImageOcs;

    private Integer featureType;

    private Integer copyright;

    /**
     * 版权方标识
     */
    private String copyrightCode;

    private String programInfo;

    private Integer payStatus;

    private Integer vipType;

    /**
     * 单片付费类型: 0-非单片付费，1-单片付费
     */
    private Integer chargeType;

    private Integer price;

    /**
     * 清晰度
     */
    private String definition;

    private String downloadMarkcode;

    private Integer downloadAble;


    /**
     * 单买节目原始价格
     */
    private BigDecimal formerPrice;

    /**
     * 单买节目现价格
     */
    private BigDecimal nowPrice;

    /**
     * 单买节目VIP价格
     */
    private BigDecimal vipPrice;

    /**
     * 付费观看有效天数
     */
    private Integer payEffectDays;

    /**
     * 上映时间
     */
    private String showTime;

    /**
     * 发布时间
     */
    private Date publishTime;

    private Date createTime;

    /**
     * 最近一次更新时间
     */
    private Date updateTime;

    /**
     * 直播开始时间
     */
    private Date startTime;

    /**
     * 直播结束时间
     */
    private Date endTime;

    private String mappingTags;

    private String virtualSid;

    /**
     * 影视范围，"oppo_video"：自建，"all_web"：全网内容
     */
    private String contentChannel;

    private String deepLink;


}
