package com.heytap.longvideo.search.model.entity;


import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * @Author: 80339123 liu ying
 * @Date: 2021/8/21 10:44
 */
public class Result<T> {

    private int code;

    private String msg = "";

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date cacheDate;

    private long cost = 0L;

    private T data;

    public static Result success() {
        return new Result();
    }

    public static Result fail( String msg) {
        return new Result(CodeMsg.PARAM_ERROR.getCode(), msg);
    }

    public static <T> Result<T> fail(String msg, T data) {
        return new Result(CodeMsg.PARAM_ERROR.getCode(), msg, data);
    }

    public static <T> Result<T> success(T data) {
        return new Result(data);
    }

    public static <T> Result<T> success(T data, long cost) {
        return new Result(data, cost);
    }

    public static <T> Result<T> success(T data, Date cacheDate, long cost) {
        return new Result(data, cacheDate, cost);
    }

    public static Result error() {
        return new Result();
    }

    public static <T> Result<T> error(CodeMsg codeMsg) {
        return new Result(codeMsg);
    }

    public static <T> Result<T> error(int code, String msg) {
        return new Result(code, msg);
    }

    public static <T> Result<T> error(int code, String msg, T data) {
        return new Result(code, msg, data);
    }

    private Result(T data, long cost) {
        this.code = CodeMsg.SUCCESS.getCode();
        this.cacheDate = new Date();
        this.cost = cost;
        this.data = data;
    }

    private Result(T data, Date date, long cost) {
        this.code = CodeMsg.SUCCESS.getCode();
        this.cacheDate = date;
        this.cost = cost;
        this.data = data;
    }

    private Result(T data) {
        this.msg = CodeMsg.SUCCESS.getMsg();
        this.code = CodeMsg.SUCCESS.getCode();
        this.cacheDate = new Date();
        this.data = data;
    }

    private Result() {
        this.code = CodeMsg.SUCCESS.getCode();
        this.cacheDate = new Date();
        this.data = null;
    }

    private Result(int code, String msg) {
        this.code = code;
        this.cacheDate = new Date();
        this.msg = msg;
    }

    private Result(int code, String msg, T data) {
        this.code = code;
        this.cacheDate = new Date();
        this.msg = msg;
        this.data = data;
    }

    private Result(CodeMsg codeMsg) {
        if (codeMsg != null) {
            this.cacheDate = new Date();
            this.code = codeMsg.getCode();
            this.msg = codeMsg.getMsg();
        }
    }

    public boolean successed() {
        return this.code == CodeMsg.SUCCESS.getCode();
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public Date getCacheDate() {
        return this.cacheDate;
    }

    public long getCost() {
        return this.cost;
    }

    public T getData() {
        return this.data;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public void setMsg(final String msg) {
        this.msg = msg;
    }

    public void setCacheDate(final Date cacheDate) {
        this.cacheDate = cacheDate;
    }

    public void setCost(final long cost) {
        this.cost = cost;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public String toString() {
        return "Result(code=" + this.getCode() + ", msg=" + this.getMsg() + ", cacheDate=" + this.getCacheDate() + ", cost=" + this.getCost() + ", data=" + this.getData() + ")";
    }
}
