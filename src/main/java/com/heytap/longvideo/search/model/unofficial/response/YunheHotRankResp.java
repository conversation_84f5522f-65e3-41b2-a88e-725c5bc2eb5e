package com.heytap.longvideo.search.model.unofficial.response;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
public class YunheHotRankResp {
    private Long date;
    private List<YunheHotRankResp.YunheHotRank> content;

    @Data
    @ToString
    public static class YunheHotRank {
        // 节目名称
        private String name;

        // 节目ID
        private Integer nameId;

        // 渠道类型
        private String channelType;

        // 总热度
        private BigDecimal allHot;

        // 反馈热度
        private BigDecimal feedbackHot;

        // 传播热度
        private BigDecimal spreadHot;

        // 微博热度
        private BigDecimal weiboHot;

        // 搜索热度
        private BigDecimal searchHot;

        // 是否为竖屏内容
        private Boolean isVertical;

        // 上线时间（时间戳）
        private Long releaseTime;

        // 渠道（多个渠道用斜杠分隔）
        private String channel;

        // 上线天数
        private Integer occurDays;

        // 是否为网络剧
        private Integer network;

        // 排名变化
        private Integer status;
    }
}
