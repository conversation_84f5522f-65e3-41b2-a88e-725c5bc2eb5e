package com.heytap.longvideo.search.model;


/**
 * <AUTHOR>
 * @since 2022/8/3
 */
public class AlgorithmRequest {

    /**
     * 关联算法
     */
    private String poolCode;

    /**
     * 请求次数，标识第几刷
     */
    private Integer pageIndex;

    /**
     * 请求数量
     */
    private Integer pageSize;

    /**
     * 用户buuid
     */
    private Long buuid;


    /**
     * 场景标识
     */
    private String bidlst;

    /**
     * 频道id【用于垂类频道】
     */
    private String channelId;


    /**
     * 主物料id，如sid等
     */
    private String docId;

    /**
     * 周边视频id
     */
    private String videoId;

    /**
     * 内容池id
     */
    private String rContentPoolId;

    /**
     * 唯一请求ID
     */
    private String predictId;

    /**
     * 页面ID
     */
    private String pageId;

    /**
     * 模块抽屉类型
     */
    private String moduleType;

    /**
     * 频道名称
     */
    private String channelName;

    /**
     * 物料id集合
     */
    private String ids;

    /**
     * 双列瀑布流垂类时，必传【value=2】
     */
    private String style;

    private String route;

    private String cid;


    public String getPoolCode() {
        return poolCode;
    }

    public void setPoolCode(String poolCode) {
        this.poolCode = poolCode;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getBuuid() {
        return buuid;
    }

    public void setBuuid(Long buuid) {
        this.buuid = buuid;
    }

    public String getBidlst() {
        return bidlst;
    }

    public void setBidlst(String bidlst) {
        this.bidlst = bidlst;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getrContentPoolId() {
        return rContentPoolId;
    }

    public void setrContentPoolId(String rContentPoolId) {
        this.rContentPoolId = rContentPoolId;
    }

    public String getPredictId() {
        return predictId;
    }

    public void setPredictId(String predictId) {
        this.predictId = predictId;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }
}
