package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Data
public class SearchStandardTrailerParams extends PageRequestParam {

    @EsField
    private String sid;

    @EsField
    private String tid;

    @EsField
    private String eid;

    private String linkValue;

    @EsField
    private String source;

    @EsField
    private String title;

    @EsField
    private String status;

    @EsField
    private String sourceStatus;

    private String originStatus;

    @EsField
    private String vid;

    /**
     * 视频类型
     * 1.预告片2.花絮3.智能看点 4.剧集看点 5.独家策划 6.粉丝饭制 7.精彩速看 8.首映式 9.MV 10.其他 11.资讯 12.普通视频', 14 高能看点
     */
    @EsField
    private String trailerType;

    @EsField
    private String payStatus;

    /**
     * 节目类型：1：正片， 2：预告片，3：微电影，4，精彩看点
     */
    @EsField
    private String albumFeatureType;

    /**
     * 节目标题
     */
    @EsField
    private String albumTitle;

    /**
     * 节目标签
     */
    @EsField
    private String albumTags;

    private Integer startDuration;

    private Integer endDuration;

    private List<DurationItem> durationItems;

    private String order;

    @EsField
    private String programType;

    @EsField
    private String sportsVideoType;

    /**
     * 适配老系统传programTitle查询条件
     */
    private String programTitle;

    private boolean preciseFlag;

    /**
     * 开始年龄
     */
    private Integer ageStart;

    /**
     * 结束年龄
     */
    private Integer ageEnd;

    /**
     * 周边视频屏蔽优酷
     * 0-返回优酷数据，1-过滤掉优酷数据 默认返回优酷
     */
    private String blockYoukuMobile;
}
