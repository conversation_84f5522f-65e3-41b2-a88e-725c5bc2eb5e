package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.entity.StandardAlbum;
import lombok.Data;

/*
 * Description
 * Date 14:47 2022/3/21
 * Author songjiajia 80350688
 */
@Data
public class StandardAlbumVo extends StandardAlbum {

    private int originStatus;

    private double sortDefine;

    private float releScore;

    private int recommendCategory = 0;
    private int publishStatus = 1;

    private String featureTypeDesc;

    private Integer orderIndex;

    /**
     * 人工配置的播放链接
     */
    private String manualWebUrl;

    /**
     * 人工配置的播放链接开关
     */
    private Boolean manualWebUrlPriority;

    /**
     * 是否在内容池中，同步锁屏使用
     */
    private Boolean isInContentPool;

}
