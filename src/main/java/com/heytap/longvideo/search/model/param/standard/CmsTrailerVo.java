package com.heytap.longvideo.search.model.param.standard;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 功能说明：com.oppo.meizi.entity.program.ProgramTrailer
 *         节目精彩看点表
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/4 16:19
 */
@Data
public class CmsTrailerVo {

    private String id;

    //-1：删除，0：不可用， 1：可用
    private int originStatus;

    private String tid;

    //剧头sid
    private String sid;

    //剧集eid
    private String eid;

    //标题
    private String title;

    //看点类型，0.默认，1.预告片2.花絮3.智能看点 4.剧集看点 5.独家策划 6.粉丝饭制 7.精彩速看 8.首映式 9.MV 10.其他 11.资讯 12.普通视频
    private int trailerType;

    //关联类型
    private int linkType;

    //关联类型值
    private String linkValue;

    //海报小竖图
    private String verticalIcon;

    //海报小横图
    private String horizontalIcon;

    //海报大竖图
    private String verticalImage;

    //海报大横图
    private String horizontalImage;

    //序号 原sign
    private int orderNum;

    //高危标识，0-低危（有版权），1-中危， 2-高危
    private int riskFlag;

    //推荐语
    private String recommendInfo;

    //左角标CODE
    private String subscriptCode;

    //会员角标CODE
    private String markCode;

    //媒资ID
    private int meiziId;

    //发布时间
    private Date publishTime;

    //时长（单位：秒）
    private int duration;

    private int status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
