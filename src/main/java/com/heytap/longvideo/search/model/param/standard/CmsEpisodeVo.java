package com.heytap.longvideo.search.model.param.standard;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 功能说明：com.oppo.meizi.entity.program.ProgramEpisode
 *         节目剧集表
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/4 15:44
 */

@Data
public class CmsEpisodeVo {

    private String id;

    private String vid;

    //剧集唯一ID
    private String eid;

    //节目唯一ID
    private String sid;

    //剧集标题
    private String title;

    //-1：删除，0：不可用， 1：可用
    private int originStatus;

    //审核状态，0:不通过，1：通过
    private int verifyStatus = 1;

    //发布状态，0:未发布，1：已发布
    private int publishStatus;

    //排序序号
    private int sequence;

    //节目类型，1：正片， 2：预告片，3: 微电影，4：精彩看点
    private int featureType;

    //当前剧集号
    private String episode;

    //后台使用的排序序号  原episodeIndex
    private int orderNum;

    //剧集简介
    private String brief;

    //海报小竖图
    private String verticalIcon;

    //海报小横图
    private String horizontalIcon;

    //海报大竖图
    private String verticalImage;

    //海报大横图
    private String horizontalImage;

    //高危标识,0-低危（有版权），1-中危，2-高危
    private int riskFlag;

    //版权标识,0-否，1-是
    private int copyright;

    //品牌方CODE
    private String copyrightCode;

    //免付费标志: 0-免费，1-付费
    private int vipType;

    //单片付费类型: 0-非单片付费，1-单片付费
    private int chargeType;

    private int payStatus;

    //左角标code
    private String subscriptCode;

    //会员角标CODE
    private String markCode;

    //节目包
    private String productCode;

    //节目包名称
    private String productName;

    //发布时间
    private Date publishTime;

    private String source;
    private String contentType;
    private int playCount;

    private int recommendCategory;

    private String linkData;   //注：预生成数据，用于打开第三方应用推荐节目
    private String restUri;    //注：预生成数据，用于打开第三方应用推荐节目

    private int status;

    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

    private Integer duration;

}
