package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.util.Date;


/*
 * Description 标准化
 * Date 10:43 2022/3/21
 * Author songjiajia 80350688
*/
@Data
@Document(indexName = IndexNameConstant.STANDARD_VIDEO_INDEX)
@Setting(settingPath = "/esConfig/setting.json")
public class StandardVideoEs{
    @Id
    private String uniKey;

    @Field(type = FieldType.Keyword)
    private String vid;

    @Field(type = FieldType.Keyword)
    private String verticalIcon;

    @Field(type = FieldType.Keyword)
    private String horizontalIcon;

    private String source;

    private String sourceAlbumId;

    private String sourceVideoId;

    @Field(type = FieldType.Keyword)
    private String playUrl;

    @Field(type = FieldType.Keyword)
    private String definition;

    @Field(type = FieldType.Keyword)
    private String platformStr;

    @Field(type = FieldType.Keyword)
    private String supplyType;

    @Field(type = FieldType.Keyword)
    private String sourceSite;

    @Field(type = FieldType.Keyword)
    private String sid;

    private String title;

    private Integer sourceStatus;
    private Integer status;
    private Integer payStatus;
    private Integer vipType;
    private Integer chargeType;
    private String programType;
    private Integer videoType;
    private String category;
    private String language;
    private String keyword;
    private Integer playType;
    private Integer manmade;
    private Integer duration;
    private Integer episodeNo;
    private String episodeTerm;
    private Integer verifyStatus;
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String tags;
    private Integer copyright;
    private String copyrightCode;
    private Integer riskFlag;
    private Integer headTime;
    private Integer tailTime;
    private Long sourcePlayCount;
    private Integer sportsVideoType;
    private Date publishTime;
    private Date createTime;
    private Date updateTime;
    private Integer isMain;

    private Long idOffSet;


    /**
     * 剧头信息
     */
    private String albumTitle;
    private String score;
    private Integer albumFeatureType;
    private Integer albumStatus;
    private Integer albumCompleted;
    private Integer albumPayStatus;
    private String albumArea;
    private Integer albumYear;
    private String albumShowTime;
    private String albumBrief;
    private String albumDirector;
    private String albumActor;
    private String albumInformation;
    private String albumProgramInfo;
    private String albumHorizontalIcon;
    private String albumVerticalIcon;
}
