package com.heytap.longvideo.search.model.param.app;

import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.ToString;


/*
 * Description 通过关键字搜索
 * Date 11:33 2021/11/29
 * Author songjiajia 80350688
 */
@Data
@ToString
public class ListFilterParam extends NextRequest {

    private String urlpack;

    private Integer number = 3;

    private Integer offset = 0;

    /**
     * 未使用，版本使用基类的version
     */
    private Integer appVersion = 50000;

    private String vipType;

    private String needRecInfo;

    //匹配标签卡时使用
    private Boolean hasMore = false;

    /**
     * 1：筛选接口调用
     * 2：searchTagCmp调用
     */
    private Integer callType = 1;

    /**
     * 是否未成年模式   1-是未成年模式
     */
    private String minors;

    /**
     * app版本 样式：71600
     */
    private Integer version;

    /**
     * 快应用版本号 样式90200
     */
    private Integer quickEngineVersion;

}
