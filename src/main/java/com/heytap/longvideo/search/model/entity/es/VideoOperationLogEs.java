package com.heytap.longvideo.search.model.entity.es;

import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

/*
 * Description 后台操作日志
 * Date 16:55 2024/3/28
 * Author songjiajia 80350688
*/
@Data
@Document(indexName = "video_operation_log")
public class VideoOperationLogEs implements Serializable {
    private static final long serialVersionUID = 1;

    @Field(type = FieldType.Text)
    private String businessCode;
    /**
     * 所属模块
     */
    @Field(type = FieldType.Keyword)
    private String businessMudule;

    /**
     * 所属模块详细功能
     */
    @Field(type = FieldType.Keyword)
    private String businessType;

    /**
     * 接口入参
     */
    @Field(type = FieldType.Text,index = false)
    private String requestInfo;

    @Field(type = FieldType.Text,index = false)
    private String responseInfo;

    @Field(type = FieldType.Keyword)
    private String info;
    /**
     * 0：业务开始执行，1业务执行成功，2业务执行超时等
     */
    @Field(type = FieldType.Integer)
    private Integer status = 0;

    @Field(type = FieldType.Text)
    private String createUser;

    @Field(type = FieldType.Long)
    private Long createTime;

    @Field(type = FieldType.Keyword)
    private String source;
}
