package com.heytap.longvideo.search.model.unofficial.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class YunheDetailInfo {
    /**
     * 导演
     */
    private String director;
    /**
     * 演员
     */
    private String actor;
    /**
     * 地区
     */
    private String area;
    /**
     * 简介
     */
    private String briefStory;
    /**
     * 海报地址
     */
    private String hpURL;
    /**
     * 上线时间
     */
    private Long releaseTime;
    /**
     * 标签
     */
    private String tags;
    /**
     * 豆瓣分
     */
    private BigDecimal rating;
    /**
     * 总集数
     */
    private Integer totalEpisodeNum;
}
