package com.heytap.longvideo.search.model.param.app;

import com.heytap.longvideo.search.constants.KeywordSearchTypeEnum;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import com.heytap.longvideo.search.utils.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/*
 * Description 通过关键字搜索
 * Date 11:33 2021/11/29
 * Author song<PERSON><PERSON><PERSON> 80350688
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class KeyWordSearchParamV2 extends PageRequestParam {

    private String keyword;

    private String vipType;

    private String searchType = "1";

    private Integer version = 50000;

    private Integer deviceType = 0;

    /**
     * 是否对外接口
     */
    private int isOut = 0;

    /**
     * 三方只会传该版本号，形式：********
     */
    private String versionName;

    private String appVersion;

    private String dv;

    private String buuid;

    /**
     * 用户id
     */
    private String ssoid;

    private Integer versionTag = 5;

    private Integer hasMore = 0;

    private Boolean duanjuHasMore = false;

    private Integer quickEngineVersion;

    private KeywordSearchTypeEnum searchTypeEnum = KeywordSearchTypeEnum.DEFAULT;

    private String lastSearchTab;

    private String sidBlackList;

    @Override
    public int getPageIndex() {
        int pageIndex = super.getPageIndex() < 0 ? 1 : super.getPageIndex();
        pageIndex = Math.min(pageIndex, 10);
        return pageIndex;
    }

    @Override
    public int getPageSize() {
        int pageSize = super.getPageSize() < 0 ? 3 : super.getPageSize();
        pageSize = Math.min(pageSize, 10);
        return pageSize;
    }

    public String getVipType() {
        if (StringUtil.isBlank(vipType)) {
            vipType = "default";
        }
        return vipType;
    }
}