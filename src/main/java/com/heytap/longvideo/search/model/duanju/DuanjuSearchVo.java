package com.heytap.longvideo.search.model.duanju;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@ToString
@Data
public class DuanjuSearchVo {
    /**
     * 短剧唯一标识
     */
    private Long id;
    /**
     * 短剧三方id
     */
    private String duanjuId;

    /**
     * 短剧标题
     */
    private String title;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;

    /**
     * 短剧描述
     */
    private String desc;

    /**
     * 总集数
     */
    private Integer total;

    /**
     * 更新状态（over-已完结 updating-更新中）
     */
    private String updateStatus;

    /**
     * 更新时间
     */
    private Long updateAt;

    /**
     * 数据来源
     */
    private String source;

    /**
     * 分类标签列表
     */
    private List<String> categories;

    /**
     * 上架状态（up:已上架）
     */
    private String status;

    /**
     * 预加载广告数量
     */
    private Integer preloadAdCount;

    /**
     * 广告位置信息
     */
    private String adPositions;

    /**
     * 短剧类型 1-普通类型; 2-快应用类型 短剧四期新增，3-h5网页类型 短剧5期新增
     */
    private Integer duanjuType;

    /**
     * 跳转链接 duanjuType=2 时为短剧快应用跳转地址 短剧四期新增，duanjuType=3时为h5网页跳转地址 短剧5期新增
     */
    private String jumpUrl;

    /**
     * 标签信息
     */
    private String tags;

    /**
     * 视频素材类型
     */
    private Integer videoMaterialType;

    /**
     * 剧集UUID
     */
    private Long dramaUuid;

    /**
     * 播放量文本显示（如：266.5万）
     */
    private String playViewText;

    /**
     * 追剧人数
     */
    private Integer bingeWatchNumber;

}
