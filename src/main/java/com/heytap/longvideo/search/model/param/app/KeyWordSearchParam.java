package com.heytap.longvideo.search.model.param.app;

import com.heytap.longvideo.search.constants.KeywordSearchTypeEnum;
import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.ToString;
import shaded_package.io.swagger.models.auth.In;

/*
 * Description 通过关键字搜索
 * Date 11:33 2021/11/29
 * Author <PERSON><PERSON>ajia 80350688
 */
@Data
@ToString
public class KeyWordSearchParam extends NextRequest {

    private String keyword;

    private Integer pageSize = 3;

    private Integer pageNo = 0;

    private Integer offset = 0;

    private Integer appVersion = 50000;

    private Integer versionTag = 5;

    private String urlpack;

    private String vipType;

    private Integer hasMore = 0;

    private Integer version = 50000;

    private Integer quickEngineVersion;

    private int isOut = 0;

    private KeywordSearchTypeEnum searchTypeEnum = KeywordSearchTypeEnum.DEFAULT;

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize < 1 ? 1 : pageSize;
    }
}
