package com.heytap.longvideo.search.model.entity.es;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.search.constants.AnalyzerConstant;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;


/*
 * Description 标准化
 * Date 10:43 2022/3/21
 * Author songjiajia 80350688
 */
@Data
@Document(indexName = IndexNameConstant.STANDARD_ALBUM_INDEX)
@Setting(settingPath = "/esConfig/setting.json")
public class StandardAlbumEs extends StandardAlbum {
    @Id
    private String sid;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String tags;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String actor;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String director;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String area;

    @Field(type = FieldType.Text,analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String language;

    @Field(type = FieldType.Keyword)
    private String programType;

    @Field(type = FieldType.Keyword)
    private String subProgramType;

    @Field(type = FieldType.Keyword)
    private String information;

    @Field(type = FieldType.Keyword)
    private String sourceScore;

    @Field(type = FieldType.Keyword)
    private String doubanScore;

    @Field(type = FieldType.Keyword)
    private String oppoScore;

    /**
     * 值同sourceScore,给后台使用
     */
    @Field(type = FieldType.Keyword)
    private String cpScore;

    @Field(type = FieldType.Keyword)
    private String showTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Integer dayNo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long oppoHot;

    @Field(type = FieldType.Long)
    private Long sidL;

    @Field(type = FieldType.Integer)
    private Integer hasPreview;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER)
    private String mappingTags;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last7DaysPlayPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last15DaysPlayPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last30DaysPlayPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last7DaysClickPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last15DaysClickPv;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Field(type = FieldType.Long)
    private Long last30DaysClickPv;

}
