package com.heytap.longvideo.search.model.param.standard;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/15 17:02
 */
@Data
public class StandardAlbumDetailVo {
    private int id;
    private String sid;
    private String title;
    private String managerName;
    private String subTitle;
    private int originStatus;
    private int verifyStatus = 1;
    private int publishStatus;
    private int completed;
    private String sourceAlbumId;
    private String programType;
    private String subProgramType;
    private int featureType;
    private int sourceType;
    private int year;
    private int duration;
    private String area;
    private String language;
    private String honor;
    private String information;
    private String mtimeId;
    private float mtimeScore;
    private String doubanId;
    private String doubanTags;
    private float doubanScore;
    private float score;
    private String sourceScore;
    private int validEpisode;
    private int totalEpisode;
    private int unit;
    private String programInfo;
    private String brief;
    private String period;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String showTime;

    private String verticalIcon;
    private String horizontalIcon;
    private String verticalImage;
    private String horizontalImage;
    private int riskFlag;
    private int copyright;
    private String copyrightCode;
    private int vipType;
    private int chargeType;
    private String subscriptCode;
    private String markCode;
    private String productCode;
    private String productName;
    private String director;
    private String actor;
    private String keyword;
    private String episodeStyle;
    private String tags;
    private String category;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date publishTime;

    private Integer managerStatus;
    private Integer payStatus;
    private String source;
    private String sourceWebUrl;
    private Integer price;
    private String showKind;
    private int recommendCategory;
    private int playCount;
    private String sourceHighestVipType;
    private String linkData;
    private String restUri;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;

    private int status;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    /**
     * 最新高能视频ID
     */
    private String highLightVid;

    private Date freeStartTime;

    private Date freeEndTime;

}