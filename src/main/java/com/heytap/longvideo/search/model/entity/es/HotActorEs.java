package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.search.constants.AnalyzerConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import org.springframework.data.elasticsearch.core.completion.Completion;

import java.io.Serializable;

/*
 * Description 热门演员
 * Date 12:29 2022/6/22
 * Author songjiajia 80350688
*/
@Data
@Document(indexName = "api_hot_actor")
@Setting(settingPath = "/esConfig/setting.json")
public class HotActorEs implements Serializable {
    private static final long serialVersionUID = 1;
    @Id
    private String name;

    @CompletionField(maxInputLength = 16)
    private Completion actorName;

    @CompletionField(analyzer = AnalyzerConstant.SUGGEST_PINYIN_ANALYZER,
            searchAnalyzer = AnalyzerConstant.SUGGEST_PINYIN_ANALYZER, maxInputLength = 32)
    private Completion actorNamePinyin;

    @Field(type = FieldType.Integer)
    private  Integer isHot =0;
}
