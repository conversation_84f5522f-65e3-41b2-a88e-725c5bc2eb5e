package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 电视端媒资ES索引定义
 * @Author: WT
 * @Version: 1.0
 * @Date: 2024/10/28 20:02
 */
@Data
@Setting(settingPath = "/esConfig/setting.json")
@Document(indexName = IndexNameConstant.TV_ALBUM_INDEX, createIndex = false)
public class TvAlbumEs implements Serializable {

    private static final long serialVersionUID = -4983919924179314623L;

    /**
     * //演员（| 分割 or json 字符串 待定）
     * // toastmaster;//主持人
     * // guest;//嘉宾
     * // host;//主演
     * // cast;演职员（包括演员，导演，主持人，嘉宾）
     */
    private String actor;

    private String platformStr;


    /**
     * //地区 （| 分割 or json 字符串）
     */

    private String area;
    /**
     * //地区code （| 分割 or json 字符串）
     */


    private String areaCode;
    /**
     * 牌照方审核状态 1：审核通过；-1：审核不通过
     * 同 audit_status;//牌照方审核状态
     */

    private Integer verifyStatus;


    private Integer publishStatus;


    /**
     * 获奖信息
     * 同 honor
     */


    private String awards;
    /**
     * 获奖信息
     * 同 honor
     */


    private String honor;
    /**
     * 简短推荐语
     */


    private String brief;
    /**
     * 专辑分类信息（用于分类标签）
     */


    private String category;
    /**
     * 单片付费类型: 0-非单片付费，1-单片付费
     */

    private Integer chargeType;
    /**
     * 是否已完结 0：未完结 1 已完结
     */

    private Integer completed;
    /**
     * 版权标识,0-否，1-是
     */

    private Integer copyright;
    /**
     * 版权方标识
     */


    private String copyrightCode;


    private Date createTime;
    /**
     * 当前剧集
     */

    private Integer currEpisode;

    /**
     * 导演
     */


    private String director;


    private String doubanId;


    private Double doubanScore;


    private String doubanTags;
    /**
     * 时长
     */

    private Integer duration;
    /**
     * number-数字；thumbnail-缩略图；text-纯文字
     */


    private String episodeStyle;
    /**
     * //正片类型，1：正片，2：片花,3：微电影
     */

    private Integer featureType;
    /**
     * 海报小横图
     */


    private String horizontalIcon;
    /**
     * 海报大横图
     */


    private String horizontalImage;
    /**
     * 简介信息
     * 同 program_info;
     */


    private String information;


    private String language;


    private String languageCode;


    private Integer managerStatus;


    private String mtimeId;


    private Double mtimeScore;
    /**
     * 海报小正图
     */

    private String normalIcon;
    /**
     * 海报大正图
     */


    private String normalImage;

    /**
     * 0:免费，1：会员免费，2：单片付费
     */

    private Integer payStatus;

    /**
     * 节目演员列表
     */


    private String persons;
    /**
     * 更新周期
     */


    private String period;
    /**
     * 价格
     */

    private Integer price;

    /**
     * 剧集状态，-1 删除，0：失效，1：生效，2：系统失效，3：合并失效，4：探测失效，5：源下线，6：注入失效
     */

    private Integer status;
    /**
     * 类型
     */


    private String programType;


    private Date publishTime;
    /**
     * 节目的季部数
     */


    private String quarterId;
    /**
     * 节目的季部数
     */


    private String quarterDetail;

    /**
     * 地域标识
     */

    private Integer riskFlag;

    /**
     * 上映时间
     */

    private String showTime;


    private String sid;
    /**
     * 来源 区分节目源
     */


    private String source;
    /**
     * 合作方专辑ID
     */


    private String sourceAlbumId;
    /**
     * 源站评分 如 优酷评分，腾讯评分
     */

    private Double sourceScore;


    private Double score;

    /**
     * 节目源状态，0：不可用 1：可用
     */

    private Integer originStatus;
    /**
     * 节目来源类型，1:内容抓取；2：外部注入；3：人工新增；4：人工合并;
     * 同 origin_type
     */

    private Integer sourceType;
    /**
     * 源站的URL地址
     */


    private String sourceWebUrl;

    /**
     * 出品方
     */
    private String station;

    /**
     * 出品方code
     */
    private String stationCode;


    private String subTitle;


    private String subProgramType;
    /**
     * 标签列表
     */


    private String tags;
    /**
     * 标签列表code
     */


    private String tagsCode;

    /**
     * 产品包
     */


    private String productName;


    private String productCode;


    private String markCode;


    private String subscriptCode;


    private String programInfo;


    private String keyword;


    private String showKind;


    private String title;
    /**
     * 总集数
     */

    private Integer totalEpisode;
    /**
     * 剧头单位 如，期 部 话等 0：默认 ，1：集，2：期，3:话，4：番
     */

    private Integer unit;


    private Integer episodeUnit;


    private Date updateTime;
    /**
     * 有效剧集数
     */

    private Integer validEpisode;
    /**
     * 海报小竖图
     */


    private String verticalIcon;
    /**
     * 海报大竖图
     */


    private String verticalImage;
    /**
     * 剧集类型：0.单片;1.剧头;2.剧集
     */

    private Integer videoType;
    /**
     * 节目会员剧集是否失效标志 ，0：此剧头下的所有会员剧集生效， 1：失效 ，默认：0
     */

    private Integer vipStatus;
    /**
     * 免付费标志: 0-免费，1-付费
     */

    private Integer vipType;
    /**
     * 关联虚拟节目SID
     */

    private String virtualSid;

    /**
     * 是否为虚拟节目
     */

    private Integer isVirtualSid;
    /**
     * 年代
     */

    private Integer year;


    private Date indexTime;


    private Date indexUpdateTime;


    private Integer recommendCategory;
}
