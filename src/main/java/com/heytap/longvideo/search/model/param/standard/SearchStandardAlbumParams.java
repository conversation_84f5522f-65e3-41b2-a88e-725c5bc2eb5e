package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import lombok.Data;

import java.util.Date;

/*
 * Description
 * Date 14:47 2022/3/21
 * Author songjiajia 80350688
 */
@Data
public class SearchStandardAlbumParams extends PageRequestParam {

    /* 标题 */
    @EsField
    private String title;

    /* 类型标签 */
    @EsField
    private String tags;

    /* 点播分类,"comic"=动漫，tv=电视剧 */
    @EsField
    private String programType;

    @EsField
    private String subProgramType;

    /* 标准剧头sid */
    @EsField
    private String sid;

    /* 正片类型 1=正片,2=预告片 */
    @EsField
    private String featureType;

    /* 管理状态,0=自动更新，1=锁定信息，2=锁定节目 */
    @EsField
    private String managerStatus;


    /* 状态，0=失效，1=生效，6=注入失效 */
    @EsField(name = "status")
    private String programStatus;

    /* 源状态,0=失效1=生效 */
    @EsField
    private String sourceStatus;

    /* 节目来源(后台渠道来源) */
    @EsField
    private String source;

    //如下字段为编排搜索字段
    @EsField
    private String verifyStatus;

    @EsField
    private String vipType;

    //来源类型
//    @EsField
//    private String sourceType;

    @EsField
    private String payStatus;

    @EsField
    private String completed;

    @EsField
    private String subTitle;

    @EsField
    private String area;

    private String yearStart;

    private String yearEnd;

    private Float sourceScoreStart;

    private Float sourceScoreEnd;

    private Float cpScoreStart;

    private Float cpScoreEnd;

    @EsField
    private String actor;

    @EsField
    private String director;

    //源状态，对应sourceStatus
    private String originStatus;

    //对应program_status,注意源下线等状态
    @EsField
    private String status;

    /**
     * 后台版权归属
     */
    @EsField
    private String copyrightCode;

    /* 排序,updateTime=按更新时间排序,year=按年份排序,score=按评分排序,createTime=按创建时间排序 */
    private String order;

    //1.媒资，2编排
    private int channel = 1;

    //内容池筛选器使用
    private Integer sortStrategy;

    private Integer sortType;

    /**
     * 适配老系统传programTitle查询条件
     */
    private String programTitle;

    private String exact;

    /**
     * 源剧头id
     */
    @EsField
    private String sourceAlbumId;

    /**
     * 0 非高能  1 高能
     */
    @EsField
    private String useHighLight;

    /**
     * 0 非高能  1 高能
     */
    @EsField
    private String programInfo;

    /**
     * 赛事名称
     */
    @EsField
    private String competitionName;

    /**
     * 赛季id
     */
    @EsField
    private String seasonId;

    /**
     * 赛季名称
     */
    @EsField
    private String seasonName;

    /**
     * 参赛方 0-人物，1-队伍，3-ufc，4-团体 5-双打
     */
    @EsField
    private Integer participantType;

    /**
     * 赛事类型， 0:非对抗赛 1:对抗赛
     */
    @EsField
    private Integer competitionType;

    /**
     * 阶段
     */
    @EsField
    private String phase;

    /**
     * 小组
     */
    @EsField
    private String matchGroup;


    /**
     * 节目开始时间（不同于赛事开始时间，直播开始了，比赛不一定开始了）
     */
    @EsField
    private Date matchStartTime;

    /**
     * 轮次
     */
    @EsField
    private String round;

    /**
     * 大项名称（奥运赛事用）
     */
    @EsField
    private String sportName;

    /**
     * 小项名称（奥运赛事用）
     */
    @EsField
    private String eventName;

    /**
     * 使用场景
     */
    private String usageScene;

    /**
     * 开始年龄
     */
    private Integer ageStart;

    /**
     * 结束年龄
     */
    private Integer ageEnd;

    /**
     * 供给类型 -> 是否独播
     */
    @EsField
    private String supplyType;

    /**
     * 编排后台查询剧头屏蔽优酷
     * 0-返回优酷数据，1-过滤掉优酷数据 默认返回优酷
     */
    private String blockYoukuMobile = "0";

    /**
     * oppo评分筛选，包含边界
     * 1：6分以下
     * 2：6-7
     * 3：7-8
     * 4:8-9
     * 5:9-10
     * 6:空
     */
    private Integer[] oppoScoreArray;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 请求来源：outOfStockContentPool--全网节目内容池（同步锁屏）
     */
    private String fromScene;

    /**
     * 内容池code
     */
    private String contentPoolCode;

}
