package com.heytap.longvideo.search.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Description:推荐算法接口响应
 */
@ToString(callSuper = true)
@Getter
@Setter
public class RecommendAlgorithmResponse implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 状态标识 0正常
     */
    private Integer status;
    /**
     * 推荐数据
     */
    private List<RecommendAlgorithmData> data;
    /**
     * 透传字段
     */
    private String transparent;
    /**
     * 实际推荐物料数
     */
    private Integer num;
    /**
     * 用于链路追踪
     */
    private String traceId;

    private Object ext;

}
