package com.heytap.longvideo.search.model.param.thirdparty.standard;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
public class GetAlbumInfoRequest implements Serializable {
    private static final long serialVersionUID = 1;

    private int pageIndex = 1;

    /**
     * 每页显示行数
     */
    private int pageSize = 20;

    /**
     * 单个查询，可以是自建内容sid或全网内容sid
     */
    private String sid;

    /**
     * 自建节目sid列表
     */
    private List<String> sids;

    /**
     * 全网内容sid列表，和自建sid分开传
     */
    private List<String> allWebSids;

    @Override
    public String toString() {
        return "GetAlbumInfoRequest{" +
                "sid='" + sid + '\'' +
                ", sids=" + sids +
                ", allWebSids=" + allWebSids +
                '}';
    }
}
