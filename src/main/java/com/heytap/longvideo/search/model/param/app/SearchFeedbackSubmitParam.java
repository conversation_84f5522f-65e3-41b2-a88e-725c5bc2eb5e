package com.heytap.longvideo.search.model.param.app;

import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@ToString
public class SearchFeedbackSubmitParam extends NextRequest {
    /**
     * 搜索词
     */
    private String word;

    /**
     * 问题类型
     */
    private Integer type;

    /**
     * 搜索词
     */
    private String note;

    /**
     * 联系电话(AES密文)
     */
    private String phone;

    /**
     * AES/CBC解密向量
     */
    private String iv;

    /**
     * 图片列表（BASE64编码）
     */
    private List<Map<String, Object>> images;
}
