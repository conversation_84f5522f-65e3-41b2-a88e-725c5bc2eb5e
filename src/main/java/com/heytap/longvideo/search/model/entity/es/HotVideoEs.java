package com.heytap.longvideo.search.model.entity.es;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

/*
 * Description 排行榜
 * Date 17:35 2022/4/20
 * Author songjiajia 80350688
*/
@Data
@Document(indexName = "api_hot_video")
public class HotVideoEs implements Serializable {
    private static final long serialVersionUID = 1;
    //节目SID
    @Field(type = FieldType.Keyword)
    private String contentType;

    //节目标题
    @Field(type = FieldType.Keyword)
    private String title;

    @Id
    @Field(type = FieldType.Keyword)
    private String key;

    //热度评分（0~10）
    @Field(type = FieldType.Float)
    private float hotScore;

    public String getKey() {
        return this.getContentType()+this.getTitle();
    }
}
