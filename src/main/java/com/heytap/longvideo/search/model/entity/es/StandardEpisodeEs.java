package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.client.media.entity.StandardEpisode;
import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/29
 */
@Data
@Document(indexName = IndexNameConstant.STANDARD_EPISODE_INDEX)
@Setting(settingPath = "/esConfig/setting.json")
public class StandardEpisodeEs extends StandardEpisode {
    @Id
    private String eid;

    @Field(type = FieldType.Keyword)
    private String sid;

    @Field(type = FieldType.Keyword)
    private String vid;

    @Field(type = FieldType.Keyword)
    private String source;

    @Field(type = FieldType.Keyword)
    private String url;

    @Field(type = FieldType.Keyword)
    private String sourceVideoId;

    @Field(type = FieldType.Keyword)
    private String sourceSite;

    @Field(type = FieldType.Keyword)
    private String episodeTerm;

    @Field(type = FieldType.Keyword)
    private String verticalIcon;

    @Field(type = FieldType.Keyword)
    private String horizontalIcon;

    @Field(type = FieldType.Keyword)
    private String copyrightCode;

    @Field(type = FieldType.Keyword)
    private String subscriptCode;

    @Field(type = FieldType.Keyword)
    private String markCode;

    /**
     * 剧头信息
     */
    private String albumTitle;
    private String score;
    private Integer albumFeatureType;
    private String albumProgramType;
    private Integer albumStatus;
    private Integer albumCompleted;
    private Integer albumPayStatus;
    private String albumTags;
    private String albumArea;
    private Integer albumYear;
    private String albumLanguage;
    private String albumShowTime;
    private String albumBrief;
    private String albumDirector;
    private String albumActor;
    private String albumInformation;
    private String albumProgramInfo;
    private String albumHorizontalIcon;
    private String albumVerticalIcon;
    private String sourceAlbumId;
}
