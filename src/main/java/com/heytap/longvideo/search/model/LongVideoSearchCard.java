package com.heytap.longvideo.search.model;

import com.heytap.longvideo.search.model.param.app.KeyWordSearchResponse;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
public class LongVideoSearchCard implements Serializable {
    private static final long serialVersionUID = 1;
    private String title;
    private String code;
    private String deepLink;
    private Boolean hasMore;
    private List<SearchInterveneCardResponse.SortType> sortTypeList;
    private List<LongVideo> contents =new ArrayList<>();
}
