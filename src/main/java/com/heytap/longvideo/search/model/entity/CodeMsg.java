package com.heytap.longvideo.search.model.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/08/23 15:56
 */
public class CodeMsg {

    private int code;
    private String msg;
    public static CodeMsg SUCCESS = new CodeMsg(200, "success");
    public static CodeMsg PARAM_ERROR = new CodeMsg(400101, "请求参数错误");
    /**
     * Not Found-资源未找到
     */
    public static CodeMsg SC_NOT_FOUND = new CodeMsg(402, "资源未找到");
    public static CodeMsg SC_IS_EXIST = new CodeMsg(403, "资源已存在");

    private CodeMsg() {
    }

    private CodeMsg(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public CodeMsg fillArgs(Object... args) {
        int code = this.code;
        String message = String.format(this.msg, args);
        return new CodeMsg(code, message);
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public void setMsg(final String msg) {
        this.msg = msg;
    }

    public String toString() {
        return "CodeMsg(code=" + this.getCode() + ", msg=" + this.getMsg() + ")";
    }
}


