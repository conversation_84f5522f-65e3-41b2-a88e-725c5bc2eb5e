package com.heytap.longvideo.search.model.entity.es;

import com.heytap.longvideo.search.constants.IndexNameConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Setting;

import java.util.Date;

/**
 * <AUTHOR> Yanping
 * @date 2022/10/24 11:50
 */
@Data
@Document(indexName = IndexNameConstant.UGC_STANDARD_AUTHOR_INDEX)
@Setting(settingPath = "/esConfig/setting.json")
public class UgcStandardAuthorEs {
    @Id
    private String sid;

    private String sourceAuthorId;

    @Field(type = FieldType.Keyword)
    private String authorPhoto;

    @Field(type = FieldType.Keyword)
    private String authorName;

    private String source;

    private Integer sourceStatus;

    private Integer status;

    private Date createTime;

    private Date updateTime;

}