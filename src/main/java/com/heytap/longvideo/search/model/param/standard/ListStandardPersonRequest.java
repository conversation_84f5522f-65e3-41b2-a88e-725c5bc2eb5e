package com.heytap.longvideo.search.model.param.standard;

import com.oppo.browser.common.next.executor.NextRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Description: 影人查询条件
 * @Author: WT
 * @Version: 2.0
 * @Date: 2024/5/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListStandardPersonRequest extends NextRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * @Description: 页码
     */
    private Integer pageIndex = 1;

    /**
     * @Description: 页大小
     */
    private Integer pageSize = 100;

    /**
     * @Description: 自增id
     */
    private Long id;

    /**
     * @Description: 影人sid
     */
    private String sid;

    /**
     * @Description: 艺人姓名
     */
    private String cnName;

    /**
     * @Description: 演员英文名称
     */
    private String foreName;

    /**
     * @Description: 来源类型  1.manual 人工 2.mtime 时光网
     */
    private String source;

    /**
     * @Description: 原始影人ID
     */
    private String sourcePersonId;

    /**
     * @Description: 影人节目状态 影人状态-1“未知”，0 “已屏蔽”，1“未屏蔽”
     */
    private Integer status;

    /**
     * @Description: 排序类型 1：id 2：创建时间 3：更新时间
     */
    private Integer sortType;
}
