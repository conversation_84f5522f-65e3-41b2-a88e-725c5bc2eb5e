package com.heytap.longvideo.search.model.entity.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heytap.longvideo.search.constants.AnalyzerConstant;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import org.springframework.data.elasticsearch.core.completion.Completion;

import java.util.Date;
import java.util.List;


/*
 * Description 剧头(对应es数据)
 * Date 11:01 2021/10/15
 * Author songjiajia 80350688
 */
@Data
@Document(indexName = "api_search_album_v2")
@Setting(settingPath = "/esConfig/setting.json")
public class ProgramAlbumEs {

    //节目SID
    @Field(type = FieldType.Keyword)
    private String sid;

    //节目标题
    @MultiField(mainField = @Field(type = FieldType.Text),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String title;

    //副标题
    @Field(type = FieldType.Keyword)
    private String epstitle;

    //会员角标CODE
    @Field(type = FieldType.Keyword)
    private String markCode;

    //导演（或者主持人）
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String director;

    //演员（或者嘉宾）
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.SHUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.SHUHAO_ANALYZER),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String actor;

    //标签列表
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String tags;

    //标准化后的标签列表
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String mappingTags;

    @Field(type = FieldType.Keyword)
    private String source;

    @Field(type = FieldType.Keyword)
    private int status;

    @Field(type = FieldType.Keyword)
    private String verticalIcon;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String area;

    @Field(type = FieldType.Float, index = false)
    private float sourceScore;

    @Field(type = FieldType.Integer)
    private Integer year;


    @Field(type = FieldType.Integer)
    private int featureType;

    @Field(type = FieldType.Integer)
    private int payStatus;

    @Field(type = FieldType.Keyword)
    private String copyrightCode;

    @Field(type = FieldType.Keyword)
    private String contentType;

    @Field(type = FieldType.Keyword)
    private String horizontalIcon;

    @Field(type = FieldType.Keyword)
    private String programInfo;

    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String language;

    @Field(type = FieldType.Integer)
    private Integer vipType;

    @Field(type = FieldType.Keyword)
    private String showTime;

    private String brief;


    @Id
    @Field(type = FieldType.Keyword)
    private String virtualSid;

    //节目标题（用于拼音搜索，网上有一种方法是直接让分词即支持拼音又支持中文，但是测试效果不了，例如输入哥哥，会搜索出格格，因此还是采用单独字段的方式）
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DEFAULT_PINYIN_ANALYZER, searchAnalyzer = AnalyzerConstant.DEFAULT_PINYIN_ANALYZER),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String titlePinyin;

    //导演（或者主持人）
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.PINYIN_ANALYZER, searchAnalyzer = AnalyzerConstant.PINYIN_ANALYZER, pattern = "\\|"),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String directorPinyin;

    //演员（或者嘉宾）
    @MultiField(mainField = @Field(type = FieldType.Text, analyzer = AnalyzerConstant.PINYIN_ANALYZER, searchAnalyzer = AnalyzerConstant.PINYIN_ANALYZER, pattern = "\\|"),
            otherFields = @InnerField(suffix = "keyword", type = FieldType.Keyword))
    private String actorPinyin;

    @Field(type = FieldType.Float, index = false)
    private float functionScore = 0;

    @Field(type = FieldType.Float, index = false)
    private Float releScore;

    @Field(type = FieldType.Float, index = false)
    private double sortDefine;

    private List<String> multipleSourceCode;

    @Field(type = FieldType.Integer, index = false)
    private Integer isSeries = 0;

    @CompletionField(maxInputLength = 16)
    private Completion seriesTitle;

    @CompletionField(analyzer = AnalyzerConstant.SUGGEST_PINYIN_ANALYZER, searchAnalyzer = AnalyzerConstant.SUGGEST_PINYIN_ANALYZER, maxInputLength = 32)
    private Completion suggestTitlePinyin;

    @CompletionField(analyzer = AnalyzerConstant.SUGGEST_PINYIN_ANALYZER, searchAnalyzer = AnalyzerConstant.SUGGEST_PINYIN_ANALYZER, maxInputLength = 32)
    private Completion seriesTitlePinyin;

    @Field(type = FieldType.Integer, index = false)
    private Integer isHot = 0;

    /**
     * 最新一天的热度
     */
    @Field(type = FieldType.Integer)
    private Integer dayNo;

    @Field(type = FieldType.Long)
    private Long oppoHot;

    @Field(type = FieldType.Long)
    private Long last7DaysPlayPv;

    @Field(type = FieldType.Long)
    private Long last15DaysPlayPv;

    @Field(type = FieldType.Long)
    private Long last30DaysPlayPv;

    @Field(type = FieldType.Long)
    private Long last7DaysClickPv;

    @Field(type = FieldType.Long)
    private Long last15DaysClickPv;

    @Field(type = FieldType.Long)
    private Long last30DaysClickPv;

    @Field(type = FieldType.Integer)
    private Integer hasVirtualSid = 0;

    private String webUrl;

    private String sourceWebUrl;

    private String deepLink;

    private int dpLinkType;

    private int thirdDate = 0;

    private Integer fitAgeMin;

    private Integer fitAgeMax;
    /**
     * 青少年内容池 code 使用逗号分割 eg: cp_00000360,cp_00000485
     */
    @Field(type = FieldType.Text, analyzer = AnalyzerConstant.DOUHAO_ANALYZER, searchAnalyzer = AnalyzerConstant.DOUHAO_ANALYZER)
    private String minorsPoolCode;

    @Field(type = FieldType.Float)
    private Float sourceHot;

    /**
     * 豆瓣评分，从豆瓣处爬取的评分
     */
    @Field(type = FieldType.Float, index = false)
    private Float doubanScore;

    /**
     * oppo评分，通过doubanScore计算得出的
     */
    @Field(type = FieldType.Float)
    private Float oppoScore;

    /**
     * 节目背景色字段
     */
    @Field(type = FieldType.Keyword)
    private String backGroundColorJson;

    /**
     * 有效剧集数
     */
    @Field(type = FieldType.Integer)
    private Integer validEpisode ;

    /**
     * 总剧集数
     */
    @Field(type = FieldType.Integer)
    private Integer totalEpisode ;

    /**
     * 剧头单位
     */
    @Field(type = FieldType.Integer)
    private Integer unit ;

    /**
     * 限时免费开始时间
     */
    @Field(type = FieldType.Date)
    private Date freeStartTime;

    /**
     * 限时免费结束时间
     */
    @Field(type = FieldType.Date)
    private Date freeEndTime;

    /**
     * 节目介绍
     */
    @Field(type = FieldType.Text)
    private String information;
}
