package com.heytap.longvideo.search.model.param.standard;

import com.heytap.longvideo.client.media.annotation.EsField;
import com.heytap.longvideo.search.model.param.PageRequestParam;
import lombok.Data;

/*
 * Description
 * Date 14:47 2022/3/21
 * Author songjiajia 80350688
 */
@Data
public class SearchStandardVideoParams extends PageRequestParam {
    @EsField(name = "sid")
    private String sid;

    @EsField(name = "sourceStatus")
    private String originStatus;
    @EsField
    private String status;
    @EsField
    private String title;
    @EsField
    private String programType;
    @EsField
    private String vid;
    /**
     * 0=免费、1=付费、2=单点付费
     */
    @EsField
    private String payStatus;

    /**
     * 来源 sohu、tencent、mgtv、senyu
     */
    @EsField
    private String source;

    @EsField
    private String tags;

    //1.媒资，2编排
    private int channel = 1;

    /**
     * 排序
     */
    private String order;

    //如下为编排字段
    //视频类型
    @EsField(name = "videoType")
    private String sourceVideoType;
    @EsField
    private String sourceAlbumId;
    @EsField
    private String sourceVideoId;
    @EsField
    private String verifyStatus;
    @EsField
    private String copyrightCode;

    private String videoType;

    @EsField
    private String sportsVideoType;

    private int exact;

    /**
     * 媒资系统视频管理名称
     */
    private String videoManageName;

    @EsField
    private String category;

    private Integer durationMin;

    private Integer durationMax;

    /**
     * 屏蔽优酷
     * 0-返回优酷数据，1-过滤掉优酷数据 默认返回优酷
     */
    private String blockYoukuMobile;
}
