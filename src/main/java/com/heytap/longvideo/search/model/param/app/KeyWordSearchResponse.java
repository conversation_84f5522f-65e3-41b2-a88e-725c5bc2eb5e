package com.heytap.longvideo.search.model.param.app;

import com.heytap.longvideo.search.model.EpisodeVO;
import com.heytap.video.client.entity.video.ButtonVO;
import com.heytap.video.client.entity.video.HighLightVO;
import lombok.*;

import java.util.List;

/*
 * Description 搜索返回对象
 * Date 11:33 2021/11/29
 * Author songjiajia 80350688
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeyWordSearchResponse{

    //节目SID
    private String sid;

    //节目标题
    private String title;

    //会员角标CODE
    private String markCode;

    //会员角标CODE
    private String markCodeUrl;

    //导演（或者主持人）
    private String directors;

    private String source;

    /**
     * 竖图
     */
    private String verticalIcon;

    private String area;

    private Float sourceScore;

    private Integer year;

    /**
     * 0:免费，1：会员免费，2：单片付费
     */
    private int payStatus;

    private String copyrightCode;

    private String contentType;

    /**
     * 横图
     */
    private String horizontalIcon;

    private String programInfo;

    private String languages;

    private Float releScore;

    private String stars;

    private float functionScore;

    private String tags;

    private String virtualSid;

    private int linkType =1;

    private String linkValue;

    private int featureType;

    private int albumFeatureType;

    private List<String> multipleSourceCode;

    private String recommendInfo;

    /**
     * 推荐标签类型
     */
    private String recommendInfoType;

    /**
     * 运营标签图标类型
     */
    private String recommendInfoIconType;

    /**
     * 榜单标签跳转dp字段
     */
    private String recommendInfoDp;

    private String webUrl;

    private String sourceWebUrl;

    private String deepLink;

    private int dpLinkType;

    private String contentTypeName;

    private String brief;

    private Integer thirdDate =0;

    private Integer sortIndex;

    private Integer allowChaseAlbum = 0;
    private Integer chaseAlbumStatus = 0;

    /**
     * 是否来源于算法，
     * 0：否， 1：是
     */
    private Integer aiSource = 0;

    private String minorsPoolCode;

    /**
     * 播放按钮状态 1：播放 2：全网搜 3：立即预约 4：已预约
     */
    private Integer buttonStatus = 1;
    /**
     * 播放按钮文案
     */
    private String buttonText = "播放";

    /**
     * 排序分数
     */
    private Double sortDefine;

    /**
     * 是否展示评分，0：否，1：是
     */
    private int showScore;

    /**
     * 右下角文案
     */
    private String showMsg;
    /**
     * 推荐理由
     */
    private List<HighLightVO> highlights;
    /**
     * 按钮列表
     */
    private List<ButtonVO> buttons;
    /**
     * 选集信息
     */
    private List<EpisodeVO> episodes;

    /**
     * 选集是否显示查看更多选项
     */
    private boolean hasMoreEpisodes;

    /**
     * 背景色值
     */
    private String baseImgColor;

    /**
     * 暗色模式背景色值
     */
    private String darkImgColor;

    /**
     * 基础信息区
     */
    private String baseInfo;
    /**
     * 共集数
     */
    private Integer totalEpisode;
    /**
     * 有效集数
     */
    private Integer validEpisode;

    /**
     * 剧头单位
     */
    private Integer unit;

    /**
     * 对外搜索，按钮文案
     * 0-立即看，1-免费看，2-播放，3-查看
     */
    private String buttonWord;

    /**
     * 外显来源 即跳转应用名
     */
    private String targetAppName;

    /**
     * 卡片类型
     * @see com.heytap.longvideo.search.constants.CardTypeEnum
     */
    private String cardType;

    /**
     * 节目介绍
     */
    private String information;

    /**
     * 来源类型
     * 1-合作源 2-非合作官方源 3-非官方源
     */
    private Integer sourceKind;

    /**
     * 播放源名称
     */
    private String playSourceName;

    /**
     * 播放源图标
     */
    private String playSourceIcon;

    /**
     * 视频APP下载地址
     */
    private String downloadUrl;

    /**
     * 合作方专辑ID
     */
    private String sourceAlbumId;
}