package com.heytap.longvideo.search.config;

import com.heytap.longvideo.search.properties.MqProperties;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;
import java.util.Random;

@Configuration
public class ExternalSearchKafkaProducerConfig {

    @Autowired
    private MqProperties mqProperties;

    @Bean
    public Producer<String,String> initKafkaProducer(){
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, mqProperties.getThirdPartyExternalSearchServers());
        props.put(ProducerConfig.CLIENT_ID_CONFIG, mqProperties.getThirdPartyExternalSearchClientId());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.RETRIES_CONFIG, 2);
        return new KafkaProducer<>(props);
    }
}
