package com.heytap.longvideo.search.config;

import com.heytap.cpc.dfoob.circuit.eye.client.protocol.filter.CircuitSpringbootFilter;
import com.oppo.basic.heracles.client.core.HeraclesClientSettings;
import com.oppo.browser.dfoob.channel.ConfigSource;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.impl.ReactiveHttpDataChannel;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * @Author: 80339123 liu ying
 * @Date: 2021/8/2 13:42
 */
@Configuration
public class HttpDataChannelConfig {

    @Bean
    public HttpDataChannel httpDataChannel() {
        return ReactiveHttpDataChannel
                .builder()
                .loginAs(HeraclesClientSettings.getInstance().getAppId(),"token", "longvideo-search-rest")
                .configurationSource(ConfigSource.LOCAL_FILE)
                .build();
    }

    @Bean
    public FilterRegistrationBean registFilter() {
        return CircuitSpringbootFilter.registerFilter();
    }
}
