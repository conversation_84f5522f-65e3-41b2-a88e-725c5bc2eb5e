package com.heytap.longvideo.search.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/*
 * Description自定义线程池
 * Date 12:55 2022/5/9
 * Author songjiajia 80350688
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Bean("commonThreadPool")
    public Executor commonThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(40);
        executor.setQueueCapacity(1000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix("commonThreadPool");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(6);
        executor.initialize();
        return executor;
    }

    @Bean("csvFileThreadPool")
    public Executor csvFileThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(0);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix("CsvFileThreadPool");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(6);
        executor.initialize();
        return executor;
    }

    @Bean("imageFileThreadPool")
    public Executor imageFileThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(6);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(0);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix("ImageFileThreadPool");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(6);
        executor.initialize();
        return executor;
    }

    @Bean("upLoadToOcsThreadPool")
    public Executor upLoadToOcsThreadPool() {
        ThreadFactory csvThreadFactory = new CustomizableThreadFactory("UpLoadToOcsThreadPool");
        return new ThreadPoolExecutor(
                5,
                5,
                0L,
                TimeUnit.MILLISECONDS,
                new SynchronousQueue<>(),
                csvThreadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}

