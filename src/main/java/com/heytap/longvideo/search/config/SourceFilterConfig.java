package com.heytap.longvideo.search.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> ye mengsheng
 * @Description：优酷接入开关控制
 * @Version: 1.0
 * @date 2024/10/18 上午11:35
 */
@Data
@Component
@Configuration
public class SourceFilterConfig {

    /**
     * 统一控制优酷节目是否下发开关
     * 0：关闭，过滤优酷节目; 1：打开，正常下发
     */
    @HeraclesDynamicConfig(key = "vod.youkumobile.filter.switch", fileName = "program_source_filter.properties")
    private Integer youkuSwitch;

    /**
     * 控制低版本过滤优酷还是下发兜底h5升级页开关， 0：过滤优酷节目 1：低版本下发H5升级页
     */
    @HeraclesDynamicConfig(key = "vod.youkumobile.lowerVersion.filter.switch", fileName = "program_source_filter.properties")
    private Integer youkuVersionSwitch;

    /**
     * 三方接口场景是否下发优酷内容开关
     * 0：关闭
     * 1：打开
     */
    @HeraclesDynamicConfig(key = "third.party.youkumobile.filter.switch", fileName = "program_source_filter.properties")
    private Integer youkuthirdPartySwitch;

    /**
     * 对于优酷内容，低版本展示H5兜底的url
     */
    @HeraclesDynamicConfig(key = "upgrade.page.url", fileName = "program_source_filter.properties")
    private String upgradePageUrl;

    /**
     * 优酷引入版本，71600格式
     */
    @HeraclesDynamicConfig(key = "vod.youkumobile.version", fileName = "program_source_filter.properties")
    private Integer youkuMobileVersion;

    /**
     * 支持跳转web快应用的最低快应用版本号
     */
    @HeraclesDynamicConfig(key = "vod.min.webQuickVersion", fileName = "program_source_filter.properties")
    private Integer minWebQuickVersion;

    /**
     * 优酷引入版本，7.16.0格式
     */
    @HeraclesDynamicConfig(key = "vod.youkumobile.appVersion", fileName = "program_source_filter.properties")
    private String youkuMobileAppVersion;

}
