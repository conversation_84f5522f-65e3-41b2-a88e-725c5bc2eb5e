package com.heytap.longvideo.search.config;

import com.heytap.longvideo.search.constants.MqConstant;
import com.heytap.longvideo.search.mq.MediaJinsConsumerService;
import com.heytap.longvideo.search.mq.ThirdPartyMediaSyncListener;
import com.heytap.longvideo.search.properties.MqProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;

/*
 * Description 媒资jins mq配置
 * Date 17:51 2022/3/24
 * Author songjiajia 80350688
 */
@Component
public class MediaJinsMqConsumerConfig {

    private static final Logger log = LoggerFactory.getLogger("updatelog");

    private final MqProperties mqConfig;

    private final MediaJinsConsumerService mediaJinsConsumerService;

    private final ThirdPartyMediaSyncListener thirdPartyMediaSyncListener;

    public MediaJinsMqConsumerConfig(MqProperties mqConfig,
                                     MediaJinsConsumerService mediaJinsConsumerService,
                                     ThirdPartyMediaSyncListener thirdPartyMediaSyncListener) {
        this.mqConfig = mqConfig;
        this.mediaJinsConsumerService = mediaJinsConsumerService;
        this.thirdPartyMediaSyncListener = thirdPartyMediaSyncListener;
    }

    @Bean(name = "thirdPartyMediaSyncConsumer")
    private DefaultMQPushConsumer thirdPartyMediaSyncConsumer() throws Exception {
        log.info("start init thirdPartyMediaSyncConsumer");
        String topic = mqConfig.getThirdPartyMediaTopic();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqConfig.getThirdPartyMediaConsumerGroup());
        consumer.setNamesrvAddr(mqConfig.getThirdPartyMediaNameServer());
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setConsumeThreadMin(mqConfig.getThirdPartyMediaThreadMin());
        consumer.setConsumeThreadMax(mqConfig.getThirdPartyMediaThreadMax());
        consumer.setMaxReconsumeTimes(mqConfig.getThirdPartyMediaMaxReconsumeTimes());
        consumer.subscribe(topic, "*");
        consumer.setInstanceName(mqConfig.getThirdPartyMediaInstanceName());
        consumer.registerMessageListener(thirdPartyMediaSyncListener);

        try {
            consumer.start();
        } catch (Exception e) {
            log.error("{} consumer init fail", topic);
            throw e;
        }
        log.info("{} consumer init success", topic);
        return consumer;
    }

    @Bean(name = "thirdPartyMediaSyncDeadLetterConsumer")
    private DefaultMQPushConsumer thirdPartyMediaSyncDeadLetterConsumer() throws Exception {
        log.info("start init thirdPartyMediaSyncDeadLetterConsumer");
        String topic = mqConfig.getThirdPartyMediaDeadLetterTopic();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(mqConfig.getThirdPartyMediaDeadLetterConsumerGroup());
        consumer.setNamesrvAddr(mqConfig.getThirdPartyMediaDeadLetterNameServer());
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.subscribe(topic, "*");
        consumer.setInstanceName(mqConfig.getThirdPartyMediaDeadLetterInstanceName());
        consumer.registerMessageListener(new MessageListenerConcurrently() {

            private final Logger mqLogger = LoggerFactory.getLogger("RocketmqClient");

            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext context) {
                if (CollectionUtils.isEmpty(msgList)) {
                    mqLogger.info("[ThirdPartyMediaSyncDeadLetterListener] messageList is null");
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }

                try {
                    for (MessageExt msg : msgList) {
                        // 打印死信消息
                        mqLogger.info("[ThirdPartyMediaSyncDeadLetterListener] received dead letter message: " + new String(msg.getBody(), RemotingHelper.DEFAULT_CHARSET));
                    }
                } catch (Exception e) {
                    mqLogger.error("[ThirdPartyMediaSyncDeadLetterListener] print deadLetter message error:", e);
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });

        try {
            consumer.start();
        } catch (Exception e) {
            log.error("{} consumer init fail", topic);
            throw e;
        }
        log.info("{} consumer init success", topic);
        return consumer;
    }


    @Bean(name = "mediaJinsConsumer")
    private DefaultMQPushConsumer jinsConsumer() throws Exception {
        log.info("start init mediaJinsConsumer");
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MqConstant.DEFAULT_CONSUMER_GROUP+"mediajins");
        String topic = mqConfig.getMediaJinsTopic();
        consumer.setNamesrvAddr(mqConfig.getMediaJinsNameServer());
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.subscribe(topic, "*");
        consumer.setInstanceName("search-mediaJinsConsumer");
        consumer.registerMessageListener(mediaJinsConsumerService);
        try {
            consumer.start();
        } catch (Exception e) {
            log.error("{} consumer init fail", topic);
            throw e;
        }
        log.info("{} consumer init success", topic);
        return consumer;
    }
}