package com.heytap.longvideo.search.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Data
@Component
public class EsMediaCacheConfig {

    @HeraclesDynamicConfig(key = "cache.unofficial.albumEs.ttl.seconds", fileName = "es_media_config.properties")
    private int albumEsCacheTtl = 86400;

    /**
     * 缓存最大散列时间，避免缓存集中失效 真正ttl = 配置ttl + random(0，maxCacheHashTimes)
     */
    @HeraclesDynamicConfig(key = "cache.max.hash.time.seconds", fileName = "es_media_config.properties")
    private int maxCacheHashTimes = 43200;
}
