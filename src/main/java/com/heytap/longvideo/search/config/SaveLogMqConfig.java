package com.heytap.longvideo.search.config;

import com.heytap.longvideo.search.mq.SaveLogConsumerService;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@Slf4j
public class SaveLogMqConfig {

    @HeraclesDynamicConfig(key = "rocketmq.name-server.saveLog", fileName = "mq.properties")
    private String namesrvAddr;

    @HeraclesDynamicConfig(key = "rocketmq.topic.operationLog", fileName = "mq.properties")
    private String topic;

    @Autowired
    private SaveLogConsumerService saveLogConsumerService;



    @Bean(name = "saveLogsConsumer")
    public DefaultMQPushConsumer jinsConsumer() throws Exception {
        log.info("start init mediaJinsConsumer");
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(topic+"_search_ConsumerGroup");
        consumer.setNamesrvAddr(namesrvAddr);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.subscribe(topic, "*");
        consumer.registerMessageListener(saveLogConsumerService);
        try {
            consumer.start();
        } catch (Exception e) {
            log.error("{} consumer init fail", topic);
            throw e;
        }
        log.info("{} consumer init success", topic);
        return consumer;
    }
}
