package com.heytap.longvideo.search.config;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.search.constants.ContentTypeEnum;
import com.heytap.longvideo.search.job.RefreshVirtualProgramJob;
import com.heytap.longvideo.search.service.app.HotVideoService;
import com.oppo.basic.heracles.client.core.store.KVStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@Slf4j
public class ApplicationListenerImpl implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private HotVideoService hotVideoService;

    @Autowired
    private RefreshVirtualProgramJob refreshVirtualProgramJob;


    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
        try {
            KVStore.Notify.getInstance().addPropertiesListener("hot_video_config.properties", new KVStore.Notify.PropertiesUpdateListener() {
                @Override
                public void handleEvent(String key, Object newValue, Object oldValue) {
                    Set<String> hotSet = JSON.parseObject(newValue.toString(), Set.class);
                    ContentTypeEnum contentTypeEnum = hotVideoService.getContentTypeEnumByConfigName(key);
                    hotVideoService.updateHeraclesConfigVideoToEs(contentTypeEnum, hotSet);
                    hotVideoService.initHotVideoSensitiveWordMap(contentTypeEnum);
                }
            });
            hotVideoService.insertAllHeraclesConfigVideoToEs();
            hotVideoService.insertRankCollectVideoToEs();
            for (ContentTypeEnum contentTypeEnum : ContentTypeEnum.values()) {
                hotVideoService.initHotVideoSensitiveWordMap(contentTypeEnum);
            }
            refreshVirtualProgramJob.createCache();
            log.info("onApplicationEvent success");
        } catch (Exception e) {
            log.error("onApplicationEvent error,{}", e);
        }
    }
}