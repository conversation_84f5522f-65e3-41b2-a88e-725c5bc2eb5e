package com.heytap.longvideo.search.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> ye mengsheng
 * @date 2024/9/6 上午9:26
 */

@Component
@Data
public class CsvExportConfig {

    /**
     * ak信息
     */
    @HeraclesDynamicConfig(key = "ocs.AccessKeyId", fileName = "configure.properties")
    private String AccessKeyId;
    /**
     * sk信息
     */
    @HeraclesDynamicConfig(key = "ocs.AccessKeySecret", fileName = "configure.properties")
    private String AccessKeySecret;
    /**
     * 域名
     */
    @HeraclesDynamicConfig(key = "ocs.EndPoint", fileName = "configure.properties")
    private String EndPoint;
    /**
     * 区域
     */
    @HeraclesDynamicConfig(key = "ocs.Region", fileName = "configure.properties")
    private String Region;

    /**
     * 桶名
     */
    @HeraclesDynamicConfig(key = "ocs.BucketName", fileName = "configure.properties")
    private String BucketName;

    /**
     * Ocs的文件路径，前后都没有/
     * Ex. aaa/bbb/ccc
     */
    @HeraclesDynamicConfig(key = "ocs.OcsPath", fileName = "configure.properties")
    private String OcsPath;

    /**
     * csv 本地临时路径
     */
    @HeraclesDynamicConfig(key = "ocs.LocalPath", fileName = "configure.properties")
    private String LocalPath;
}