package com.heytap.longvideo.search.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR> Yanping
 * @date 2025/6/5
 */
@Data
@Slf4j
@Configuration
public class ThirdPartyCommonConfig {

    @HeraclesDynamicConfig(key = "third.party.default.deeplink.detail.page.style", fileName = "third-party-common.properties")
    private int defaultDpDetailPageStyle = 1;

    @HeraclesDynamicConfig(key = "third.card.default.deeplink.detail.page.style.map", fileName = "third-party-common.properties", textType = TextType.JSON)
    private Map<String, Integer> defaultDpDetailStyleMap;

    @HeraclesDynamicConfig(key = "unofficial.album.rocketmq.name-server", fileName = "mq.properties")
    private String nameServer;

    @HeraclesDynamicConfig(key = "unofficial.album.rocketmq.producer.group", fileName = "mq.properties")
    private String producerGroup = "longvideo-search-rest-unofficial-album";

    @HeraclesDynamicConfig(key = "unofficial.album.rocketmq.instance.name", fileName = "mq.properties")
    private String instanceName = "longvideo-search-rest-unofficial-album-instance";


    @Bean
    public DefaultMQProducer initProducer() throws MQClientException {
        log.info("init Producer begin");
        DefaultMQProducer producer = new DefaultMQProducer(producerGroup);
        producer.setNamesrvAddr(nameServer);
        producer.setInstanceName(instanceName);
        producer.start();
        log.info("init Producer end");
        return producer;
    }
}