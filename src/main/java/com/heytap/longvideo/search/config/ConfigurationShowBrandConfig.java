package com.heytap.longvideo.search.config;

import com.heytap.longvideo.search.constants.SearchConstant;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: 配置
 *
 * <AUTHOR> 80237102
 * @date 2021/12/31 14:11
 */
@Configuration
@Slf4j
@Getter
public class ConfigurationShowBrandConfig {

    private final static String SHOW_BRAND = "&showBrand=%s";

    // appId 与 结果卡关系
    public final static Map<String,String> APPID_AND_CARD_MAP = new HashMap<String,String>(){{
        // 浏览器appId对应卡
        put("browser_sousuozhida", SearchConstant.BROWSER_RESULT_CARD);

        // 全搜appid与对应卡
        put("quansou_sousuozhida", SearchConstant.QS_RESULT_CARD);

        // 锁屏appId对应卡
        put("screenoff_searchbanner", SearchConstant.MAGAZINE_RESULT_CARD);

        // 小布appid与对应卡
        put("breeno_searchresults", SearchConstant.BREENO_RESULT_CARD);
    }};

    @HeraclesDynamicConfig(key = "scene.show.brand", fileName = "configure.properties",textType = TextType.JSON)
    private Map<String,Integer> showBrandMap;

    /**
     * 获取三方场景展示配置
     */
    public String getShowBrandConfig(String cardName){
        if(MapUtils.isEmpty(showBrandMap) || !showBrandMap.containsKey(cardName)){
            return String.format(SHOW_BRAND, 0);
        }
        return String.format(SHOW_BRAND, showBrandMap.get(cardName));
    }

    /**
     * 通过appId获取配置
     */
    public String getShowBrandConfigByAppId(String appId){
        if(MapUtils.isEmpty(APPID_AND_CARD_MAP) || !APPID_AND_CARD_MAP.containsKey(appId)){
            return String.format(SHOW_BRAND, 0);
        }
        return getShowBrandConfig(APPID_AND_CARD_MAP.get(appId));
    }
}