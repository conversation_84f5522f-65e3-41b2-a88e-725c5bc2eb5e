<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <app.encoding>UTF-8</app.encoding>
        <app.version>1.0.0</app.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
        <!--suppress UnresolvedMavenProperty -->
        <java.home>${env.JAVA_HOME}</java.home>
        <powermock.version>2.0.2</powermock.version>
        <jacoco.version>0.8.5</jacoco.version>
        <dubbo.extension.version>1.0.6.RELEASE</dubbo.extension.version>
        <org.springframework.version>5.1.4.RELEASE</org.springframework.version>
        <junit.version>4.12</junit.version>
        <spring.boot.version>2.3.0.RELEASE</spring.boot.version>
        <log4j.version>2.17.2</log4j.version>
        <video-framework.version>0.0.71</video-framework.version>
    </properties>

    <groupId>com.heytap.longvideo</groupId>
    <artifactId>longvideo-search-rest</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>longvideo-search-rest</name>
    <description>The project of longvideo-search-rest</description>

    <!-- maven私服配置 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>http://nexus.os.adc.com/nexus/content/repositories/releases/</url>
        </repository>

        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.os.adc.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jcl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.16.1</version>
            </dependency>
            <dependency>
                <groupId>com.heytap.video</groupId>
                <artifactId>framework-models</artifactId>
                <version>0.0.72</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>longvideo-search-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <artifactId>framework-components-core</artifactId>
            <groupId>com.heytap.video</groupId>
            <version>${video-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.oppo.cpc</groupId>
            <artifactId>com-oppo-cpc-video-framework-lib</artifactId>
            <version>3.0.52</version>
            <exclusions>
                <exclusion>
                    <groupId>com.oppo.basic.heracles</groupId>
                    <artifactId>heracles-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>esa</groupId>
                    <artifactId>commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-test</artifactId>
                </exclusion>
                <!--勿改成<artifactId>*</artifactId>，云平台不生效-->
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jdbc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-recipes</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-test</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>

            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.oppo.basic.heracles</groupId>
            <artifactId>heracles-client</artifactId>
            <version>2.3.12</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.62</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>${spring.boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--
                <dependency>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jdbc</arspringtifactId>
                    <version>5.2.2.RELEASE</version>
                </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <version>${spring.boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <version>${spring.boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.26</version>
        </dependency>

        <dependency>
            <groupId>com.github.stuxuhai</groupId>
            <artifactId>jpinyin</artifactId>
            <version>1.1.8</version>
        </dependency>

        <!--单元测试依赖 begin-->
        <dependency>
            <groupId>com.heytap.cpc.dfoob</groupId>
            <artifactId>goblin-all</artifactId>
            <version>1.0.7</version>
            <type>pom</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.heytap.cpc.dfoob</groupId>
            <artifactId>goblin-container-obox</artifactId>
            <version>1.0.7</version>
        </dependency>


        <!--单元测试依赖 end-->

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.8</version>
        </dependency>

        <dependency>
            <groupId>com.heytap.media</groupId>
            <artifactId>longvideo-media-api</artifactId>
            <version>2.1.30</version>
            <exclusions>
                <exclusion>
                    <artifactId>com-oppo-cpc-video-framework-lib</artifactId>
                    <groupId>com.oppo.cpc</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0-OPEXT-NO-CHECK-RELEASE-2</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-client-java</artifactId>
            <version>5.13.2</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>esa</groupId>
            <artifactId>esa-rpc-spring-boot-starter</artifactId>
            <version>1.1.7.GA</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>esa-rpc-config</artifactId>
                    <groupId>esa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.oppo.euler</groupId>
            <artifactId>euler-discovery-esarpc</artifactId>
            <version>1.1.35</version>
        </dependency>


        <dependency>
            <groupId>com.oppo.paas</groupId>
            <artifactId>cloudjob-spring-boot-starter</artifactId>
            <version>1.0.0-beta1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
            <version>1.1.0-cdh5.14.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-recipes</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.heytap.longvideo</groupId>-->
<!--            <artifactId>longvideo-arrange-api</artifactId>-->
<!--            <version>2.1.37</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>arrange-search-api</artifactId>
            <version>1.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.heytap.video</groupId>
            <artifactId>search-client-lib</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>esa</groupId>
            <artifactId>esa-rpc-test-support</artifactId>
            <version>1.1.7.GA</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>

        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>2.11.0</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>

        <dependency>
            <groupId>com.heytap.video</groupId>
            <artifactId>com-heytap-video-common-lib</artifactId>
            <version>3.1.20</version>
        </dependency>
        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>video-common-client-lib</artifactId>
            <version>1.3.44</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.heytap.video</groupId>
            <artifactId>video-pb-client-lib</artifactId>
            <version>1.0.57</version>
        </dependency>

        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>com-heytap-longvideo-common-lib</artifactId>
            <version>1.0.60</version>
            <exclusions>
                <exclusion>
                    <artifactId>jedis</artifactId>
                    <groupId>redis.clients</groupId>
                </exclusion>
                <exclusion>
                    <groupId>esa</groupId>
                    <artifactId>commons</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>framework-trace-async</artifactId>
                    <groupId>com.oppo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>com-oppo-cpc-video-framework-lib</artifactId>
                    <groupId>com.oppo.cpc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- PowerMock 依赖 -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>

        <!--HTTP服务接入欧拉-->
        <dependency>
            <groupId>com.oppo.euler</groupId>
            <artifactId>euler-compatible-provider-restlight-starter</artifactId>
            <version>1.1.9</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.16.1</version>
        </dependency>

        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>com-heytap-longvideo-vip-sdk</artifactId>
            <version>2.0.31</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>framework-components-core</artifactId>
                    <groupId>com.heytap.video</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>arrange-list-api</artifactId>
            <version>2.0.10</version>
        </dependency>
        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>arrange-default-api</artifactId>
            <version>0.0.14</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>assembly/conf</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>

        <!--单元测试时引用src/main/resources下的资源文件-->

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>

            <!--jacoco单测覆盖率插件 begin-->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>

                <!--include、exclude指定jacoco 扫描 和 排除 的class文件路径，根据自身项目修改-->
                <configuration>
                    <excludes>
                        <exclude>**/bootstrap/*.class</exclude>
                        <exclude>**/config/*.class</exclude>
                        <exclude>**/model/**/*.class</exclude>
                        <exclude>**/constants/*.class</exclude>
                        <exclude>**/utils/*.class</exclude>
                        <exclude>**/controller/*.class</exclude>
                        <exclude>**/controller/thirdparty/*.class</exclude>
                        <exclude>**/executors/**/*.class</exclude>
                        <exclude>**/job/*.class</exclude>
                        <exclude>**/properties/*.class</exclude>
                        <!--废弃接口 不补单测-->
                        <exclude>**/app/RelationRecommendService.class</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>target/jacoco.exec</dataFile>
                            <outputDirectory>target/site/jacoco</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>longvideo-search-rest-${app.version}-${maven.build.timestamp}</finalName>
                    <descriptors>
                        <descriptor>assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>3.2.0</version>
            <configuration>
                <configLocation>checkstyle.xml</configLocation>
            </configuration>
            <executions>
                <execution>
                    <phase>validate</phase>
                    <goals>
                        <goal>check</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
        </plugins>
    </build>
</project>